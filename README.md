# Microservice Template

## Overview
This microservice template is designed as a starting point for creating new services in our ecosystem. It encapsulates our best practices, standard configurations, and leverages the building blocks we've established for efficient and consistent service development.

This repository is a template that can be used when creating a new repository. The template includes a structure compliant with Clean Architecture as well as an example of implementing a busniess feature. The example should be removed after using the template and replaced with a real Use Case

### CI/CD

CoverGo is using a combination of Github actions workflow, custom Github actions and templating tool gflows to produce a workflow for your service. Workflow template is stored in Covergo/gflows public repository.

**Update Configuration**
- After using the template, you should go to the  **build-publish.settings.yml** file (**.gflows/workflow-configuration/build-publish/build-publish.settings.yml**) and replace all occurrences of "ServiceName" with the target service name.
- Then, update the CI/CD related configuration files by running the command **gflows update**. If the gflows tool is not installed, follow the instructions in this -> [GFlows tool](https://covergo.atlassian.net/wiki/spaces/BE/pages/703397889/Install+gflows+system-wide)


### Key Features
- **Pre-configured with Building Blocks**: Includes our core building blocks like Data Access, Observability (logs, metrics, tracing), API and more.
- **Domain-Driven Design**: Structured around the principles of Domain-Driven Design (DDD) to ensure clear domain modeling.
- **Clean Architecture**: Adheres to the principles of Clean Architecture for maintainability and scalability.
- **Configurability**: Easy to configure for different environments and scenarios (will be added in the future)

### Prerequisites
- .NET 7.0 or later
- Docker (for containerization and orchestration)
- Any other specific dependencies our services typically require

### Usage
To use this template, clone the repository and rename the solution and projects as per your new service requirements. 
Follow the guidelines provided in the comments for customizing the service to fit your specific domain.
Remove folders with examples in all layers.


### Customization
- **Domain Models**: Define your entities and value objects in the domain project. In this layer, the objects encompass both the data and the logic that are intrinsic to the Domain. This logic is distinct from and unaffected by the business processes that activate it. These objects operate independently and remain completely oblivious to the Application Layer.
- **Data Access**: Use repository for data access (by default we use mongo db, but in specific cases we can choose other option)
- **Business Logic**: Place your business logic in the domain layer and uses cases in the application layer. 
The use cases are the processes that can be triggered in the Application Core by one or several User Interfaces (API, integration event)
- **API Controllers**: Define your endpoints in the API layer. Only endpoint, nothing more, never place a business logic here.

### Testing GraphQL API Instructions

Follow these steps to test the GraphQL API:

1. **Run Docker Compose**:
    - Make sure Docker is installed on your system.
    - Navigate to the directory containing the Docker Compose file.
    - Run the following command to start the services:
      ```bash
      docker-compose up
      ```

2. **GraphQL Service Address**:
    - After Docker Compose has successfully started, the example of service for business domain (GraphQL) will be available at the following address:
      ```http
      http://localhost:5201/graphql/
      ```


3. **Testing GraphQL Endpoint**:
    - You can test the GraphQL endpoint using Postman or any other tool of your choice.
    - Set up a new request in your chosen tool.
    - Use the HTTP POST method.
    - Set the request URL to the GraphQL service address mentioned above.
    - Include the GraphQL query or mutation in the request body.
    - Send the request and observe the response.

     ```http
      http://localhost:5201/graphql/
      ```

## Additional Resources
- [Application Architecture](https://covergo.atlassian.net/wiki/spaces/BE/pages/1141145643/Application+Architecture)
- [Building blocks](https://covergo.atlassian.net/wiki/spaces/BE/pages/1141145690/Building+Blocks+BB)
- [Coding Standards](https://covergo.atlassian.net/wiki/spaces/BE/pages/899710983/Code+Style+Guide)
- [API guidelines](https://covergo.atlassian.net/wiki/spaces/Engineering/pages/1171521775/API+Guidelines)
