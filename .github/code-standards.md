# CoverGo Policies V3 - Code Standards & Rules

## 🎯 Overview

This document establishes mandatory coding standards for the .NET 9 Policies V3 microservice. These rules ensure consistency, maintainability, and adherence to established patterns across the codebase.

## 🚫 Critical Anti-Patterns (NEVER DO THESE)

### ❌ Magic Strings - ZERO TOLERANCE

```csharp
// ❌ FORBIDDEN
var connectionString = configuration["DefaultConnection"];
throw new ArgumentException("Policy not found");
var columnName = "policy_id";

// ✅ REQUIRED
var connectionString = configuration[ConfigurationConstants.ConnectionStrings.DefaultConnection];
throw new ArgumentException(ErrorCodes.PolicyNotFound);
var columnName = DatabaseNamingHelpers.GetColumnName<Policy>("PolicyId");
```

### ❌ Business Logic in Wrong Layers

```csharp
// ❌ FORBIDDEN: Business logic in Application layer
public class CreatePolicyHandler
{
    public async Task<Result> Handle(Command cmd)
    {
        if (cmd.StartDate > cmd.EndDate) // Business rule in application layer
            return Result.Failure("Invalid dates");
    }
}

// ✅ REQUIRED: Business logic in Domain layer
public class Policy
{
    public Result<Policy> Create(DateOnly startDate, DateOnly endDate)
    {
        if (startDate > endDate)
            return Result.Failure(Errors.InvalidDateRange("startDate"));
        // Business validation here
    }
}
```

### ❌ Defensive Null Checks with Proper Typing

```csharp
// ❌ FORBIDDEN: Defensive checks when types guarantee non-null
public Result ProcessPolicy(required Policy policy)
{
    if (policy == null) return Result.Failure(...); // WRONG - 'required' guarantees non-null
}

// ✅ REQUIRED: Use proper typing
public record CreatePolicyCommand
{
    public required string PolicyNumber { get; init; } // Compiler enforced
}
```

## 🏗️ Strongly-Typed Domain IDs

### Mandatory Implementation Pattern

```csharp
// ✅ REQUIRED: All domain IDs must follow this exact pattern
public record PolicyId(Guid Value) : ValueObject<Guid>(Value)
{
    public static PolicyId Empty => new(Guid.Empty);
    public static PolicyId New => new(Guid.CreateVersion7()); // Use Version 7 UUIDs

    /// <summary>
    /// Implicit conversion from Guid to PolicyId
    /// </summary>
    public static implicit operator PolicyId(Guid value) => new(value);

    /// <summary>
    /// Implicit conversion from string to PolicyId
    /// </summary>
    public static implicit operator PolicyId(string value) =>
        string.IsNullOrWhiteSpace(value)
            ? throw new ArgumentException("PolicyId cannot be null or empty", nameof(value))
            : new(Guid.Parse(value));

    /// <summary>
    /// Implicit conversion from PolicyId to Guid
    /// </summary>
    public static implicit operator Guid(PolicyId policyId) => policyId.Value;
}
```

### ID Validation Rules

```csharp
// ✅ REQUIRED: Consistent validation patterns
- Pattern A (PolicyId, PolicyMemberId): ArgumentException for null/empty, FormatException for invalid GUID
- Pattern B (EndorsementId): ArgumentException for all invalid inputs
- Pattern C (PolicyMemberStateId): ArgumentException for null, FormatException for invalid GUID
```

## 🛡️ Unified Validation System

### Result Pattern (MANDATORY)

```csharp
// ✅ REQUIRED: Always use Result pattern for operations that can fail
public async Task<Result<Policy>> CreatePolicyAsync(CreatePolicyCommand command)
{
    // Validation
    if (string.IsNullOrEmpty(command.PolicyNumber))
        return Result.Failure(Errors.Required("policyNumber", "Policy Number"));

    // Business logic
    var policy = Policy.Create(command.PolicyNumber, command.ProductId);
    return policy.IsSuccess ? Result<Policy>.Success(policy.Value) : Result<Policy>.Failure(policy.Errors);
}
```

### ValidationError Creation (Three Approved Methods)

```csharp
// Method 1: Errors Factory (PREFERRED - Most Concise)
var error = Errors.Required("member.email", "Email Address");
var error = Errors.OutOfRange("member.age", 18, 65, "Age");
var error = Errors.InvalidPlanId("planId", planId, availablePlans, "Plan ID");

// Method 2: ValidationErrorBuilder (For Complex Context)
var error = ValidationErrorBuilder.For("member.salary")
    .WithLabel("Annual Salary")
    .WithContext("Currency", "USD")
    .WithContext("MinValue", 30000)
    .OutOfRange(30000, 500000);

// Method 3: Direct Constructor (Full Control)
var error = new ValidationError(
    ErrorCodes.MemberIdTaken,
    "memberId",
    "Member ID",
    new Dictionary<string, object?> { ["ExistingId"] = existingId }
);
```

### Error Code Standards

```csharp
// ✅ REQUIRED: Use constants from ErrorCodes class
public static class ErrorCodes
{
    public const string Required = "REQUIRED";
    public const string InvalidFormat = "INVALID_FORMAT";
    public const string UniqueViolation = "UNIQUE_VIOLATION";
    public const string OutOfRange = "OUT_OF_RANGE";
    public const string PolicyNotFound = "POLICY_NOT_FOUND";
    // Never inline error codes as strings
}
```

## 📊 Database & Entity Configuration Rules

### Naming Convention (MANDATORY)

```csharp
// ✅ REQUIRED: Use DatabaseNamingHelpers for ALL database names
public class PolicyConfiguration : IEntityTypeConfiguration<Policy>
{
    public void Configure(EntityTypeBuilder<Policy> builder)
    {
        // Table name
        builder.ToTable(DatabaseNamingHelpers.GetTableName<Policy>()); // "policies"

        // Primary key
        builder.HasPrimaryKeyWithAutoName(x => x.Id); // "pk_policies"

        // Indexes
        builder.HasIndexWithAutoName(x => x.PolicyNumber); // "ix_policies_policy_number"
        builder.HasUniqueIndexWithAutoName(x => x.PolicyNumber); // "uix_policies_policy_number"

        // Foreign keys
        builder.HasForeignKeyWithAutoName<Policy, Product>(x => x.ProductId, p => p.Policies);

        // Columns
        builder.HasColumnWithAutoName(x => x.PolicyNumber); // "policy_number"
    }
}
```

### Entity Patterns

```csharp
// ✅ REQUIRED: All entities must follow this pattern
public class PolicyMember : AggregateRootBase<PolicyMemberId>
{
    #region Constructors
    private PolicyMember(PolicyMemberId id) : base(id) { }
    public PolicyMember() : this(PolicyMemberId.Empty) { }
    #endregion

    #region Fields / Properties
    public required string MemberId { get; init; }
    public required PolicyId PolicyId { get; init; }

    // Concurrency Control - maps to PostgreSQL xmin system column
    [Timestamp]
    public uint RowVersion { get; set; }
    #endregion

    #region Factory Methods
    public static PolicyMember Create(PolicyId policyId, string memberId, DateOnly startDate, DateOnly? endDate, string planId)
    {
        // Business logic and validation here
    }
    #endregion
}
```

## 🏛️ CQRS Implementation Rules

### Command Structure (MANDATORY)

```csharp
// ✅ REQUIRED: Commands must be immutable records or classes with init-only properties
public class CreatePolicyMemberCommand : ICommand<CreatePolicyMemberResponse>
{
    public required string MemberId { get; init; }
    public required Guid PolicyId { get; init; }
    public DateOnly? StartDate { get; init; }

    // Constructor validation for fail-fast behavior
    public CreatePolicyMemberCommand()
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(MemberId);
        ArgumentNullException.ThrowIfNull(PolicyId);
    }
}
```

### Handler Rules (ORCHESTRATION ONLY)

```csharp
// ✅ REQUIRED: Handlers ONLY orchestrate - NO business logic
public class CreatePolicyMemberHandler(
    IPaginatedRepository<PolicyMember, PolicyMemberId> repository)
    : ICommandHandler<CreatePolicyMemberCommand, CreatePolicyMemberResponse>
{
    public async Task<CreatePolicyMemberResponse> Handle(CreatePolicyMemberCommand command, CancellationToken cancellationToken)
    {
        // 1. Convert to domain objects
        var policyId = (PolicyId)command.PolicyId;

        // 2. Delegate to domain for business logic
        var result = PolicyMember.Create(policyId, command.MemberId, command.StartDate, command.EndDate, command.PlanId);

        // 3. Persist if successful
        if (result.IsFailure) return CreatePolicyMemberResponse.Failure(result.Errors);

        await repository.InsertAsync(result.Value, cancellationToken);

        // 4. Return response
        return CreatePolicyMemberResponse.Success(result.Value.Id);
    }
}
```

## 🔧 Builder Pattern Rules

### When to Use Builders (MANDATORY for 4+ Parameters)

```csharp
// ✅ REQUIRED: Use builder pattern for complex object construction
public class PolicyBuilder
{
    private string? _policyNumber;
    private ProductId? _productId;
    private DateOnly? _startDate;
    private DateOnly? _endDate;

    public PolicyBuilder WithPolicyNumber(string policyNumber)
    {
        _policyNumber = policyNumber;
        return this;
    }

    public PolicyBuilder WithProduct(ProductId productId)
    {
        _productId = productId;
        return this;
    }

    public Policy Build()
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(_policyNumber);
        ArgumentNullException.ThrowIfNull(_productId);

        return Policy.Create(_policyNumber, _productId.Value, _startDate, _endDate);
    }
}

// ❌ FORBIDDEN: Factory methods with excessive parameters
public static Policy CreatePolicy(string number, ProductId product,
    string contractHolder, DateTime effective, DateTime expiry,
    decimal premium, string currency, PolicyStatus status,
    List<PolicyMember> members, Dictionary<string, object> fields) // TOO MANY PARAMS
```

## 🧪 Testing Standards

### Naming Conventions (MANDATORY)

```csharp
// ✅ REQUIRED: Test class naming pattern
{ClassUnderTest}Tests.cs
ValidatePolicyMemberUploadHandlerTests.cs
PolicyMemberUniquenessServiceTests.cs

// ✅ REQUIRED: Test method naming pattern
[Fact]
[Trait("Ticket", "CH-25857")] // Link to Jira tickets when applicable
public async Task Handle_WithValidUpload_ShouldReturnSuccess()
public async Task CreatePolicy_WithInvalidDates_ShouldReturnValidationError()
public void PolicyId_ImplicitConversion_WithValidGuid_ShouldSucceed()
```

### Test Structure (MANDATORY)

```csharp
// ✅ REQUIRED: Arrange-Act-Assert pattern with clear sections
[Fact]
public async Task Handle_WithDuplicateEmail_ShouldReturnValidationError()
{
    // Arrange
    var command = new ValidateUploadCommand { PolicyId = Guid.NewGuid() };
    var mockRepository = new Mock<IPolicyMemberUploadRepository>();

    // Act
    var result = await _handler.Handle(command, CancellationToken.None);

    // Assert
    result.IsSuccess.Should().BeFalse();
    result.Errors.Should().ContainSingle()
        .Which.Code.Should().Be(ErrorCodes.UniqueViolation);
}
```

### Test Traits (MANDATORY)

```csharp
// ✅ REQUIRED: Use consistent test traits for categorization
[Trait("Category", "Unit")]
[Trait("Component", "Infrastructure")]
[Trait("Feature", "FileParser")]
public class XlsxFileParserTests { }

[Trait("Category", "Integration")]
[Trait("Component", "Application")]
public class ValidatePolicyMemberUploadHandlerIntegrationTests { }
```

## 🚀 Async & Performance Rules

### Async Method Standards

```csharp
// ✅ REQUIRED: All async methods must accept CancellationToken
public async Task<Result<Policy>> CreatePolicyAsync(CreatePolicyCommand command, CancellationToken cancellationToken = default)

// ✅ REQUIRED: Use ConfigureAwait(false) in infrastructure layer
var result = await repository.GetByIdAsync(policyId, cancellationToken).ConfigureAwait(false);

// ✅ REQUIRED: Concurrent processing with proper resource management
private async Task<HashSet<string>> ProcessBatchesAsync(
    IAsyncEnumerable<(IReadOnlyList<T> Batch, int StartIndex)> batches,
    CancellationToken cancellationToken = default)
{
    using var semaphore = new SemaphoreSlim(ValidationConstants.Concurrency.DefaultMaxConcurrentValidations);
    // Implement with proper semaphore usage
}
```

### Concurrency Configuration

```csharp
// ✅ REQUIRED: Use constants for concurrency limits
public static class ValidationConstants
{
    public static class Concurrency
    {
        public const int DefaultMaxConcurrentValidations = 20;
        public const int DefaultMemberBatchSize = 1000;
    }
}
```

## 📁 File Organization Rules

### Feature Structure (MANDATORY)

```
Features/
  {AggregateRoot}/
    {OperationName}/
      {OperationName}Command.cs       # Input contract
      {OperationName}Handler.cs       # Orchestration logic
      {OperationName}Validator.cs     # FluentValidation rules
      {OperationName}Response.cs      # Output contract
```

### Domain Organization (MANDATORY)

```
Domain/
  {AggregateRoot}/
    {AggregateRoot}.cs              # Entity/Aggregate root
    {AggregateRoot}Id.cs            # Strongly-typed ID
    Events/{Event}Event.cs          # Domain events
    Exceptions/{Exception}.cs       # Domain exceptions
  Specifications/
    {BusinessRule}Specification.cs  # Business rule implementations
```

## 🔐 Security & Validation Rules

### Input Validation (MANDATORY)

```csharp
// ✅ REQUIRED: Validate at boundaries, trust within domain
public class CreatePolicyMemberValidator : AbstractValidator<CreatePolicyMemberCommand>
{
    public CreatePolicyMemberValidator()
    {
        RuleFor(x => x.MemberId).NotEmpty().WithMessage("Member ID is required");
        RuleFor(x => x.PolicyId).NotEmpty().WithMessage("Policy ID is required");
        RuleFor(x => x.PlanId).NotEmpty().WithMessage("Plan ID is required");
    }
}
```

### Multi-Tenant Rules (MANDATORY)

```csharp
// ✅ REQUIRED: All data access must be tenant-aware
services.AddScoped<TenantId>(provider => /* resolve from context */);

// ✅ REQUIRED: Include tenant context in all service calls
public class LegacyPolicyService(HttpClient httpClient, TenantId tenantId)
{
    public async Task<PolicyDto?> GetPolicyById(string policyId, CancellationToken ct) =>
        await _client.Policy_GetPolicyAsync(tenantId.Value, policyId, null, ct);
}
```

## 📋 Pre-Commit Checklist

Before submitting code, verify:

- [ ] ✅ No magic strings anywhere in the code
- [ ] ✅ All business logic is in appropriate domain layer
- [ ] ✅ Result pattern used for all operations that can fail
- [ ] ✅ Strongly-typed IDs used for all entities
- [ ] ✅ DatabaseNamingHelpers used for all database objects
- [ ] ✅ Proper layer separation maintained (no domain logic in application/infrastructure)
- [ ] ✅ Builder pattern used for complex object construction (4+ parameters)
- [ ] ✅ All async methods accept CancellationToken
- [ ] ✅ Test methods follow naming conventions with Arrange-Act-Assert
- [ ] ✅ Error codes used from ErrorCodes constants
- [ ] ✅ All compiler warnings resolved

## ⚡ Quick Reference

### Essential Classes to Use

- `ErrorCodes` - All error code constants
- `Errors` - Factory methods for common ValidationErrors
- `ValidationErrorBuilder` - Fluent error construction
- `DatabaseNamingHelpers` - All database naming operations
- `Result<T>` and `Result` - Success/failure return types

### Essential Patterns

- **Strongly-typed IDs**: `{Entity}Id(Guid Value) : ValueObject<Guid>(Value)`
- **Entity constructors**: Private primary, public parameterless calling Empty
- **Command handlers**: ONLY orchestration, delegate to domain
- **Validation**: Use Errors factory → ValidationErrorBuilder → Direct constructor
- **Database naming**: Always use DatabaseNamingHelpers extensions
- **Async**: Always accept CancellationToken, use ConfigureAwait(false) in infrastructure

Remember: These are not suggestions but **mandatory standards**. Deviations require explicit approval and documented justification.
