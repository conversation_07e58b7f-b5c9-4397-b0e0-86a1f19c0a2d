name: Build and publish
on:
- push
jobs:
  build-and-publish:
    uses: CoverGo/reusable-workflows/.github/workflows/build-and-publish.yml@master
    with:
      serviceName: policies-v3
      hasClient: false
      hasCustomMongoImage: false
      isDotnetSonarScannerEnabled: true
      sonarDotnetVersion: 9.x
      isVulnerabilityScannerEnabled: true
    secrets:
      PAT_USER_READ_PACKAGES: ${{ secrets.PAT_USER_READ_PACKAGES }}
      PAT_READ_PACKAGES: ${{ secrets.PAT_READ_PACKAGES }}
      COSIGN_PASSWORD: ${{ secrets.COSIGN_PASSWORD }}
      COSIGN_PRIVATE_KEY: ${{ secrets.SIGNING_SECRET }}
      ALI_CONTAINER_REGISTRY_USER: ${{ secrets.ALI_CONTAINER_REGISTRY_USER }}
      ALI_CONTAINER_REGISTRY_PASSWORD: ${{ secrets.ALI_CONTAINER_REGISTRY_PASSWORD }}
      PRIVATE_ACTION_APP_ID: ${{ secrets.PRIVATE_ACTION_APP_ID }}
      PRIVATE_ACTION_APP_PRIVATE_KEY: ${{ secrets.PRIVATE_ACTION_APP_PRIVATE_KEY }}
      DIAGNOSTIC_PASSWORD: ${{ secrets.DIAGNOSTIC_PASSWORD }}
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}