name: Create New Release

on:
  workflow_dispatch:
    inputs:
      commit:
        description: 'Commit SHA (leave blank for latest)'
        required: false
        default: ''
        type: string
      ReleaseCandidate:
        description: 'Create a release candidate (rc) version'
        required: false
        default: true
        type: boolean


jobs:
  create-release:
    uses: CoverGo/reusable-workflows/.github/workflows/create-microservice-release.yml@master
    secrets:
      RELEASE_MANAGER_TOKEN: ${{ secrets.RELEASE_MANAGER_TOKEN }} # pass just this secret        
    with:
      commit: ${{github.event.inputs.commit}}
      ReleaseCandidate: ${{fromJSON(github.event.inputs.ReleaseCandidate)}}
