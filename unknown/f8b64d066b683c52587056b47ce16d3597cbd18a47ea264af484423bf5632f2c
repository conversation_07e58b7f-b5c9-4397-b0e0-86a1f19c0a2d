{"Serilog": {"Enrich": ["WithSpan", "FromLogContext"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Compact.RenderedCompactJsonFormatter, Serilog.Formatting.Compact"}}]}, "serviceUrls": {"logging": "http://covergo-logging:9200/", "auth": "http://covergo-auth:8080/", "users": "http://covergo-users:8080/", "cases": "http://covergo-cases:8080/", "filesystem": "http://covergo-filesystem:8080", "policies": "http://covergo-policies:8080", "products": "http://covergo-products:8080/"}}