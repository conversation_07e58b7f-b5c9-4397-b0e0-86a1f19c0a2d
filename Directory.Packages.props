<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <!-- ============================== -->
  <!-- CoverGo Internal Packages      -->
  <!-- ============================== -->
  <ItemGroup Label="CoverGo Building Blocks">
    <PackageVersion Include="CoverGo.BuildingBlocks.Api.GraphQl" Version="3.2.0" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Application.Core" Version="11.0.0" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Auth" Version="2.0.0" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Bootstrapper" Version="2.4.0" />
    <PackageVersion Include="CoverGo.BuildingBlocks.DataAccess" Version="9.2.1-postgresql-repository.4" />
    <PackageVersion Include="CoverGo.BuildingBlocks.DataAccess.Mongo" Version="9.2.1-postgresql-repository.4" />
    <PackageVersion Include="CoverGo.BuildingBlocks.DataAccess.PostgreSql" Version="9.2.1-postgresql-repository.4" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Domain.Core" Version="11.0.0" />
    <PackageVersion Include="CoverGo.BuildingBlocks.MessageBus.Abstractions" Version="5.0.3" />
    <PackageVersion Include="CoverGo.BuildingBlocks.MessageBus.Dapr" Version="5.0.3" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Observability" Version="2.0.2" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Scheduler.Hangfire" Version="5.1.0" />
    <PackageVersion Include="CoverGo.FeatureManagement" Version="4.0.0" />
  </ItemGroup>
  <ItemGroup Label="CoverGo Service Clients">
    <PackageVersion Include="CoverGo.Cases.Client" Version="2.1.171" />
    <PackageVersion Include="CoverGo.Cases.Client.Rest" Version="2.57.0-rc.1" />
    <PackageVersion Include="CoverGo.FileSystem.Client" Version="2.40.0-rc.1" />
    <PackageVersion Include="CoverGo.Multitenancy" Version="4.0.0" />
    <PackageVersion Include="CoverGo.Policies.Client" Version="2.348.0" />
    <PackageVersion Include="CoverGo.Products.Client" Version="2.304.0-rc.5" />
    <PackageVersion Include="CoverGo.Users.Client" Version="2.70.0" />
  </ItemGroup>
  <!-- ============================== -->
  <!-- ASP.NET Core & Web             -->
  <!-- ============================== -->
  <ItemGroup Label="ASP.NET Core">
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.7" />
    <PackageVersion Include="Microsoft.AspNetCore.HeaderPropagation" Version="9.0.7" />
    <PackageVersion Include="Microsoft.AspNetCore.Hosting.Abstractions" Version="2.3.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.7" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.7" />
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.7" />
    <PackageVersion Include="Microsoft.Extensions.Http.Resilience" Version="9.6.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.7" />
    <PackageVersion Include="Microsoft.FeatureManagement.AspNetCore" Version="4.2.1" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="9.0.3" />
    <PackageVersion Include="StackExchange.Redis" Version="2.8.58" />
  </ItemGroup>
  <!-- ============================== -->
  <!-- Communication                  -->
  <!-- ============================== -->
  <ItemGroup Label="GraphQL">
    <PackageVersion Include="GraphQL.Client" Version="6.1.0" />
    <PackageVersion Include="GraphQL.Client.Serializer.SystemTextJson" Version="6.1.0" />
    <PackageVersion Include="Grpc.AspNetCore" Version="2.71.0" />
    <PackageVersion Include="HotChocolate.AspNetCore" Version="13.9.14" />
    <PackageVersion Include="HotChocolate.AspNetCore.Authorization" Version="13.9.14" />
    <PackageVersion Include="HotChocolate.AspNetCore.CommandLine" Version="13.9.14" />
    <PackageVersion Include="HotChocolate.Types" Version="13.9.14" />
    <PackageVersion Include="HotChocolate.Types.Analyzers" Version="13.9.14" />
    <PackageVersion Include="HotChocolate.Types.Scalars" Version="13.9.14" />
    <PackageVersion Include="HotChocolate.Abstractions" Version="13.9.14" />
  </ItemGroup>
  <!-- ============================== -->
  <!-- Data Access & Entity Framework -->
  <!-- ============================== -->
  <ItemGroup Label="Entity Framework Core">
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.7" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.7">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageVersion>
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.4" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.7" />
    <PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
  </ItemGroup>
  <!-- ============================== -->
  <!-- Validation & Mapping           -->
  <!-- ============================== -->
  <ItemGroup Label="Validation">
    <PackageVersion Include="FluentValidation" Version="12.0.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
  </ItemGroup>
  <ItemGroup Label="Object Mapping">
    <PackageVersion Include="Mapster" Version="7.4.0" />
  </ItemGroup>
  <!-- ============================== -->
  <!-- Utilities & Helpers            -->
  <!-- ============================== -->
  <ItemGroup Label="Utilities">
    <PackageVersion Include="Humanizer.Core" Version="2.14.1" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="NPOI" Version="2.7.3" />
  </ItemGroup>
  <!-- ============================== -->
  <!-- Development & Build Tools      -->
  <!-- ============================== -->
  <ItemGroup Label="Source Control and Build">
    <PackageVersion Include="Microsoft.SourceLink.GitHub" Version="8.0.0" />
  </ItemGroup>
  <!-- ============================== -->
  <!-- Testing Framework              -->
  <!-- ============================== -->
  <ItemGroup Label="Test Framework">
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.1.1" />
  </ItemGroup>
  <ItemGroup Label="Test Utilities">
    <PackageVersion Include="AutoFixture" Version="4.18.1" />
    <PackageVersion Include="AutoFixture.Xunit2" Version="4.18.1" />
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
    <PackageVersion Include="Moq" Version="4.20.72" />
  </ItemGroup>
  <ItemGroup Label="Test Coverage and Reporting">
    <PackageVersion Include="coverlet.collector" Version="6.0.4" />
    <PackageVersion Include="JunitXml.TestLogger" Version="6.1.0" />
  </ItemGroup>
</Project>