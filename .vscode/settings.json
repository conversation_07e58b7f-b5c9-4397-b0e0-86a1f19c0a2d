{"dotnet.defaultSolution": "CoverGo.PoliciesV3.sln", "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableRoslynAnalyzers": true, "files.exclude": {"**/bin": true, "**/obj": true, "**/.vs": true}, "search.exclude": {"**/bin": true, "**/obj": true, "**/.vs": true, "**/node_modules": true}, "files.watcherExclude": {"**/bin/**": true, "**/obj/**": true, "**/.vs/**": true}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "csharp.format.enable": true, "csharp.semanticHighlighting.enabled": true, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "dotnet.inlayHints.enableInlayHintsForParameters": true, "dotnet.inlayHints.enableInlayHintsForLiteralParameters": true, "dotnet.inlayHints.enableInlayHintsForIndexerParameters": true, "dotnet.inlayHints.enableInlayHintsForObjectCreationParameters": true, "dotnet.inlayHints.enableInlayHintsForOtherParameters": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatDifferOnlyBySuffix": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchMethodIntent": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchArgumentName": true, "sonarlint.connectedMode.project": {"connectionId": "covergo", "projectKey": "CoverGo_policies-v3"}, "files.eol": "\n"}