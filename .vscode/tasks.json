{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/CoverGo.PoliciesV3.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/src/CoverGo.PoliciesV3.Api/CoverGo.PoliciesV3.Api.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/src/CoverGo.PoliciesV3.Api/CoverGo.PoliciesV3.Api.csproj"], "problemMatcher": "$msCompile"}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/CoverGo.PoliciesV3.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/CoverGo.PoliciesV3.sln"], "problemMatcher": "$msCompile"}, {"label": "test", "command": "dotnet", "type": "process", "args": ["test", "${workspaceFolder}/CoverGo.PoliciesV3.sln", "--no-build", "--verbosity", "normal"], "problemMatcher": "$msCompile", "group": {"kind": "test", "isDefault": true}}, {"label": "ef-migrations-add", "command": "dotnet", "type": "process", "args": ["ef", "migrations", "add", "${input:migrationName}", "--project", "${workspaceFolder}/src/CoverGo.PoliciesV3.Infrastructure/CoverGo.PoliciesV3.Infrastructure.csproj", "--startup-project", "${workspaceFolder}/src/CoverGo.PoliciesV3.Api/CoverGo.PoliciesV3.Api.csproj"], "problemMatcher": "$msCompile"}, {"label": "ef-database-update", "command": "dotnet", "type": "process", "args": ["ef", "database", "update", "--project", "${workspaceFolder}/src/CoverGo.PoliciesV3.Infrastructure/CoverGo.PoliciesV3.Infrastructure.csproj", "--startup-project", "${workspaceFolder}/src/CoverGo.PoliciesV3.Api/CoverGo.PoliciesV3.Api.csproj"], "problemMatcher": "$msCompile"}], "inputs": [{"id": "migrationName", "description": "Enter migration name", "default": "NewMigration", "type": "promptString"}]}