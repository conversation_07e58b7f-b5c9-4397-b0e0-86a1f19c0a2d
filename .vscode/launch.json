{"version": "0.2.0", "configurations": [{"name": "Launch CoverGo.PoliciesV3.Api", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/src/CoverGo.PoliciesV3.Api/bin/Debug/net9.0/CoverGo.PoliciesV3.Api.dll", "args": [], "cwd": "${workspaceFolder}/src/CoverGo.PoliciesV3.Api", "console": "internalConsole", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:7001;http://localhost:5001"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Launch CoverGo.PoliciesV3.Api (HTTP Only)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/src/CoverGo.PoliciesV3.Api/bin/Debug/net9.0/CoverGo.PoliciesV3.Api.dll", "args": [], "cwd": "${workspaceFolder}/src/CoverGo.PoliciesV3.Api", "console": "internalConsole", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5001"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Attach to CoverGo.PoliciesV3.Api", "type": "coreclr", "request": "attach"}]}