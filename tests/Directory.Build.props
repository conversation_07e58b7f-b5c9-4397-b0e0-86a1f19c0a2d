<Project>
    <!-- Import root Directory.Build.props -->
    <Import Project="$([MSBuild]::GetPathOfFileAbove('Directory.Build.props', '$(MSBuildThisFileDirectory)../'))" />

    <!-- Test-specific properties -->
    <PropertyGroup>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <!-- Common test dependencies -->
    <ItemGroup>
        <!-- Core test framework -->
        <PackageReference Include="Microsoft.NET.Test.Sdk" />
        <PackageReference Include="xunit" />
        <PackageReference Include="xunit.runner.visualstudio" />
        
        <!-- Test utilities -->
        <PackageReference Include="coverlet.collector" />
        <PackageReference Include="JunitXml.TestLogger" />
        
        <!-- Unit test specific dependencies -->
        <PackageReference Include="Moq" Condition="'$(MSBuildProjectName)' == 'CoverGo.PoliciesV3.Tests.Unit'" />
        <PackageReference Include="FluentAssertions" Condition="'$(MSBuildProjectName)' == 'CoverGo.PoliciesV3.Tests.Unit'" />
        <PackageReference Include="AutoFixture" Condition="'$(MSBuildProjectName)' == 'CoverGo.PoliciesV3.Tests.Unit'" />
        <PackageReference Include="AutoFixture.Xunit2" Condition="'$(MSBuildProjectName)' == 'CoverGo.PoliciesV3.Tests.Unit'" />
        
        <!-- Common dependencies for both test projects -->
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" />
    </ItemGroup>

    <!-- Global usings for test projects -->
    <ItemGroup>
        <Using Include="Xunit" />
        
        <!-- Unit test specific usings -->
        <Using Include="FluentAssertions" Condition="'$(MSBuildProjectName)' == 'CoverGo.PoliciesV3.Tests.Unit'" />
        <Using Include="AutoFixture" Condition="'$(MSBuildProjectName)' == 'CoverGo.PoliciesV3.Tests.Unit'" />
        <Using Include="AutoFixture.Xunit2" Condition="'$(MSBuildProjectName)' == 'CoverGo.PoliciesV3.Tests.Unit'" />
        <Using Include="Moq" Condition="'$(MSBuildProjectName)' == 'CoverGo.PoliciesV3.Tests.Unit'" />
    </ItemGroup>
</Project>
