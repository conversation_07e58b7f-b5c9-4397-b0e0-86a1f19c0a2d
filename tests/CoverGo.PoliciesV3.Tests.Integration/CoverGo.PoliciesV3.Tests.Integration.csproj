﻿<Project Sdk="Microsoft.NET.Sdk">

  <!-- Integration test specific dependencies not covered by Directory.Build.props -->
  <ItemGroup>
	  <PackageReference Include="CoverGo.Cases.Client.Rest" />
	  <PackageReference Include="CoverGo.FileSystem.Client" />
	  <PackageReference Include="CoverGo.Policies.Client" />
	  <PackageReference Include="CoverGo.Products.Client" />
	  <PackageReference Include="FluentAssertions" />
	  <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
	  <PackageReference Include="Microsoft.AspNetCore.Hosting.Abstractions" />
	  <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\CoverGo.PoliciesV3.Api\CoverGo.PoliciesV3.Api.csproj" />
  </ItemGroup>

</Project>
