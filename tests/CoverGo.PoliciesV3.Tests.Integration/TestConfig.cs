﻿using Microsoft.Extensions.Configuration;

namespace CoverGo.PoliciesV3.Tests.Integration;

internal class TestConfig
{
    public string AuthUrl { get; set; } = null!;
    public string PoliciesUrl { get; set; } = null!;
    public string ProductsUrl { get; set; } = null!;
    public string UsersUrl { get; set; } = null!;
    public string GatewayUrl { get; set; } = null!;
    public string CasesUrl { get; set; } = null!;
    public string FileSystemUrl { get; set; } = null!;

    public static readonly TestConfig Local = new()
    {
        AuthUrl = "http://localhost:60000",
        UsersUrl = "http://localhost:60010",
        PoliciesUrl = "http://localhost:60050",
        ProductsUrl = "http://localhost:60020",
        GatewayUrl = "http://localhost:60060",
        CasesUrl = "http://localhost:60600",
        FileSystemUrl = "http://localhost:61872"
    };

    private const string EnvironmentVariablePrefix = "POLICIES_INTEGRATION_TEST-";

    public static TestConfig Load(string prefix = EnvironmentVariablePrefix)
    {
        var cfg = new TestConfig();
        var builder = new ConfigurationBuilder();

        builder.AddEnvironmentVariables(source =>
        {
            source.Prefix = prefix;
        });

        builder.Build().Bind(cfg);

        return string.IsNullOrWhiteSpace(cfg.PoliciesUrl) ? Local : cfg;
    }
}
