using CoverGo.Policies.Client.GraphQl;
using FluentAssertions;
using GraphQL;

namespace CoverGo.PoliciesV3.Tests.Integration.Features.Policy;

public class PoliciesRoutingTests(CustomWebApplicationFactory factory) : TestBase(factory)
{
    [Fact]
    public async Task GIVEN_registerPolicyMemberUpload_WHEN_valid_input_THEN_returns_success()
    {
        // Arrange
        await SetLoggedInUser("testUser", "writePolicies", "all");
        string policyId = Guid.NewGuid().ToString();
        string path = "/uploads/test-file.csv";

        var gqlRequest = new GraphQLRequest
        {
            Query = @"
                mutation RegisterPolicyMemberUpload($input: policies_RegisterPolicyMemberUploadInput!) {
                    registerPolicyMemberUpload(input: $input) {
                        result {
                            id
                            policyId
                            path
                            status
                            membersCount
                            validMembersCount
                            invalidMembersCount
                            createdAt
                            lastModifiedAt
                        }
                    }
                }",
            Variables = new
            {
                input = new
                {
                    policyId,
                    endorsementId = (string?)null,
                    path
                }
            }
        };
        using var gqlCts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
        
        //act
        GraphQLResponse<policies_RegisterPolicyMemberUploadPayload> gqlResponse = await UserLoggedInClient!.SendMutationAsync<policies_RegisterPolicyMemberUploadPayload>(gqlRequest, gqlCts.Token);
        
        //assert
        gqlResponse.Should().NotBeNull();
        (gqlResponse.Data != null || gqlResponse.Errors != null).Should().BeTrue("GraphQL response should contain either 'data' or 'errors'");
    }
}