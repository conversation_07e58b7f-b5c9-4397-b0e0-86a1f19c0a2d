{"MemberSystemFields": [], "MemberCustomFields": [{"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "gender", "Label": "Gender", "Type": {"Options": [{"Name": "Female", "Key": "161490697310685652", "Value": "female", "$type": "StringOption"}, {"Name": "Male", "Key": "161490698482855869", "Value": "male", "$type": "StringOption"}], "Validations": "required", "ValidationInfo": [{"Name": "required", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": true, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "memberType", "Label": "Member Type", "Type": {"Options": [{"Name": "Primary", "Key": "167840377167826686", "Value": "employee", "$type": "StringOption"}, {"Name": "Dependent", "Key": "167840377878295915", "Value": "dependent", "$type": "StringOption"}], "Validations": "required", "ValidationInfo": [{"Name": "required", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": true, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "email", "Label": "Email", "Type": {"Options": null, "Validations": "email", "ValidationInfo": [{"Name": "email", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "phone", "Label": "Contact Number", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "bankAccountNo", "Label": "Bank Account No", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": true, "Condition": null, "Name": "passportNo", "Label": "Passport No.", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": true, "Condition": null, "Name": "staffNo", "Label": "Staff No.", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "claimsSettlementMode", "Label": "Claims Settlement Mode", "Type": {"Options": [{"Name": "AE - AUTO PAY TO EMPLOYEE", "Key": "162297597132678896", "Value": "ae", "$type": "StringOption"}, {"Name": "CE - CHEQUE TO EMPLOYEE", "Key": "162297601938446902", "Value": "ce", "$type": "StringOption"}, {"Name": "AP - AUTO PAY TO POLICYHOLDER", "Key": "162297604165430580", "Value": "ap", "$type": "StringOption"}, {"Name": "CP - CHEQUE TO POLICYHOLDER", "Key": "16229760518644089", "Value": "cp", "$type": "StringOption"}], "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": true, "Condition": null, "Name": "hkid", "Label": "National ID", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "fullName", "Label": "Name", "Type": {"Options": null, "Validations": "required", "ValidationInfo": [{"Name": "required", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": true, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "flatNumber", "Label": "Flat Number / Building", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "streetNumberAndName", "Label": "Street Number & Street Name", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "district", "Label": "District", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "region", "Label": "Region", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "dateOfBirth", "Label": "Date of Birth", "Type": {"Validations": "required", "ValidationInfo": [{"Name": "required", "Arguments": [], "$type": "ValidationInfo"}], "$type": "DateFieldType"}, "IsRequired": true, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": true, "IsIdentityField": false, "Condition": null, "Name": "relationshipToEmployee", "Label": "Relationship to Employee", "Type": {"Options": [{"Name": "Spouse", "Key": "163608076654142892", "Value": "spouse", "$type": "StringOption"}, {"Name": "Child", "Key": "163608084390978679", "Value": "child", "$type": "StringOption"}, {"Name": "Parent", "Key": "163608085111725557", "Value": "parent", "$type": "StringOption"}], "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "MemberFields": [{"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "gender", "Label": "Gender", "Type": {"Options": [{"Name": "Female", "Key": "161490697310685652", "Value": "female", "$type": "StringOption"}, {"Name": "Male", "Key": "161490698482855869", "Value": "male", "$type": "StringOption"}], "Validations": "required", "ValidationInfo": [{"Name": "required", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": true, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "memberType", "Label": "Member Type", "Type": {"Options": [{"Name": "Primary", "Key": "167840377167826686", "Value": "employee", "$type": "StringOption"}, {"Name": "Dependent", "Key": "167840377878295915", "Value": "dependent", "$type": "StringOption"}], "Validations": "required", "ValidationInfo": [{"Name": "required", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": true, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "email", "Label": "Email", "Type": {"Options": null, "Validations": "email", "ValidationInfo": [{"Name": "email", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "phone", "Label": "Contact Number", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "bankAccountNo", "Label": "Bank Account No", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": true, "Condition": null, "Name": "passportNo", "Label": "Passport No.", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": true, "Condition": null, "Name": "staffNo", "Label": "Staff No.", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "claimsSettlementMode", "Label": "Claims Settlement Mode", "Type": {"Options": [{"Name": "AE - AUTO PAY TO EMPLOYEE", "Key": "162297597132678896", "Value": "ae", "$type": "StringOption"}, {"Name": "CE - CHEQUE TO EMPLOYEE", "Key": "162297601938446902", "Value": "ce", "$type": "StringOption"}, {"Name": "AP - AUTO PAY TO POLICYHOLDER", "Key": "162297604165430580", "Value": "ap", "$type": "StringOption"}, {"Name": "CP - CHEQUE TO POLICYHOLDER", "Key": "16229760518644089", "Value": "cp", "$type": "StringOption"}], "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": true, "Condition": null, "Name": "hkid", "Label": "National ID", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "fullName", "Label": "Name", "Type": {"Options": null, "Validations": "required", "ValidationInfo": [{"Name": "required", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": true, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "flatNumber", "Label": "Flat Number / Building", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "streetNumberAndName", "Label": "Street Number & Street Name", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "district", "Label": "District", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "region", "Label": "Region", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "dateOfBirth", "Label": "Date of Birth", "Type": {"Validations": "required", "ValidationInfo": [{"Name": "required", "Arguments": [], "$type": "ValidationInfo"}], "$type": "DateFieldType"}, "IsRequired": true, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": true, "IsIdentityField": false, "Condition": null, "Name": "relationshipToEmployee", "Label": "Relationship to Employee", "Type": {"Options": [{"Name": "Spouse", "Key": "163608076654142892", "Value": "spouse", "$type": "StringOption"}, {"Name": "Child", "Key": "163608084390978679", "Value": "child", "$type": "StringOption"}, {"Name": "Parent", "Key": "163608085111725557", "Value": "parent", "$type": "StringOption"}], "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "ProductFields": [], "CensusFields": [], "OneOfValidations": [{"Validations": [{"Field": {"IsRequiredForDependent": false, "IsIdentityField": true, "Condition": null, "Name": "passportNo", "Label": "Passport No.", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "IsRequired": true, "$type": "CustomFieldRequiredValidation"}, {"Field": {"IsRequiredForDependent": false, "IsIdentityField": true, "Condition": null, "Name": "hkid", "Label": "National ID", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "IsRequired": true, "$type": "CustomFieldRequiredValidation"}, {"Field": {"IsRequiredForDependent": false, "IsIdentityField": true, "Condition": null, "Name": "staffNo", "Label": "Staff No.", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "IsRequired": true, "$type": "CustomFieldRequiredValidation"}], "$type": "CustomFieldOneOfValidation"}], "Fields": [{"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "gender", "Label": "Gender", "Type": {"Options": [{"Name": "Female", "Key": "161490697310685652", "Value": "female", "$type": "StringOption"}, {"Name": "Male", "Key": "161490698482855869", "Value": "male", "$type": "StringOption"}], "Validations": "required", "ValidationInfo": [{"Name": "required", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": true, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "memberType", "Label": "Member Type", "Type": {"Options": [{"Name": "Primary", "Key": "167840377167826686", "Value": "employee", "$type": "StringOption"}, {"Name": "Dependent", "Key": "167840377878295915", "Value": "dependent", "$type": "StringOption"}], "Validations": "required", "ValidationInfo": [{"Name": "required", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": true, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "email", "Label": "Email", "Type": {"Options": null, "Validations": "email", "ValidationInfo": [{"Name": "email", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "phone", "Label": "Contact Number", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "bankAccountNo", "Label": "Bank Account No", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": true, "Condition": null, "Name": "passportNo", "Label": "Passport No.", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": true, "Condition": null, "Name": "staffNo", "Label": "Staff No.", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "claimsSettlementMode", "Label": "Claims Settlement Mode", "Type": {"Options": [{"Name": "AE - AUTO PAY TO EMPLOYEE", "Key": "162297597132678896", "Value": "ae", "$type": "StringOption"}, {"Name": "CE - CHEQUE TO EMPLOYEE", "Key": "162297601938446902", "Value": "ce", "$type": "StringOption"}, {"Name": "AP - AUTO PAY TO POLICYHOLDER", "Key": "162297604165430580", "Value": "ap", "$type": "StringOption"}, {"Name": "CP - CHEQUE TO POLICYHOLDER", "Key": "16229760518644089", "Value": "cp", "$type": "StringOption"}], "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": true, "Condition": null, "Name": "hkid", "Label": "National ID", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "fullName", "Label": "Name", "Type": {"Options": null, "Validations": "required", "ValidationInfo": [{"Name": "required", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": true, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "flatNumber", "Label": "Flat Number / Building", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "streetNumberAndName", "Label": "Street Number & Street Name", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "district", "Label": "District", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "region", "Label": "Region", "Type": {"Options": null, "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "address", "Label": "Home Address", "Type": {"InnerFieldDefinitions": [{"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": null, "IsIdentityField": null, "Condition": null, "Name": null, "Label": null, "Type": null, "IsRequired": null, "IsUnique": null, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, "$type": "PolicyMemberFieldDefinition"}], "CheckExtraFields": true, "Validations": null, "ValidationInfo": [], "$type": "AddressFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": false, "IsIdentityField": false, "Condition": null, "Name": "dateOfBirth", "Label": "Date of Birth", "Type": {"Validations": "required", "ValidationInfo": [{"Name": "required", "Arguments": [], "$type": "ValidationInfo"}], "$type": "DateFieldType"}, "IsRequired": true, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}, {"IsRequiredForDependent": true, "IsIdentityField": false, "Condition": null, "Name": "relationshipToEmployee", "Label": "Relationship to Employee", "Type": {"Options": [{"Name": "Spouse", "Key": "163608076654142892", "Value": "spouse", "$type": "StringOption"}, {"Name": "Child", "Key": "163608084390978679", "Value": "child", "$type": "StringOption"}, {"Name": "Parent", "Key": "163608085111725557", "Value": "parent", "$type": "StringOption"}], "Validations": "", "ValidationInfo": [{"Name": "", "Arguments": [], "$type": "ValidationInfo"}], "$type": "StringFieldType"}, "IsRequired": false, "IsUnique": false, "Parent": null, "$type": "PolicyMemberFieldDefinition"}], "$type": "PolicyMemberFieldsSchema"}