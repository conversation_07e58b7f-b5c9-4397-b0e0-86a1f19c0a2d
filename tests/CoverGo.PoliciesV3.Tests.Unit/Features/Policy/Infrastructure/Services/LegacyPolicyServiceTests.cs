using CoverGo.Multitenancy;
using CoverGo.Policies.Client;
using CoverGo.PoliciesV3.Application.Mapping.LegacyPolicyMapping;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Infrastructure.Policies;
using Microsoft.Extensions.Logging;
using LegacyPolicy = CoverGo.Policies.Client.Policy;

namespace CoverGo.PoliciesV3.Tests.Unit.Features.Policy.Infrastructure.Services;

/// <summary>
/// Unit tests for LegacyPolicyService
/// Tests business logic, parameter validation, and error handling scenarios
/// Note: Since LegacyPolicyService is internal and methods are not virtual,
/// these tests focus on testing the mapping logic and edge cases that can be tested
/// </summary>
public class LegacyPolicyServiceTests
{
    private readonly Fixture _fixture;
    private readonly TenantId _tenantId;

    public LegacyPolicyServiceTests()
    {
        _fixture = new Fixture();
        _tenantId = new TenantId("test-tenant");
    }

    #region Test Helpers

    private LegacyPolicyService CreateService() => new(new HttpClient(), _tenantId, new Mock<ILogger<LegacyPolicyService>>().Object);

    private LegacyPolicy CreateValidLegacyPolicy(string? policyId = null) => new()
    {
        Id = policyId ?? _fixture.Create<string>(),
        ProductId = new Policies.Client.ProductId { Type = "health", Plan = "basic", Version = "v1" },
        ContractHolder = new Entity { Id = _fixture.Create<string>() },
        StartDate = DateTime.Today,
        EndDate = DateTime.Today.AddYears(1),
        IsIssued = false,
        Endorsements =
        [
            new() { Id = _fixture.Create<string>(), Status = "APPROVED" },
            new() { Id = _fixture.Create<string>(), Status = "DRAFT" }
        ]
    };

    private static LegacyPolicy CreateInvalidLegacyPolicy(string policyId) => new()
    {
        Id = policyId,
        // Missing required fields to cause mapping failure
        ProductId = null,
        StartDate = null,
        EndDate = null
    };

    #endregion

    #region Service Creation Tests

    [Fact]
    public void CreateService_WithValidParameters_ShouldCreateInstance()
    {
        // Arrange & Act
        LegacyPolicyService service = CreateService();

        // Assert
        service.Should().NotBeNull();
        service.Should().BeOfType<LegacyPolicyService>();
    }

    #endregion

    #region PolicyDtoMapper Integration Tests

    [Fact]
    public void PolicyDtoMapper_WithValidLegacyPolicy_ShouldMapCorrectly()
    {
        // Arrange
        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy("test-policy-123");

        // Act
        PolicyDto result = PolicyDtoMapper.MapToDto(legacyPolicy);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be("test-policy-123");
        result.ProductId.Should().NotBeNull();
        result.ProductId!.Type.Should().Be("health");
        result.ProductId.Plan.Should().Be("basic");
        result.ProductId.Version.Should().Be("v1");
        result.ContractHolderId.Should().NotBeNullOrEmpty();
        result.StartDate.Should().Be(DateOnly.FromDateTime(DateTime.Today));
        result.EndDate.Should().Be(DateOnly.FromDateTime(DateTime.Today.AddYears(1)));
        result.IsIssued.Should().BeFalse();
        result.Endorsements.Should().HaveCount(2);
    }

    [Fact]
    public void PolicyDtoMapper_WithNullLegacyPolicy_ShouldThrowArgumentNullException() =>
        // Arrange & Act & Assert
        Assert.Throws<ArgumentNullException>(() => PolicyDtoMapper.MapToDto(null!));

    [Fact]
    public void PolicyDtoMapper_WithInvalidLegacyPolicy_ShouldThrowInvalidOperationException()
    {
        // Arrange
        LegacyPolicy invalidPolicy = CreateInvalidLegacyPolicy("test-policy-123");

        // Act & Assert
        InvalidOperationException exception = Assert.Throws<InvalidOperationException>(() => PolicyDtoMapper.MapToDto(invalidPolicy));
        exception.Message.Should().Contain("Policy start date is not set");
    }

    [Fact]
    public void PolicyDtoMapper_WithNullContractHolder_ShouldHandleGracefully()
    {
        // Arrange
        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy("test-policy-123");
        legacyPolicy.ContractHolder = null;

        // Act
        PolicyDto result = PolicyDtoMapper.MapToDto(legacyPolicy);

        // Assert
        result.Should().NotBeNull();
        result.ContractHolderId.Should().BeNull();
        result.Id.Should().Be("test-policy-123");
    }

    #endregion

    #region Business Logic Tests

    [Fact]
    public void ValidatePolicyDtoMapping_WithComplexEndorsements_ShouldMapCorrectly()
    {
        // Arrange
        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy("complex-policy");
        legacyPolicy.Endorsements =
        [
            new() { Id = "end-1", Status = "APPROVED" },
            new() { Id = "end-2", Status = "DRAFT" },
            new() { Id = "end-3", Status = "APPROVED" },
            new() { Id = "end-4", Status = "REJECTED" }
        ];

        // Act
        PolicyDto result = PolicyDtoMapper.MapToDto(legacyPolicy);

        // Assert
        result.Endorsements.Should().HaveCount(4);
        result.ApprovedEndorsementIds.Should().HaveCount(2);
        result.ApprovedEndorsementIds.Should().Contain("end-1");
        result.ApprovedEndorsementIds.Should().Contain("end-3");
    }

    [Fact]
    public void ValidatePolicyDtoMapping_WithEmptyEndorsements_ShouldHandleGracefully()
    {
        // Arrange
        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy("simple-policy");
        legacyPolicy.Endorsements = [];

        // Act
        PolicyDto result = PolicyDtoMapper.MapToDto(legacyPolicy);

        // Assert
        result.Endorsements.Should().BeEmpty();
        result.ApprovedEndorsementIds.Should().BeEmpty();
    }

    [Fact]
    public void ValidatePolicyDtoMapping_WithIssuedPolicy_ShouldMapIssuedFlag()
    {
        // Arrange
        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy("issued-policy");
        legacyPolicy.IsIssued = true;

        // Act
        PolicyDto result = PolicyDtoMapper.MapToDto(legacyPolicy);

        // Assert
        result.IsIssued.Should().BeTrue();
    }

    #endregion

}