using CoverGo.Policies.Client;
using CoverGo.PoliciesV3.Application.Mapping.LegacyPolicyMapping;
using CoverGo.PoliciesV3.Domain.Policies;
using Newtonsoft.Json.Linq;
using LegacyPolicy = CoverGo.Policies.Client.Policy;
using ProductId = CoverGo.Policies.Client.ProductId;

namespace CoverGo.PoliciesV3.Tests.Unit.Features.Policy.Infrastructure.Mapping;

// Simple test class to represent ContractHolder for testing
public class ContractHolder : Entity
{
    public new string? Id { get; set; }
}

public class PolicyDtoMapperTests
{
    [Fact]
    public void MapToDto_WithValidLegacyPolicy_ShouldMapCorrectly()
    {
        // Arrange
        var legacyPolicy = new LegacyPolicy
        {
            Id = "policy-123",
            ProductId = new ProductId { Type = "health", Plan = "basic", Version = "v1" },
            ContractHolder = new ContractHolder { Id = "ch-456" },
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddYears(1),
            Endorsements =
            [
                new() { Id = "end-1", Status = "APPROVED" },
                new() { Id = "end-2", Status = "DRAFT" },
                new() { Id = "end-3", Status = "APPROVED" }
            ]
        };

        // Act
        PolicyDto policyDto = PolicyDtoMapper.MapToDto(legacyPolicy);

        // Assert
        Assert.Equal(legacyPolicy.Id, policyDto.Id);
        Assert.NotNull(policyDto.ProductId);
        Assert.Equal("health", policyDto.ProductId.Type);
        Assert.Equal("basic", policyDto.ProductId.Plan);
        Assert.Equal("v1", policyDto.ProductId.Version);
        Assert.Equal(legacyPolicy.ContractHolder.Id, policyDto.ContractHolderId);
        Assert.Equal(DateOnly.FromDateTime(legacyPolicy.StartDate.Value), policyDto.StartDate);
        Assert.Equal(DateOnly.FromDateTime(legacyPolicy.EndDate.Value), policyDto.EndDate);
        Assert.Equal(2, policyDto.ApprovedEndorsementIds.Count);
        Assert.Contains("end-1", policyDto.ApprovedEndorsementIds);
        Assert.Contains("end-3", policyDto.ApprovedEndorsementIds);
        Assert.DoesNotContain("end-2", policyDto.ApprovedEndorsementIds);
        Assert.True(policyDto.CanUploadMembers);
    }

    [Fact]
    public void MapToDto_WithNullContractHolder_ShouldHandleGracefully()
    {
        // Arrange
        var legacyPolicy = new LegacyPolicy
        {
            Id = "policy-123",
            ProductId = new ProductId { Type = "health", Plan = "basic", Version = "v1" },
            ContractHolder = null,
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddYears(1)
        };

        // Act
        PolicyDto policyDto = PolicyDtoMapper.MapToDto(legacyPolicy);

        // Assert
        Assert.Null(policyDto.ContractHolderId);
        Assert.Equal(legacyPolicy.Id, policyDto.Id);
        Assert.NotNull(policyDto.ProductId);
        Assert.Equal("health", policyDto.ProductId.Type);
        Assert.Equal("basic", policyDto.ProductId.Plan);
        Assert.Equal("v1", policyDto.ProductId.Version);
    }

    [Fact]
    public void MapToDto_WithDatesInFields_ShouldExtractCorrectly()
    {
        // Arrange
        var fieldsJson = JObject.Parse(@"{
            'expectedStartDate': '2024-01-01',
            'policy': {
                'endDate': '2024-12-31'
            }
        }");

        var legacyPolicy = new LegacyPolicy
        {
            Id = "policy-123",
            ProductId = new ProductId { Type = "health", Plan = "basic", Version = "v1" },
            StartDate = null,
            EndDate = null,
            Fields = fieldsJson
        };

        // Act
        PolicyDto policyDto = PolicyDtoMapper.MapToDto(legacyPolicy);

        // Assert
        Assert.Equal(new DateOnly(2024, 1, 1), policyDto.StartDate);
        Assert.Equal(new DateOnly(2024, 12, 31), policyDto.EndDate);
    }

    [Fact]
    public void MapToDto_WithNullPolicy_ShouldThrowArgumentNullException() =>
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => PolicyDtoMapper.MapToDto(null!));

    [Fact]
    public void MapToDto_WithEmptyPolicyId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var legacyPolicy = new LegacyPolicy
        {
            Id = "",
            ProductId = new ProductId { Type = "health", Plan = "basic", Version = "v1" }
        };

        // Act & Assert
        InvalidOperationException exception = Assert.Throws<InvalidOperationException>(() => PolicyDtoMapper.MapToDto(legacyPolicy));
        Assert.Equal("Policy ID is required", exception.Message);
    }

    [Fact]
    public void MapToDto_WithEmptyProductId_ShouldReturnNullProductId()
    {
        // Arrange
        var legacyPolicy = new LegacyPolicy
        {
            Id = "policy-123",
            ProductId = new ProductId { Type = "health", Plan = "", Version = "v1" },
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddYears(1)
        };

        // Act
        PolicyDto policyDto = PolicyDtoMapper.MapToDto(legacyPolicy);

        // Assert
        Assert.Null(policyDto.ProductId);
        Assert.Equal(legacyPolicy.Id, policyDto.Id);
    }

    [Fact]
    public void MapToDto_WithNullProductId_ShouldReturnNullProductId()
    {
        // Arrange
        var legacyPolicy = new LegacyPolicy
        {
            Id = "policy-123",
            ProductId = null,
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddYears(1)
        };

        // Act
        PolicyDto policyDto = PolicyDtoMapper.MapToDto(legacyPolicy);

        // Assert
        Assert.Null(policyDto.ProductId);
        Assert.Equal(legacyPolicy.Id, policyDto.Id);
    }

    [Fact]
    public void MapToDto_WithMissingDates_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var legacyPolicy = new LegacyPolicy
        {
            Id = "policy-123",
            ProductId = new ProductId { Type = "health", Plan = "basic", Version = "v1" },
            StartDate = null,
            EndDate = null,
            Fields = null
        };

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => PolicyDtoMapper.MapToDto(legacyPolicy));
    }

    [Fact]
    public void MapToDto_WithExpiredPolicy_ShouldSetCanUploadMembersToFalse()
    {
        // Arrange
        var legacyPolicy = new LegacyPolicy
        {
            Id = "policy-123",
            ProductId = new ProductId { Type = "health", Plan = "basic", Version = "v1" },
            StartDate = DateTime.Today.AddYears(-2),
            EndDate = DateTime.Today.AddYears(-1) // Expired policy
        };

        // Act
        PolicyDto policyDto = PolicyDtoMapper.MapToDto(legacyPolicy);

        // Assert
        Assert.False(policyDto.CanUploadMembers);
    }

    [Fact]
    public void FilterForContractHolderScopeValidation_WithNonV2Policy_ShouldIncludeAllPolicies()
    {
        // Arrange
        var policies = new List<PolicyDto>
        {
            new() { Id = "policy-1", IsV2 = false, Status = "DRAFT", IsRenewal = false, StartDate = DateOnly.FromDateTime(DateTime.Today), EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)) },
            new() { Id = "policy-2", IsV2 = true, Status = "DRAFT", IsRenewal = true, StartDate = DateOnly.FromDateTime(DateTime.Today), EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)) },
            new() { Id = "policy-3", IsV2 = false, Status = "ISSUED", IsRenewal = false, StartDate = DateOnly.FromDateTime(DateTime.Today), EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)) }
        };

        // Act
        List<PolicyDto> result = PolicyDto.FilterForContractHolderScopeValidation(policies, isPolicyV2: false);

        // Assert
        Assert.Equal(3, result.Count);
        Assert.Contains(result, p => p.Id == "policy-1");
        Assert.Contains(result, p => p.Id == "policy-2");
        Assert.Contains(result, p => p.Id == "policy-3");
    }

    [Fact]
    public void FilterForContractHolderScopeValidation_WithV2Policy_ShouldFilterCorrectly()
    {
        // Arrange
        var policies = new List<PolicyDto>
        {
            new() { Id = "policy-1", IsV2 = true, Status = "ISSUED", IsRenewal = false, StartDate = DateOnly.FromDateTime(DateTime.Today), EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)) }, // Should be included
            new() { Id = "policy-2", IsV2 = true, Status = "DRAFT", IsRenewal = false, StartDate = DateOnly.FromDateTime(DateTime.Today), EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)) }, // Should be excluded (DRAFT)
            new() { Id = "policy-3", IsV2 = true, Status = "ISSUED", IsRenewal = true, StartDate = DateOnly.FromDateTime(DateTime.Today), EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)) }, // Should be excluded (IsRenewal)
            new() { Id = "policy-4", IsV2 = false, Status = "ISSUED", IsRenewal = false, StartDate = DateOnly.FromDateTime(DateTime.Today), EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)) }, // Should be excluded (not V2)
            new() { Id = "policy-5", IsV2 = true, Status = "IN_FORCE", IsRenewal = false, StartDate = DateOnly.FromDateTime(DateTime.Today), EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)) } // Should be included
        };

        // Act
        List<PolicyDto> result = PolicyDto.FilterForContractHolderScopeValidation(policies, isPolicyV2: true);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, p => p.Id == "policy-1");
        Assert.Contains(result, p => p.Id == "policy-5");
        Assert.DoesNotContain(result, p => p.Id == "policy-2");
        Assert.DoesNotContain(result, p => p.Id == "policy-3");
        Assert.DoesNotContain(result, p => p.Id == "policy-4");
    }

    [Fact]
    public void FilterForContractHolderScopeValidation_WithEmptyList_ShouldReturnEmptyList()
    {
        // Arrange
        var policies = new List<PolicyDto>();

        // Act
        List<PolicyDto> resultV2 = PolicyDto.FilterForContractHolderScopeValidation(policies, isPolicyV2: true);
        List<PolicyDto> resultNonV2 = PolicyDto.FilterForContractHolderScopeValidation(policies, isPolicyV2: false);

        // Assert
        Assert.Empty(resultV2);
        Assert.Empty(resultNonV2);
    }
}