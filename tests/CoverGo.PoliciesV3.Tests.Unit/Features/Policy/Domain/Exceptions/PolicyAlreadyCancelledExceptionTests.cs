using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;

namespace CoverGo.PoliciesV3.Tests.Unit.Features.Policy.Domain.Exceptions;

public class PolicyAlreadyCancelledExceptionTests
{
    [Fact]
    public void Constructor_WithPolicyId_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();

        // Act
        var exception = new PolicyAlreadyCancelledException(policyId);

        // Assert
        exception.PolicyId.Should().Be(policyId);
        exception.Code.Should().Be(ErrorCodes.PolicyAlreadyCancelled);
        exception.Message.Should().Be($"Policy {policyId} is already cancelled");
    }

    [Fact]
    public void Constructor_WithPolicyIdAndInnerException_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        var innerException = new InvalidOperationException("Inner exception message");

        // Act
        var exception = new PolicyAlreadyCancelledException(policyId, innerException);

        // Assert
        exception.PolicyId.Should().Be(policyId);
        exception.Code.Should().Be(ErrorCodes.PolicyAlreadyCancelled);
        exception.Message.Should().Be($"Policy {policyId} is already cancelled");
        exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void Code_ShouldReturnCorrectErrorCode()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        var exception = new PolicyAlreadyCancelledException(policyId);

        // Act & Assert
        exception.Code.Should().Be(ErrorCodes.PolicyAlreadyCancelled);
        exception.Code.Should().Be("POLICY_ALREADY_CANCELLED");
    }

    [Fact]
    public void Exception_ShouldInheritFromDomainException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();

        // Act
        var exception = new PolicyAlreadyCancelledException(policyId);

        // Assert
        exception.Should().BeAssignableTo<DomainException>();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("policy-123")]
    [InlineData("550e8400-e29b-41d4-a716-************")]
    public void Constructor_WithVariousPolicyIds_ShouldHandleCorrectly(string policyId)
    {
        // Act
        var exception = new PolicyAlreadyCancelledException(policyId);

        // Assert
        exception.PolicyId.Should().Be(policyId);

        // Only assert message containment for non-empty strings (FluentAssertions doesn't allow empty string containment)
        if (!string.IsNullOrEmpty(policyId))
        {
            exception.Message.Should().Contain(policyId);
        }
    }

    [Fact]
    public void Exception_ShouldBeSerializable()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        var originalException = new PolicyAlreadyCancelledException(policyId);

        // Act & Assert - Exception should be serializable for logging/monitoring
        originalException.Should().NotBeNull();
        originalException.Data.Should().NotBeNull();
    }
}
