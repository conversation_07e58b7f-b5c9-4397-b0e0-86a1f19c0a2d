using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;
using CoverGo.PoliciesV3.Domain.Products;

namespace CoverGo.PoliciesV3.Tests.Unit.Features.Policy.Domain.Validation;

public class PolicyDtoValidationTests
{
    [Fact]
    public void ValidateProductIdForMemberUpload_WithValidProductId_ShouldNotThrow()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();

        // Act & Assert
        policy.ValidateProductIdForMemberUpload(); // Should not throw
    }

    [Fact]
    public void ValidateProductIdForMemberUpload_WithMissingProductId_ShouldThrowPolicyProductIdMissingException()
    {
        // Arrange
        PolicyDto policy = CreatePolicyWithoutProductId();

        // Act & Assert
        Assert.Throws<PolicyProductIdMissingException>(() => policy.ValidateProductIdForMemberUpload());
    }

    [Fact]
    public void ValidatePolicyStateForMemberUpload_WithNonIssuedPolicyAndNoEndorsement_ShouldNotThrow()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();

        // Act & Assert
        policy.ValidatePolicyStateForMemberUpload(); // Should not throw
    }

    [Fact]
    public void ValidatePolicyStateForMemberUpload_WithIssuedPolicyAndNoEndorsement_ShouldThrowPolicyIssuedException()
    {
        // Arrange
        PolicyDto policy = CreateIssuedPolicy();

        // Act & Assert
        Assert.Throws<PolicyIssuedException>(() => policy.ValidatePolicyStateForMemberUpload());
    }

    [Fact]
    public void ValidatePolicyStateForMemberUpload_WithInvalidEndorsement_ShouldThrowEndorsementNotFoundException()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();
        var endorsementId = Guid.NewGuid(); // Non-existent endorsement

        // Act & Assert
        Assert.Throws<EndorsementNotFoundException>(() =>
            policy.ValidatePolicyStateForMemberUpload(endorsementId));
    }

    [Fact]
    public void CanAcceptMemberUpload_UsesNewSeparatedMethods_ShouldMaintainSameBehavior()
    {
        // Arrange
        PolicyDto validPolicy = CreateValidPolicy();
        PolicyDto issuedPolicy = CreateIssuedPolicy();
        PolicyDto policyWithoutProductId = CreatePolicyWithoutProductId();

        // Act & Assert
        Assert.True(validPolicy.CanAcceptMemberUpload());
        Assert.False(issuedPolicy.CanAcceptMemberUpload());
        Assert.False(policyWithoutProductId.CanAcceptMemberUpload());
    }

    private static PolicyDto CreateValidPolicy()
    {
        return new PolicyDto
        {
            Id = Guid.NewGuid().ToString(),
            ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
            ContractHolderId = Guid.NewGuid().ToString(),
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            IsIssued = false,
            Endorsements = [],
            ApprovedEndorsementIds = []
        };
    }

    private static PolicyDto CreateIssuedPolicy()
    {
        return new PolicyDto
        {
            Id = Guid.NewGuid().ToString(),
            ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
            ContractHolderId = Guid.NewGuid().ToString(),
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            IsIssued = true, // This will cause validation to fail
            Endorsements = [],
            ApprovedEndorsementIds = []
        };
    }

    private static PolicyDto CreatePolicyWithoutProductId()
    {
        return new PolicyDto
        {
            Id = Guid.NewGuid().ToString(),
            ProductId = null, // Missing ProductId
            ContractHolderId = Guid.NewGuid().ToString(),
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            IsIssued = false,
            Endorsements = [],
            ApprovedEndorsementIds = []
        };
    }
}
