namespace CoverGo.PoliciesV3.Tests.Unit.Common;

/// <summary>
/// Provides fixed date constants for deterministic test behavior.
/// Using fixed dates ensures tests produce consistent results across different environments and time zones.
/// </summary>
public static class TestDateConstants
{
    /// <summary>
    /// Fixed reference date for test scenarios (June 15, 2024, 12:00 PM UTC).
    /// Use this as the "current date" in tests instead of DateTime.Now or DateTime.Today.
    /// </summary>
    public static readonly DateTime ReferenceDateTime = new DateTime(2024, 6, 15, 12, 0, 0, DateTimeKind.Utc);

    /// <summary>
    /// Fixed reference date as DateOnly for test scenarios (June 15, 2024).
    /// Use this as the "current date" in tests instead of DateOnly.FromDateTime(DateTime.Today).
    /// </summary>
    public static readonly DateOnly ReferenceDate = DateOnly.FromDateTime(ReferenceDateTime);

    /// <summary>
    /// Common test birth dates for age validation scenarios.
    /// </summary>
    public static class BirthDates
    {
        /// <summary>16 years old as of reference date - too young for employee validation</summary>
        public static readonly DateOnly Age16 = ReferenceDate.AddYears(-16);

        /// <summary>18 years old as of reference date - minimum employee age</summary>
        public static readonly DateOnly Age18 = ReferenceDate.AddYears(-18);

        /// <summary>25 years old as of reference date - typical employee age</summary>
        public static readonly DateOnly Age25 = ReferenceDate.AddYears(-25);

        /// <summary>30 years old as of reference date - typical employee age</summary>
        public static readonly DateOnly Age30 = ReferenceDate.AddYears(-30);

        /// <summary>65 years old as of reference date - maximum employee age</summary>
        public static readonly DateOnly Age65 = ReferenceDate.AddYears(-65);

        /// <summary>70 years old as of reference date - too old for employee validation</summary>
        public static readonly DateOnly Age70 = ReferenceDate.AddYears(-70);
    }

    /// <summary>
    /// Common test policy dates.
    /// </summary>
    public static class PolicyDates
    {
        /// <summary>Policy start date - 30 days before reference date</summary>
        public static readonly DateOnly PolicyStart = ReferenceDate.AddDays(-30);

        /// <summary>Policy end date - 1 year after reference date</summary>
        public static readonly DateOnly PolicyEnd = ReferenceDate.AddYears(1);

        /// <summary>Effective date for new members - same as reference date</summary>
        public static readonly DateOnly EffectiveDate = ReferenceDate;
    }
}
