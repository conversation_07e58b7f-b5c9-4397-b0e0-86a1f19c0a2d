namespace CoverGo.PoliciesV3.Tests.Unit.Common;

/// <summary>
/// Provides configurable timeout values for tests that require timeout behavior.
/// These timeouts can be adjusted via environment variables for different environments.
/// </summary>
public static class TestTimeouts
{
    /// <summary>
    /// Short timeout for quick operations (default: 1 second).
    /// Can be overridden with TEST_SHORT_TIMEOUT environment variable.
    /// </summary>
    public static readonly TimeSpan Short = GetConfigurableTimeout("TEST_SHORT_TIMEOUT", TimeSpan.FromSeconds(1));

    /// <summary>
    /// Medium timeout for moderate operations (default: 5 seconds).
    /// Can be overridden with TEST_MEDIUM_TIMEOUT environment variable.
    /// </summary>
    public static readonly TimeSpan Medium = GetConfigurableTimeout("TEST_MEDIUM_TIMEOUT", TimeSpan.FromSeconds(5));

    /// <summary>
    /// Long timeout for complex operations (default: 30 seconds).
    /// Can be overridden with TEST_LONG_TIMEOUT environment variable.
    /// </summary>
    public static readonly TimeSpan Long = GetConfigurableTimeout("TEST_LONG_TIMEOUT", TimeSpan.FromSeconds(30));

    /// <summary>
    /// Very short timeout for immediate cancellation tests (default: 100 milliseconds).
    /// Can be overridden with TEST_IMMEDIATE_TIMEOUT environment variable.
    /// </summary>
    public static readonly TimeSpan Immediate = GetConfigurableTimeout("TEST_IMMEDIATE_TIMEOUT", TimeSpan.FromMilliseconds(100));

    /// <summary>
    /// Deadlock detection timeout (default: 10 seconds).
    /// Can be overridden with TEST_DEADLOCK_TIMEOUT environment variable.
    /// </summary>
    public static readonly TimeSpan DeadlockDetection = GetConfigurableTimeout("TEST_DEADLOCK_TIMEOUT", TimeSpan.FromSeconds(10));

    /// <summary>
    /// Gets a configurable timeout value from environment variable or uses default.
    /// </summary>
    /// <param name="environmentVariable">Name of the environment variable</param>
    /// <param name="defaultValue">Default timeout value if environment variable is not set</param>
    /// <returns>Configured timeout value</returns>
    private static TimeSpan GetConfigurableTimeout(string environmentVariable, TimeSpan defaultValue)
    {
        string? envValue = Environment.GetEnvironmentVariable(environmentVariable);
        
        if (string.IsNullOrEmpty(envValue))
            return defaultValue;

        if (TimeSpan.TryParse(envValue, out TimeSpan timeout))
            return timeout;

        if (double.TryParse(envValue, out double seconds))
            return TimeSpan.FromSeconds(seconds);

        // If parsing fails, return default value
        return defaultValue;
    }
}
