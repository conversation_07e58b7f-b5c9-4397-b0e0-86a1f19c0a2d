namespace CoverGo.PoliciesV3.Tests.Unit.Common;

/// <summary>
/// Constants for test categories to enable selective test execution.
/// Use these categories to separate different types of tests for CI/CD pipelines.
/// </summary>
public static class TestCategories
{
    /// <summary>
    /// Fast, deterministic unit tests that should run in every build.
    /// These tests focus on correctness and business logic validation.
    /// </summary>
    public const string Unit = "Unit";

    /// <summary>
    /// Performance and benchmark tests that measure execution time, memory usage, or throughput.
    /// These tests may be environment-dependent and should be run separately from unit tests.
    /// </summary>
    public const string Performance = "Performance";

    /// <summary>
    /// Load tests that simulate high-volume scenarios.
    /// These tests are typically slow and resource-intensive.
    /// </summary>
    public const string Load = "Load";

    /// <summary>
    /// Integration tests that test component interactions.
    /// </summary>
    public const string Integration = "Integration";

    /// <summary>
    /// Tests that are known to be flaky or timing-dependent.
    /// These should be fixed or moved to appropriate categories.
    /// </summary>
    public const string Flaky = "Flaky";
}
