using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.Products.Client;
using DomainProductId = CoverGo.PoliciesV3.Domain.Policies.ProductId;

namespace CoverGo.PoliciesV3.Tests.Unit.Services;

/// <summary>
/// Examples of how to create valuable mock data tests for ProductService
/// This demonstrates the proper approach for testing business logic with meaningful data
/// </summary>
[Trait("Category", "Unit")]
[Trait("Component", "Infrastructure")]
[Trait("Feature", "Product")]
public class ProductServiceMockTests
{
    private readonly Mock<IProductService> _mockProductService;
    private readonly Fixture _fixture;
    private readonly CancellationToken _cancellationToken;

    public ProductServiceMockTests()
    {
        _mockProductService = new Mock<IProductService>();
        _fixture = new Fixture();
        _cancellationToken = CancellationToken.None;
    }

    #region Valuable Mock Data Examples

    [Fact]
    public async Task ProductService_WithSMEProduct_ShouldReturnSMESchema()
    {
        // Arrange - Create meaningful test data
        ProductId productId = CreateProductId("health-insurance", "sme", "v1.0");
        string expectedSchema = CreateSMEHealthInsuranceSchema();

        _mockProductService
            .Setup(x => x.GetProductMemberSchema(productId, _cancellationToken))
            .ReturnsAsync(expectedSchema);

        _mockProductService
            .Setup(x => x.GetProductPackageType(productId, _cancellationToken))
            .ReturnsAsync("sme");

        // Act
        string? schema = await _mockProductService.Object.GetProductMemberSchema(productId, _cancellationToken);
        string? packageType = await _mockProductService.Object.GetProductPackageType(productId, _cancellationToken);

        // Assert - Verify business logic with real data
        schema.Should().NotBeNull();
        schema.Should().Contain("companySize");
        schema.Should().Contain("annualRevenue");
        packageType.Should().Be("sme");
    }

    [Fact]
    public async Task ProductService_WithIndividualProduct_ShouldReturnIndividualSchema()
    {
        // Arrange - Create meaningful test data
        ProductId productId = CreateProductId("life-insurance", "individual", "v2.1");
        string expectedSchema = CreateIndividualLifeInsuranceSchema();

        _mockProductService
            .Setup(x => x.GetProductMemberSchema(productId, _cancellationToken))
            .ReturnsAsync(expectedSchema);

        _mockProductService
            .Setup(x => x.GetProductPackageType(productId, _cancellationToken))
            .ReturnsAsync("individual");

        // Act
        string? schema = await _mockProductService.Object.GetProductMemberSchema(productId, _cancellationToken);
        string? packageType = await _mockProductService.Object.GetProductPackageType(productId, _cancellationToken);

        // Assert - Verify business logic with real data
        schema.Should().NotBeNull();
        schema.Should().Contain("age");
        schema.Should().Contain("gender");
        schema.Should().Contain("smokingStatus");
        packageType.Should().Be("individual");
    }

    [Fact]
    public async Task ProductService_WithMultiplePlanIds_ShouldReturnAllAvailablePlans()
    {
        // Arrange - Create meaningful test data
        DomainProductId productId = CreateDomainProductId("travel-insurance", "group", "v1.5");
        var expectedPlanIds = new List<string>
        {
            "basic-travel-plan",
            "premium-travel-plan",
            "family-travel-plan"
        };

        _mockProductService
            .Setup(x => x.GetAvailablePlanIds(productId, _cancellationToken))
            .ReturnsAsync(expectedPlanIds);

        // Act
        IReadOnlyList<string>? planIds = await _mockProductService.Object.GetAvailablePlanIds(productId, _cancellationToken);

        // Assert - Verify business logic with real data
        planIds.Should().NotBeNull();
        planIds.Should().HaveCount(3);
        planIds.Should().Contain("basic-travel-plan");
        planIds.Should().Contain("premium-travel-plan");
        planIds.Should().Contain("family-travel-plan");
    }

    [Theory]
    [InlineData("sme", "companySize")]
    [InlineData("individual", "age")]
    [InlineData("group", "groupSize")]
    [InlineData("corporate", "employeeCount")]
    public async Task ProductService_WithDifferentPackageTypes_ShouldReturnAppropriateSchemas(
        string packageType,
        string expectedField)
    {
        // Arrange - Create meaningful test data for different package types
        ProductId productId = CreateProductId("health-insurance", packageType, "v1.0");
        string schema = CreateSchemaForPackageType(packageType);

        _mockProductService
            .Setup(x => x.GetProductMemberSchema(productId, _cancellationToken))
            .ReturnsAsync(schema);

        _mockProductService
            .Setup(x => x.GetProductPackageType(productId, _cancellationToken))
            .ReturnsAsync(packageType);

        // Act
        string? resultSchema = await _mockProductService.Object.GetProductMemberSchema(productId, _cancellationToken);
        string? resultPackageType = await _mockProductService.Object.GetProductPackageType(productId, _cancellationToken);

        // Assert - Verify business logic with real data
        resultSchema.Should().NotBeNull();
        resultSchema.Should().Contain(expectedField);
        resultPackageType.Should().Be(packageType);
    }

    #endregion

    #region Test Data Helpers

    private static ProductId CreateProductId(string plan, string type, string version) => new()
    {
        Plan = plan,
        Type = type,
        Version = version
    };

    private static DomainProductId CreateDomainProductId(string plan, string type, string version) => new(plan, type, version);

    private static string CreateSMEHealthInsuranceSchema() => """
        {
          "type": "object",
          "properties": {
            "companySize": {
              "type": "integer",
              "minimum": 1,
              "maximum": 250,
              "description": "Number of employees"
            },
            "annualRevenue": {
              "type": "number",
              "minimum": 0,
              "description": "Annual revenue in USD"
            },
            "industry": {
              "type": "string",
              "enum": ["technology", "healthcare", "finance", "manufacturing", "retail"]
            },
            "businessAddress": {
              "type": "object",
              "properties": {
                "country": {"type": "string"},
                "state": {"type": "string"},
                "city": {"type": "string"}
              }
            }
          },
          "required": ["companySize", "annualRevenue", "industry"]
        }
        """;

    private static string CreateIndividualLifeInsuranceSchema() => """
        {
          "type": "object",
          "properties": {
            "age": {
              "type": "integer",
              "minimum": 18,
              "maximum": 75,
              "description": "Age of the insured person"
            },
            "gender": {
              "type": "string",
              "enum": ["male", "female", "other"]
            },
            "smokingStatus": {
              "type": "string",
              "enum": ["never", "former", "current"]
            },
            "height": {
              "type": "number",
              "description": "Height in centimeters"
            },
            "weight": {
              "type": "number",
              "description": "Weight in kilograms"
            },
            "occupation": {
              "type": "string",
              "description": "Current occupation"
            }
          },
          "required": ["age", "gender", "smokingStatus"]
        }
        """;

    private string CreateSchemaForPackageType(string packageType) => packageType switch
    {
        "sme" => CreateSMEHealthInsuranceSchema(),
        "individual" => CreateIndividualLifeInsuranceSchema(),
        "group" => """{"type": "object", "properties": {"groupSize": {"type": "integer"}}}""",
        "corporate" => """{"type": "object", "properties": {"employeeCount": {"type": "integer"}}}""",
        _ => """{"type": "object", "properties": {"genericField": {"type": "string"}}}"""
    };

    #endregion
}
