using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Infrastructure.ExternalServices;
using CoverGo.Products.Client;
using Microsoft.Extensions.Logging;
using DomainProductId = CoverGo.PoliciesV3.Domain.Policies.ProductId;

namespace CoverGo.PoliciesV3.Tests.Unit.Services;

/// <summary>
/// Unit tests for ProductService
/// Tests service instantiation and basic functionality
/// Note: Complex GraphQL mocking is handled at the interface level for handler tests
/// </summary>
[Trait("Category", "Unit")]
[Trait("Component", "Infrastructure")]
[Trait("Feature", "Product")]
public class ProductServiceTests : IDisposable
{
    private readonly Mock<ILogger<ProductService>> _mockLogger;
    private readonly HttpClient _httpClient;
    private readonly Fixture _fixture;
    private readonly CancellationToken _cancellationToken;

    public ProductServiceTests()
    {
        _mockLogger = new Mock<ILogger<ProductService>>();
        _httpClient = new HttpClient
        {
            BaseAddress = new Uri("https://test-products-api.com/")
        };
        _fixture = new Fixture();
        _cancellationToken = CancellationToken.None;
    }

    #region Test Helpers

    private ProductService CreateService() => new(_httpClient, _mockLogger.Object);

    private ProductId CreateProductsClientProductId() => new()
    {
        Plan = _fixture.Create<string>(),
        Type = _fixture.Create<string>(),
        Version = _fixture.Create<string>()
    };

    private DomainProductId CreateDomainProductId() => new(
            _fixture.Create<string>(),
            _fixture.Create<string>(),
            _fixture.Create<string>());

    #endregion

    #region Service Instantiation and Basic Functionality Tests

    [Fact]
    public void ProductService_CanBeInstantiated()
    {
        // Arrange & Act
        ProductService service = CreateService();

        // Assert
        service.Should().NotBeNull();
        service.Should().BeAssignableTo<IProductService>();
    }

    [Fact]
    public async Task GetProductMemberSchema_WithNullProductId_ShouldThrowException()
    {
        // Arrange
        ProductService service = CreateService();

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(() =>
            service.GetProductMemberSchema(null!, _cancellationToken));
    }

    [Fact]
    public async Task GetProductPackageType_WithNullProductId_ShouldThrowException()
    {
        // Arrange
        ProductService service = CreateService();

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(() =>
            service.GetProductPackageType(null!, _cancellationToken));
    }

    [Fact]
    public async Task GetAvailablePlanIds_WithNullProductId_ShouldReturnNull()
    {
        // Arrange
        ProductService service = CreateService();

        // Act
        IReadOnlyList<string>? result = await service.GetAvailablePlanIds(null!, _cancellationToken);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ProductService_ShouldImplementIProductService()
    {
        // Arrange & Act
        ProductService service = CreateService();

        // Assert - Service should implement the interface correctly
        service.Should().BeAssignableTo<IProductService>();

        // Verify service has the expected methods
        Type serviceType = service.GetType();
        serviceType.GetMethod(nameof(IProductService.GetProductMemberSchema)).Should().NotBeNull();
        serviceType.GetMethod(nameof(IProductService.GetProductPackageType)).Should().NotBeNull();
        serviceType.GetMethod(nameof(IProductService.GetAvailablePlanIds)).Should().NotBeNull();
    }

    [Fact]
    public void ProductService_WithValidDependencies_ShouldInstantiateCorrectly()
    {
        // Arrange
        var httpClient = new HttpClient { BaseAddress = new Uri("https://test.com/") };
        ILogger<ProductService> logger = new Mock<ILogger<ProductService>>().Object;

        // Act
        var service = new ProductService(httpClient, logger);

        // Assert
        service.Should().NotBeNull();
        service.Should().BeAssignableTo<IProductService>();
    }

    #endregion

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            _httpClient?.Dispose();
        }
    }

    void IDisposable.Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
