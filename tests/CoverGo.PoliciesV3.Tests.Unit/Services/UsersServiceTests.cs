using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Common.DTOs;
using CoverGo.PoliciesV3.Infrastructure.ExternalServices;
using CoverGo.Users.Client;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Services;

/// <summary>
/// Unit tests for UsersService methods by mocking the QueryIndividuals method while testing the real business logic
/// </summary>
public class UsersServiceTests
{
    private readonly Mock<UsersService> _mockUsersService;
    private readonly Mock<ILogger<UsersService>> _mockLogger;
    private readonly TenantId _tenantId;
    private readonly CancellationToken _cancellationToken;

    public UsersServiceTests()
    {
        _mockLogger = new Mock<ILogger<UsersService>>();
        _tenantId = new TenantId("test-tenant");
        _cancellationToken = CancellationToken.None;

        // Create a partial mock of UsersService - mocks virtual methods but allows real implementation to run
        _mockUsersService = new Mock<UsersService>(new HttpClient(), _tenantId, _mockLogger.Object)
        {
            CallBase = true // This allows real methods to execute while we mock only what we setup
        };
    }

    private void SetupQueryIndividualsResponse(List<Individual>? response) => _mockUsersService
            .Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), _cancellationToken))
            .ReturnsAsync(response ?? []);

    private void VerifyQueryIndividualsCalled(List<string> expectedMemberIds) => _mockUsersService.Verify(x => x.QueryIndividuals(
                                                                                          It.Is<QueryArgumentsOfIndividualWhere>(q =>
                                                                                              q.Where != null &&
                                                                                              q.Where.InternalCode_in != null &&
                                                                                              q.Where.InternalCode_in.All(expectedMemberIds.Contains) &&
                                                                                              expectedMemberIds.All(q.Where.InternalCode_in.Contains)),
                                                                                          _cancellationToken), Times.Once);

    private void VerifyQueryIndividualsCalledWithEmails(List<string> expectedEmails) => _mockUsersService.Verify(x => x.QueryIndividuals(
                                                                                                 It.Is<QueryArgumentsOfIndividualWhere>(q =>
                                                                                                     q.Where != null &&
                                                                                                     q.Where.Email_in != null &&
                                                                                                     q.Where.Email_in.All(expectedEmails.Contains) &&
                                                                                                     expectedEmails.All(q.Where.Email_in.Contains)),
                                                                                                 _cancellationToken), Times.Once);

    #region GetMemberIdsWithoutExistingIndividual Tests

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task GetMemberIdsWithoutExistingIndividual_WhenNoIndividualsExist_ShouldReturnAllMemberIds()
    {
        // Arrange
        var memberIds = new HashSet<string> { "member1", "member2", "member3" };
        var emptyIndividualsList = new List<Individual>();

        SetupQueryIndividualsResponse(emptyIndividualsList);

        // Act
        HashSet<string> result = await _mockUsersService.Object.GetMemberIdsWithoutExistingIndividual(memberIds, _cancellationToken);

        // Assert
        result.Should().BeEquivalentTo(memberIds);
        VerifyQueryIndividualsCalled([.. memberIds]);
    }

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task GetMemberIdsWithoutExistingIndividual_WhenSomeIndividualsExist_ShouldReturnOnlyMissingMemberIds()
    {
        // Arrange
        var inputMemberIds = new HashSet<string> { "member1", "member2", "member3" };
        var existingIndividuals = new List<Individual>
        {
            new() { InternalCode = "member1" },
            new() { InternalCode = "member3" }
        };

        SetupQueryIndividualsResponse(existingIndividuals);

        // Act
        HashSet<string> result = await _mockUsersService.Object.GetMemberIdsWithoutExistingIndividual(inputMemberIds, _cancellationToken);

        // Assert
        result.Should().HaveCount(1);
        result.Should().Contain("member2");
        result.Should().NotContain("member1");
        result.Should().NotContain("member3");
    }

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task GetMemberIdsWithoutExistingIndividual_WhenAllIndividualsExist_ShouldReturnEmptySet()
    {
        // Arrange
        var memberIds = new HashSet<string> { "member1", "member2" };
        var existingIndividuals = new List<Individual>
        {
            new() { InternalCode = "member1" },
            new() { InternalCode = "member2" }
        };

        SetupQueryIndividualsResponse(existingIndividuals);

        // Act
        HashSet<string> result = await _mockUsersService.Object.GetMemberIdsWithoutExistingIndividual(memberIds, _cancellationToken);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task GetMemberIdsWithoutExistingIndividual_WhenEmptyMemberIdSet_ShouldReturnEmptySet()
    {
        // Arrange
        var memberIds = new HashSet<string>();
        var emptyIndividualsList = new List<Individual>();

        SetupQueryIndividualsResponse(emptyIndividualsList);

        // Act
        HashSet<string> result = await _mockUsersService.Object.GetMemberIdsWithoutExistingIndividual(memberIds, _cancellationToken);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task GetMemberIdsWithoutExistingIndividual_WhenClientReturnsNull_ShouldReturnAllMemberIds()
    {
        // Arrange
        var memberIds = new HashSet<string> { "member1", "member2" };

        SetupQueryIndividualsResponse(null);

        // Act
        HashSet<string> result = await _mockUsersService.Object.GetMemberIdsWithoutExistingIndividual(memberIds, _cancellationToken);

        // Assert
        result.Should().BeEquivalentTo(memberIds);
    }

    #endregion

    #region GetDuplicatedIndividualsByEmails Tests

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task GetDuplicatedIndividualsByEmails_WhenNoDuplicatesExist_ShouldReturnEmptyList()
    {
        // Arrange
        var emailToMemberIdDictionary = new Dictionary<string, string>
        {
            { "<EMAIL>", "member1" },
            { "<EMAIL>", "member2" }
        };

        var individuals = new List<Individual>
        {
            new() { Email = "<EMAIL>", InternalCode = "member1", Id = Guid.NewGuid() },
            new() { Email = "<EMAIL>", InternalCode = "member2", Id = Guid.NewGuid() }
        };

        SetupQueryIndividualsResponse(individuals);

        // Act
        List<DuplicatedIndividual>? result = await _mockUsersService.Object.GetDuplicatedIndividualsByEmails(emailToMemberIdDictionary, _cancellationToken);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task GetDuplicatedIndividualsByEmails_WhenDuplicatesExist_ShouldReturnDuplicateEmails()
    {
        // Arrange
        var emailToMemberIdDictionary = new Dictionary<string, string>
        {
            { "<EMAIL>", "member1" },
            { "<EMAIL>", "member2" }
        };

        var duplicateId = Guid.NewGuid();
        var individuals = new List<Individual>
        {
            new() { Email = "<EMAIL>", InternalCode = "different-member", Id = duplicateId }, // Different member ID = duplicate
            new() { Email = "<EMAIL>", InternalCode = "member2", Id = Guid.NewGuid() } // Same member ID = not duplicate
        };

        SetupQueryIndividualsResponse(individuals);

        // Act
        List<DuplicatedIndividual>? result = await _mockUsersService.Object.GetDuplicatedIndividualsByEmails(emailToMemberIdDictionary, _cancellationToken);

        // Assert
        result.Should().HaveCount(1);
        result.Should().Contain(x => x.Email == "<EMAIL>" && x.DuplicatedByEntityId == duplicateId.ToString());
    }

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task GetDuplicatedIndividualsByEmails_WhenClientReturnsNull_ShouldReturnEmptyList()
    {
        // Arrange
        var emailToMemberIdDictionary = new Dictionary<string, string>
        {
            { "<EMAIL>", "member1" }
        };

        SetupQueryIndividualsResponse(null);

        // Act
        List<DuplicatedIndividual>? result = await _mockUsersService.Object.GetDuplicatedIndividualsByEmails(emailToMemberIdDictionary, _cancellationToken);

        // Assert
        result.Should().BeEmpty(); // QueryIndividuals returns [] when null, so result is empty list
    }

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task GetDuplicatedIndividualsByEmails_WhenEmptyDictionary_ShouldCallClientWithEmptyEmailList()
    {
        // Arrange
        var emailToMemberIdDictionary = new Dictionary<string, string>();
        var emptyIndividualsList = new List<Individual>();

        SetupQueryIndividualsResponse(emptyIndividualsList);

        // Act
        List<DuplicatedIndividual>? result = await _mockUsersService.Object.GetDuplicatedIndividualsByEmails(emailToMemberIdDictionary, _cancellationToken);

        // Assert
        result.Should().BeEmpty();
        VerifyQueryIndividualsCalledWithEmails([]);
    }

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task GetDuplicatedIndividualsByEmails_ShouldCallClientWithCorrectEmailList()
    {
        // Arrange
        var emailToMemberIdDictionary = new Dictionary<string, string>
        {
            { "<EMAIL>", "member1" },
            { "<EMAIL>", "member2" }
        };

        var individuals = new List<Individual>();

        SetupQueryIndividualsResponse(individuals);

        // Act
        await _mockUsersService.Object.GetDuplicatedIndividualsByEmails(emailToMemberIdDictionary, _cancellationToken);

        // Assert
        VerifyQueryIndividualsCalledWithEmails(["<EMAIL>", "<EMAIL>"]);
    }

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task GetDuplicatedIndividualsByEmails_WithEmailNotInDictionary_ShouldReturnAsDuplicate()
    {
        // Arrange
        var emailToMemberIdDictionary = new Dictionary<string, string>
        {
            { "<EMAIL>", "member1" }
        };

        var duplicateId = Guid.NewGuid();
        var individuals = new List<Individual>
        {
            new() { Email = "<EMAIL>", InternalCode = "member1", Id = Guid.NewGuid() }, // Matches dictionary
            new() { Email = "<EMAIL>", InternalCode = "member2", Id = duplicateId } // Not in dictionary = duplicate
        };

        SetupQueryIndividualsResponse(individuals);

        // Act
        List<DuplicatedIndividual>? result = await _mockUsersService.Object.GetDuplicatedIndividualsByEmails(emailToMemberIdDictionary, _cancellationToken);

        // Assert
        result.Should().HaveCount(1);
        result.Should().Contain(x => x.Email == "<EMAIL>" && x.DuplicatedByEntityId == duplicateId.ToString()); // Email is preserved, not null
    }

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task GetDuplicatedIndividualsByEmails_WithMultipleDuplicates_ShouldReturnAllDuplicates()
    {
        // Arrange
        var emailToMemberIdDictionary = new Dictionary<string, string>
        {
            { "<EMAIL>", "member1" },
            { "<EMAIL>", "member2" },
            { "<EMAIL>", "member3" }
        };

        var duplicate1Id = Guid.NewGuid();
        var duplicate2Id = Guid.NewGuid();
        var individuals = new List<Individual>
        {
            new() { Email = "<EMAIL>", InternalCode = "wrong-member", Id = duplicate1Id }, // Different member ID
            new() { Email = "<EMAIL>", InternalCode = "member2", Id = Guid.NewGuid() }, // Correct member ID
            new() { Email = "<EMAIL>", InternalCode = "another-wrong-member", Id = duplicate2Id } // Different member ID
        };

        SetupQueryIndividualsResponse(individuals);

        // Act
        List<DuplicatedIndividual>? result = await _mockUsersService.Object.GetDuplicatedIndividualsByEmails(emailToMemberIdDictionary, _cancellationToken);

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain(x => x.Email == "<EMAIL>" && x.DuplicatedByEntityId == duplicate1Id.ToString());
        result.Should().Contain(x => x.Email == "<EMAIL>" && x.DuplicatedByEntityId == duplicate2Id.ToString());
        result.Should().NotContain(x => x.Email == "<EMAIL>");
    }

    #endregion

    #region QueryIndividualsByEmails Tests

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task QueryIndividualsByEmails_WhenEmailsProvided_ShouldReturnMatchingIndividuals()
    {
        // Arrange
        var emails = new List<string> { "<EMAIL>", "<EMAIL>" };
        var expectedIndividuals = new List<Individual>
        {
            new() { Email = "<EMAIL>", InternalCode = "member1", Id = Guid.NewGuid() },
            new() { Email = "<EMAIL>", InternalCode = "member2", Id = Guid.NewGuid() }
        };

        SetupQueryIndividualsResponse(expectedIndividuals);

        // Act
        List<Individual> result = await _mockUsersService.Object.QueryIndividualsByEmails(emails, _cancellationToken);

        // Assert
        result.Should().BeEquivalentTo(expectedIndividuals);
        VerifyQueryIndividualsCalledWithEmails(emails);
    }

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task QueryIndividualsByEmails_WhenEmptyEmailList_ShouldReturnEmptyList()
    {
        // Arrange
        var emails = new List<string>();

        // Act
        List<Individual> result = await _mockUsersService.Object.QueryIndividualsByEmails(emails, _cancellationToken);

        // Assert
        result.Should().BeEmpty();
        _mockUsersService.Verify(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    [Trait("Ticket", "CH-24760")]
    public async Task QueryIndividualsByEmails_WhenNoMatchingIndividuals_ShouldReturnEmptyList()
    {
        // Arrange
        var emails = new List<string> { "<EMAIL>" };
        var emptyIndividualsList = new List<Individual>();

        SetupQueryIndividualsResponse(emptyIndividualsList);

        // Act
        List<Individual> result = await _mockUsersService.Object.QueryIndividualsByEmails(emails, _cancellationToken);

        // Assert
        result.Should().BeEmpty();
        VerifyQueryIndividualsCalledWithEmails(emails);
    }

    #endregion
}