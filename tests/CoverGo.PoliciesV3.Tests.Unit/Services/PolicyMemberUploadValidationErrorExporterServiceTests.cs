using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Tests.Unit.Services;

/// <summary>
/// Unit tests for PolicyMemberUploadValidationErrorExporterService methods
/// </summary>
public class PolicyMemberUploadValidationErrorExporterServiceTests
{
    #region BuildErrorMemberCsvString Tests

    [Fact]
    [Trait("Ticket", "CH-18823")]
    public void BuildErrorMemberCsvString_WithValidData_ShouldGenerateCorrectCsv()
    {
        // Arrange
        string[] headers = new[] { "Name", "Staff No." };
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" } },
            new Dictionary<string, string?> { { "Name", "<PERSON>" }, { "Staff No.", "EMP002" } },
            new Dictionary<string, string?> { { "Name", "<PERSON>" }, { "Staff No.", "EMP003" } }
        };
        var errorRowIndexes = new HashSet<int> { 1, 3 };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildErrorMemberCsvString(headers, contents, errorRowIndexes);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.{Environment.NewLine}" +
                             $"1,John Doe,EMP001{Environment.NewLine}" +
                             $"3,Bob Wilson,EMP003{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18823")]
    public void BuildErrorMemberCsvString_WithEmptyErrorRows_ShouldReturnOnlyHeader()
    {
        // Arrange
        string[] headers = new[] { "Name", "Staff No." };
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" } },
            new Dictionary<string, string?> { { "Name", "Jane Smith" }, { "Staff No.", "EMP002" } }
        };
        var errorRowIndexes = new HashSet<int>(); // No error rows

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildErrorMemberCsvString(headers, contents, errorRowIndexes);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.{Environment.NewLine}";
        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18823")]
    public void BuildErrorMemberCsvString_WithMissingValues_ShouldHandleNullValues()
    {
        // Arrange
        string[] headers = new[] { "Name", "Staff No." };
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" } },
            new Dictionary<string, string?> { { "Name", null }, { "Staff No.", "EMP002" } }
        };
        var errorRowIndexes = new HashSet<int> { 1, 2 };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildErrorMemberCsvString(headers, contents, errorRowIndexes);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.{Environment.NewLine}" +
                             $"1,John Doe,EMP001{Environment.NewLine}" +
                             $"2,,EMP002{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18823")]
    public void BuildErrorMemberCsvString_WithAllRowsAsErrors_ShouldIncludeAllRows()
    {
        // Arrange
        string[] headers = new[] { "Name", "Staff No." };
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" } },
            new Dictionary<string, string?> { { "Name", "Jane Smith" }, { "Staff No.", "EMP002" } }
        };
        var errorRowIndexes = new HashSet<int> { 1, 2 }; // All rows have errors

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildErrorMemberCsvString(headers, contents, errorRowIndexes);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.{Environment.NewLine}" +
                             $"1,John Doe,EMP001{Environment.NewLine}" +
                             $"2,Jane Smith,EMP002{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    #endregion

    #region BuildValidationErrorDetails Tests

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithValidData_ShouldGenerateCorrectCsv()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" }, { "Passport No.", "A123456" }, { "National ID", "HK123456" } },
            new Dictionary<string, string?> { { "Name", "Jane Smith" }, { "Staff No.", "EMP002" }, { "Passport No.", "B789012" }, { "National ID", "HK789012" } },
            new Dictionary<string, string?> { { "Name", "Bob Wilson" }, { "Staff No.", "EMP003" }, { "Passport No.", "C345678" }, { "National ID", "HK345678" } }
        };

        var errors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(1, "INVALID_NAME", "Name is required"),
            CreateValidationError(3, "INVALID_PASSPORT", "Passport number format is invalid")
        };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}" +
                             $"1,John Doe,EMP001,A123456,HK123456,INVALID_NAME,Name is required{Environment.NewLine}" +
                             $"3,Bob Wilson,EMP003,C345678,HK345678,INVALID_PASSPORT,Passport number format is invalid{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithMissingValues_ShouldHandleNullValues()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", null }, { "Staff No.", "EMP001" }, { "Passport No.", null }, { "National ID", "HK123456" } },
            new Dictionary<string, string?> { { "Name", "Jane Smith" }, { "Staff No.", null }, { "Passport No.", "B789012" }, { "National ID", null } }
        };

        var errors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(1, "MISSING_NAME", "Name is required"),
            CreateValidationError(2, "MISSING_STAFF_NO", "Staff number is required")
        };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}" +
                             $"1,,EMP001,,HK123456,MISSING_NAME,Name is required{Environment.NewLine}" +
                             $"2,Jane Smith,,B789012,,MISSING_STAFF_NO,Staff number is required{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithErrorMessagesContainingCommas_ShouldRemoveCommas()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" }, { "Passport No.", "A123456" }, { "National ID", "HK123456" } }
        };

        var errors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(1, "INVALID_FORMAT", "Invalid format, please check the data, and try again")
        };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}" +
                             $"1,John Doe,EMP001,A123456,HK123456,INVALID_FORMAT,Invalid format please check the data and try again{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithErrorsNotInOrder_ShouldSortByRowIndex()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" }, { "Passport No.", "A123456" }, { "National ID", "HK123456" } },
            new Dictionary<string, string?> { { "Name", "Jane Smith" }, { "Staff No.", "EMP002" }, { "Passport No.", "B789012" }, { "National ID", "HK789012" } },
            new Dictionary<string, string?> { { "Name", "Bob Wilson" }, { "Staff No.", "EMP003" }, { "Passport No.", "C345678" }, { "National ID", "HK345678" } }
        };

        var errors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(3, "INVALID_PASSPORT", "Passport number format is invalid"),
            CreateValidationError(1, "INVALID_NAME", "Name is required"),
            CreateValidationError(2, "INVALID_STAFF_NO", "Staff number format is invalid")
        };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}" +
                             $"1,John Doe,EMP001,A123456,HK123456,INVALID_NAME,Name is required{Environment.NewLine}" +
                             $"2,Jane Smith,EMP002,B789012,HK789012,INVALID_STAFF_NO,Staff number format is invalid{Environment.NewLine}" +
                             $"3,Bob Wilson,EMP003,C345678,HK345678,INVALID_PASSPORT,Passport number format is invalid{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithEmptyErrorsList_ShouldReturnOnlyHeader()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" }, { "Passport No.", "A123456" }, { "National ID", "HK123456" } }
        };

        var errors = new List<PolicyMemberUploadValidationError>();

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}";
        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithMissingFieldsInContent_ShouldUseEmptyStrings()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" } } // Missing other fields
        };

        var errors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(1, "MISSING_FIELDS", "Required fields are missing")
        };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}" +
                             $"1,John Doe,,,,MISSING_FIELDS,Required fields are missing{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithOutOfBoundsRowIndex_ShouldOutputPlaceholder()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" }, { "Passport No.", "A123456" }, { "National ID", "HK123456" } }
        };
        var errors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(0, "ERR_CODE", "Error message for row 0"), // 0 is out of bounds
            CreateValidationError(2, "ERR_CODE2", "Error message for row 2") // 2 is out of bounds (contents.Count == 1)
        };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}" +
                             $"0,,,,,ERR_CODE,Error message for row 0{Environment.NewLine}" +
                             $"2,,,,,ERR_CODE2,Error message for row 2{Environment.NewLine}";
        result.Should().Be(expectedCsv);
    }

    #endregion

    #region Helper Methods

    private static PolicyMemberUploadValidationError CreateValidationError(int rowIndex, string code, string message)
    {
        return PolicyMemberUploadValidationError.Create(
            PolicyMemberUploadId.Empty, // Use empty ID for testing
            rowIndex,
            code,
            message);
    }

    #endregion
}