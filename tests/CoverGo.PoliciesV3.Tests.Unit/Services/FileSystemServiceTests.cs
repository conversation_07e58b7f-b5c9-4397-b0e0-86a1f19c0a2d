using System.Net;
using System.Text;
using System.Text.Json;
using CoverGo.FileSystem.Client;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Infrastructure.ExternalServices;
using Microsoft.Extensions.Logging;
using Moq.Protected;

namespace CoverGo.PoliciesV3.Tests.Unit.Services;

/// <summary>
/// Unit tests for FileSystemService
/// Tests file retrieval functionality, error handling, and logging behavior
/// </summary>
[Trait("Category", "Unit")]
[Trait("Component", "Infrastructure")]
[Trait("Feature", "FileSystem")]
public class FileSystemServiceTests : IDisposable
{
    private readonly Mock<HttpMessageHandler> _mockHttpMessageHandler;
    private readonly Mock<ILogger<FileSystemService>> _mockLogger;
    private readonly HttpClient _httpClient;
    private readonly TenantId _tenantId;
    private readonly Fixture _fixture;
    private readonly CancellationToken _cancellationToken;

    public FileSystemServiceTests()
    {
        _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
        _mockLogger = new Mock<ILogger<FileSystemService>>();
        _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
        {
            BaseAddress = new Uri("https://test-filesystem-api.com/")
        };
        _tenantId = new TenantId("test-tenant");
        _fixture = new Fixture();
        _cancellationToken = CancellationToken.None;
    }

    #region Test Helpers

    private FileSystemService CreateService() => new(_httpClient, _tenantId, _mockLogger.Object);

    private void SetupHttpResponse(HttpStatusCode statusCode, string responseContent)
    {
        var response = new HttpResponseMessage(statusCode)
        {
            Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
        };

        _mockHttpMessageHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(response);
    }

    private void SetupSuccessfulFileResponse(byte[] fileContent)
    {
        var result = new ResultOfByteOf
        {
            IsSuccess = true,
            Value = fileContent,
            Errors = null
        };

        string responseContent = JsonSerializer.Serialize(result);
        SetupHttpResponse(HttpStatusCode.OK, responseContent);
    }

    private void SetupFailedFileResponse(string[] errors)
    {
        var result = new ResultOfByteOf
        {
            IsSuccess = false,
            Value = null,
            Errors = [.. errors]
        };

        string responseContent = JsonSerializer.Serialize(result);
        SetupHttpResponse(HttpStatusCode.OK, responseContent);
    }

    #endregion

    #region GetFileByPath Tests

    [Fact]
    public async Task GetFileByPath_WithValidPath_ShouldReturnFileContent()
    {
        // Arrange
        FileSystemService service = CreateService();
        string filePath = "uploads/test-file.csv";
        byte[] expectedContent = Encoding.UTF8.GetBytes("test,file,content\n1,2,3");

        SetupSuccessfulFileResponse(expectedContent);

        // Act
        byte[]? result = await service.GetFileByPath(filePath, _cancellationToken);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(expectedContent);
    }

    [Fact]
    public async Task GetFileByPath_WithEmptyFile_ShouldReturnEmptyByteArray()
    {
        // Arrange
        FileSystemService service = CreateService();
        string filePath = "uploads/empty-file.csv";
        byte[] expectedContent = Array.Empty<byte>();

        SetupSuccessfulFileResponse(expectedContent);

        // Act
        byte[]? result = await service.GetFileByPath(filePath, _cancellationToken);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GetFileByPath_WithFileNotFound_ShouldReturnNullAndLogError()
    {
        // Arrange
        FileSystemService service = CreateService();
        string filePath = "uploads/non-existent-file.csv";
        string[] errors = ["File not found"];

        SetupFailedFileResponse(errors);

        // Act
        byte[]? result = await service.GetFileByPath(filePath, _cancellationToken);

        // Assert
        result.Should().BeNull();

        // Verify error logging
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Failed to get file for path {filePath}")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task GetFileByPath_WithNullResult_ShouldReturnNullAndLogError()
    {
        // Arrange
        FileSystemService service = CreateService();
        string filePath = "uploads/test-file.csv";

        SetupHttpResponse(HttpStatusCode.OK, "null");

        // Act
        byte[]? result = await service.GetFileByPath(filePath, _cancellationToken);

        // Assert
        result.Should().BeNull();

        // Verify error logging
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Failed to get file for path {filePath}")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task GetFileByPath_WithHttpError_ShouldThrowException()
    {
        // Arrange
        FileSystemService service = CreateService();
        string filePath = "uploads/test-file.csv";

        SetupHttpResponse(HttpStatusCode.InternalServerError, "Internal Server Error");

        // Act & Assert
        await Assert.ThrowsAsync<FileSystemException>(() =>
            service.GetFileByPath(filePath, _cancellationToken));
    }

    [Fact]
    public async Task GetFileByPath_WithNetworkTimeout_ShouldThrowException()
    {
        // Arrange
        FileSystemService service = CreateService();
        string filePath = "uploads/test-file.csv";

        _mockHttpMessageHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ThrowsAsync(new TaskCanceledException("Request timeout"));

        // Act & Assert
        await Assert.ThrowsAsync<TaskCanceledException>(() =>
            service.GetFileByPath(filePath, _cancellationToken));
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("uploads/test-file.csv")]
    [InlineData("folder/subfolder/document.pdf")]
    public async Task GetFileByPath_WithVariousValidPaths_ShouldCallCorrectEndpoint(string filePath)
    {
        // Arrange
        FileSystemService service = CreateService();
        byte[] expectedContent = Encoding.UTF8.GetBytes("test content");

        SetupSuccessfulFileResponse(expectedContent);

        // Act
        await service.GetFileByPath(filePath, _cancellationToken);

        // Assert
        _mockHttpMessageHandler
            .Protected()
            .Verify(
                "SendAsync",
                Times.Once(),
                ItExpr.Is<HttpRequestMessage>(req =>
                    req.Method == HttpMethod.Post &&
                    req.RequestUri!.ToString().Contains("filesystem/get")),
                ItExpr.IsAny<CancellationToken>());
    }

    [Fact]
    public async Task GetFileByPath_ShouldUseTenantIdInRequest()
    {
        // Arrange
        FileSystemService service = CreateService();
        string filePath = "uploads/test-file.csv";
        byte[] expectedContent = Encoding.UTF8.GetBytes("test content");

        SetupSuccessfulFileResponse(expectedContent);

        // Act
        await service.GetFileByPath(filePath, _cancellationToken);

        // Assert
        _mockHttpMessageHandler
            .Protected()
            .Verify(
                "SendAsync",
                Times.Once(),
                ItExpr.Is<HttpRequestMessage>(req =>
                    req.RequestUri!.ToString().Contains(_tenantId.Value)),
                ItExpr.IsAny<CancellationToken>());
    }

    [Fact]
    public async Task GetFileByPath_WithLargeFile_ShouldReturnCompleteContent()
    {
        // Arrange
        FileSystemService service = CreateService();
        string filePath = "uploads/large-file.csv";
        byte[] largeContent = new byte[1024 * 1024]; // 1MB file
        _fixture.Create<Random>().NextBytes(largeContent);

        SetupSuccessfulFileResponse(largeContent);

        // Act
        byte[]? result = await service.GetFileByPath(filePath, _cancellationToken);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(largeContent.Length);
        result.Should().BeEquivalentTo(largeContent);
    }

    #endregion

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            _httpClient?.Dispose();
        }
    }

    void IDisposable.Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}