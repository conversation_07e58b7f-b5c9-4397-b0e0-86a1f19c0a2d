using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.Policies.Exceptions;

public class InvalidProductIdComponentExceptionTests
{
    [Fact]
    public void Constructor_WithPolicyIdAndComponentName_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string componentName = "Type";

        // Act
        var exception = new InvalidProductIdComponentException(policyId, componentName);

        // Assert
        exception.PolicyId.Should().Be(policyId);
        exception.ComponentName.Should().Be(componentName);
        exception.Code.Should().Be(ErrorCodes.InvalidProductIdComponent);
        exception.Message.Should().Be($"Policy {policyId} ProductId is missing {componentName}");
    }

    [Fact]
    public void Constructor_WithPolicyIdComponentNameAndInnerException_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string componentName = "Plan";
        var innerException = new ArgumentException("Inner exception message");

        // Act
        var exception = new InvalidProductIdComponentException(policyId, componentName, innerException);

        // Assert
        exception.PolicyId.Should().Be(policyId);
        exception.ComponentName.Should().Be(componentName);
        exception.Code.Should().Be(ErrorCodes.InvalidProductIdComponent);
        exception.Message.Should().Be($"Policy {policyId} ProductId is missing {componentName}");
        exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void Code_ShouldReturnCorrectErrorCode()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string componentName = "Version";
        var exception = new InvalidProductIdComponentException(policyId, componentName);

        // Act & Assert
        exception.Code.Should().Be(ErrorCodes.InvalidProductIdComponent);
        exception.Code.Should().Be("INVALID_PRODUCT_ID_COMPONENT");
    }

    [Fact]
    public void Exception_ShouldInheritFromDomainException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string componentName = "Type";

        // Act
        var exception = new InvalidProductIdComponentException(policyId, componentName);

        // Assert
        exception.Should().BeAssignableTo<DomainException>();
    }

    [Theory]
    [InlineData("policy-123", "Type")]
    [InlineData("550e8400-e29b-41d4-a716-************", "Plan")]
    [InlineData("", "Version")]
    [InlineData("   ", "   ")]
    public void Constructor_WithVariousInputs_ShouldHandleCorrectly(string policyId, string componentName)
    {
        // Act
        var exception = new InvalidProductIdComponentException(policyId, componentName);

        // Assert
        exception.PolicyId.Should().Be(policyId);
        exception.ComponentName.Should().Be(componentName);

        // Only assert message containment for non-empty strings (FluentAssertions doesn't allow empty string containment)
        if (!string.IsNullOrEmpty(policyId))
        {
            exception.Message.Should().Contain(policyId);
        }
        if (!string.IsNullOrEmpty(componentName))
        {
            exception.Message.Should().Contain(componentName);
        }
    }

    [Theory]
    [InlineData("Type")]
    [InlineData("Plan")]
    [InlineData("Version")]
    public void Constructor_WithDifferentComponentNames_ShouldCreateCorrectMessage(string componentName)
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();

        // Act
        var exception = new InvalidProductIdComponentException(policyId, componentName);

        // Assert
        exception.Message.Should().Be($"Policy {policyId} ProductId is missing {componentName}");
    }

    [Fact]
    public void Exception_ShouldBeSerializable()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string componentName = "Type";
        var originalException = new InvalidProductIdComponentException(policyId, componentName);

        // Act & Assert - Exception should be serializable for logging/monitoring
        originalException.Should().NotBeNull();
        originalException.Data.Should().NotBeNull();
    }
}
