using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields.FieldTypes;

public class FieldTypeTests
{
    #region BooleanFieldType Tests

    [Fact]
    public void BooleanFieldType_ValidateField_WithValidBooleanTrue_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new BooleanFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testBooleanField");
        bool value = true;

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(true);
    }

    [Fact]
    public void BooleanFieldType_ValidateField_WithValidBooleanFalse_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new BooleanFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testBooleanField");
        bool value = false;

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(false);
    }

    [Fact]
    public void BooleanFieldType_TryParseField_WithStringTrue_ShouldReturnParsedBoolean()
    {
        // Arrange
        var fieldType = new BooleanFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testBooleanField");
        string value = "true";

        // Act
        Result<object?> result = fieldType.TryParseField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(true);
    }

    [Fact]
    public void BooleanFieldType_TryParseField_WithStringFalse_ShouldReturnParsedBoolean()
    {
        // Arrange
        var fieldType = new BooleanFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testBooleanField");
        string value = "false";

        // Act
        Result<object?> result = fieldType.TryParseField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(false);
    }

    [Fact]
    public void BooleanFieldType_ValidateField_WithInvalidString_ShouldReturnError()
    {
        // Arrange
        var fieldType = new BooleanFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testBooleanField");
        string value = "invalid";

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidType);
    }

    [Fact]
    public void BooleanFieldType_ValidateField_WithNullValue_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new BooleanFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testBooleanField");
        object? value = null;

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeNull();
    }

    #endregion

    #region NumberFieldType Tests

    [Fact]
    public void NumberFieldType_ValidateField_WithValidInteger_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new NumberFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testNumberField");
        int value = 42;

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(42);
    }

    [Fact]
    public void NumberFieldType_ValidateField_WithValidDecimal_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new NumberFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testNumberField");
        decimal value = 42.5m;

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(42.5m);
    }

    [Fact]
    public void NumberFieldType_TryParseField_WithValidStringNumber_ShouldReturnParsedNumber()
    {
        // Arrange
        var fieldType = new NumberFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testNumberField");
        string value = "42.5";

        // Act
        Result<object?> result = fieldType.TryParseField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(42.5m);
    }

    [Fact]
    public void NumberFieldType_ValidateField_WithInvalidString_ShouldReturnError()
    {
        // Arrange
        var fieldType = new NumberFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testNumberField");
        string value = "not-a-number";

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidNumber);
    }

    [Fact]
    public void NumberFieldType_ValidateField_WithNullValue_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new NumberFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testNumberField");
        object? value = null;

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeNull();
    }

    #endregion

    #region FilesFieldType Tests

    [Fact]
    public void FilesFieldType_ValidateField_WithValidFilesList_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new FilesFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testFilesField");
        var value = new List<Dictionary<string, object>>
        {
            new() { { "fileId", "12345" }, { "fileName", "document.pdf" } }
        };

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(value);
    }

    [Fact]
    public void FilesFieldType_ValidateField_WithNullValue_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new FilesFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testFilesField");
        object? value = null;

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeNull();
    }

    [Fact]
    public void FilesFieldType_ValidateField_WithInvalidType_ShouldReturnError()
    {
        // Arrange
        var fieldType = new FilesFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testFilesField");
        var value = new { fileId = "12345", fileName = "document.pdf" }; // Invalid: not a List<Dictionary<string, object>>

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidType);
    }

    #endregion

    #region AddressFieldType Tests

    [Fact]
    public void AddressFieldType_ValidateField_WithValidAddress_ShouldReturnNull()
    {
        // Arrange
        var innerFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "street",
                Label = "Street",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            },
            new()
            {
                Name = "city",
                Label = "City",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            }
        };

        var fieldType = new AddressFieldType(innerFields);
        PolicyMemberFieldDefinition field = CreateTestField("testAddressField");

        var value = new Dictionary<string, object?>
        {
            ["street"] = "123 Main St",
            ["city"] = "New York"
        };

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(value);
    }

    [Fact]
    public void AddressFieldType_ValidateField_WithNullValue_ShouldReturnSuccess()
    {
        // Arrange
        var innerFields = new List<PolicyMemberFieldDefinition>();
        var fieldType = new AddressFieldType(innerFields);
        PolicyMemberFieldDefinition field = CreateTestField("testAddressField");
        object? value = null;

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeNull();
    }

    [Fact]
    public void AddressFieldType_ValidateField_WithExtraFields_ShouldReturnError()
    {
        // Arrange
        var innerFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "street",
                Label = "Street",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            },
            new()
            {
                Name = "city",
                Label = "City",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            }
        };

        var fieldType = new AddressFieldType(innerFields);
        PolicyMemberFieldDefinition field = CreateTestField("testAddressField");

        // Address with extra field that's not defined
        var value = new Dictionary<string, object?>
        {
            ["street"] = "123 Main St",
            ["city"] = "New York",
            ["extraField"] = "This should not be allowed" // Extra field
        };

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.NotAllowed);
        result.Errors[0].PropertyPath.Should().Be("extraField");
    }

    #endregion

    #region Helper Methods

    private static PolicyMemberFieldDefinition CreateTestField(string name) => new()
    {
        Name = name,
        Label = $"Test {name} Field",
        Type = new StringFieldType(),
        IsRequired = false,
        IsUnique = false
    };

    #endregion
}
