using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields.FieldTypes;

public class BooleanFieldTypeTests
{
    private readonly IFixture _fixture;
    private readonly BooleanFieldType _fieldType;

    public BooleanFieldTypeTests()
    {
        _fixture = new Fixture();
        _fieldType = new BooleanFieldType();
    }

    #region ValidateField Tests

    [Fact]
    public void ValidateField_WithBooleanTrue_ShouldReturnSuccess()
    {
        // Arrange
        bool fieldValue = true;
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.ValidateField(fieldValue, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(true);
    }

    [Fact]
    public void ValidateField_WithBooleanFalse_ShouldReturnSuccess()
    {
        // Arrange
        bool fieldValue = false;
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.ValidateField(fieldValue, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(false);
    }

    [Fact]
    public void ValidateField_WithNullValue_ShouldReturnSuccess()
    {
        // Arrange
        object? fieldValue = null;
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.ValidateField(fieldValue, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeNull();
    }

    [Theory]
    [InlineData("true")]
    [InlineData("false")]
    [InlineData("True")]
    [InlineData("False")]
    [InlineData("TRUE")]
    [InlineData("FALSE")]
    public void ValidateField_WithValidBooleanString_ShouldReturnSuccess(string booleanString)
    {
        // Arrange
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.ValidateField(booleanString, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Theory]
    [InlineData("invalid")]
    [InlineData("1")]
    [InlineData("0")]
    [InlineData("yes")]
    [InlineData("no")]
    [InlineData("")]
    [InlineData(" ")]
    public void ValidateField_WithInvalidBooleanString_ShouldReturnValidationError(string invalidString)
    {
        // Arrange
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.ValidateField(invalidString, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidType);
        result.Errors[0].PropertyPath.Should().Be(field.Name);
        result.Errors[0].PropertyLabel.Should().Be(field.Label);
    }

    [Fact]
    public void ValidateField_WithIntegerValue_ShouldReturnValidationError()
    {
        // Arrange
        int fieldValue = 42;
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.ValidateField(fieldValue, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidType);
    }

    [Fact]
    public void ValidateField_WithDecimalValue_ShouldReturnValidationError()
    {
        // Arrange
        decimal fieldValue = 42.5m;
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.ValidateField(fieldValue, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidType);
    }

    [Fact]
    public void ValidateField_WithObjectValue_ShouldReturnValidationError()
    {
        // Arrange
        var fieldValue = new { property = "value" };
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.ValidateField(fieldValue, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidType);
    }

    [Fact]
    public void ValidateField_WithArrayValue_ShouldReturnValidationError()
    {
        // Arrange
        string[] fieldValue = ["item1", "item2"];
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.ValidateField(fieldValue, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidType);
    }

    [Fact]
    public void ValidateField_WithOtherFields_ShouldIgnoreOtherFields()
    {
        // Arrange
        bool fieldValue = true;
        PolicyMemberFieldDefinition field = CreateTestField();
        var otherFields = new Dictionary<string, object?>
        {
            ["field1"] = "value1",
            ["field2"] = 123
        };

        // Act
        Result<object?> result = _fieldType.ValidateField(fieldValue, field, otherFields);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(true);
    }

    #endregion

    #region TryParseField Tests

    [Fact]
    public void TryParseField_WithBooleanTrue_ShouldReturnSuccessAndParsedValue()
    {
        // Arrange
        bool fieldValue = true;
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.TryParseField(fieldValue, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(true);
    }

    [Fact]
    public void TryParseField_WithBooleanFalse_ShouldReturnSuccessAndParsedValue()
    {
        // Arrange
        bool fieldValue = false;
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.TryParseField(fieldValue, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(false);
    }

    [Theory]
    [InlineData("true", true)]
    [InlineData("false", false)]
    [InlineData("True", true)]
    [InlineData("False", false)]
    [InlineData("TRUE", true)]
    [InlineData("FALSE", false)]
    public void TryParseField_WithValidBooleanString_ShouldReturnSuccessAndParsedBoolean(string input, bool expected)
    {
        // Arrange
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.TryParseField(input, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(expected);
    }

    [Fact]
    public void TryParseField_WithNullValue_ShouldReturnSuccessAndNullParsedValue()
    {
        // Arrange
        object? fieldValue = null;
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.TryParseField(fieldValue, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeNull();
    }

    [Fact]
    public void TryParseField_WithInvalidString_ShouldReturnValidationError()
    {
        // Arrange
        string fieldValue = "invalid";
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.TryParseField(fieldValue, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidType);
    }

    [Fact]
    public void TryParseField_WithIntegerValue_ShouldReturnValidationError()
    {
        // Arrange
        int fieldValue = 42;
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.TryParseField(fieldValue, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidType);
    }

    [Fact]
    public void TryParseField_WithOtherFields_ShouldIgnoreOtherFields()
    {
        // Arrange
        bool fieldValue = true;
        PolicyMemberFieldDefinition field = CreateTestField();
        var otherFields = new Dictionary<string, object?>
        {
            ["field1"] = "value1",
            ["field2"] = 123
        };

        // Act
        Result<object?> result = _fieldType.TryParseField(fieldValue, field, otherFields);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(true);
    }

    #endregion

    #region Helper Methods

    private static PolicyMemberFieldDefinition CreateTestField() => new()
    {
        Name = "testBooleanField",
        Label = "Test Boolean Field",
        Type = new BooleanFieldType(),
        IsRequired = false,
        IsUnique = false
    };

    #endregion
}
