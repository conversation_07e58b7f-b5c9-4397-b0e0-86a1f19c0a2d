using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields.FieldTypes;

public class StringFieldTypeTests
{
    private readonly IFixture _fixture;

    public StringFieldTypeTests()
    {
        _fixture = new Fixture();
    }

    [Fact]
    public void ValidateField_WithNullValue_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new StringFieldType { Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = fieldType.ValidateField(null, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeNull();
    }

    [Fact]
    public void ValidateField_WithValidStringValue_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new StringFieldType { Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "Valid string value";

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(value);
    }

    [Fact]
    public void ValidateField_WithNonStringValue_ShouldReturnTypeError()
    {
        // Arrange
        var fieldType = new StringFieldType { Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();
        int value = 123; // Non-string value

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidType);
        result.Errors[0].PropertyPath.Should().Be(field.Name);
        result.Errors[0].PropertyLabel.Should().Be(field.Label);
    }

    [Fact]
    public void ValidateField_WithValidOption_ShouldReturnSuccess()
    {
        // Arrange
        var options = new List<StringOption>
        {
            new() { Value = "Option1", Label = "Option 1" },
            new() { Value = "Option2", Label = "Option 2" }
        };
        var fieldType = new StringFieldType { Options = options, Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "Option1";

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(value);
    }

    [Fact]
    public void ValidateField_WithInvalidOption_ShouldReturnOptionError()
    {
        // Arrange
        var options = new List<StringOption>
        {
            new() { Value = "Option1", Label = "Option 1" },
            new() { Value = "Option2", Label = "Option 2" }
        };
        var fieldType = new StringFieldType { Options = options, Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "InvalidOption";

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidOption);
        result.Errors[0].PropertyPath.Should().Be(field.Name);
        result.Errors[0].PropertyLabel.Should().Be(field.Label);
        result.Errors[0].Context["AvailableOptions"].Should().BeEquivalentTo(new[] { "Option1", "Option2" });
    }

    [Fact]
    public void ValidateField_WithEmptyStringAndOptions_ShouldReturnSuccess()
    {
        // Arrange
        var options = new List<StringOption>
        {
            new() { Value = "Option1", Label = "Option 1" }
        };
        var fieldType = new StringFieldType { Options = options, Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "";

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue(); // Empty string should not be validated against options
        result.Value.Should().Be("");
    }

    [Theory]
    [InlineData("123", true)]
    [InlineData("123.45", true)]
    [InlineData("-123", true)]
    [InlineData("-123.45", true)]
    [InlineData("abc", false)]
    [InlineData("123abc", false)]
    [InlineData("", false)]
    public void ValidateField_WithNumberValidation_ShouldValidateCorrectly(string value, bool shouldBeValid)
    {
        // Arrange
        var fieldType = new StringFieldType { Validations = "number" };
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        if (shouldBeValid)
        {
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().Be(value);
        }
        else
        {
            result.IsFailure.Should().BeTrue();
            result.Errors.Should().HaveCount(1);
            result.Errors[0].Code.Should().Be(ErrorCodes.InvalidNumber);
        }
    }

    [Theory]
    [InlineData("regex:^[A-Z]+$", "ABC", true)]
    [InlineData("regex:^[A-Z]+$", "abc", false)]
    [InlineData("/^[0-9]+$/", "123", true)]
    [InlineData("/^[0-9]+$/", "abc", false)]
    public void ValidateField_WithRegexValidation_ShouldValidateCorrectly(string validation, string value, bool shouldBeValid)
    {
        // Arrange
        var fieldType = new StringFieldType { Validations = validation };
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        if (shouldBeValid)
        {
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().Be(value);
        }
        else
        {
            result.IsFailure.Should().BeTrue();
            result.Errors.Should().HaveCount(1);
            result.Errors[0].Code.Should().Be(ErrorCodes.InvalidString);
            // Extract expected pattern from validation
            string expectedPattern = validation.StartsWith("regex:") ? validation[6..] : validation.Trim('/');
            result.Errors[0].Context["Pattern"].Should().Be(expectedPattern);
        }
    }

    [Fact]
    public void ValidateField_WithMultipleValidations_ShouldValidateAll()
    {
        // Arrange
        var fieldType = new StringFieldType { Validations = "number|regex:^[0-9]+$" };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "123";

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue(); // Should pass both number and regex validation
        result.Value.Should().Be(value);
    }

    [Fact]
    public void ValidateField_WithMultipleValidationsFirstFails_ShouldReturnFirstError()
    {
        // Arrange
        var fieldType = new StringFieldType { Validations = "number|regex:^[A-Z]+$" };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "abc"; // Fails number validation

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidNumber);
    }

    [Fact]
    public void TryParseField_ShouldCallValidateField()
    {
        // Arrange
        var fieldType = new StringFieldType { Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "test value";

        // Act
        Result<object?> result = fieldType.TryParseField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(value); // Should return the same value
    }

    [Fact]
    public void StringOption_ShouldHaveCorrectProperties()
    {
        // Arrange & Act
        var option = new StringOption { Value = "TestValue", Label = "Test Label" };

        // Assert
        option.Value.Should().Be("TestValue");
        option.Label.Should().Be("Test Label");
    }

    [Fact]
    public void StringOption_WithNullLabel_ShouldBeValid()
    {
        // Arrange & Act
        var option = new StringOption { Value = "TestValue", Label = null };

        // Assert
        option.Value.Should().Be("TestValue");
        option.Label.Should().BeNull();
    }

    private static PolicyMemberFieldDefinition CreateTestField() => new()
    {
        Name = "testField",
        Label = "Test Field",
        Type = new StringFieldType(),
        IsRequired = false,
        IsUnique = false
    };
}
