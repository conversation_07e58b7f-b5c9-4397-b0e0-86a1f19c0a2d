using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Common.Providers;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Tests.Unit.Common;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields.FieldTypes;

public class DateFieldTypeAdvancedValidationTests
{
    private readonly IFixture _fixture;

    public DateFieldTypeAdvancedValidationTests()
    {
        _fixture = new Fixture();
    }

    [Fact]
    public void TryParseField_WithMinEmployeeAgeValidation_WhenEmployeeTooYoung_ShouldReturnError()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minEmployeeAge:18" };
        PolicyMemberFieldDefinition field = CreateTestField();

        // 16 years old (too young) - using fixed test date for deterministic behavior
        DateOnly birthDate = TestDateConstants.BirthDates.Age16;
        var otherFields = new Dictionary<string, object?>
        {
            { "memberType", "employee" }
        };

        // Act
        Result<object?> result = fieldType.TryParseField(birthDate, field, otherFields);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.EmployeeMinAge);
        result.Errors[0].PropertyPath.Should().Be(field.Name);
        result.Errors[0].PropertyLabel.Should().Be(field.Label);
        result.Errors[0].Context["MinAge"].Should().Be(18);
        result.Errors[0].Context["ActualAge"].Should().BeOfType<int>();
        ((int)result.Errors[0].Context["ActualAge"]!).Should().BeLessThan(18);
    }

    [Fact]
    public void TryParseField_WithMinEmployeeAgeValidation_WhenEmployeeOldEnough_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minEmployeeAge:18" };
        PolicyMemberFieldDefinition field = CreateTestField();

        // 25 years old (old enough) - using fixed test date for deterministic behavior
        DateOnly birthDate = TestDateConstants.BirthDates.Age25;
        var otherFields = new Dictionary<string, object?>
        {
            { "memberType", "employee" }
        };

        // Act
        Result<object?> result = fieldType.TryParseField(birthDate, field, otherFields);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(birthDate);
    }

    [Fact]
    public void TryParseField_WithMaxEmployeeAgeValidation_WhenEmployeeTooOld_ShouldReturnError()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "maxEmployeeAge:65" };
        PolicyMemberFieldDefinition field = CreateTestField();

        // 70 years old (too old) - using fixed test date for deterministic behavior
        DateOnly birthDate = TestDateConstants.BirthDates.Age70;
        var otherFields = new Dictionary<string, object?>
        {
            { "memberType", "employee" }
        };

        // Act
        Result<object?> result = fieldType.TryParseField(birthDate, field, otherFields);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.EmployeeMaxAge);
        result.Errors[0].PropertyPath.Should().Be(field.Name);
        result.Errors[0].PropertyLabel.Should().Be(field.Label);
        result.Errors[0].Context["MaxAge"].Should().Be(65);
        result.Errors[0].Context["ActualAge"].Should().BeOfType<int>();
        ((int)result.Errors[0].Context["ActualAge"]!).Should().BeGreaterThan(65);
    }

    [Fact]
    public void TryParseField_WithMinChildDaysValidation_WhenChildTooYoung_ShouldReturnError()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minChildDays:30" };
        PolicyMemberFieldDefinition field = CreateTestField();

        // 15 days old (too young)
        var birthDate = DateOnly.FromDateTime(DateTimeProvider.UtcNow.AddDays(-15));
        var otherFields = new Dictionary<string, object?>
        {
            { "relationshipToEmployee", "child" }
        };

        // Act
        Result<object?> result = fieldType.TryParseField(birthDate, field, otherFields);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.ChildMinDays);
        result.Errors[0].PropertyPath.Should().Be(field.Name);
        result.Errors[0].PropertyLabel.Should().Be(field.Label);
        result.Errors[0].Context["MinDays"].Should().Be(30);
        result.Errors[0].Context["ActualDays"].Should().BeOfType<int>();
        ((int)result.Errors[0].Context["ActualDays"]!).Should().BeLessThan(30);
    }

    [Fact]
    public void TryParseField_WithMinSpouseAgeValidation_WhenSpouseTooYoung_ShouldReturnError()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minSpouseAge:18" };
        PolicyMemberFieldDefinition field = CreateTestField();

        // 16 years old (too young)
        var birthDate = DateOnly.FromDateTime(DateTimeProvider.UtcNow.AddYears(-16));
        var otherFields = new Dictionary<string, object?>
        {
            { "relationshipToEmployee", "spouse" }
        };

        // Act
        Result<object?> result = fieldType.TryParseField(birthDate, field, otherFields);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.SpouseMinAge);
        result.Errors[0].PropertyPath.Should().Be(field.Name);
        result.Errors[0].PropertyLabel.Should().Be(field.Label);
        result.Errors[0].Context["MinAge"].Should().Be(18);
        result.Errors[0].Context["ActualAge"].Should().BeOfType<int>();
        ((int)result.Errors[0].Context["ActualAge"]!).Should().BeLessThan(18);
    }

    [Fact]
    public void TryParseField_WithEmployeeValidation_WhenNotEmployee_ShouldNotApplyValidation()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minEmployeeAge:18" };
        PolicyMemberFieldDefinition field = CreateTestField();

        // 16 years old but not an employee
        var birthDate = DateOnly.FromDateTime(DateTimeProvider.UtcNow.AddYears(-16));
        var otherFields = new Dictionary<string, object?>
        {
            { "memberType", "dependent" } // Not an employee
        };

        // Act
        Result<object?> result = fieldType.TryParseField(birthDate, field, otherFields);

        // Assert
        result.IsSuccess.Should().BeTrue(); // Should not apply employee validation to non-employees
        result.Value.Should().Be(birthDate);
    }

    [Fact]
    public void TryParseField_WithMultipleValidations_ShouldApplyAllApplicable()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minEmployeeAge:18|maxEmployeeAge:65" };
        PolicyMemberFieldDefinition field = CreateTestField();

        // 16 years old (violates min age) - using fixed test date for deterministic behavior
        DateOnly birthDate = TestDateConstants.BirthDates.Age16;
        var otherFields = new Dictionary<string, object?>
        {
            { "memberType", "employee" }
        };

        // Act
        Result<object?> result = fieldType.TryParseField(birthDate, field, otherFields);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1); // Should return first validation error
        result.Errors[0].Code.Should().Be(ErrorCodes.EmployeeMinAge);
    }

    [Fact]
    public void TryParseField_WithNoOtherFields_ShouldNotApplyContextValidation()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minEmployeeAge:18" };
        PolicyMemberFieldDefinition field = CreateTestField();

        // 16 years old - using fixed test date for deterministic behavior
        DateOnly birthDate = TestDateConstants.BirthDates.Age16;

        // Act
        Result<object?> result = fieldType.TryParseField(birthDate, field);

        // Assert
        result.IsSuccess.Should().BeTrue(); // Should not apply validation without context
        result.Value.Should().Be(birthDate);
    }

    private static PolicyMemberFieldDefinition CreateTestField() => new()
    {
        Name = "testDateField",
        Label = "Test Date Field",
        Type = new DateFieldType(),
        IsRequired = false,
        IsUnique = false
    };
}
