using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields.FieldTypes;

/// <summary>
/// Unit tests for StringFieldType validation using the new validation system.
/// </summary>
public class StringFieldTypeValidationTests
{
    private readonly PolicyMemberFieldDefinition _testField;

    public StringFieldTypeValidationTests()
    {
        _testField = new PolicyMemberFieldDefinition
        {
            Name = "email",
            Label = "Email Address",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };
    }

    [Fact]
    public void TryParseField_WithValidString_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new StringFieldType();
        const string value = "<EMAIL>";

        // Act
        Result<object?> result = fieldType.TryParseField(value, _testField);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(value);
    }

    [Fact]
    public void TryParseField_WithNull_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new StringFieldType();

        // Act
        Result<object?> result = fieldType.TryParseField(null, _testField);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeNull();
    }

    [Fact]
    public void TryParseField_WithEmptyString_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new StringFieldType();

        // Act
        Result<object?> result = fieldType.TryParseField("", _testField);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be("");
    }

    [Fact]
    public void TryParseField_WithNonStringValue_ShouldConvertToString()
    {
        // Arrange
        var fieldType = new StringFieldType();
        const int value = 123;

        // Act
        Result<object?> result = fieldType.TryParseField(value, _testField);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be("123");
    }

    [Fact]
    public void ValidateField_WithValidOption_ShouldReturnSuccess()
    {
        // Arrange
        string[] options = ["active", "inactive", "pending"];
        var fieldType = new StringFieldType { Options = [.. options.Select(o => new StringOption { Value = o })] };
        const string value = "active";

        // Act
        Result<object?> result = fieldType.ValidateField(value, _testField);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(value);
    }

    [Fact]
    public void ValidateField_WithInvalidOption_ShouldReturnFailure()
    {
        // Arrange
        string[] options = ["active", "inactive", "pending"];
        var fieldType = new StringFieldType { Options = [.. options.Select(o => new StringOption { Value = o })] };
        const string value = "invalid";

        // Act
        Result<object?> result = fieldType.ValidateField(value, _testField);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().ContainSingle();
        ValidationError error = result.Errors.First();
        error.Code.Should().Be(ErrorCodes.InvalidOption);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["AvailableOptions"].Should().BeEquivalentTo(options);
    }

    [Fact]
    public void ValidateField_WithValidRegexPattern_ShouldReturnSuccess()
    {
        // Arrange
        const string pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
        var fieldType = new StringFieldType { Validations = $"regex:{pattern}" };
        const string value = "<EMAIL>";

        // Act
        Result<object?> result = fieldType.ValidateField(value, _testField);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(value);
    }

    [Fact]
    public void ValidateField_WithInvalidRegexPattern_ShouldReturnFailure()
    {
        // Arrange
        const string pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
        var fieldType = new StringFieldType { Validations = $"regex:{pattern}" };
        const string value = "invalid-email";

        // Act
        Result<object?> result = fieldType.ValidateField(value, _testField);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().ContainSingle();
        ValidationError error = result.Errors.First();
        error.Code.Should().Be(ErrorCodes.InvalidString);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["Pattern"].Should().Be(pattern);
    }

    [Fact]
    public void ValidateField_WithNullAndNoValidation_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new StringFieldType();

        // Act
        Result<object?> result = fieldType.ValidateField(null, _testField);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeNull();
    }

    [Fact]
    public void ValidateField_WithEmptyStringAndOptions_ShouldReturnSuccess()
    {
        // Arrange
        string[] options = ["active", "inactive", ""];
        var fieldType = new StringFieldType { Options = [.. options.Select(o => new StringOption { Value = o })] };

        // Act
        Result<object?> result = fieldType.ValidateField("", _testField);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be("");
    }

    [Fact]
    public void ValidateField_WithBothOptionsAndRegex_ShouldValidateBoth()
    {
        // Arrange
        string[] options = ["<EMAIL>", "<EMAIL>"];
        const string pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
        var fieldType = new StringFieldType
        {
            Options = [.. options.Select(o => new StringOption { Value = o })],
            Validations = $"regex:{pattern}"
        };
        const string value = "<EMAIL>";

        // Act
        Result<object?> result = fieldType.ValidateField(value, _testField);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(value);
    }

    [Fact]
    public void ValidateField_WithValidRegexButInvalidOption_ShouldReturnFailure()
    {
        // Arrange
        string[] options = ["<EMAIL>", "<EMAIL>"];
        const string pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
        var fieldType = new StringFieldType
        {
            Options = [.. options.Select(o => new StringOption { Value = o })],
            Validations = $"regex:{pattern}"
        };
        const string value = "<EMAIL>"; // Valid email but not in options

        // Act
        Result<object?> result = fieldType.ValidateField(value, _testField);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().ContainSingle();
        ValidationError error = result.Errors.First();
        error.Code.Should().Be(ErrorCodes.InvalidOption);
    }

    [Fact]
    public void ValidateField_WithValidOptionButInvalidRegex_ShouldReturnFailure()
    {
        // Arrange
        string[] options = ["invalid-email", "another-invalid"];
        const string pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
        var fieldType = new StringFieldType
        {
            Options = [.. options.Select(o => new StringOption { Value = o })],
            Validations = $"regex:{pattern}"
        };
        const string value = "invalid-email"; // In options but doesn't match regex

        // Act
        Result<object?> result = fieldType.ValidateField(value, _testField);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().ContainSingle();
        ValidationError error = result.Errors.First();
        error.Code.Should().Be(ErrorCodes.InvalidString);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("\t")]
    [InlineData("\n")]
    public void ValidateField_WithWhitespaceAndRegex_ShouldSkipRegexValidation(string whitespaceValue)
    {
        // Arrange
        const string pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
        var fieldType = new StringFieldType { Validations = $"regex:{pattern}" };

        // Act
        Result<object?> result = fieldType.ValidateField(whitespaceValue, _testField);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(whitespaceValue);
    }

    [Fact]
    public void ValidateField_WithCaseSensitiveOptions_ShouldRespectCase()
    {
        // Arrange
        string[] options = ["Active", "Inactive", "Pending"];
        var fieldType = new StringFieldType { Options = [.. options.Select(o => new StringOption { Value = o })] };
        const string value = "active"; // Different case

        // Act
        Result<object?> result = fieldType.ValidateField(value, _testField);

        // Assert
        result.IsSuccess.Should().BeTrue(); // StringFieldType uses case-insensitive comparison
        result.Value.Should().Be(value);
    }
}