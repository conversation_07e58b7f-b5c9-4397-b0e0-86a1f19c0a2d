using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Values;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields.FieldTypes;

[Trait("Category", "Unit")]
[Trait("Ticket", "CH-1644")]
[Trait("Ticket", "CH-1646")]
public class NumberFieldTypeTests
{
    private readonly NumberFieldType _fieldType;

    public NumberFieldTypeTests()
    {
        _fieldType = new NumberFieldType();
    }

    #region ValidateField Tests

    public static IEnumerable<object?[]> ValidNumberOptionsData =>
    [
        ["123"],
        [123],
        [123L],
        [123.0f],
        [123.0d],
        [123M],
        [123u],
        [123uL],
        [null],
    ];

    [Theory]
    [MemberData(nameof(ValidNumberOptionsData))]
    public void ValidateField_WithValidNumberOptions_ShouldReturnSuccess(object? value)
    {
        // Arrange
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Theory]
    [InlineData(true)]
    [InlineData("invalid")]
    [InlineData("")]
    [InlineData("abc123")]
    public void ValidateField_WithInvalidNumberOptions_ShouldReturnValidationError(object? value)
    {
        // Arrange
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.ValidateField(value, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().ContainSingle(it => it.PropertyPath == field.Name);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidNumber);
    }

    [Fact]
    public void ValidateField_WithNullValue_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.ValidateField(null, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeNull();
    }

    [Theory]
    [InlineData(42)]
    [InlineData(42L)]
    [InlineData(42.5f)]
    [InlineData(42.5d)]
    [InlineData("42")]
    [InlineData("42.5")]
    public void ValidateField_WithValidNumbers_ShouldReturnSuccess(object value)
    {
        // Arrange
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.ValidateField(value, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Fact]
    public void ValidateField_WithoutOptions_ShouldValidateBasicNumberTypes()
    {
        // Arrange
        var fieldType = new NumberFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act & Assert
        fieldType.ValidateField(10, field).IsSuccess.Should().BeTrue();
        fieldType.ValidateField(10L, field).IsSuccess.Should().BeTrue();
        fieldType.ValidateField(10.5f, field).IsSuccess.Should().BeTrue();
        fieldType.ValidateField(10.5d, field).IsSuccess.Should().BeTrue();
        fieldType.ValidateField(10.5m, field).IsSuccess.Should().BeTrue();
        fieldType.ValidateField("10", field).IsSuccess.Should().BeTrue();
        fieldType.ValidateField("10.5", field).IsSuccess.Should().BeTrue();
    }

    [Fact]
    public void ValidateField_WithOptions_AndValidIntegerOption_ShouldReturnSuccess()
    {
        // Arrange
        var options = new List<NumberOption>
        {
            new() { Value = NumberValue.Create(10), Label = "Ten" },
            new() { Value = NumberValue.Create(20), Label = "Twenty" }
        };
        var fieldType = new NumberFieldType { Options = options };
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = fieldType.ValidateField(10, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Fact]
    public void ValidateField_WithOptions_AndValidLongOption_ShouldReturnSuccess()
    {
        // Arrange
        var options = new List<NumberOption>
        {
            new() { Value = 10L, Label = "Ten" },
            new() { Value = 20L, Label = "Twenty" }
        };
        var fieldType = new NumberFieldType { Options = options };
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = fieldType.ValidateField("10", field);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Fact]
    public void ValidateField_WithOptions_AndInvalidOption_ShouldReturnValidationError()
    {
        // Arrange
        var options = new List<NumberOption>
        {
            new() { Value = 10, Label = "Ten" },
            new() { Value = 20, Label = "Twenty" }
        };
        var fieldType = new NumberFieldType { Options = options };
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = fieldType.ValidateField(30, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidOption);
    }

    [Fact]
    public void ValidateField_WithOptions_AndTypeMismatch_ShouldReturnValidationError()
    {
        // Arrange
        var options = new List<NumberOption>
        {
            new() { Value = 10, Label = "Ten" },
            new() { Value = 20, Label = "Twenty" }
        };
        var fieldType = new NumberFieldType { Options = options };
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = fieldType.ValidateField("10", field);

        // Assert
        result.IsSuccess.Should().BeTrue(); // String "10" should be parsed and match option 10
    }

    #endregion

    #region TryParseField Tests

    [Fact]
    public void TryParseField_WithIntegerValue_ShouldReturnParsedValue()
    {
        // Arrange
        int fieldValue = 42;
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.TryParseField(fieldValue, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(42);
    }

    [Fact]
    public void TryParseField_WithStringNumber_ShouldReturnParsedValue()
    {
        // Arrange
        string fieldValue = "42.5";
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.TryParseField(fieldValue, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(42.5m);
    }

    [Fact]
    public void TryParseField_WithNullValue_ShouldReturnNull()
    {
        // Arrange
        object? fieldValue = null;
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.TryParseField(fieldValue, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeNull();
    }

    [Fact]
    public void TryParseField_WithInvalidString_ShouldReturnValidationError()
    {
        // Arrange
        string fieldValue = "invalid";
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.TryParseField(fieldValue, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidNumber);
    }

    [Fact]
    public void TryParseField_WithBooleanValue_ShouldReturnValidationError()
    {
        // Arrange
        bool fieldValue = true;
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = _fieldType.TryParseField(fieldValue, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.InvalidNumber);
    }

    #endregion

    #region NumberValue Tests

    [Fact]
    public void NumberValue_Create_WithInteger_ShouldReturnCorrectValue()
    {
        // Act
        var result = NumberValue.Create(42);

        // Assert
        result.Value.Should().Be(42);
    }

    [Fact]
    public void NumberValue_Create_WithLong_ShouldReturnCorrectValue()
    {
        // Act
        var result = NumberValue.Create(42L);

        // Assert
        result.Value.Should().Be(42L);
    }

    [Fact]
    public void NumberValue_Create_WithFloat_ShouldReturnCorrectValue()
    {
        // Act
        var result = NumberValue.Create(42.5f);

        // Assert
        result.Value.Should().Be(42.5f);
    }

    [Fact]
    public void NumberValue_Create_WithDouble_ShouldReturnCorrectValue()
    {
        // Act
        var result = NumberValue.Create(42.5d);

        // Assert
        result.Value.Should().Be(42.5d);
    }

    [Fact]
    public void NumberValue_Create_WithStringInteger_ShouldReturnLong()
    {
        // Act
        var result = NumberValue.Create("42");

        // Assert
        result.Value.Should().Be(42L);
    }

    [Fact]
    public void NumberValue_Create_WithStringDecimal_ShouldReturnDecimal()
    {
        // Act
        var result = NumberValue.Create("42.5");

        // Assert
        result.Value.Should().Be(42.5m);
    }

    [Fact]
    public void NumberValue_CreateNullable_WithNull_ShouldReturnNull()
    {
        // Act
        NumberValue? result = NumberValue.CreateNullable(null!);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void NumberValue_CreateNullable_WithValidValue_ShouldReturnNumberValue()
    {
        // Act
        NumberValue? result = NumberValue.CreateNullable(42);

        // Assert
        result.Should().NotBeNull();
        result!.ToString().Should().Be("42");
    }

    [Fact]
    public void NumberValue_CreateNullable_WithInvalidString_ShouldReturnNull()
    {
        // Act
        NumberValue? result = NumberValue.CreateNullable("invalid");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void NumberValue_CreateNullable_WithBoolean_ShouldReturnNull()
    {
        // Act
        NumberValue? result = NumberValue.CreateNullable(true);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void NumberValue_CreateNullable_WithObject_ShouldReturnNull()
    {
        // Act
        NumberValue? result = NumberValue.CreateNullable(new object());

        // Assert
        result.Should().BeNull();
    }

    #endregion

    #region Helper Methods

    private static PolicyMemberFieldDefinition CreateTestField() => new()
    {
        Name = "numberField",
        Label = "Test Number Field",
        Type = new NumberFieldType(),
        IsRequired = false,
        IsUnique = false
    };

    #endregion
}
