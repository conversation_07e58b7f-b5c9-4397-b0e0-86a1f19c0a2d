using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields.FieldTypes;

[Trait("Category", "Unit")]
public class FormulaFieldTypeTests
{
    private readonly IFixture _fixture;

    public FormulaFieldTypeTests()
    {
        _fixture = new Fixture();
    }

    #region ValidateField Tests

    [Fact]
    public void ValidateField_WithNullValue_ShouldReturnSuccess()
    {
        // Arrange
        var fieldType = new FormulaFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = fieldType.ValidateField(null, field);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeNull();
    }

    [Fact]
    public void ValidateField_WithNonNullValue_ShouldReturnValidationError()
    {
        // Arrange
        var fieldType = new FormulaFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "some value";

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.NotAllowed);
        result.Errors[0].PropertyPath.Should().Be(field.Name);
    }

    [Theory]
    [InlineData("test")]
    [InlineData(123)]
    [InlineData(true)]
    [InlineData(42.5)]
    public void ValidateField_WithAnyNonNullValue_ShouldReturnValidationError(object value)
    {
        // Arrange
        var fieldType = new FormulaFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        Result<object?> result = fieldType.ValidateField(value, field);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.NotAllowed);
    }

    #endregion

    #region Formula Calculation Tests

    [Fact]
    public void ShouldCalculateTheJoinFormulaValue()
    {
        // Arrange
        var customFields = new Dictionary<string, object?>
        {
            ["givenName"] = "John",
            ["surname"] = "Doe"
        };

        var joinFormula = new JoinFormula
        {
            Inputs =
            [
                new FormulaData { Path = "givenName" },
                new FormulaData { Path = "surname" }
            ],
            Separator = new FormulaValue { Value = " " }
        };

        var fieldType = new FormulaFieldType { Func = joinFormula };

        // Act
        object? result = fieldType.Func!.Calculate(customFields);

        // Assert
        result.Should().Be("John Doe");
    }

    [Fact]
    public void JoinFormula_WithMissingFields_ShouldIgnoreMissingValues()
    {
        // Arrange
        var customFields = new Dictionary<string, object?>
        {
            ["givenName"] = "John"
            // surname is missing
        };

        var joinFormula = new JoinFormula
        {
            Inputs =
            [
                new FormulaData { Path = "givenName" },
                new FormulaData { Path = "surname" }
            ],
            Separator = new FormulaValue { Value = " " }
        };

        // Act
        object? result = joinFormula.Calculate(customFields);

        // Assert
        result.Should().Be("John");
    }

    [Fact]
    public void JoinFormula_WithEmptyFields_ShouldReturnEmptyString()
    {
        // Arrange
        var customFields = new Dictionary<string, object?>();

        var joinFormula = new JoinFormula
        {
            Inputs =
            [
                new FormulaData { Path = "givenName" },
                new FormulaData { Path = "surname" }
            ],
            Separator = new FormulaValue { Value = " " }
        };

        // Act
        object? result = joinFormula.Calculate(customFields);

        // Assert
        result.Should().Be("");
    }

    [Fact]
    public void JoinFormula_WithCustomSeparator_ShouldUseCustomSeparator()
    {
        // Arrange
        var customFields = new Dictionary<string, object?>
        {
            ["givenName"] = "John",
            ["surname"] = "Doe"
        };

        var joinFormula = new JoinFormula
        {
            Inputs =
            [
                new FormulaData { Path = "givenName" },
                new FormulaData { Path = "surname" }
            ],
            Separator = new FormulaValue { Value = ", " }
        };

        // Act
        object? result = joinFormula.Calculate(customFields);

        // Assert
        result.Should().Be("John, Doe");
    }

    [Fact]
    public void JoinFormula_WithValueParameters_ShouldIncludeStaticValues()
    {
        // Arrange
        var customFields = new Dictionary<string, object?>
        {
            ["givenName"] = "John"
        };

        var joinFormula = new JoinFormula
        {
            Inputs =
            [
                new FormulaData { Path = "givenName" },
                new FormulaValue { Value = "Static" },
                new FormulaValue { Value = "Value" }
            ],
            Separator = new FormulaValue { Value = " " }
        };

        // Act
        object? result = joinFormula.Calculate(customFields);

        // Assert
        result.Should().Be("John Static Value");
    }

    [Fact]
    public void JoinFormula_WithNullAndEmptyValues_ShouldFilterThem()
    {
        // Arrange
        var customFields = new Dictionary<string, object?>
        {
            ["givenName"] = "John",
            ["middleName"] = null,
            ["surname"] = "Doe",
            ["emptyField"] = ""
        };

        var joinFormula = new JoinFormula
        {
            Inputs =
            [
                new FormulaData { Path = "givenName" },
                new FormulaData { Path = "middleName" },
                new FormulaData { Path = "surname" },
                new FormulaData { Path = "emptyField" }
            ],
            Separator = new FormulaValue { Value = " " }
        };

        // Act
        object? result = joinFormula.Calculate(customFields);

        // Assert
        result.Should().Be("John Doe");
    }

    #endregion

    #region FormulaData Tests

    [Fact]
    public void FormulaData_Get_WithExistingPath_ShouldReturnValue()
    {
        // Arrange
        var fields = new Dictionary<string, object?> { ["testPath"] = "testValue" };
        var formulaData = new FormulaData { Path = "testPath" };

        // Act
        object? result = formulaData.Get(fields);

        // Assert
        result.Should().Be("testValue");
    }

    [Fact]
    public void FormulaData_Get_WithNonExistingPath_ShouldReturnNull()
    {
        // Arrange
        var fields = new Dictionary<string, object?> { ["otherPath"] = "testValue" };
        var formulaData = new FormulaData { Path = "testPath" };

        // Act
        object? result = formulaData.Get(fields);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void FormulaData_Get_WithNullFields_ShouldReturnNull()
    {
        // Arrange
        var formulaData = new FormulaData { Path = "testPath" };

        // Act
        object? result = formulaData.Get(null);

        // Assert
        result.Should().BeNull();
    }

    #endregion

    #region Helper Methods

    private static PolicyMemberFieldDefinition CreateTestField() => new()
    {
        Name = "fullName",
        Label = "Full Name",
        Type = new FormulaFieldType(),
        IsRequired = false,
        IsUnique = false
    };

    #endregion
}
