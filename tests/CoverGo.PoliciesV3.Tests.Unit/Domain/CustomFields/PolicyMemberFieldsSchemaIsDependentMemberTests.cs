using System.Reflection;
using CoverGo.PoliciesV3.Domain.CustomFields;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields;

/// <summary>
/// Unit tests for the IsDependentMember method in PolicyMemberFieldsSchema.
/// Tests the refactored implementation using TryGetValueOrDefault consistently.
/// </summary>
public class PolicyMemberFieldsSchemaIsDependentMemberTests
{
    /// <summary>
    /// Helper method to invoke the private IsDependentMember method using reflection
    /// </summary>
    private static bool InvokeIsDependentMember(IDictionary<string, object?>? otherFields)
    {
        MethodInfo? method = typeof(PolicyMemberFieldsSchema)
            .GetMethod("IsDependentMember", BindingFlags.NonPublic | BindingFlags.Static);

        method.Should().NotBeNull("IsDependentMember method should exist");

        object? result = method!.Invoke(null, [otherFields]);
        return (bool)result!;
    }

    [Fact]
    public void IsDependentMember_WithNullOtherFields_ShouldReturnFalse()
    {
        // Act
        bool result = InvokeIsDependentMember(null);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsDependentMember_WithEmptyOtherFields_ShouldReturnFalse()
    {
        // Arrange
        var otherFields = new Dictionary<string, object?>();

        // Act
        bool result = InvokeIsDependentMember(otherFields);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsDependentMember_WithRelationshipToEmployeeNotNull_ShouldReturnTrue()
    {
        // Arrange
        var otherFields = new Dictionary<string, object?>
        {
            ["relationshipToEmployee"] = "spouse"
        };

        // Act
        bool result = InvokeIsDependentMember(otherFields);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsDependentMember_WithRelationshipToEmployeeNull_ShouldReturnFalse()
    {
        // Arrange
        var otherFields = new Dictionary<string, object?>
        {
            ["relationshipToEmployee"] = null
        };

        // Act
        bool result = InvokeIsDependentMember(otherFields);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsDependentMember_WithMemberTypeDependent_ShouldReturnTrue()
    {
        // Arrange
        var otherFields = new Dictionary<string, object?>
        {
            ["memberType"] = "dependent"
        };

        // Act
        bool result = InvokeIsDependentMember(otherFields);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsDependentMember_WithMemberTypeEmployee_ShouldReturnFalse()
    {
        // Arrange
        var otherFields = new Dictionary<string, object?>
        {
            ["memberType"] = "employee"
        };

        // Act
        bool result = InvokeIsDependentMember(otherFields);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsDependentMember_WithMemberTypeNull_ShouldReturnFalse()
    {
        // Arrange
        var otherFields = new Dictionary<string, object?>
        {
            ["memberType"] = null
        };

        // Act
        bool result = InvokeIsDependentMember(otherFields);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsDependentMember_WithBothRelationshipAndMemberType_ShouldReturnTrue()
    {
        // Arrange
        var otherFields = new Dictionary<string, object?>
        {
            ["relationshipToEmployee"] = "child",
            ["memberType"] = "dependent"
        };

        // Act
        bool result = InvokeIsDependentMember(otherFields);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsDependentMember_WithRelationshipNullAndMemberTypeDependent_ShouldReturnTrue()
    {
        // Arrange
        var otherFields = new Dictionary<string, object?>
        {
            ["relationshipToEmployee"] = null,
            ["memberType"] = "dependent"
        };

        // Act
        bool result = InvokeIsDependentMember(otherFields);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsDependentMember_WithRelationshipNotNullAndMemberTypeEmployee_ShouldReturnTrue()
    {
        // Arrange
        var otherFields = new Dictionary<string, object?>
        {
            ["relationshipToEmployee"] = "spouse",
            ["memberType"] = "employee"
        };

        // Act
        bool result = InvokeIsDependentMember(otherFields);

        // Assert
        result.Should().BeTrue(); // Should return true because relationshipToEmployee is not null
    }

    [Fact]
    public void IsDependentMember_WithNeitherFieldPresent_ShouldReturnFalse()
    {
        // Arrange
        var otherFields = new Dictionary<string, object?>
        {
            ["someOtherField"] = "value"
        };

        // Act
        bool result = InvokeIsDependentMember(otherFields);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsDependentMember_WithMemberTypeAsInteger_ShouldConvertToString()
    {
        // Arrange - Testing ToString() behavior
        var otherFields = new Dictionary<string, object?>
        {
            ["memberType"] = 123 // Non-string object
        };

        // Act
        bool result = InvokeIsDependentMember(otherFields);

        // Assert
        result.Should().BeFalse(); // 123.ToString() != "dependent"
    }

    [Fact]
    public void IsDependentMember_WithMemberTypeAsObjectWithDependentToString_ShouldReturnTrue()
    {
        // Arrange - Testing ToString() behavior with custom object
        var customObject = new TestObject("dependent");
        var otherFields = new Dictionary<string, object?>
        {
            ["memberType"] = customObject
        };

        // Act
        bool result = InvokeIsDependentMember(otherFields);

        // Assert
        result.Should().BeTrue();
    }

    /// <summary>
    /// Test helper class to verify ToString() behavior
    /// </summary>
    private class TestObject(string value)
    {
        private readonly string _value = value;

        public override string ToString() => _value;
    }
}