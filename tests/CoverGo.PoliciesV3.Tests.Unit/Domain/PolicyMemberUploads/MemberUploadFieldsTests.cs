using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.PolicyMemberUploads;

/// <summary>
/// Unit tests for MemberUploadFields to ensure proper member type detection.
/// </summary>
public class MemberUploadFieldsTests
{
    [Fact]
    public void IsDependent_WithDependentMemberType_ShouldReturnTrue()
    {
        // Arrange
        var fields = new Dictionary<string, string?>
        {
            ["memberType"] = "dependent"
        };
        var memberUploadFields = new MemberUploadFields(fields);

        // Act
        bool result = memberUploadFields.IsDependent();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsDependent_WithDependentMemberTypeCaseInsensitive_ShouldReturnTrue()
    {
        // Arrange
        var fields = new Dictionary<string, string?>
        {
            ["memberType"] = "DEPENDENT"
        };
        var memberUploadFields = new MemberUploadFields(fields);

        // Act
        bool result = memberUploadFields.IsDependent();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsDependent_WithDependentMemberTypeMixedCase_ShouldReturnTrue()
    {
        // Arrange
        var fields = new Dictionary<string, string?>
        {
            ["memberType"] = "Dependent"
        };
        var memberUploadFields = new MemberUploadFields(fields);

        // Act
        bool result = memberUploadFields.IsDependent();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsDependent_WithEmployeeMemberType_ShouldReturnFalse()
    {
        // Arrange
        var fields = new Dictionary<string, string?>
        {
            ["memberType"] = "employee"
        };
        var memberUploadFields = new MemberUploadFields(fields);

        // Act
        bool result = memberUploadFields.IsDependent();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsDependent_WithNullMemberType_ShouldReturnFalse()
    {
        // Arrange
        var fields = new Dictionary<string, string?>
        {
            ["memberType"] = null
        };
        var memberUploadFields = new MemberUploadFields(fields);

        // Act
        bool result = memberUploadFields.IsDependent();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsDependent_WithoutMemberTypeKey_ShouldReturnFalse()
    {
        // Arrange
        var fields = new Dictionary<string, string?>
        {
            ["otherField"] = "value"
        };
        var memberUploadFields = new MemberUploadFields(fields);

        // Act
        bool result = memberUploadFields.IsDependent();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsDependent_WithEmptyMemberType_ShouldReturnFalse()
    {
        // Arrange
        var fields = new Dictionary<string, string?>
        {
            ["memberType"] = ""
        };
        var memberUploadFields = new MemberUploadFields(fields);

        // Act
        bool result = memberUploadFields.IsDependent();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsDependent_WithWhitespaceMemberType_ShouldReturnFalse()
    {
        // Arrange
        var fields = new Dictionary<string, string?>
        {
            ["memberType"] = "   "
        };
        var memberUploadFields = new MemberUploadFields(fields);

        // Act
        bool result = memberUploadFields.IsDependent();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsDependent_WithOtherMemberType_ShouldReturnFalse()
    {
        // Arrange
        var fields = new Dictionary<string, string?>
        {
            ["memberType"] = "spouse"
        };
        var memberUploadFields = new MemberUploadFields(fields);

        // Act
        bool result = memberUploadFields.IsDependent();

        // Assert
        result.Should().BeFalse();
    }
}
