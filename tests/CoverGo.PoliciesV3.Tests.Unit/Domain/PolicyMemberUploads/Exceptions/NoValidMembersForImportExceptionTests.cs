using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.PolicyMemberUploads.Exceptions;

public class NoValidMembersForImportExceptionTests
{
    [Fact]
    public void Constructor_WithUploadId_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        string uploadId = Guid.NewGuid().ToString();

        // Act
        var exception = new NoValidMembersForImportException(uploadId);

        // Assert
        exception.UploadId.Should().Be(uploadId);
        exception.Code.Should().Be(ErrorCodes.NoValidMembersForImport);
        exception.Message.Should().Be($"Cannot import upload {uploadId} when there are no valid members");
    }

    [Fact]
    public void Constructor_WithUploadIdAndInnerException_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        string uploadId = Guid.NewGuid().ToString();
        var innerException = new InvalidOperationException("Inner exception message");

        // Act
        var exception = new NoValidMembersForImportException(uploadId, innerException);

        // Assert
        exception.UploadId.Should().Be(uploadId);
        exception.Code.Should().Be(ErrorCodes.NoValidMembersForImport);
        exception.Message.Should().Be($"Cannot import upload {uploadId} when there are no valid members");
        exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void Code_ShouldReturnCorrectErrorCode()
    {
        // Arrange
        string uploadId = Guid.NewGuid().ToString();
        var exception = new NoValidMembersForImportException(uploadId);

        // Act & Assert
        exception.Code.Should().Be(ErrorCodes.NoValidMembersForImport);
        exception.Code.Should().Be("NO_VALID_MEMBERS_FOR_IMPORT");
    }

    [Fact]
    public void Exception_ShouldInheritFromDomainException()
    {
        // Arrange
        string uploadId = Guid.NewGuid().ToString();

        // Act
        var exception = new NoValidMembersForImportException(uploadId);

        // Assert
        exception.Should().BeAssignableTo<DomainException>();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("upload-123")]
    [InlineData("550e8400-e29b-41d4-a716-************")]
    public void Constructor_WithVariousUploadIds_ShouldHandleCorrectly(string uploadId)
    {
        // Act
        var exception = new NoValidMembersForImportException(uploadId);

        // Assert
        exception.UploadId.Should().Be(uploadId);

        // Only assert message containment for non-empty strings (FluentAssertions doesn't allow empty string containment)
        if (!string.IsNullOrEmpty(uploadId))
        {
            exception.Message.Should().Contain(uploadId);
        }
    }

    [Fact]
    public void Exception_ShouldBeSerializable()
    {
        // Arrange
        string uploadId = Guid.NewGuid().ToString();
        var originalException = new NoValidMembersForImportException(uploadId);

        // Act & Assert - Exception should be serializable for logging/monitoring
        originalException.Should().NotBeNull();
        originalException.Data.Should().NotBeNull();
    }
}
