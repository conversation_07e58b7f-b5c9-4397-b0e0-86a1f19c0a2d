using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.ValueObjects;

/// <summary>
/// Tests to verify behavioral compatibility of implicit string conversion operators
/// for all domain ID types after standardization changes
/// </summary>
public class DomainIdImplicitConversionTests
{
    private readonly string _validGuidString = Guid.NewGuid().ToString();
    private readonly string _invalidGuidString = "invalid-guid-format";

    #region PolicyId Tests (Pattern A - ArgumentException for null/empty, FormatException for invalid)

    [Fact]
    public void PolicyId_ImplicitConversion_WithValidGuid_ShouldSucceed()
    {
        // Act
        PolicyId result = _validGuidString;

        // Assert
        result.Value.Should().Be(Guid.Parse(_validGuidString));
    }

    [Fact]
    public void PolicyId_ImplicitConversion_WithNull_ShouldThrowArgumentException()
    {
        // Act & Assert
        ArgumentException exception = Assert.Throws<ArgumentException>(() => (PolicyId)(string)null!);
        exception.Message.Should().Contain("PolicyId cannot be null or empty");
        exception.ParamName.Should().Be("value");
    }

    [Fact]
    public void PolicyId_ImplicitConversion_WithEmpty_ShouldThrowArgumentException()
    {
        // Act & Assert
        ArgumentException exception = Assert.Throws<ArgumentException>(() => (PolicyId)"");
        exception.Message.Should().Contain("PolicyId cannot be null or empty");
        exception.ParamName.Should().Be("value");
    }

    [Fact]
    public void PolicyId_ImplicitConversion_WithWhitespace_ShouldThrowArgumentException()
    {
        // Act & Assert
        ArgumentException exception = Assert.Throws<ArgumentException>(() => (PolicyId)"   ");
        exception.Message.Should().Contain("PolicyId cannot be null or empty");
        exception.ParamName.Should().Be("value");
    }

    [Fact]
    public void PolicyId_ImplicitConversion_WithInvalidGuid_ShouldThrowFormatException()
    {
        // Act & Assert
        Assert.Throws<FormatException>(() => (PolicyId)_invalidGuidString);
    }

    #endregion

    #region PolicyMemberId Tests (Pattern A - Same as PolicyId)

    [Fact]
    public void PolicyMemberId_ImplicitConversion_WithValidGuid_ShouldSucceed()
    {
        // Act
        PolicyMemberId result = _validGuidString;

        // Assert
        result.Value.Should().Be(Guid.Parse(_validGuidString));
    }

    [Fact]
    public void PolicyMemberId_ImplicitConversion_WithNull_ShouldThrowArgumentException()
    {
        // Act & Assert
        ArgumentException exception = Assert.Throws<ArgumentException>(() => (PolicyMemberId)(string)null!);
        exception.Message.Should().Contain("PolicyMemberId cannot be null or empty");
        exception.ParamName.Should().Be("value");
    }

    [Fact]
    public void PolicyMemberId_ImplicitConversion_WithEmpty_ShouldThrowArgumentException()
    {
        // Act & Assert
        ArgumentException exception = Assert.Throws<ArgumentException>(() => (PolicyMemberId)"");
        exception.Message.Should().Contain("PolicyMemberId cannot be null or empty");
        exception.ParamName.Should().Be("value");
    }

    [Fact]
    public void PolicyMemberId_ImplicitConversion_WithWhitespace_ShouldThrowArgumentException()
    {
        // Act & Assert
        ArgumentException exception = Assert.Throws<ArgumentException>(() => (PolicyMemberId)"   ");
        exception.Message.Should().Contain("PolicyMemberId cannot be null or empty");
        exception.ParamName.Should().Be("value");
    }

    [Fact]
    public void PolicyMemberId_ImplicitConversion_WithInvalidGuid_ShouldThrowFormatException()
    {
        // Act & Assert
        Assert.Throws<FormatException>(() => (PolicyMemberId)_invalidGuidString);
    }

    #endregion

    #region EndorsementId Tests (Pattern B - ArgumentException for all invalid inputs)

    [Fact]
    public void EndorsementId_ImplicitConversion_WithValidGuid_ShouldSucceed()
    {
        // Act
        EndorsementId result = _validGuidString;

        // Assert
        result.Value.Should().Be(Guid.Parse(_validGuidString));
    }

    [Fact]
    public void EndorsementId_ImplicitConversion_WithNull_ShouldThrowArgumentException()
    {
        // Act & Assert
        ArgumentException exception = Assert.Throws<ArgumentException>(() => (EndorsementId)(string)null!);
        exception.Message.Should().Contain("EndorsementId cannot be null or empty");
        exception.ParamName.Should().Be("value");
    }

    [Fact]
    public void EndorsementId_ImplicitConversion_WithEmpty_ShouldThrowArgumentException()
    {
        // Act & Assert
        ArgumentException exception = Assert.Throws<ArgumentException>(() => (EndorsementId)"");
        exception.Message.Should().Contain("EndorsementId cannot be null or empty");
        exception.ParamName.Should().Be("value");
    }

    [Fact]
    public void EndorsementId_ImplicitConversion_WithWhitespace_ShouldThrowArgumentException()
    {
        // Act & Assert
        ArgumentException exception = Assert.Throws<ArgumentException>(() => (EndorsementId)"   ");
        exception.Message.Should().Contain("EndorsementId cannot be null or empty");
        exception.ParamName.Should().Be("value");
    }

    [Fact]
    public void EndorsementId_ImplicitConversion_WithInvalidGuid_ShouldThrowFormatException()
    {
        // Act & Assert
        Assert.Throws<FormatException>(() => (EndorsementId)_invalidGuidString);
    }

    #endregion

    #region PolicyMemberUploadValidationErrorId Tests (Pattern B - Same as EndorsementId)

    [Fact]
    public void PolicyMemberUploadValidationErrorId_ImplicitConversion_WithValidGuid_ShouldSucceed()
    {
        // Act
        PolicyMemberUploadValidationErrorId result = _validGuidString;

        // Assert
        result.Value.Should().Be(Guid.Parse(_validGuidString));
    }

    [Fact]
    public void PolicyMemberUploadValidationErrorId_ImplicitConversion_WithNull_ShouldThrowArgumentException()
    {
        // Act & Assert
        ArgumentException exception = Assert.Throws<ArgumentException>(() => (PolicyMemberUploadValidationErrorId)(string)null!);
        exception.Message.Should().Contain("Invalid GUID format:");
        exception.ParamName.Should().Be("value");
    }

    [Fact]
    public void PolicyMemberUploadValidationErrorId_ImplicitConversion_WithEmpty_ShouldThrowArgumentException()
    {
        // Act & Assert
        ArgumentException exception = Assert.Throws<ArgumentException>(() => (PolicyMemberUploadValidationErrorId)"");
        exception.Message.Should().Contain("Invalid GUID format:");
        exception.ParamName.Should().Be("value");
    }

    [Fact]
    public void PolicyMemberUploadValidationErrorId_ImplicitConversion_WithWhitespace_ShouldThrowArgumentException()
    {
        // Act & Assert
        ArgumentException exception = Assert.Throws<ArgumentException>(() => (PolicyMemberUploadValidationErrorId)"   ");
        exception.Message.Should().Contain("Invalid GUID format:");
        exception.ParamName.Should().Be("value");
    }

    [Fact]
    public void PolicyMemberUploadValidationErrorId_ImplicitConversion_WithInvalidGuid_ShouldThrowArgumentException()
    {
        // Act & Assert
        ArgumentException exception = Assert.Throws<ArgumentException>(() => (PolicyMemberUploadValidationErrorId)_invalidGuidString);
        exception.Message.Should().Contain("Invalid GUID format:");
        exception.ParamName.Should().Be("value");
    }

    #endregion

    #region PolicyMemberStateId Tests (Pattern C - ArgumentNullException for null, FormatException for invalid)

    [Fact]
    public void PolicyMemberStateId_ImplicitConversion_WithValidGuid_ShouldSucceed()
    {
        // Act
        PolicyMemberStateId result = _validGuidString;

        // Assert
        result.Value.Should().Be(Guid.Parse(_validGuidString));
    }

    [Fact]
    public void PolicyMemberStateId_ImplicitConversion_WithNull_ShouldThrowArgumentException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => (PolicyMemberStateId)(string)null!);
    }

    [Fact]
    public void PolicyMemberStateId_ImplicitConversion_WithEmpty_ShouldThrowArgumentException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => (PolicyMemberStateId)"");
    }

    [Fact]
    public void PolicyMemberStateId_ImplicitConversion_WithWhitespace_ShouldThrowArgumentException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => (PolicyMemberStateId)"   ");
    }

    [Fact]
    public void PolicyMemberStateId_ImplicitConversion_WithInvalidGuid_ShouldThrowFormatException()
    {
        // Act & Assert
        Assert.Throws<FormatException>(() => (PolicyMemberStateId)_invalidGuidString);
    }

    #endregion

    #region PolicyMemberUploadId Tests (Pattern C - Same as PolicyMemberStateId)

    [Fact]
    public void PolicyMemberUploadId_ImplicitConversion_WithValidGuid_ShouldSucceed()
    {
        // Act
        PolicyMemberUploadId result = _validGuidString;

        // Assert
        result.Value.Should().Be(Guid.Parse(_validGuidString));
    }

    [Fact]
    public void PolicyMemberUploadId_ImplicitConversion_WithNull_ShouldThrowArgumentException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => (PolicyMemberUploadId)(string)null!);
    }

    [Fact]
    public void PolicyMemberUploadId_ImplicitConversion_WithEmpty_ShouldThrowArgumentException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => (PolicyMemberUploadId)"");
    }

    [Fact]
    public void PolicyMemberUploadId_ImplicitConversion_WithWhitespace_ShouldThrowArgumentException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => (PolicyMemberUploadId)"   ");
    }

    [Fact]
    public void PolicyMemberUploadId_ImplicitConversion_WithInvalidGuid_ShouldThrowFormatException()
    {
        // Act & Assert
        Assert.Throws<FormatException>(() => (PolicyMemberUploadId)_invalidGuidString);
    }

    #endregion
}
