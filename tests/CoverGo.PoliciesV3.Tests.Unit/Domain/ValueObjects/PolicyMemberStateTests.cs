using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.ValueObjects;

public class PolicyMemberStateTests
{
    #region ValidateDateRange Tests

    [Fact]
    public void ValidateDateRange_WithValidDates_ShouldNotThrow()
    {
        // Arrange
        var startDate = new DateOnly(2024, 1, 1);
        var endDate = new DateOnly(2024, 12, 31);

        // Act & Assert
        Action action = () => PolicyMemberState.ValidateDateRange(startDate, endDate);
        action.Should().NotThrow();
    }

    [Fact]
    public void ValidateDateRange_WithStartDateEqualToEndDate_ShouldThrow()
    {
        // Arrange
        var date = new DateOnly(2024, 6, 15);

        // Act & Assert
        Action action = () => PolicyMemberState.ValidateDateRange(date, date);
        action.Should().Throw<ArgumentException>()
            .WithMessage($"Start date ({date}) must be before end date ({date})*")
            .And.ParamName.Should().Be("startDate");
    }

    [Fact]
    public void ValidateDateRange_WithStartDateAfterEndDate_ShouldThrow()
    {
        // Arrange
        var startDate = new DateOnly(2024, 12, 31);
        var endDate = new DateOnly(2024, 1, 1);

        // Act & Assert
        Action action = () => PolicyMemberState.ValidateDateRange(startDate, endDate);
        action.Should().Throw<ArgumentException>()
            .WithMessage($"Start date ({startDate}) must be before end date ({endDate})*")
            .And.ParamName.Should().Be("startDate");
    }

    [Theory]
    [InlineData(2024, 6, 15, 2024, 6, 14)] // End one day before start
    [InlineData(2024, 12, 31, 2024, 1, 1)] // End much before start
    [InlineData(2024, 6, 15, 2024, 6, 15)] // Same date
    public void ValidateDateRange_WithInvalidDateRanges_ShouldThrow(int startYear, int startMonth, int startDay, int endYear, int endMonth, int endDay)
    {
        // Arrange
        var startDate = new DateOnly(startYear, startMonth, startDay);
        var endDate = new DateOnly(endYear, endMonth, endDay);

        // Act & Assert
        Action action = () => PolicyMemberState.ValidateDateRange(startDate, endDate);
        action.Should().Throw<ArgumentException>()
            .WithMessage($"Start date ({startDate}) must be before end date ({endDate})*")
            .And.ParamName.Should().Be("startDate");
    }

    #endregion

    #region Create Method Tests

    [Fact]
    public void Create_WithValidDates_ShouldCreatePolicyMemberState()
    {
        // Arrange
        PolicyMemberId policyMemberId = PolicyMemberId.New;
        var startDate = new DateOnly(2024, 1, 1);
        var endDate = new DateOnly(2024, 12, 31);
        string planId = "PLAN-001";

        // Act
        var state = PolicyMemberState.Create(policyMemberId, startDate, endDate, planId);

        // Assert
        state.Should().NotBeNull();
        state.PolicyMemberId.Should().Be(policyMemberId);
        state.StartDate.Should().Be(startDate);
        state.EndDate.Should().Be(endDate);
        state.PlanId.Should().Be(planId);
        state.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Create_WithInvalidDates_ShouldThrowArgumentException()
    {
        // Arrange
        PolicyMemberId policyMemberId = PolicyMemberId.New;
        var startDate = new DateOnly(2024, 12, 31);
        var endDate = new DateOnly(2024, 1, 1);
        string planId = "PLAN-001";

        // Act & Assert
        Func<PolicyMemberState> action = () => PolicyMemberState.Create(policyMemberId, startDate, endDate, planId);
        action.Should().Throw<ArgumentException>()
            .WithMessage($"Start date ({startDate}) must be before end date ({endDate})*")
            .And.ParamName.Should().Be("startDate");
    }

    [Fact]
    public void Create_WithOptionalParameters_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        PolicyMemberId policyMemberId = PolicyMemberId.New;
        var startDate = new DateOnly(2024, 1, 1);
        var endDate = new DateOnly(2024, 12, 31);
        string planId = "PLAN-001";
        string @class = "CLASS-A";
        EndorsementId endorsementId = EndorsementId.New;
        var customFields = new List<PolicyField>();

        // Act
        var state = PolicyMemberState.Create(policyMemberId, startDate, endDate, planId, @class, endorsementId, customFields);

        // Assert
        state.Should().NotBeNull();
        state.PolicyMemberId.Should().Be(policyMemberId);
        state.StartDate.Should().Be(startDate);
        state.EndDate.Should().Be(endDate);
        state.PlanId.Should().Be(planId);
        state.Class.Should().Be(@class);
        state.EndorsementId.Should().Be(endorsementId);
        state.Fields.Should().BeSameAs(customFields);
    }

    #endregion
}
