using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.ValueObjects;

public class EndorsementIdCollectionTests
{
    private readonly Guid _validGuid1 = Guid.NewGuid();
    private readonly Guid _validGuid2 = Guid.NewGuid();

    [Fact]
    public void FromStringIds_WithValidGuids_ShouldCreateCollection()
    {
        // Arrange
        var stringIds = new List<string?>
            {
                _validGuid1.ToString(),
                _validGuid2.ToString()
            };

        // Act
        var collection = EndorsementIdCollection.FromStringIds(stringIds);

        // Assert
        collection.Value.Should().HaveCount(2);
        collection.Value.Should().Contain((EndorsementId)_validGuid1);
        collection.Value.Should().Contain((EndorsementId)_validGuid2);
    }

    [Fact]
    public void FromStringIds_WithNullValues_ShouldFilterOutNulls()
    {
        // Arrange
        var stringIds = new List<string?>
            {
                _validGuid1.ToString(),
                null,
                _validGuid2.ToString()
            };

        // Act
        var collection = EndorsementIdCollection.FromStringIds(stringIds);

        // Assert
        collection.Value.Should().HaveCount(2);
        collection.Value.Should().Contain((EndorsementId)_validGuid1);
        collection.Value.Should().Contain((EndorsementId)_validGuid2);
    }

    [Fact]
    public void FromStringIds_WithInvalidGuids_ShouldFilterOutInvalids()
    {
        // Arrange
        var stringIds = new List<string?>
            {
                _validGuid1.ToString(),
                "invalid-guid",
                _validGuid2.ToString(),
                "another-invalid"
            };

        // Act
        var collection = EndorsementIdCollection.FromStringIds(stringIds);

        // Assert
        collection.Value.Should().HaveCount(2);
        collection.Value.Should().Contain((EndorsementId)_validGuid1);
        collection.Value.Should().Contain((EndorsementId)_validGuid2);
    }

    [Fact]
    public void FromStringIds_WithEmptyList_ShouldCreateEmptyCollection()
    {
        // Arrange
        var stringIds = new List<string?>();

        // Act
        var collection = EndorsementIdCollection.FromStringIds(stringIds);

        // Assert
        collection.Value.Should().BeEmpty();
        collection.IsEmpty.Should().BeTrue();
    }

    [Fact]
    public void FromStringIds_WithAllNullsAndInvalids_ShouldCreateEmptyCollection()
    {
        // Arrange
        var stringIds = new List<string?> { null, "invalid", "", "not-a-guid" };

        // Act
        var collection = EndorsementIdCollection.FromStringIds(stringIds);

        // Assert
        collection.Value.Should().BeEmpty();
        collection.IsEmpty.Should().BeTrue();
    }

    [Fact]
    public void ToStringIds_ShouldConvertBackToStringList()
    {
        // Arrange
        var endorsementIds = new List<EndorsementId>
            {
                (EndorsementId)_validGuid1,
                (EndorsementId)_validGuid2
            };
        var collection = new EndorsementIdCollection(endorsementIds);

        // Act
        List<string?> stringIds = collection.ToStringIds();

        // Assert
        stringIds.Should().HaveCount(2);
        stringIds.Should().Contain(_validGuid1.ToString());
        stringIds.Should().Contain(_validGuid2.ToString());
    }

    [Fact]
    public void ContainsNullEndorsement_WithNullInList_ShouldReturnTrue()
    {
        // Arrange
        var stringIds = new List<string?>
            {
                _validGuid1.ToString(),
                null,
                _validGuid2.ToString()
            };

        // Act
        bool result = EndorsementIdCollection.ContainsNullEndorsement(stringIds);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void ContainsNullEndorsement_WithoutNullInList_ShouldReturnFalse()
    {
        // Arrange
        var stringIds = new List<string?>
            {
                _validGuid1.ToString(),
                _validGuid2.ToString()
            };

        // Act
        bool result = EndorsementIdCollection.ContainsNullEndorsement(stringIds);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void Contains_WithExistingEndorsementId_ShouldReturnTrue()
    {
        // Arrange
        var endorsementId = (EndorsementId)_validGuid1;
        var collection = new EndorsementIdCollection([endorsementId]);

        // Act
        bool result = collection.Contains(endorsementId);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void Contains_WithNonExistingEndorsementId_ShouldReturnFalse()
    {
        // Arrange
        var endorsementId1 = (EndorsementId)_validGuid1;
        var endorsementId2 = (EndorsementId)_validGuid2;
        var collection = new EndorsementIdCollection([endorsementId1]);

        // Act
        bool result = collection.Contains(endorsementId2);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void Count_ShouldReturnCorrectCount()
    {
        // Arrange
        var endorsementIds = new List<EndorsementId>
            {
                (EndorsementId)_validGuid1,
                (EndorsementId)_validGuid2
            };
        var collection = new EndorsementIdCollection(endorsementIds);

        // Act & Assert
        collection.Count.Should().Be(2);
    }

    [Fact]
    public void IsEmpty_WithEmptyCollection_ShouldReturnTrue()
    {
        // Arrange
        var collection = new EndorsementIdCollection([]);

        // Act & Assert
        collection.IsEmpty.Should().BeTrue();
    }

    [Fact]
    public void IsEmpty_WithNonEmptyCollection_ShouldReturnFalse()
    {
        // Arrange
        var collection = new EndorsementIdCollection([(EndorsementId)_validGuid1]);

        // Act & Assert
        collection.IsEmpty.Should().BeFalse();
    }

    [Fact]
    public void ImplicitConversion_FromListToCollection_ShouldWork()
    {
        // Arrange
        var endorsementIds = new List<EndorsementId> { (EndorsementId)_validGuid1 };

        // Act
        EndorsementIdCollection collection = endorsementIds;

        // Assert
        collection.Value.Should().BeEquivalentTo(endorsementIds);
    }

    [Fact]
    public void ImplicitConversion_FromCollectionToList_ShouldWork()
    {
        // Arrange
        var endorsementIds = new List<EndorsementId> { (EndorsementId)_validGuid1 };
        var collection = new EndorsementIdCollection(endorsementIds);

        // Act
        List<EndorsementId> result = collection;

        // Assert
        result.Should().BeEquivalentTo(endorsementIds);
    }

    [Fact]
    public void FromStringIdsIncludingNull_ShouldCreateCollectionWithValidIds()
    {
        // Arrange
        var stringIds = new List<string?>
            {
                _validGuid1.ToString(),
                null,
                _validGuid2.ToString(),
                "invalid-guid"
            };

        // Act
        var collection = EndorsementIdCollection.FromStringIdsIncludingNull(stringIds);

        // Assert
        collection.Value.Should().HaveCount(2);
        collection.Value.Should().Contain((EndorsementId)_validGuid1);
        collection.Value.Should().Contain((EndorsementId)_validGuid2);
    }

    [Fact]
    public void ValueObject_Equality_ShouldWorkCorrectly()
    {
        // Arrange
        var endorsementIds = new List<EndorsementId> { (EndorsementId)_validGuid1 };
        var collection1 = new EndorsementIdCollection(endorsementIds);
        var collection2 = new EndorsementIdCollection(endorsementIds);
        var collection3 = new EndorsementIdCollection([(EndorsementId)_validGuid2]);

        // Act & Assert
        collection1.Should().Be(collection2);
        collection1.Should().NotBe(collection3);
        (collection1 == collection2).Should().BeTrue();
        (collection1 == collection3).Should().BeFalse();
    }
}
