using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Exceptions;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.PolicyMembers.Exceptions;

public class InvalidPolicyMemberStateDateExceptionTests
{
    [Fact]
    public void Constructor_WithStartDateAndEndDate_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        var startDate = new DateOnly(2024, 1, 15);
        var endDate = new DateOnly(2024, 1, 10); // End date before start date

        // Act
        var exception = new InvalidPolicyMemberStateDateException(startDate, endDate);

        // Assert
        exception.StartDate.Should().Be(startDate);
        exception.EndDate.Should().Be(endDate);
        exception.Code.Should().Be(ErrorCodes.InvalidPolicyMemberStateDate);
        exception.Message.Should().Be($"Invalid state: EndDate {endDate} must be after or equal to StartDate {startDate}");
    }

    [Fact]
    public void Constructor_WithDatesAndInnerException_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        var startDate = new DateOnly(2024, 6, 1);
        var endDate = new DateOnly(2024, 5, 31); // End date before start date
        var innerException = new ArgumentException("Inner exception message");

        // Act
        var exception = new InvalidPolicyMemberStateDateException(startDate, endDate, innerException);

        // Assert
        exception.StartDate.Should().Be(startDate);
        exception.EndDate.Should().Be(endDate);
        exception.Code.Should().Be(ErrorCodes.InvalidPolicyMemberStateDate);
        exception.Message.Should().Be($"Invalid state: EndDate {endDate} must be after or equal to StartDate {startDate}");
        exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void Code_ShouldReturnCorrectErrorCode()
    {
        // Arrange
        var startDate = new DateOnly(2024, 1, 15);
        var endDate = new DateOnly(2024, 1, 10);
        var exception = new InvalidPolicyMemberStateDateException(startDate, endDate);

        // Act & Assert
        exception.Code.Should().Be(ErrorCodes.InvalidPolicyMemberStateDate);
        exception.Code.Should().Be("INVALID_POLICY_MEMBER_STATE_DATE");
    }

    [Fact]
    public void Exception_ShouldInheritFromDomainException()
    {
        // Arrange
        var startDate = new DateOnly(2024, 1, 15);
        var endDate = new DateOnly(2024, 1, 10);

        // Act
        var exception = new InvalidPolicyMemberStateDateException(startDate, endDate);

        // Assert
        exception.Should().BeAssignableTo<DomainException>();
    }

    [Theory]
    [InlineData(2024, 1, 15, 2024, 1, 10)] // End before start
    [InlineData(2024, 12, 31, 2024, 1, 1)] // End much before start
    [InlineData(2024, 6, 15, 2024, 6, 14)] // End one day before start
    public void Constructor_WithInvalidDateRanges_ShouldCreateCorrectMessage(int startYear, int startMonth, int startDay, int endYear, int endMonth, int endDay)
    {
        // Arrange
        var startDate = new DateOnly(startYear, startMonth, startDay);
        var endDate = new DateOnly(endYear, endMonth, endDay);

        // Act
        var exception = new InvalidPolicyMemberStateDateException(startDate, endDate);

        // Assert
        exception.Message.Should().Be($"Invalid state: EndDate {endDate} must be after or equal to StartDate {startDate}");
        exception.StartDate.Should().Be(startDate);
        exception.EndDate.Should().Be(endDate);
    }

    [Fact]
    public void Constructor_WithSameDates_ShouldStillCreateException()
    {
        // Arrange - Even though same dates might be valid, the exception can still be created
        var date = new DateOnly(2024, 6, 15);

        // Act
        var exception = new InvalidPolicyMemberStateDateException(date, date);

        // Assert
        exception.StartDate.Should().Be(date);
        exception.EndDate.Should().Be(date);
        exception.Message.Should().Contain(date.ToString());
    }

    [Fact]
    public void Exception_ShouldBeSerializable()
    {
        // Arrange
        var startDate = new DateOnly(2024, 1, 15);
        var endDate = new DateOnly(2024, 1, 10);
        var originalException = new InvalidPolicyMemberStateDateException(startDate, endDate);

        // Act & Assert - Exception should be serializable for logging/monitoring
        originalException.Should().NotBeNull();
        originalException.Data.Should().NotBeNull();
    }
}
