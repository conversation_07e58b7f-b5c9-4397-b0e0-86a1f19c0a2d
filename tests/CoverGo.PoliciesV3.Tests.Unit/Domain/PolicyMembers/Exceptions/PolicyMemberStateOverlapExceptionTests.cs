using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Exceptions;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.PolicyMembers.Exceptions;

public class PolicyMemberStateOverlapExceptionTests
{
    [Fact]
    public void Constructor_WithOverlappingDates_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        var firstStateEndDate = new DateOnly(2024, 1, 15);
        var secondStateStartDate = new DateOnly(2024, 1, 10); // Overlaps with first state

        // Act
        var exception = new PolicyMemberStateOverlapException(firstStateEndDate, secondStateStartDate);

        // Assert
        exception.FirstStateEndDate.Should().Be(firstStateEndDate);
        exception.SecondStateStartDate.Should().Be(secondStateStartDate);
        exception.Code.Should().Be(ErrorCodes.PolicyMemberStateOverlap);
        exception.Message.Should().Be($"State overlap detected: State ending {firstStateEndDate} overlaps with state starting {secondStateStartDate}");
    }

    [Fact]
    public void Constructor_WithDatesAndInnerException_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        var firstStateEndDate = new DateOnly(2024, 6, 15);
        var secondStateStartDate = new DateOnly(2024, 6, 10); // Overlaps with first state
        var innerException = new InvalidOperationException("Inner exception message");

        // Act
        var exception = new PolicyMemberStateOverlapException(firstStateEndDate, secondStateStartDate, innerException);

        // Assert
        exception.FirstStateEndDate.Should().Be(firstStateEndDate);
        exception.SecondStateStartDate.Should().Be(secondStateStartDate);
        exception.Code.Should().Be(ErrorCodes.PolicyMemberStateOverlap);
        exception.Message.Should().Be($"State overlap detected: State ending {firstStateEndDate} overlaps with state starting {secondStateStartDate}");
        exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void Code_ShouldReturnCorrectErrorCode()
    {
        // Arrange
        var firstStateEndDate = new DateOnly(2024, 1, 15);
        var secondStateStartDate = new DateOnly(2024, 1, 10);
        var exception = new PolicyMemberStateOverlapException(firstStateEndDate, secondStateStartDate);

        // Act & Assert
        exception.Code.Should().Be(ErrorCodes.PolicyMemberStateOverlap);
        exception.Code.Should().Be("POLICY_MEMBER_STATE_OVERLAP");
    }

    [Fact]
    public void Exception_ShouldInheritFromDomainException()
    {
        // Arrange
        var firstStateEndDate = new DateOnly(2024, 1, 15);
        var secondStateStartDate = new DateOnly(2024, 1, 10);

        // Act
        var exception = new PolicyMemberStateOverlapException(firstStateEndDate, secondStateStartDate);

        // Assert
        exception.Should().BeAssignableTo<DomainException>();
    }

    [Theory]
    [InlineData(2024, 1, 15, 2024, 1, 10)] // Clear overlap
    [InlineData(2024, 6, 30, 2024, 6, 15)] // Overlap in same month
    [InlineData(2024, 12, 31, 2024, 12, 1)] // Overlap at year end
    [InlineData(2024, 3, 15, 2024, 3, 15)] // Same date (edge case)
    public void Constructor_WithVariousOverlappingDates_ShouldCreateCorrectMessage(int firstEndYear, int firstEndMonth, int firstEndDay, int secondStartYear, int secondStartMonth, int secondStartDay)
    {
        // Arrange
        var firstStateEndDate = new DateOnly(firstEndYear, firstEndMonth, firstEndDay);
        var secondStateStartDate = new DateOnly(secondStartYear, secondStartMonth, secondStartDay);

        // Act
        var exception = new PolicyMemberStateOverlapException(firstStateEndDate, secondStateStartDate);

        // Assert
        exception.Message.Should().Be($"State overlap detected: State ending {firstStateEndDate} overlaps with state starting {secondStateStartDate}");
        exception.FirstStateEndDate.Should().Be(firstStateEndDate);
        exception.SecondStateStartDate.Should().Be(secondStateStartDate);
    }

    [Fact]
    public void Constructor_WithNonOverlappingDates_ShouldStillCreateException()
    {
        // Arrange - Even though dates don't actually overlap, the exception can still be created
        var firstStateEndDate = new DateOnly(2024, 1, 15);
        var secondStateStartDate = new DateOnly(2024, 1, 16); // No overlap

        // Act
        var exception = new PolicyMemberStateOverlapException(firstStateEndDate, secondStateStartDate);

        // Assert
        exception.FirstStateEndDate.Should().Be(firstStateEndDate);
        exception.SecondStateStartDate.Should().Be(secondStateStartDate);
        exception.Message.Should().Contain("overlap detected");
    }

    [Fact]
    public void Exception_ShouldBeSerializable()
    {
        // Arrange
        var firstStateEndDate = new DateOnly(2024, 1, 15);
        var secondStateStartDate = new DateOnly(2024, 1, 10);
        var originalException = new PolicyMemberStateOverlapException(firstStateEndDate, secondStateStartDate);

        // Act & Assert - Exception should be serializable for logging/monitoring
        originalException.Should().NotBeNull();
        originalException.Data.Should().NotBeNull();
    }
}
