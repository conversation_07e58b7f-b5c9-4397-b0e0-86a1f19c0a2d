using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.PolicyMembers;

public class PolicyMemberTests
{
    private readonly Fixture _fixture;

    public PolicyMemberTests()
    {
        _fixture = new Fixture();
        _fixture.Customize<PolicyMemberId>(c => c.FromFactory(() => PolicyMemberId.New));
        _fixture.Customize<PolicyId>(c => c.FromFactory(() => PolicyId.New));
    }

    [Fact]
    public void MarkAsRemoved_WhenCalled_ShouldSetIsRemovedToTrue()
    {
        // Arrange
        PolicyMember policyMember = CreatePolicyMember();

        // Act
        policyMember.MarkAsRemoved();

        // Assert
        policyMember.IsRemoved.Should().BeTrue();
    }

    [Fact]
    public void MarkAsRemoved_WhenAlreadyRemoved_ShouldRemainTrue()
    {
        // Arrange
        PolicyMember policyMember = CreatePolicyMember();
        policyMember.MarkAsRemoved(); // Mark as removed first

        // Act
        policyMember.MarkAsRemoved();

        // Assert
        policyMember.IsRemoved.Should().BeTrue();
    }

    [Fact]
    public void MarkAsRemoved_WhenCalledMultipleTimes_ShouldRemainTrue()
    {
        // Arrange
        PolicyMember policyMember = CreatePolicyMember();

        // Act
        policyMember.MarkAsRemoved();
        policyMember.MarkAsRemoved();
        policyMember.MarkAsRemoved();

        // Assert
        policyMember.IsRemoved.Should().BeTrue();
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public void MarkAsRemoved_WithDifferentInitialStates_ShouldAlwaysResultInTrue(bool initialIsRemovedState)
    {
        // Arrange
        PolicyMember policyMember = CreatePolicyMember();
        if (initialIsRemovedState)
        {
            policyMember.MarkAsRemoved(); // Set initial state to removed
        }

        // Act
        policyMember.MarkAsRemoved();

        // Assert
        policyMember.IsRemoved.Should().BeTrue();
    }

    private PolicyMember CreatePolicyMember()
    {
        return PolicyMember.Create(
            _fixture.Create<PolicyId>(),
            "TEST-MEMBER",
            DateOnly.FromDateTime(DateTime.Today),
            DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            "TEST-PLAN");
    }
}
