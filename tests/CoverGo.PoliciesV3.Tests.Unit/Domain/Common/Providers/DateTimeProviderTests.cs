using CoverGo.PoliciesV3.Domain.Common.Providers;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.Common.Providers;

public class DateTimeProviderTests : IDisposable
{
    public void Dispose()
    {
        // Reset the provider after each test to avoid side effects
        DateTimeProvider.Reset();
    }

    [Fact]
    public void UtcNow_ByDefault_ShouldReturnCurrentUtcTime()
    {
        // Act
        DateTime result = DateTimeProvider.UtcNow;

        // Assert
        result.Kind.Should().Be(DateTimeKind.Utc);
        result.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Today_ByDefault_ShouldReturnCurrentUtcDate()
    {
        // Act
        DateOnly result = DateTimeProvider.Today;

        // Assert
        result.Should().Be(DateOnly.FromDateTime(DateTime.UtcNow));
    }

    [Fact]
    public void SetFixedUtcDateTime_WithValidUtcDateTime_ShouldSetFixedTime()
    {
        // Arrange
        var fixedDateTime = new DateTime(2024, 6, 15, 12, 0, 0, DateTimeKind.Utc);

        // Act
        DateTimeProvider.SetFixedUtcDateTime(fixedDateTime);

        // Assert
        DateTimeProvider.UtcNow.Should().Be(fixedDateTime);
        DateTimeProvider.Today.Should().Be(DateOnly.FromDateTime(fixedDateTime));
    }

    [Fact]
    public void SetFixedUtcDateTime_WithNonUtcDateTime_ShouldThrowArgumentException()
    {
        // Arrange
        var nonUtcDateTime = new DateTime(2024, 6, 15, 12, 0, 0, DateTimeKind.Local);

        // Act & Assert
        Action act = () => DateTimeProvider.SetFixedUtcDateTime(nonUtcDateTime);
        act.Should().Throw<ArgumentException>()
            .WithMessage("DateTime must be UTC*");
    }

    [Fact]
    public void SetUtcNowProvider_WithCustomProvider_ShouldUseCustomProvider()
    {
        // Arrange
        var customDateTime = new DateTime(2024, 12, 25, 10, 30, 0, DateTimeKind.Utc);
        Func<DateTime> customProvider = () => customDateTime;

        // Act
        DateTimeProvider.SetUtcNowProvider(customProvider);

        // Assert
        DateTimeProvider.UtcNow.Should().Be(customDateTime);
        DateTimeProvider.Today.Should().Be(DateOnly.FromDateTime(customDateTime));
    }

    [Fact]
    public void SetUtcNowProvider_WithNullProvider_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Action act = () => DateTimeProvider.SetUtcNowProvider(null!);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Reset_AfterSettingCustomProvider_ShouldRestoreDefaultBehavior()
    {
        // Arrange
        var fixedDateTime = new DateTime(2024, 6, 15, 12, 0, 0, DateTimeKind.Utc);
        DateTimeProvider.SetFixedUtcDateTime(fixedDateTime);

        // Act
        DateTimeProvider.Reset();

        // Assert
        DateTimeProvider.UtcNow.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        DateTimeProvider.Today.Should().Be(DateOnly.FromDateTime(DateTime.UtcNow));
    }
}
