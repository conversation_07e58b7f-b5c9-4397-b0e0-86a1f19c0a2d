using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.Common.Validation;

/// <summary>
/// Unit tests for PolicyMemberFieldDefinitionExtensions to ensure proper validation error creation.
/// </summary>
public class PolicyMemberFieldDefinitionExtensionsTests
{
    private readonly PolicyMemberFieldDefinition _testField;

    public PolicyMemberFieldDefinitionExtensionsTests()
    {
        _testField = new PolicyMemberFieldDefinition
        {
            Name = "email",
            Label = "Email Address",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };
    }

    [Fact]
    public void GetFullLabel_WithLabel_ShouldReturnLabel()
    {
        // Act
        string result = _testField.GetFullLabel();

        // Assert
        result.Should().Be("Email Address");
    }

    [Fact]
    public void GetFullLabel_WithoutLabel_ShouldReturnName()
    {
        // Arrange
        var field = new PolicyMemberFieldDefinition
        {
            Name = "phone",
            Label = "",
            Type = new StringFieldType(),
            IsRequired = false,
            IsUnique = false
        };

        // Act
        string result = field.GetFullLabel();

        // Assert
        result.Should().Be("phone");
    }

    [Fact]
    public void CreateRequiredError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateRequiredError();

        // Assert
        error.Code.Should().Be(ErrorCodes.Required);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Message.Should().Be("Email Address is required");
    }

    [Fact]
    public void CreateInvalidFormatError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateInvalidFormatError();

        // Assert
        error.Code.Should().Be(ErrorCodes.InvalidFormat);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Message.Should().Be("Email Address has an invalid format");
    }

    [Fact]
    public void CreateUniqueViolationError_WithDefaultScope_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateUniqueViolationError();

        // Assert
        error.Code.Should().Be(ErrorCodes.UniqueViolation);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["Scope"].Should().Be("upload");
        error.Message.Should().Be("Email Address must be unique within upload");
    }

    [Fact]
    public void CreateUniqueViolationError_WithCustomScope_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateUniqueViolationError("database");

        // Assert
        error.Code.Should().Be(ErrorCodes.UniqueViolation);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["Scope"].Should().Be("database");
        error.Message.Should().Be("Email Address must be unique within database");
    }

    [Fact]
    public void CreateInvalidOptionError_ShouldCreateCorrectError()
    {
        // Arrange
        string[] availableOptions = ["active", "inactive", "pending"];

        // Act
        ValidationError error = _testField.CreateInvalidOptionError(availableOptions);

        // Assert
        error.Code.Should().Be(ErrorCodes.InvalidOption);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["AvailableOptions"].Should().BeEquivalentTo(availableOptions);
    }

    [Fact]
    public void CreateOutOfRangeError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateOutOfRangeError(18, 65);

        // Assert
        error.Code.Should().Be(ErrorCodes.OutOfRange);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["MinValue"].Should().Be(18);
        error.Context["MaxValue"].Should().Be(65);
        error.Message.Should().Be("Email Address must be between 18 and 65");
    }

    [Fact]
    public void CreatePatternMismatchError_ShouldCreateCorrectError()
    {
        // Arrange
        const string pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";

        // Act
        ValidationError error = _testField.CreatePatternMismatchError(pattern);

        // Assert
        error.Code.Should().Be(ErrorCodes.PatternMismatch);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["Pattern"].Should().Be(pattern);
    }

    [Fact]
    public void CreateAgeTooLowError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateAgeTooLowError(18);

        // Assert
        error.Code.Should().Be(ErrorCodes.AgeTooLow);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["MinAge"].Should().Be(18);
    }

    [Fact]
    public void CreateAgeTooHighError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateAgeTooHighError(65);

        // Assert
        error.Code.Should().Be(ErrorCodes.AgeTooHigh);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["MaxAge"].Should().Be(65);
    }

    [Fact]
    public void CreateMemberNotFoundError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateMemberNotFoundError("EMP001");

        // Assert
        error.Code.Should().Be(ErrorCodes.MemberNotFound);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["MemberId"].Should().Be("EMP001");
    }

    [Fact]
    public void CreateMemberIdTakenError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateMemberIdTakenError("EMP001", "existing-id");

        // Assert
        error.Code.Should().Be(ErrorCodes.MemberIdTaken);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["MemberId"].Should().Be("EMP001");
        error.Context["ExistingPolicyMemberId"].Should().Be("existing-id");
    }

    [Fact]
    public void CreateMemberNotInContractHolderError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateMemberNotInContractHolderError("EMP001", "POL001");

        // Assert
        error.Code.Should().Be(ErrorCodes.MemberNotInContractHolder);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["MemberId"].Should().Be("EMP001");
        error.Context["PolicyId"].Should().Be("POL001");
    }

    [Fact]
    public void CreateInvalidPlanIdError_ShouldCreateCorrectError()
    {
        // Arrange
        string[] availablePlans = ["PLAN_A", "PLAN_B", "PLAN_C"];

        // Act
        ValidationError error = _testField.CreateInvalidPlanIdError("INVALID_PLAN", availablePlans, "test-product");

        // Assert
        error.Code.Should().Be(ErrorCodes.InvalidPlanId);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["PlanId"].Should().Be("INVALID_PLAN");
        error.Context["AvailablePlans"].Should().BeEquivalentTo(availablePlans);
        error.Context["ProductId"].Should().Be("test-product");
    }

    [Fact]
    public void CreateDependentPlanMismatchError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateDependentPlanMismatchError();

        // Assert
        error.Code.Should().Be(ErrorCodes.DependentPlanMismatch);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
    }

    [Fact]
    public void CreateDependentPlanError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateDependentPlanError();

        // Assert
        error.Code.Should().Be(ErrorCodes.DependentPlanMismatch);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        // Note: Message is now handled by the error code's default message, not context
    }

    [Fact]
    public void CreateNotAllowedError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateNotAllowedError();

        // Assert
        error.Code.Should().Be(ErrorCodes.NotAllowed);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
    }

    [Fact]
    public void AllExtensionMethods_WithFieldWithoutLabel_ShouldUseNameAsLabel()
    {
        // Arrange
        var fieldWithoutLabel = new PolicyMemberFieldDefinition
        {
            Name = "testField",
            Label = "",
            Type = new StringFieldType(),
            IsRequired = false,
            IsUnique = false
        };

        // Act
        ValidationError requiredError = fieldWithoutLabel.CreateRequiredError();
        ValidationError formatError = fieldWithoutLabel.CreateInvalidFormatError();

        // Assert
        requiredError.PropertyLabel.Should().Be("testField");
        formatError.PropertyLabel.Should().Be("testField");
    }
}