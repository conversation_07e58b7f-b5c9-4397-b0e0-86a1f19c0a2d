using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.Common.Validation;

/// <summary>
/// Unit tests for ValidationError record to ensure proper behavior and immutability.
/// </summary>
public class ValidationErrorTests
{
    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateValidationError()
    {
        // Arrange
        const string errorCode = ErrorCodes.Required;
        const string propertyPath = "email";
        const string propertyLabel = "Email Address";

        // Act
        var error = new ValidationError(errorCode, propertyPath, propertyLabel);

        // Assert
        error.Code.Should().Be(errorCode);
        error.PropertyPath.Should().Be(propertyPath);
        error.PropertyLabel.Should().Be(propertyLabel);
        error.Context.Should().NotBeNull();
        error.Context.Should().BeEmpty();
    }

    [Fact]
    public void Constructor_WithNullPropertyLabel_ShouldUsePropertyPath()
    {
        // Arrange
        const string errorCode = ErrorCodes.Required;
        const string propertyPath = "email";

        // Act
        var error = new ValidationError(errorCode, propertyPath);

        // Assert
        error.Code.Should().Be(errorCode);
        error.PropertyPath.Should().Be(propertyPath);
        error.PropertyLabel.Should().Be(propertyPath);
    }

    [Fact]
    public void Constructor_WithEmptyPropertyLabel_ShouldUsePropertyPath()
    {
        // Arrange
        const string errorCode = ErrorCodes.Required;
        const string propertyPath = "email";

        // Act
        var error = new ValidationError(errorCode, propertyPath, "");

        // Assert
        error.PropertyLabel.Should().Be(propertyPath);
    }

    [Fact]
    public void Constructor_WithWhitespacePropertyLabel_ShouldUsePropertyPath()
    {
        // Arrange
        const string errorCode = ErrorCodes.Required;
        const string propertyPath = "email";

        // Act
        var error = new ValidationError(errorCode, propertyPath, "   ");

        // Assert
        error.PropertyLabel.Should().Be(propertyPath);
    }

    [Fact]
    public void Constructor_WithContext_ShouldStoreContext()
    {
        // Arrange
        const string errorCode = ErrorCodes.OutOfRange;
        const string propertyPath = "age";
        var context = new Dictionary<string, object?>
        {
            { "MinValue", 18 },
            { "MaxValue", 65 },
            { "ActualValue", 16 }
        };

        // Act
        var error = new ValidationError(errorCode, propertyPath, "Age", context);

        // Assert
        error.Context.Should().BeEquivalentTo(context);
        error.Context["MinValue"].Should().Be(18);
        error.Context["MaxValue"].Should().Be(65);
        error.Context["ActualValue"].Should().Be(16);
    }

    [Fact]
    public void Message_WithBasicError_ShouldGenerateCorrectMessage()
    {
        // Arrange
        var error = new ValidationError(ErrorCodes.Required, "email", "Email Address");

        // Act
        string message = error.Message;

        // Assert
        message.Should().Be("Email Address is required");
    }

    [Fact]
    public void Message_WithInvalidFormatError_ShouldGenerateCorrectMessage()
    {
        // Arrange
        var error = new ValidationError(ErrorCodes.InvalidFormat, "phone", "Phone Number");

        // Act
        string message = error.Message;

        // Assert
        message.Should().Be("Phone Number has an invalid format");
    }

    [Fact]
    public void Message_WithOutOfRangeError_ShouldIncludeContextValues()
    {
        // Arrange
        var context = new Dictionary<string, object?>
        {
            { "MinValue", 18 },
            { "MaxValue", 65 }
        };
        var error = new ValidationError(ErrorCodes.OutOfRange, "age", "Age", context);

        // Act
        string message = error.Message;

        // Assert
        message.Should().Be("Age must be between 18 and 65");
    }

    [Fact]
    public void Message_WithUniqueViolationError_ShouldIncludeScope()
    {
        // Arrange
        var context = new Dictionary<string, object?> { { "Scope", "database" } };
        var error = new ValidationError(ErrorCodes.UniqueViolation, "username", "Username", context);

        // Act
        string message = error.Message;

        // Assert
        message.Should().Be("Username must be unique within database");
    }

    [Fact]
    public void Message_WithUnknownErrorCode_ShouldReturnGenericMessage()
    {
        // Arrange
        var error = new ValidationError("UNKNOWN_ERROR", "field", "Field");

        // Act
        string message = error.Message;

        // Assert
        message.Should().Be("Field validation failed");
    }

    [Fact]
    public void Equality_WithSameValues_ShouldBeEqual()
    {
        // Arrange
        var error1 = new ValidationError(ErrorCodes.Required, "email", "Email");
        var error2 = new ValidationError(ErrorCodes.Required, "email", "Email");

        // Act & Assert
        error1.Should().Be(error2);
        error1.GetHashCode().Should().Be(error2.GetHashCode());
    }

    [Fact]
    public void Equality_WithDifferentValues_ShouldNotBeEqual()
    {
        // Arrange
        var error1 = new ValidationError(ErrorCodes.Required, "email", "Email");
        var error2 = new ValidationError(ErrorCodes.Required, "phone", "Phone");

        // Act & Assert
        error1.Should().NotBe(error2);
    }

    [Fact]
    public void ToString_ShouldReturnMeaningfulRepresentation()
    {
        // Arrange
        var error = new ValidationError(ErrorCodes.Required, "email", "Email Address");

        // Act
        string result = error.ToString();

        // Assert
        result.Should().Contain(ErrorCodes.Required);
        result.Should().Contain("email");
        result.Should().Contain("Email Address");
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void Constructor_WithInvalidErrorCode_ShouldThrowArgumentException(string? errorCode)
    {
        // Act & Assert
        Func<ValidationError> act = () => new ValidationError(errorCode!, "property", "Property");
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void Constructor_WithInvalidPropertyPath_ShouldThrowArgumentException(string? propertyPath)
    {
        // Act & Assert
        Func<ValidationError> act = () => new ValidationError(ErrorCodes.Required, propertyPath!, "Property");
        act.Should().Throw<ArgumentException>();
    }
}