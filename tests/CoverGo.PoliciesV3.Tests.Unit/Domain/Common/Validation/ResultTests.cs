using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.Common.Validation;

/// <summary>
/// Unit tests for the Result<T> class to ensure proper success/failure handling.
/// </summary>
public class ResultTests
{
    [Fact]
    public void Success_WithValue_ShouldCreateSuccessResult()
    {
        // Arrange
        const string value = "test value";

        // Act
        var result = Result<string>.Success(value);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.IsFailure.Should().BeFalse();
        result.Value.Should().Be(value);
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void Success_WithNullValue_ShouldCreateSuccessResult()
    {
        // Act
        var result = Result<string?>.Success(null);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.IsFailure.Should().BeFalse();
        result.Value.Should().BeNull();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void Failure_WithSingleError_ShouldCreateFailureResult()
    {
        // Arrange
        var error = new ValidationError(ErrorCodes.Required, "email", "Email");

        // Act
        var result = Result<string>.Failure(error);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().ContainSingle().Which.Should().Be(error);
    }

    [Fact]
    public void Failure_WithMultipleErrors_ShouldCreateFailureResult()
    {
        // Arrange
        ValidationError[] errors =
        [
            new ValidationError(ErrorCodes.Required, "email", "Email"),
            new ValidationError(ErrorCodes.InvalidFormat, "phone", "Phone")
        ];

        // Act
        var result = Result<string>.Failure(errors);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().BeEquivalentTo(errors);
    }

    [Fact]
    public void Failure_WithErrorList_ShouldCreateFailureResult()
    {
        // Arrange
        var errors = new List<ValidationError>
        {
            new(ErrorCodes.Required, "email", "Email"),
            new(ErrorCodes.InvalidFormat, "phone", "Phone")
        };

        // Act
        var result = Result<string>.Failure(errors);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().BeEquivalentTo(errors);
    }

    [Fact]
    public void Value_OnFailureResult_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var error = new ValidationError(ErrorCodes.Required, "email", "Email");
        var result = Result<string>.Failure(error);

        // Act & Assert
        Func<string> act = () => result.Value;
        act.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot access Value on a failed result. Check IsSuccess first.");
    }

    [Fact]
    public void Errors_OnSuccessResult_ShouldReturnEmptyList()
    {
        // Arrange
        var result = Result<string>.Success("test");

        // Act
        IReadOnlyList<ValidationError> errors = result.Errors;

        // Assert
        errors.Should().BeEmpty();
    }

    [Fact]
    public void ImplicitConversion_FromValue_ShouldCreateSuccessResult()
    {
        // Act
        Result<string> result = "test value";

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be("test value");
    }

    [Fact]
    public void ImplicitConversion_FromValidationError_ShouldCreateFailureResult()
    {
        // Arrange
        var error = new ValidationError(ErrorCodes.Required, "email", "Email");

        // Act
        Result<string> result = error;

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().ContainSingle().Which.Should().Be(error);
    }

    [Fact]
    public void ImplicitConversion_FromValidationErrorArray_ShouldCreateFailureResult()
    {
        // Arrange
        ValidationError[] errors =
        [
            new ValidationError(ErrorCodes.Required, "email", "Email"),
            new ValidationError(ErrorCodes.InvalidFormat, "phone", "Phone")
        ];

        // Act
        Result<string> result = errors;

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().BeEquivalentTo(errors);
    }

    [Fact]
    public void ToString_OnSuccessResult_ShouldReturnSuccessMessage()
    {
        // Arrange
        var result = Result<string>.Success("test");

        // Act
        string stringResult = result.ToString();

        // Assert
        stringResult.Should().Contain("Success");
        stringResult.Should().Contain("test");
    }

    [Fact]
    public void ToString_OnFailureResult_ShouldReturnFailureMessage()
    {
        // Arrange
        var error = new ValidationError(ErrorCodes.Required, "email", "Email");
        var result = Result<string>.Failure(error);

        // Act
        string stringResult = result.ToString();

        // Assert
        stringResult.Should().Contain("Failure");
        stringResult.Should().Contain("1 error");
    }

    [Fact]
    public void ToString_OnFailureResultWithMultipleErrors_ShouldReturnCorrectCount()
    {
        // Arrange
        ValidationError[] errors =
        [
            new ValidationError(ErrorCodes.Required, "email", "Email"),
            new ValidationError(ErrorCodes.InvalidFormat, "phone", "Phone"),
            new ValidationError(ErrorCodes.OutOfRange, "age", "Age")
        ];
        var result = Result<string>.Failure(errors);

        // Act
        string stringResult = result.ToString();

        // Assert
        stringResult.Should().Contain("Failure");
        stringResult.Should().Contain("3 errors");
    }

    [Fact]
    public void Equality_SuccessResults_WithSameValue_ShouldBeEqual()
    {
        // Arrange
        var result1 = Result<string>.Success("test");
        var result2 = Result<string>.Success("test");

        // Act & Assert
        result1.Should().BeEquivalentTo(result2);
        result1.GetHashCode().Should().Be(result2.GetHashCode());
    }

    [Fact]
    public void Equality_SuccessResults_WithDifferentValues_ShouldNotBeEqual()
    {
        // Arrange
        var result1 = Result<string>.Success("test1");
        var result2 = Result<string>.Success("test2");

        // Act & Assert
        result1.Equals(result2).Should().BeFalse();
        (result1 == result2).Should().BeFalse();
        result1.GetHashCode().Should().NotBe(result2.GetHashCode());
    }

    [Fact]
    public void Equality_FailureResults_WithSameErrors_ShouldBeEqual()
    {
        // Arrange
        var error = new ValidationError(ErrorCodes.Required, "email", "Email");
        var result1 = Result<string>.Failure(error);
        var result2 = Result<string>.Failure(error);

        // Act & Assert
        result1.Should().BeEquivalentTo(result2);
        result1.GetHashCode().Should().Be(result2.GetHashCode());
    }

    [Fact]
    public void Equality_SuccessAndFailure_ShouldNotBeEqual()
    {
        // Arrange
        var successResult = Result<string>.Success("test");
        var failureResult = Result<string>.Failure(new ValidationError(ErrorCodes.Required, "field", "Field"));

        // Act & Assert
        successResult.Should().NotBeEquivalentTo(failureResult);
    }

    [Fact]
    public void Failure_WithNullError_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Func<Result<string>> act = () => Result<string>.Failure((ValidationError)null!);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Failure_WithNullErrorArray_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Func<Result<string>> act = () => Result<string>.Failure((ValidationError[])null!);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Failure_WithEmptyErrorArray_ShouldThrowArgumentException()
    {
        // Act & Assert
        Func<Result<string>> act = () => Result<string>.Failure();
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void Failure_WithNullErrorList_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Func<Result<string>> act = () => Result<string>.Failure((List<ValidationError>)null!);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Failure_WithEmptyErrorList_ShouldThrowArgumentException()
    {
        // Act & Assert
        Func<Result<string>> act = () => Result<string>.Failure(new List<ValidationError>());
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void GenericResult_WithComplexType_ShouldWorkCorrectly()
    {
        // Arrange
        var complexObject = new { Name = "Test", Value = 42 };

        // Act
        var result = Result<object>.Success(complexObject);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(complexObject);
    }

    [Fact]
    public void GenericResult_WithValueType_ShouldWorkCorrectly()
    {
        // Act
        var result = Result<int>.Success(42);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(42);
    }
}