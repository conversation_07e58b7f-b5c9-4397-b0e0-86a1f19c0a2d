using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.Common.Validation;

/// <summary>
/// Unit tests for ValidationErrorBuilder to ensure proper fluent API behavior.
/// </summary>
public class ValidationErrorBuilderTests
{
    [Fact]
    public void For_WithPropertyPath_ShouldCreateBuilder()
    {
        // Act
        var builder = ValidationErrorBuilder.For("email");

        // Assert
        builder.Should().NotBeNull();
    }

    [Fact]
    public void WithLabel_ShouldSetPropertyLabel()
    {
        // Arrange
        var builder = ValidationErrorBuilder.For("email");

        // Act
        ValidationErrorBuilder result = builder.WithLabel("Email Address");

        // Assert
        result.Should().BeSameAs(builder); // Fluent interface
    }

    [Fact]
    public void WithContext_ShouldAddContextValue()
    {
        // Arrange
        var builder = ValidationErrorBuilder.For("age");

        // Act
        ValidationErrorBuilder result = builder.WithContext("MinValue", 18);

        // Assert
        result.Should().BeSameAs(builder); // Fluent interface
    }

    [Fact]
    public void WithContext_MultipleValues_ShouldAddAllValues()
    {
        // Arrange
        var builder = ValidationErrorBuilder.For("age");

        // Act
        builder.WithContext("MinValue", 18)
            .WithContext("MaxValue", 65)
            .WithContext("ActualValue", 16);

        ValidationError error = builder.Build("OUT_OF_RANGE");

        // Assert
        error.Context["MinValue"].Should().Be(18);
        error.Context["MaxValue"].Should().Be(65);
        error.Context["ActualValue"].Should().Be(16);
    }

    [Fact]
    public void Required_ShouldCreateRequiredError()
    {
        // Arrange
        ValidationErrorBuilder builder = ValidationErrorBuilder.For("email").WithLabel("Email Address");

        // Act
        ValidationError error = builder.Required();

        // Assert
        error.Code.Should().Be(ErrorCodes.Required);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Message.Should().Be("Email Address is required");
    }

    [Fact]
    public void InvalidFormat_ShouldCreateInvalidFormatError()
    {
        // Arrange
        ValidationErrorBuilder builder = ValidationErrorBuilder.For("phone").WithLabel("Phone Number");

        // Act
        ValidationError error = builder.InvalidFormat();

        // Assert
        error.Code.Should().Be(ErrorCodes.InvalidFormat);
        error.PropertyPath.Should().Be("phone");
        error.PropertyLabel.Should().Be("Phone Number");
        error.Message.Should().Be("Phone Number has an invalid format");
    }

    [Fact]
    public void UniqueViolation_WithDefaultScope_ShouldCreateUniqueError()
    {
        // Arrange
        ValidationErrorBuilder builder = ValidationErrorBuilder.For("username").WithLabel("Username");

        // Act
        ValidationError error = builder.UniqueViolation();

        // Assert
        error.Code.Should().Be(ErrorCodes.UniqueViolation);
        error.PropertyPath.Should().Be("username");
        error.PropertyLabel.Should().Be("Username");
        error.Context["Scope"].Should().Be("upload");
        error.Message.Should().Be("Username must be unique within upload");
    }

    [Fact]
    public void UniqueViolation_WithCustomScope_ShouldCreateUniqueError()
    {
        // Arrange
        ValidationErrorBuilder builder = ValidationErrorBuilder.For("email").WithLabel("Email");

        // Act
        ValidationError error = builder.UniqueViolation("database");

        // Assert
        error.Code.Should().Be(ErrorCodes.UniqueViolation);
        error.Context["Scope"].Should().Be("database");
        error.Message.Should().Be("Email must be unique within database");
    }

    [Fact]
    public void InvalidOption_ShouldCreateInvalidOptionError()
    {
        // Arrange
        ValidationErrorBuilder builder = ValidationErrorBuilder.For("status").WithLabel("Status");
        string[] availableOptions = ["active", "inactive", "pending"];

        // Act
        ValidationError error = builder.InvalidOption(availableOptions);

        // Assert
        error.Code.Should().Be(ErrorCodes.InvalidOption);
        error.PropertyPath.Should().Be("status");
        error.PropertyLabel.Should().Be("Status");
        error.Context["AvailableOptions"].Should().BeEquivalentTo(availableOptions);
    }

    [Fact]
    public void OutOfRange_ShouldCreateOutOfRangeError()
    {
        // Arrange
        ValidationErrorBuilder builder = ValidationErrorBuilder.For("age").WithLabel("Age");

        // Act
        ValidationError error = builder.OutOfRange(18, 65);

        // Assert
        error.Code.Should().Be(ErrorCodes.OutOfRange);
        error.PropertyPath.Should().Be("age");
        error.PropertyLabel.Should().Be("Age");
        error.Context["MinValue"].Should().Be(18);
        error.Context["MaxValue"].Should().Be(65);
        error.Message.Should().Be("Age must be between 18 and 65");
    }

    [Fact]
    public void PatternMismatch_ShouldCreatePatternMismatchError()
    {
        // Arrange
        ValidationErrorBuilder builder = ValidationErrorBuilder.For("phone").WithLabel("Phone Number");
        const string pattern = @"^\d{3}-\d{3}-\d{4}$";

        // Act
        ValidationError error = builder.PatternMismatch(pattern);

        // Assert
        error.Code.Should().Be(ErrorCodes.PatternMismatch);
        error.PropertyPath.Should().Be("phone");
        error.PropertyLabel.Should().Be("Phone Number");
        error.Context["Pattern"].Should().Be(pattern);
    }

    [Fact]
    public void Build_WithCustomErrorCode_ShouldCreateCustomError()
    {
        // Arrange
        ValidationErrorBuilder builder = ValidationErrorBuilder.For("custom")
            .WithLabel("Custom Field")
            .WithContext("CustomData", "test");

        // Act
        ValidationError error = builder.Build("CUSTOM_ERROR");

        // Assert
        error.Code.Should().Be("CUSTOM_ERROR");
        error.PropertyPath.Should().Be("custom");
        error.PropertyLabel.Should().Be("Custom Field");
        error.Context["CustomData"].Should().Be("test");
    }

    [Fact]
    public void FluentChaining_ShouldWorkCorrectly()
    {
        // Act
        ValidationError error = ValidationErrorBuilder.For("age")
            .WithLabel("Employee Age")
            .WithContext("MinValue", 18)
            .WithContext("MaxValue", 65)
            .WithContext("ActualValue", 16)
            .OutOfRange(18, 65);

        // Assert
        error.Code.Should().Be(ErrorCodes.OutOfRange);
        error.PropertyPath.Should().Be("age");
        error.PropertyLabel.Should().Be("Employee Age");
        error.Context["MinValue"].Should().Be(18);
        error.Context["MaxValue"].Should().Be(65);
        error.Context["ActualValue"].Should().Be(16);
        error.Message.Should().Be("Employee Age must be between 18 and 65");
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void For_WithInvalidPropertyPath_ShouldThrowArgumentException(string? propertyPath)
    {
        // Act & Assert
        Func<ValidationErrorBuilder> act = () => ValidationErrorBuilder.For(propertyPath!);
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void Build_WithInvalidErrorCode_ShouldThrowArgumentException(string? errorCode)
    {
        // Arrange
        var builder = ValidationErrorBuilder.For("test");

        // Act & Assert
        Func<ValidationError> act = () => builder.Build(errorCode!);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void WithContext_WithNullKey_ShouldThrowArgumentException()
    {
        // Arrange
        var builder = ValidationErrorBuilder.For("test");

        // Act & Assert
        Func<ValidationErrorBuilder> act = () => builder.WithContext(null!, "value");
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void WithContext_WithEmptyKey_ShouldThrowArgumentException()
    {
        // Arrange
        var builder = ValidationErrorBuilder.For("test");

        // Act & Assert
        Func<ValidationErrorBuilder> act = () => builder.WithContext("", "value");
        act.Should().Throw<ArgumentException>();
    }
}