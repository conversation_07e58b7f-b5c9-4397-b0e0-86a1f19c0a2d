using CoverGo.PoliciesV3.Domain.Common.Extensions;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.Common.Extensions;

/// <summary>
/// Unit tests for DictionaryExtensions to ensure proper dictionary value retrieval.
/// </summary>
public class DictionaryExtensionsTests
{
    [Fact]
    public void TryGetValueOrDefault_WithExistingKey_ShouldReturnValue()
    {
        // Arrange
        var dictionary = new Dictionary<string, string?>
        {
            ["key1"] = "value1",
            ["key2"] = "value2"
        };

        // Act
        string? result = ((IDictionary<string, string?>)dictionary).TryGetValueOrDefault("key1");

        // Assert
        result.Should().Be("value1");
    }

    [Fact]
    public void TryGetValueOrDefault_WithNonExistingKey_ShouldReturnDefault()
    {
        // Arrange
        var dictionary = new Dictionary<string, string?>
        {
            ["key1"] = "value1"
        };

        // Act
        string? result = ((IDictionary<string, string?>)dictionary).TryGetValueOrDefault("nonExistingKey");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void TryGetValueOrDefault_WithNonExistingKeyAndCustomDefault_ShouldReturnCustomDefault()
    {
        // Arrange
        var dictionary = new Dictionary<string, string?>
        {
            ["key1"] = "value1"
        };
        const string customDefault = "custom default";

        // Act
        string? result = ((IDictionary<string, string?>)dictionary).TryGetValueOrDefault("nonExistingKey", customDefault);

        // Assert
        result.Should().Be(customDefault);
    }

    [Fact]
    public void TryGetValueOrDefault_WithNullValue_ShouldReturnNull()
    {
        // Arrange
        var dictionary = new Dictionary<string, string?>
        {
            ["key1"] = null
        };

        // Act
        string? result = ((IDictionary<string, string?>)dictionary).TryGetValueOrDefault("key1");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void TryGetValueOrDefault_WithIntegerDictionary_ShouldWork()
    {
        // Arrange
        var dictionary = new Dictionary<string, int>
        {
            ["count"] = 42
        };

        // Act
        int result = ((IDictionary<string, int>)dictionary).TryGetValueOrDefault("count");
        int defaultResult = ((IDictionary<string, int>)dictionary).TryGetValueOrDefault("nonExisting");

        // Assert
        result.Should().Be(42);
        defaultResult.Should().Be(0); // default for int
    }

    [Fact]
    public void TryGetValueOrDefault_WithIntegerDictionaryAndCustomDefault_ShouldWork()
    {
        // Arrange
        var dictionary = new Dictionary<string, int>
        {
            ["count"] = 42
        };

        // Act
        int result = ((IDictionary<string, int>)dictionary).TryGetValueOrDefault("nonExisting", 999);

        // Assert
        result.Should().Be(999);
    }
}
