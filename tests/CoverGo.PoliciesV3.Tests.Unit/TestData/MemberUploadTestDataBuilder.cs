using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Products;
using CoverGo.PoliciesV3.Tests.Unit.Common;

namespace CoverGo.PoliciesV3.Tests.Unit.TestData;

/// <summary>
/// Test data builder for creating realistic member upload test scenarios.
/// Supports different dataset sizes and complexity levels for comprehensive testing.
/// </summary>
public class MemberUploadTestDataBuilder
{
    private string? _policyId;

    public MemberUploadTestDataBuilder WithPolicyId(string policyId)
    {
        _policyId = policyId;
        return this;
    }
    private int _memberCount = 100;
    private double _duplicatePercentage = 0.1; // 10% duplicates by default
    private bool _includeDependents = true;
    private bool _includeExistingMembers = true;
    private bool _includeComplexScenarios = false;
    private List<string> _availablePlanIds = ["PLAN-001", "PLAN-002", "PLAN-003"];
    private Random _random = new(42); // Fixed seed for reproducible tests

    public static MemberUploadTestDataBuilder Create() => new();

    public MemberUploadTestDataBuilder WithMemberCount(int count)
    {
        _memberCount = count;
        return this;
    }

    public MemberUploadTestDataBuilder WithDuplicatePercentage(double percentage)
    {
        _duplicatePercentage = Math.Max(0, Math.Min(1, percentage));
        return this;
    }

    public MemberUploadTestDataBuilder WithDependents(bool includeDependents = true)
    {
        _includeDependents = includeDependents;
        return this;
    }

    public MemberUploadTestDataBuilder WithExistingMembers(bool includeExisting = true)
    {
        _includeExistingMembers = includeExisting;
        return this;
    }

    public MemberUploadTestDataBuilder WithComplexScenarios(bool includeComplex = true)
    {
        _includeComplexScenarios = includeComplex;
        return this;
    }

    public MemberUploadTestDataBuilder WithAvailablePlans(params string[] planIds)
    {
        _availablePlanIds = [.. planIds];
        return this;
    }

    public MemberUploadTestDataBuilder WithRandomSeed(int seed)
    {
        _random = new Random(seed);
        return this;
    }

    /// <summary>
    /// Creates a small dataset (100 members, 10% duplicates) for basic testing
    /// </summary>
    public static MemberUploadTestDataBuilder SmallDataset() =>
        Create().WithMemberCount(100).WithDuplicatePercentage(0.1);

    /// <summary>
    /// Creates a medium dataset (1,000 members, 5% duplicates) for integration testing
    /// </summary>
    public static MemberUploadTestDataBuilder MediumDataset() =>
        Create().WithMemberCount(1000).WithDuplicatePercentage(0.05);

    /// <summary>
    /// Creates a large dataset (5,000 members, 2% duplicates) for performance testing
    /// </summary>
    public static MemberUploadTestDataBuilder LargeDataset() =>
        Create().WithMemberCount(5000).WithDuplicatePercentage(0.02);

    /// <summary>
    /// Creates a complex scenario dataset with dependents, existing members, and edge cases
    /// </summary>
    public static MemberUploadTestDataBuilder ComplexScenario() =>
        Create()
            .WithMemberCount(500)
            .WithDuplicatePercentage(0.08)
            .WithDependents(true)
            .WithExistingMembers(true)
            .WithComplexScenarios(true);

    /// <summary>
    /// Builds the member upload fields data
    /// </summary>
    public MembersUploadFields BuildMembersUploadFields()
    {
        var memberDataList = new List<IReadOnlyDictionary<string, string?>>();
        var duplicateTracker = new DuplicateTracker();

        for (int i = 0; i < _memberCount; i++)
        {
            Dictionary<string, string?> memberData = CreateMemberData(i, duplicateTracker);
            memberDataList.Add(memberData);
        }

        var memberUploadFields = memberDataList
            .Select(memberData => new MemberUploadFields(memberData))
            .ToList();

        return memberUploadFields.Count == 0
            ? MembersUploadFields.Empty()
            : new MembersUploadFields(memberUploadFields);
    }

    /// <summary>
    /// Builds a policy member upload entity
    /// </summary>
    public PolicyMemberUpload BuildPolicyMemberUpload(PolicyId? policyId = null)
    {
        PolicyId actualPolicyId = policyId ?? PolicyId.New;
        var upload = PolicyMemberUpload.Create(
            actualPolicyId,
            $"/uploads/test-upload-{Guid.NewGuid()}.xlsx",
            _memberCount,
            null);

        // Keep upload in REGISTERED state for validation testing
        // The handler will call StartValidating() itself
        return upload;
    }

    /// <summary>
    /// Builds a test policy DTO
    /// </summary>
    public PolicyDto BuildPolicyDto(string? policyId = null) => new()
    {
        Id = policyId ?? Guid.NewGuid().ToString(),
        ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
        ContractHolderId = Guid.NewGuid().ToString(),
        StartDate = TestDateConstants.PolicyDates.PolicyStart,
        EndDate = TestDateConstants.PolicyDates.PolicyEnd,
        IsIssued = false,
        Endorsements = [],
        ApprovedEndorsementIds = []
    };

    /// <summary>
    /// Builds a test schema with common fields
    /// </summary>
    public PolicyMemberFieldsSchema BuildSchema()
    {
        var fields = new List<PolicyMemberFieldDefinition>
        {
            CreateFieldDefinition("memberId", "Member ID", false, true),
            CreateFieldDefinition("planId", "Plan ID", true, false),
            CreateFieldDefinition("effectiveDate", "Effective Date", true, false),
            CreateFieldDefinition("email", "Email", true, false, isUnique: true),
            CreateFieldDefinition("name", "Name", true, false),
            CreateFieldDefinition("memberType", "Member Type", true, false),
            CreateFieldDefinition("hkid", "HKID", false, false, isUnique: true),
            CreateFieldDefinition("passportNo", "Passport Number", false, false, isUnique: true),
            CreateFieldDefinition("staffNo", "Staff Number", false, false, isUnique: true),
            CreateFieldDefinition("firstName", "First Name", false, false),
            CreateFieldDefinition("lastName", "Last Name", false, false),
            CreateFieldDefinition("dateOfBirth", "Date of Birth", true, false),
            CreateFieldDefinition("dependentOf", "Dependent Of", false, false)
        };

        return new PolicyMemberFieldsSchema(fields);
    }

    private Dictionary<string, string?> CreateMemberData(int index, DuplicateTracker duplicateTracker)
    {
        var memberData = new Dictionary<string, string?>();
        bool isExistingMember = _includeExistingMembers && _random.NextDouble() < 0.3; // 30% existing members

        // Ensure first member is always an employee to serve as primary for dependents
        // For subsequent members, 20% chance of being dependent
        bool isDependent = _includeDependents && index > 0 && _random.NextDouble() < 0.2;

        // Member ID (for existing members only) - use field name as key
        if (isExistingMember)
        {
            memberData["memberId"] = duplicateTracker.GetMemberId(index, _duplicatePercentage);
        }

        // Required fields - use field names as keys for validation testing
        memberData["planId"] = _availablePlanIds[_random.Next(_availablePlanIds.Count)];
        memberData["effectiveDate"] = DateTime.Today.AddDays(_random.Next(-30, 30)).ToString("yyyy-MM-dd");
        memberData["firstName"] = $"FirstName{index:D4}";
        memberData["lastName"] = $"LastName{index:D4}";
        memberData["name"] = $"FirstName{index:D4} LastName{index:D4}";
        memberData["memberType"] = isDependent ? "dependent" : "employee";
        memberData["dateOfBirth"] = DateTime.Today.AddYears(-_random.Next(18, 65)).ToString("yyyy-MM-dd");

        // Email with potential duplicates - use field name as key
        memberData["email"] = duplicateTracker.GetEmail(index, _duplicatePercentage);

        // Identification fields with potential duplicates - use field names as keys
        if (_random.NextDouble() < 0.7) // 70% have HKID
        {
            memberData["hkid"] = duplicateTracker.GetHKID(index, _duplicatePercentage);
        }
        else if (_random.NextDouble() < 0.5) // 50% of remaining have passport
        {
            memberData["passportNo"] = duplicateTracker.GetPassport(index, _duplicatePercentage);
        }

        if (_random.NextDouble() < 0.4) // 40% have staff number
        {
            memberData["staffNo"] = duplicateTracker.GetStaffNumber(index, _duplicatePercentage);
        }

        // Dependent relationship - use field name as key
        if (isDependent)
        {
            // Reference the first member (index 0) as primary, since we ensure it's always an employee
            memberData["dependentOf"] = "FirstName0000 LastName0000";
        }

        return memberData;
    }

    private static PolicyMemberFieldDefinition CreateFieldDefinition(
        string name,
        string displayName,
        bool isRequired,
        bool isReadOnly,
        bool isUnique = false) => new()
    {
        Name = name,
        Label = displayName,
        IsRequired = isRequired,
        IsUnique = isUnique,
        Type = new StringFieldType()
    };

    /// <summary>
    /// Helper class to track and generate duplicate values for testing
    /// </summary>
    private class DuplicateTracker
    {
        private readonly Dictionary<string, List<string>> _usedValues = [];
        private readonly Random _random = new(42);

        public string GetMemberId(int index, double duplicateRate) => GetValueWithDuplicates("memberId", index, duplicateRate,
            i => $"EMP{i:D6}", "EMP000001");

        public string GetEmail(int index, double duplicateRate) => GetValueWithDuplicates("email", index, duplicateRate,
            i => $"user{i:D4}@test.com", "<EMAIL>");

        public string GetHKID(int index, double duplicateRate) => GetValueWithDuplicates("hkid", index, duplicateRate,
            i => $"A{i:D6}({i % 10})", "A123456(7)");

        public string GetPassport(int index, double duplicateRate) => GetValueWithDuplicates("passport", index, duplicateRate,
            i => $"P{i:D7}", "P1234567");

        public string GetStaffNumber(int index, double duplicateRate) => GetValueWithDuplicates("staffNo", index, duplicateRate,
            i => $"STAFF{i:D4}", "STAFF0001");

        private string GetValueWithDuplicates(string fieldType, int index, double duplicateRate,
            Func<int, string> uniqueGenerator, string duplicateValue)
        {
            if (!_usedValues.ContainsKey(fieldType))
                _usedValues[fieldType] = [];

            // Decide if this should be a duplicate
            if (_random.NextDouble() < duplicateRate && _usedValues[fieldType].Count > 0)
            {
                // Return a previously used value (duplicate)
                return _usedValues[fieldType][_random.Next(_usedValues[fieldType].Count)];
            }

            // Generate unique value
            string uniqueValue = uniqueGenerator(index);
            _usedValues[fieldType].Add(uniqueValue);
            return uniqueValue;
        }
    }
}