using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Tests.Unit.TestData;

public static class ResolvedValidationDataTestDataBuilder
{
    public static ResolvedValidationData Create(
        bool useTheSamePlanForEmployeeAndDependents = false,
        bool onlyApplyForSmeProducts = false,
        bool allowMembersFromOtherContractHolders = false,
        IReadOnlySet<string>? availablePlans = null,
        bool isProductSme = false,
        IReadOnlyList<string>? contractHolderPolicyIds = null,
        IReadOnlyList<string>? validEndorsementIds = null,
        IReadOnlyList<EndorsementId>? contractHolderScopeEndorsements = null,
        bool isPolicyV2 = false,
        string? tenantId = null,
        IReadOnlyDictionary<string, PolicyMember?>? dependentMembersCache = null,
        IReadOnlySet<string>? existingIndividualIds = null,
        IReadOnlyDictionary<string, PolicyMember?>? existingPolicyMembers = null,
        IReadOnlyDictionary<string, IReadOnlyList<PolicyMember>>? memberValidationStates = null,
        IReadOnlyDictionary<string, bool>? individualExistenceMap = null
    ) => new()
    {
        UseTheSamePlanForEmployeeAndDependents = useTheSamePlanForEmployeeAndDependents,
        OnlyApplyForSmeProducts = onlyApplyForSmeProducts,
        AllowMembersFromOtherContractHolders = allowMembersFromOtherContractHolders,
        AvailablePlans = availablePlans ?? new HashSet<string> { "PLAN-001", "PLAN-002" },
        IsProductSme = isProductSme,
        ContractHolderPolicyIds = contractHolderPolicyIds ?? [],
        ValidEndorsementIds = validEndorsementIds ?? [],
        ContractHolderScopeEndorsements = contractHolderScopeEndorsements ?? [],
        IsPolicyV2 = isPolicyV2,
        TenantId = tenantId,
        DependentMembersCache = dependentMembersCache ?? new Dictionary<string, PolicyMember?>(),
        ExistingIndividualIds = existingIndividualIds ?? new HashSet<string>(),
        ExistingPolicyMembers = existingPolicyMembers ?? new Dictionary<string, PolicyMember?>(),
        MemberValidationStates = memberValidationStates ?? new Dictionary<string, IReadOnlyList<PolicyMember>>(),
        IndividualExistenceMap = individualExistenceMap ?? new Dictionary<string, bool>()
    };
}