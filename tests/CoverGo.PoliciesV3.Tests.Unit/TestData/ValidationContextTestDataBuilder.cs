using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Products;
using CoverGo.PoliciesV3.Tests.Unit.Common;

namespace CoverGo.PoliciesV3.Tests.Unit.TestData;

/// <summary>
/// Test data builder for creating validation contexts used by domain specifications.
/// Provides realistic test scenarios for comprehensive specification testing.
/// </summary>
public class ValidationContextTestDataBuilder
{
    private PolicyDto _policy = CreateDefaultPolicy();
    private PolicyMemberFieldsSchema _schema = CreateDefaultSchema();
    private List<PolicyMember> _existingMembers = [];
    private List<PolicyMember> _memberValidationStates = [];
    private bool _allowMembersFromOtherContractHolders = false;
    private readonly bool _individualExists = false;
    private readonly Guid? _endorsementId = null;
    private string? _memberId;
    private string? _email;
    private string? _hkid;
    private string? _passportNo;
    private string? _staffNo;
    private Dictionary<string, string?> _memberFields = [];

    public static ValidationContextTestDataBuilder Create() => new();

    public ValidationContextTestDataBuilder WithPolicy(PolicyDto policy)
    {
        _policy = policy;
        return this;
    }

    public ValidationContextTestDataBuilder WithSchema(PolicyMemberFieldsSchema schema)
    {
        _schema = schema;
        return this;
    }

    public ValidationContextTestDataBuilder WithExistingMembers(params PolicyMember[] members)
    {
        _existingMembers = [.. members];
        return this;
    }

    public ValidationContextTestDataBuilder WithMemberValidationStates(params PolicyMember[] members)
    {
        _memberValidationStates = [.. members];
        return this;
    }

    public ValidationContextTestDataBuilder WithAllowMembersFromOtherContractHolders(bool allow = true)
    {
        _allowMembersFromOtherContractHolders = allow;
        return this;
    }

    public ValidationContextTestDataBuilder WithMemberId(string? memberId)
    {
        _memberId = memberId;
        if (memberId != null)
            _memberFields["memberId"] = memberId;
        return this;
    }

    public ValidationContextTestDataBuilder WithEmail(string? email)
    {
        _email = email;
        if (email != null)
            _memberFields["email"] = email;
        return this;
    }

    public ValidationContextTestDataBuilder WithHKID(string? hkid)
    {
        _hkid = hkid;
        if (hkid != null)
            _memberFields["hkid"] = hkid;
        return this;
    }

    public ValidationContextTestDataBuilder WithPassport(string? passport)
    {
        _passportNo = passport;
        if (passport != null)
            _memberFields["passportNo"] = passport;
        return this;
    }

    public ValidationContextTestDataBuilder WithStaffNumber(string? staffNo)
    {
        _staffNo = staffNo;
        if (staffNo != null)
            _memberFields["staffNo"] = staffNo;
        return this;
    }

    public ValidationContextTestDataBuilder WithMemberFields(Dictionary<string, string?> fields)
    {
        _memberFields = new Dictionary<string, string?>(fields);
        return this;
    }

    /// <summary>
    /// Builds an MemberIdValidationContext for member ID business rules testing
    /// </summary>
    public MemberIdValidationContext BuildMemberIdValidationContext() => MemberIdValidationContext.Create(
        memberFields: new MemberUploadFields(_memberFields),
        policy: _policy,
        schema: _schema,
        contractHolderPolicyIds: [_policy.Id],
        individualExists: _individualExists,
        allowMembersFromOtherContractHolders: _allowMembersFromOtherContractHolders,
        existingPolicyMember: _existingMembers.FirstOrDefault(),
        memberValidationStates: _memberValidationStates,
        endorsementId: _endorsementId);

    /// <summary>
    /// Builds a UniquenessValidationContext for member uniqueness testing
    /// </summary>
    public UniquenessValidationContext BuildUniquenessValidationContext(
        ValidationScope scope = ValidationScope.Tenant,
        ValidationType validationType = ValidationType.Unique) => UniquenessValidationContext.Builder()
        .WithMemberFields(new MemberUploadFields(_memberFields))
        .WithPolicy(_policy)
        .WithSchema(_schema)
        .WithPolicyId(_policy.Id)
        .WithPlanId("basic-plan")
        .Build();

    /// <summary>
    /// Builds a FieldValidationContext for schema validation testing
    /// </summary>
    public FieldValidationContext BuildFieldValidationContext() => FieldValidationContext.Create(
        memberFields: new MemberUploadFields(_memberFields),
        policy: _policy,
        schema: _schema);

    /// <summary>
    /// Builds an UploadUniquenessValidationContext for upload-wide uniqueness testing
    /// </summary>
    public UploadUniquenessValidationContext BuildUploadUniquenessValidationContext(
        MembersUploadFields membersFields) => UploadUniquenessValidationContext.Create(
        membersFields: membersFields,
        schema: _schema);

    /// <summary>
    /// Builds a DependentValidationContext for dependent member testing
    /// </summary>
    public DependentValidationContext BuildDependentValidationContext(
        List<PolicyMember>? dependentMembersCache = null,
        List<string>? validEndorsementIds = null,
        Guid? endorsementId = null)
    {
        var membersCache = (dependentMembersCache ?? [])
            .ToDictionary(m => m.MemberId, m => (PolicyMember?)m);

        return DependentValidationContext.Create(
            dependentMemberFields: new MemberUploadFields(_memberFields),
            policyId: _policy.Id,
            policy: _policy,
            schema: _schema,
            membersCache: membersCache,
            validEndorsementIds: validEndorsementIds ?? [],
            endorsementId: endorsementId);
    }

    /// <summary>
    /// Creates a test scenario with a member that exists in another policy
    /// </summary>
    public static ValidationContextTestDataBuilder MemberInAnotherPolicy(string memberId)
    {
        PolicyMember otherPolicyMember = CreateTestPolicyMember(memberId, Guid.NewGuid());
        return Create()
            .WithMemberId(memberId)
            .WithMemberValidationStates(otherPolicyMember);
    }

    /// <summary>
    /// Creates a test scenario with a member that exists in the same policy
    /// </summary>
    public static ValidationContextTestDataBuilder MemberInSamePolicy(string memberId, PolicyDto policy)
    {
        PolicyMember samePolicyMember = CreateTestPolicyMember(memberId, Guid.Parse(policy.Id));
        return Create()
            .WithPolicy(policy)
            .WithMemberId(memberId)
            .WithExistingMembers(samePolicyMember);
    }

    /// <summary>
    /// Creates a test scenario with duplicate email addresses
    /// </summary>
    public static ValidationContextTestDataBuilder DuplicateEmail(string email)
    {
        PolicyMember existingMember = CreateTestPolicyMember("EMP001", Guid.NewGuid());
        // Note: In real scenario, we'd set email on the member, but for testing we simulate the conflict
        return Create()
            .WithEmail(email)
            .WithExistingMembers(existingMember);
    }

    /// <summary>
    /// Creates a test scenario with valid member data
    /// </summary>
    public static ValidationContextTestDataBuilder ValidMember() => Create()
        .WithMemberId("EMP001")
        .WithEmail("<EMAIL>")
        .WithHKID("A123456(7)")
        .WithMemberFields(new Dictionary<string, string?>
        {
            ["memberId"] = "EMP001",
            ["email"] = "<EMAIL>",
            ["hkid"] = "A123456(7)",
            ["planId"] = "PLAN-001",
            ["effectiveDate"] = DateTime.Today.ToString("yyyy-MM-dd"),
            ["firstName"] = "John",
            ["lastName"] = "Doe"
        });

    private static PolicyDto CreateDefaultPolicy() => new()
    {
        Id = Guid.NewGuid().ToString(),
        ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
        ContractHolderId = Guid.NewGuid().ToString(),
        StartDate = TestDateConstants.PolicyDates.PolicyStart,
        EndDate = TestDateConstants.PolicyDates.PolicyEnd,
        IsIssued = false,
        Endorsements = [],
        ApprovedEndorsementIds = []
    };

    private static PolicyMemberFieldsSchema CreateDefaultSchema()
    {
        var fields = new List<PolicyMemberFieldDefinition>
        {
            new() { Name = "memberId", Label = "Member ID", IsRequired = false, Type = new StringFieldType() },
            new() { Name = "planId", Label = "Plan ID", IsRequired = true, Type = new StringFieldType() },
            new() { Name = "effectiveDate", Label = "Effective Date", IsRequired = true, Type = new StringFieldType() },
            new() { Name = "email", Label = "Email", IsRequired = true, Type = new StringFieldType(), IsUnique = true },
            new() { Name = "hkid", Label = "HKID", IsRequired = false, Type = new StringFieldType(), IsUnique = true },
            new() { Name = "passportNo", Label = "Passport", IsRequired = false, Type = new StringFieldType(), IsUnique = true },
            new() { Name = "staffNo", Label = "Staff Number", IsRequired = false, Type = new StringFieldType(), IsUnique = true },
            new() { Name = "firstName", Label = "First Name", IsRequired = true, Type = new StringFieldType() },
            new() { Name = "lastName", Label = "Last Name", IsRequired = true, Type = new StringFieldType() }
        };

        return new PolicyMemberFieldsSchema(fields);
    }

    private static PolicyMember CreateTestPolicyMember(string memberId, Guid policyId)
    {
        PolicyId policyIdDomain = policyId;
        return PolicyMember.Create(
            policyIdDomain,
            memberId,
            DateOnly.FromDateTime(DateTime.Today),
            DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            "TEST-PLAN");
    }
}