using System.Collections.Concurrent;
using System.Diagnostics;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Products;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.Users.Client;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Services;

/// <summary>
/// Unit tests for PolicyMemberValidationDataService.
/// Tests the service responsible for gathering validation data for policy member operations.
/// </summary>
public class PolicyMemberValidationDataServiceTests
{
    #region Test Setup and Dependencies

    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IMultiTenantFeatureManager> _mockFeatureManager;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _mockSchemaProvider;
    private readonly Mock<ILogger<PolicyMemberValidationDataService>> _mockLogger;
    private readonly PolicyMemberValidationDataService _service;
    private readonly TenantId _tenantId;

    public PolicyMemberValidationDataServiceTests()
    {
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockProductService = new Mock<IProductService>();
        _mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        _mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _mockLogger = new Mock<ILogger<PolicyMemberValidationDataService>>();
        _tenantId = new TenantId("test-tenant");

        _service = new PolicyMemberValidationDataService(
            _mockLegacyPolicyService.Object,
            _mockProductService.Object,
            _mockFeatureManager.Object,
            _tenantId,
            _mockSchemaProvider.Object,
            _mockLogger.Object);

        SetupDefaultMocks();
    }

    private void SetupDefaultMocks()
    {
        // Setup default feature flags
        _mockFeatureManager.Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("AllowMembersFromOtherContractHolders", _tenantId.Value))
            .ReturnsAsync(false);

        // Setup default product service responses
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);
        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("standard");

        // Setup default schema provider
        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateTestSchema());

        // Setup default legacy policy service
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);
    }

    #endregion

    #region Helper Methods

    private PolicyDto CreateValidPolicy() => new()
    {
        Id = Guid.NewGuid().ToString(),
        ContractHolderId = "CONTRACT001",
        ProductId = new ProductIdDto { Plan = "BASIC", Type = "HEALTH", Version = "1.0" }, // ensure not null
        Endorsements = new List<EndorsementDto>(),
        IsV2 = false,
        StartDate = DateOnly.FromDateTime(DateTime.Today),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1))
    };

    private PolicyMemberFieldsSchema CreateTestSchema() => new(
        new List<PolicyMemberFieldDefinition>
        {
            new() { Name = "firstName", Label = "First Name", Type = new StringFieldType(), IsRequired = true },
            new() { Name = "lastName", Label = "Last Name", Type = new StringFieldType(), IsRequired = true },
            new() { Name = "email", Label = "Email", Type = new StringFieldType(), IsRequired = true, IsUnique = true }
        });

    #endregion

    #region Feature Flag and Product Data Gathering Tests

    [Fact]
    public async Task GatherFeatureFlagsAndProductDataAsync_WithUseTheSamePlanForEmployeeAndDependentsDisabled_ShouldReturnDisabledFlags()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();

        // Setup feature flags - main feature DISABLED
        _mockFeatureManager.Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("AllowMembersFromOtherContractHolders", _tenantId.Value))
            .ReturnsAsync(false);

        // Setup product service to return SME product type
        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("sme");

        // Act
        (bool[] featureFlags, IReadOnlyList<string>? availablePlans, string? packageType, List<string>? contractHolderPolicies) = 
            await _service.GatherFeatureFlagsAndProductDataAsync(policy, CancellationToken.None);

        // Assert
        featureFlags[0].Should().BeFalse("UseTheSamePlanForEmployeeAndDependents should be disabled");
        featureFlags[1].Should().BeFalse("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts should be disabled");
        featureFlags[2].Should().BeFalse("AllowMembersFromOtherContractHolders should be disabled");
        availablePlans.Should().NotBeNull();
        packageType.Should().Be("sme");
        contractHolderPolicies.Should().NotBeNull();
    }

    [Fact]
    public async Task GatherFeatureFlagsAndProductDataAsync_WithUseTheSamePlanForEmployeeAndDependentsEnabledAndSmeOnlyDisabled_ShouldReturnEnabledMainFlag()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();

        // Setup feature flags - main feature ENABLED, SME-only DISABLED
        _mockFeatureManager.Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", _tenantId.Value))
            .ReturnsAsync(true);
        _mockFeatureManager.Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("AllowMembersFromOtherContractHolders", _tenantId.Value))
            .ReturnsAsync(false);

        // Setup product service to return non-SME product type
        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("enterprise");

        // Act
        (bool[] featureFlags, IReadOnlyList<string>? availablePlans, string? packageType, List<string>? contractHolderPolicies) = 
            await _service.GatherFeatureFlagsAndProductDataAsync(policy, CancellationToken.None);

        // Assert
        featureFlags[0].Should().BeTrue("UseTheSamePlanForEmployeeAndDependents should be enabled");
        featureFlags[1].Should().BeFalse("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts should be disabled");
        featureFlags[2].Should().BeFalse("AllowMembersFromOtherContractHolders should be disabled");
        availablePlans.Should().NotBeNull();
        packageType.Should().Be("enterprise");
        contractHolderPolicies.Should().NotBeNull();
    }

    [Fact]
    public async Task GatherFeatureFlagsAndProductDataAsync_WithBothFlagsEnabledAndSmeProduct_ShouldReturnEnabledFlags()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();

        // Setup feature flags - both ENABLED
        _mockFeatureManager.Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", _tenantId.Value))
            .ReturnsAsync(true);
        _mockFeatureManager.Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", _tenantId.Value))
            .ReturnsAsync(true);
        _mockFeatureManager.Setup(x => x.IsEnabled("AllowMembersFromOtherContractHolders", _tenantId.Value))
            .ReturnsAsync(false);

        // Setup product service to return SME product type
        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("sme");

        // Act
        (bool[] featureFlags, IReadOnlyList<string>? availablePlans, string? packageType, List<string>? contractHolderPolicies) = 
            await _service.GatherFeatureFlagsAndProductDataAsync(policy, CancellationToken.None);

        // Assert
        featureFlags[0].Should().BeTrue("UseTheSamePlanForEmployeeAndDependents should be enabled");
        featureFlags[1].Should().BeTrue("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts should be enabled");
        featureFlags[2].Should().BeFalse("AllowMembersFromOtherContractHolders should be disabled");
        availablePlans.Should().NotBeNull();
        packageType.Should().Be("sme");
        contractHolderPolicies.Should().NotBeNull();
    }

    [Fact]
    public async Task GatherFeatureFlagsAndProductDataAsync_WithBothFlagsEnabledAndNonSmeProduct_ShouldReturnEnabledFlags()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();

        // Setup feature flags - both ENABLED
        _mockFeatureManager.Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", _tenantId.Value))
            .ReturnsAsync(true);
        _mockFeatureManager.Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", _tenantId.Value))
            .ReturnsAsync(true);
        _mockFeatureManager.Setup(x => x.IsEnabled("AllowMembersFromOtherContractHolders", _tenantId.Value))
            .ReturnsAsync(false);

        // Setup product service to return non-SME product type
        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("enterprise");

        // Act
        (bool[] featureFlags, IReadOnlyList<string>? availablePlans, string? packageType, List<string>? contractHolderPolicies) = 
            await _service.GatherFeatureFlagsAndProductDataAsync(policy, CancellationToken.None);

        // Assert
        featureFlags[0].Should().BeTrue("UseTheSamePlanForEmployeeAndDependents should be enabled");
        featureFlags[1].Should().BeTrue("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts should be enabled");
        featureFlags[2].Should().BeFalse("AllowMembersFromOtherContractHolders should be disabled");
        availablePlans.Should().NotBeNull();
        packageType.Should().Be("enterprise");
        contractHolderPolicies.Should().NotBeNull();
    }

    #endregion

    #region Product Type Detection Tests

    [Fact]
    public async Task GetPackageTypeAsync_WithSmeProductPackageType_ShouldReturnSme()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();

        // Setup product service to return SME product type (case insensitive)
        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("SME");

        // Act
        string? result = await _service.GetPackageTypeAsync(policy, CancellationToken.None);

        // Assert
        result.Should().Be("SME");

        // Verify product service was called to determine product type
        _mockProductService.Verify(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetPackageTypeAsync_WithEnterpriseProductPackageType_ShouldReturnEnterprise()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();

        // Setup product service to return non-SME product type
        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("enterprise");

        // Act
        string? result = await _service.GetPackageTypeAsync(policy, CancellationToken.None);

        // Assert
        result.Should().Be("enterprise");

        // Verify product service was called to determine product type
        _mockProductService.Verify(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetPackageTypeAsync_WithNullProductPackageType_ShouldReturnNull()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();

        // Setup product service to return null (product doesn't have productPackage field)
        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((string?)null);

        // Act
        string? result = await _service.GetPackageTypeAsync(policy, CancellationToken.None);

        // Assert
        result.Should().BeNull();

        // Verify product service was called to determine product type
        _mockProductService.Verify(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region Schema and Data Gathering Tests

    [Fact]
    public async Task GetDataSchemaForUploadAsync_WithValidPolicy_ShouldReturnSchema()
    {
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema expectedSchema = CreateTestSchema();

        Assert.NotNull(policy.ProductId);
        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedSchema);

        PolicyMemberFieldsSchema result = await _service.GetDataSchemaForUploadAsync(policy, null, CancellationToken.None);

        result.Should().Be(expectedSchema);
        _mockSchemaProvider.Verify(x => x.GetMemberUploadSchema(policy.ContractHolderId, new ProductId(policy.ProductId!.Plan, policy.ProductId.Type, policy.ProductId.Version), null, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetDataSchemaForUploadAsync_WithEndorsement_ShouldPassEndorsementToProvider()
    {
        PolicyDto policy = CreateValidPolicy();
        EndorsementId endorsementId = EndorsementId.New;
        PolicyMemberFieldsSchema expectedSchema = CreateTestSchema();

        Assert.NotNull(policy.ProductId);
        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedSchema);

        PolicyMemberFieldsSchema result = await _service.GetDataSchemaForUploadAsync(policy, endorsementId, CancellationToken.None);

        result.Should().Be(expectedSchema);
        _mockSchemaProvider.Verify(x => x.GetMemberUploadSchema(policy.ContractHolderId, new ProductId(policy.ProductId!.Plan, policy.ProductId.Type, policy.ProductId.Version), endorsementId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAvailablePlansAsync_WithValidPolicy_ShouldReturnPlans()
    {
        PolicyDto policy = CreateValidPolicy();
        var expectedPlans = new List<string> { "PLAN-001", "PLAN-002" };

        Assert.NotNull(policy.ProductId);
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPlans);

        IReadOnlyList<string>? result = await _service.GetAvailablePlansAsync(policy, CancellationToken.None);

        result.Should().BeEquivalentTo(expectedPlans);
        _mockProductService.Verify(x => x.GetAvailablePlanIds(new ProductId(policy.ProductId!.Plan, policy.ProductId.Type, policy.ProductId.Version), It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region Async Architecture and Concurrent Execution Tests

    [Fact]
    public async Task GatherFeatureFlagsAndProductDataAsync_WithTaskWhenAllPattern_ShouldExecuteConcurrentlyWithoutDeadlocks()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();

        // Setup concurrent execution tracking
        var concurrentExecutionTracker = new ConcurrentDictionary<string, DateTime>();
        var executionOrder = new ConcurrentBag<(string Operation, DateTime StartTime, DateTime EndTime)>();

        SetupConcurrentExecutionTracking(concurrentExecutionTracker, executionOrder);

        // Act
        var stopwatch = Stopwatch.StartNew();
        (bool[] featureFlags, IReadOnlyList<string>? availablePlans, string? packageType, List<string>? contractHolderPolicies) = 
            await _service.GatherFeatureFlagsAndProductDataAsync(policy, CancellationToken.None);
        stopwatch.Stop();

        // Assert
        featureFlags.Should().NotBeNull();
        availablePlans.Should().NotBeNull();
        packageType.Should().NotBeNull();
        contractHolderPolicies.Should().NotBeNull();

        // Verify concurrent execution occurred (operations overlapped in time)
        var orderedExecutions = executionOrder.OrderBy(e => e.StartTime).ToList();
        orderedExecutions.Should().HaveCountGreaterThan(1, "multiple async operations should have executed");

        // Verify no deadlocks occurred (reasonable execution time)
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000, "execution should complete without deadlocks");

        // Verify Task.WhenAll pattern was used (operations started concurrently)
        VerifyConcurrentExecution(orderedExecutions);
    }

    [Fact]
    public async Task GatherFeatureFlagsAndProductDataAsync_WithConcurrentServiceCalls_ShouldMaintainDataConsistency()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();

        // Setup concurrent service call tracking
        var serviceCallTracker = new ConcurrentBag<(string Service, DateTime CallTime, int ThreadId)>();
        SetupConcurrentServiceCallTracking(serviceCallTracker);

        // Act
        (bool[] featureFlags, IReadOnlyList<string>? availablePlans, string? packageType, List<string>? contractHolderPolicies) = 
            await _service.GatherFeatureFlagsAndProductDataAsync(policy, CancellationToken.None);

        // Assert
        featureFlags.Should().NotBeNull();
        availablePlans.Should().NotBeNull();
        packageType.Should().NotBeNull();
        contractHolderPolicies.Should().NotBeNull();

        // Verify concurrent service calls occurred
        serviceCallTracker.Should().NotBeEmpty("service calls should have been tracked");

        // Verify multiple services were called concurrently
        var uniqueServices = serviceCallTracker.Select(call => call.Service).Distinct().ToList();
        uniqueServices.Should().HaveCountGreaterOrEqualTo(2, "multiple services should have been called concurrently");

        // Verify thread safety (calls from different threads)
        var uniqueThreads = serviceCallTracker.Select(call => call.ThreadId).Distinct().ToList();
        uniqueThreads.Should().HaveCountGreaterOrEqualTo(1, "calls should have been made from async context");
    }

    [Fact]
    public async Task GatherFeatureFlagsAndProductDataAsync_WithCancellationTokenPropagation_ShouldCancelAllOperations()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();
        var cancellationTokenSource = new CancellationTokenSource();

        // Setup cancellation tracking
        var cancellationTracker = new ConcurrentBag<string>();
        SetupCancellationTracking(cancellationTracker);

        // Act - Start the operation and then cancel it
        Task<(bool[] FeatureFlags, IReadOnlyList<string>? AvailablePlans, string? PackageType, List<string>? ContractHolderPolicies)> task = _service.GatherFeatureFlagsAndProductDataAsync(policy, cancellationTokenSource.Token);
            
        // Cancel after a short delay
        await Task.Delay(100);
        cancellationTokenSource.Cancel();

        // Assert
        await Assert.ThrowsAsync<TaskCanceledException>(() => task);
            
        // Verify cancellation was tracked
        cancellationTracker.Should().NotBeEmpty("cancellation should have been tracked");
    }

    #endregion

    #region Helper Methods for Async Tests

    private void SetupConcurrentExecutionTracking(
        ConcurrentDictionary<string, DateTime> concurrentExecutionTracker,
        ConcurrentBag<(string Operation, DateTime StartTime, DateTime EndTime)> executionOrder)
    {
        // Track GetAvailablePlanIds (runs concurrently in GatherFeatureFlagsAndProductDataAsync)
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .Returns<ProductId, CancellationToken>(async (_, ct) =>
            {
                DateTime startTime = DateTime.UtcNow;
                concurrentExecutionTracker.TryAdd($"GetAvailablePlanIds-{startTime:HH:mm:ss.fff}", startTime);
                await Task.Delay(50, ct); // Simulate work
                DateTime endTime = DateTime.UtcNow;
                executionOrder.Add(("GetAvailablePlanIds", startTime, endTime));
                return ["PLAN-001", "PLAN-002"];
            });

        // Track GetProductPackageType (runs concurrently in GatherFeatureFlagsAndProductDataAsync)
        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .Returns<Products.Client.ProductId, CancellationToken>(async (_, ct) =>
            {
                DateTime startTime = DateTime.UtcNow;
                concurrentExecutionTracker.TryAdd($"GetProductPackageType-{startTime:HH:mm:ss.fff}", startTime);
                await Task.Delay(50, ct); // Simulate work
                DateTime endTime = DateTime.UtcNow;
                executionOrder.Add(("GetProductPackageType", startTime, endTime));
                return "sme";
            });

        // Track GetIdsByContractHolderId (runs concurrently in GatherFeatureFlagsAndProductDataAsync)
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, CancellationToken>(async (_, ct) =>
            {
                DateTime startTime = DateTime.UtcNow;
                concurrentExecutionTracker.TryAdd($"GetIdsByContractHolderId-{startTime:HH:mm:ss.fff}", startTime);
                await Task.Delay(50, ct); // Simulate work
                DateTime endTime = DateTime.UtcNow;
                executionOrder.Add(("GetIdsByContractHolderId", startTime, endTime));
                return ["POL-001", "POL-002"];
            });

        // Track feature flag checks (run concurrently in GatherFeatureFlagsAndProductDataAsync)
        _mockFeatureManager.Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .Returns<string, string>(async (featureName, _) =>
            {
                DateTime startTime = DateTime.UtcNow;
                concurrentExecutionTracker.TryAdd($"FeatureCheck-{featureName}-{startTime:HH:mm:ss.fff}", startTime);
                await Task.Delay(30); // Simulate work
                DateTime endTime = DateTime.UtcNow;
                executionOrder.Add(($"FeatureCheck-{featureName}", startTime, endTime));
                return false;
            });
    }

    private void SetupConcurrentServiceCallTracking(ConcurrentBag<(string Service, DateTime CallTime, int ThreadId)> serviceCallTracker)
    {
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .Returns<ProductId, CancellationToken>(async (_, ct) =>
            {
                serviceCallTracker.Add(("ProductService", DateTime.UtcNow, Thread.CurrentThread.ManagedThreadId));
                await Task.Delay(20, ct);
                return ["PLAN-001"];
            });

        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, CancellationToken>(async (_, ct) =>
            {
                serviceCallTracker.Add(("LegacyPolicyService", DateTime.UtcNow, Thread.CurrentThread.ManagedThreadId));
                await Task.Delay(20, ct);
                return [];
            });
    }

    private void SetupCancellationTracking(ConcurrentBag<string> cancellationTracker)
    {
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .Returns<ProductId, CancellationToken>(async (_, ct) =>
            {
                try
                {
                    await Task.Delay(1000, ct); // Long operation that should be cancelled
                    return ["PLAN-001"];
                }
                catch (OperationCanceledException)
                {
                    cancellationTracker.Add($"GetAvailablePlanIds-Cancelled-{DateTime.UtcNow:HH:mm:ss.fff}");
                    throw;
                }
            });
    }

    private void VerifyConcurrentExecution(List<(string Operation, DateTime StartTime, DateTime EndTime)> orderedExecutions)
    {
        if (orderedExecutions.Count < 2) return;

        // Check if any operations overlapped in time (indicating concurrent execution)
        bool foundConcurrentExecution = false;
        for (int i = 0; i < orderedExecutions.Count - 1; i++)
        {
            for (int j = i + 1; j < orderedExecutions.Count; j++)
            {
                (string Operation, DateTime StartTime, DateTime EndTime) op1 = orderedExecutions[i];
                (string Operation, DateTime StartTime, DateTime EndTime) op2 = orderedExecutions[j];

                // Check if operations overlapped
                if (op1.StartTime < op2.EndTime && op2.StartTime < op1.EndTime)
                {
                    foundConcurrentExecution = true;
                    break;
                }
            }
            if (foundConcurrentExecution) break;
        }

        foundConcurrentExecution.Should().BeTrue("operations should have executed concurrently");
    }

    #endregion

    #region Batch Processing Tests

    [Fact]
    public async Task ExtractAllMemberIdentifiersAsync_WithEmptyMemberData_ShouldReturnEmptySet()
    {
        // Arrange
        MembersUploadFields emptyMemberData = MembersUploadFields.Empty();

        // Act
        HashSet<string> result = await _service.ExtractAllMemberIdentifiersAsync(emptyMemberData);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task ExtractAllMemberIdentifiersAsync_WithValidMemberData_ShouldReturnUniqueIds()
    {
        // Arrange
        var memberData = new List<MemberUploadFields>
        {
            new(new Dictionary<string, string?> { ["memberId"] = "MEM001" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM002" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM003" })
        };
        MembersUploadFields membersUploadFields = MembersUploadFields.CreateNonEmpty(memberData);

        // Act
        HashSet<string> result = await _service.ExtractAllMemberIdentifiersAsync(membersUploadFields);

        // Assert
        result.Should().HaveCount(3);
        result.Should().Contain("MEM001");
        result.Should().Contain("MEM002");
        result.Should().Contain("MEM003");
    }

    [Fact]
    public async Task ExtractAllMemberIdentifiersAsync_WithDuplicateMemberIds_ShouldReturnUniqueIds()
    {
        // Arrange
        var memberData = new List<MemberUploadFields>
        {
            new(new Dictionary<string, string?> { ["memberId"] = "MEM001" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM002" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM001" }), // Duplicate
            new(new Dictionary<string, string?> { ["memberId"] = "MEM003" })
        };
        MembersUploadFields membersUploadFields = MembersUploadFields.CreateNonEmpty(memberData);

        // Act
        HashSet<string> result = await _service.ExtractAllMemberIdentifiersAsync(membersUploadFields);

        // Assert
        result.Should().HaveCount(3);
        result.Should().Contain("MEM001");
        result.Should().Contain("MEM002");
        result.Should().Contain("MEM003");
    }

    [Fact]
    public async Task ExtractAllMemberIdentifiersAsync_WithNullOrEmptyMemberIds_ShouldFilterOutInvalidIds()
    {
        // Arrange
        var memberData = new List<MemberUploadFields>
        {
            new(new Dictionary<string, string?> { ["memberId"] = "MEM001" }),
            new(new Dictionary<string, string?> { ["memberId"] = null }),
            new(new Dictionary<string, string?> { ["memberId"] = "" }),
            new(new Dictionary<string, string?> { ["memberId"] = "   " }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM002" })
        };
        MembersUploadFields membersUploadFields = MembersUploadFields.CreateNonEmpty(memberData);

        // Act
        HashSet<string> result = await _service.ExtractAllMemberIdentifiersAsync(membersUploadFields);

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain("MEM001");
        result.Should().Contain("MEM002");
    }

    [Fact]
    public async Task ExtractAllMemberIdentifiersAsync_WithCaseInsensitiveIds_ShouldReturnUniqueIds()
    {
        // Arrange
        var memberData = new List<MemberUploadFields>
        {
            new(new Dictionary<string, string?> { ["memberId"] = "MEM001" }),
            new(new Dictionary<string, string?> { ["memberId"] = "mem001" }), // Different case
            new(new Dictionary<string, string?> { ["memberId"] = "Mem001" }), // Different case
            new(new Dictionary<string, string?> { ["memberId"] = "MEM002" })
        };
        MembersUploadFields membersUploadFields = MembersUploadFields.CreateNonEmpty(memberData);

        // Act
        HashSet<string> result = await _service.ExtractAllMemberIdentifiersAsync(membersUploadFields);

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain("MEM001");
        result.Should().Contain("MEM002");
    }

    [Fact]
    public async Task ExtractPrimaryMemberIdentifiersAsync_WithEmptyMemberData_ShouldReturnEmptySet()
    {
        // Arrange
        MembersUploadFields emptyMemberData = MembersUploadFields.Empty();

        // Act
        HashSet<string> result = await _service.ExtractPrimaryMemberIdentifiersAsync(emptyMemberData);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task ExtractPrimaryMemberIdentifiersAsync_WithNoDependentMembers_ShouldReturnEmptySet()
    {
        // Arrange
        var memberData = new List<MemberUploadFields>
        {
            new(new Dictionary<string, string?> { ["memberId"] = "MEM001", ["memberType"] = "employee" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM002", ["memberType"] = "employee" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM003", ["memberType"] = "employee" })
        };
        MembersUploadFields membersUploadFields = MembersUploadFields.CreateNonEmpty(memberData);

        // Act
        HashSet<string> result = await _service.ExtractPrimaryMemberIdentifiersAsync(membersUploadFields);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task ExtractPrimaryMemberIdentifiersAsync_WithDependentMembers_ShouldReturnPrimaryMemberIds()
    {
        // Arrange
        var memberData = new List<MemberUploadFields>
        {
            new(new Dictionary<string, string?> { ["memberId"] = "MEM001", ["memberType"] = "employee" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM002", ["memberType"] = "dependent", ["dependentOfId"] = "MEM001" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM003", ["memberType"] = "dependent", ["dependentOfId"] = "MEM001" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM004", ["memberType"] = "employee" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM005", ["memberType"] = "dependent", ["dependentOfId"] = "MEM004" })
        };
        MembersUploadFields membersUploadFields = MembersUploadFields.CreateNonEmpty(memberData);

        // Act
        HashSet<string> result = await _service.ExtractPrimaryMemberIdentifiersAsync(membersUploadFields);

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain("MEM001");
        result.Should().Contain("MEM004");
    }

    [Fact]
    public async Task ExtractPrimaryMemberIdentifiersAsync_WithInvalidDependentOfId_ShouldFilterOutInvalidIds()
    {
        // Arrange
        var memberData = new List<MemberUploadFields>
        {
            new(new Dictionary<string, string?> { ["memberId"] = "MEM001", ["memberType"] = "employee" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM002", ["memberType"] = "dependent", ["dependentOfId"] = null }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM003", ["memberType"] = "dependent", ["dependentOfId"] = "" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM004", ["memberType"] = "dependent", ["dependentOfId"] = "MEM001" })
        };
        MembersUploadFields membersUploadFields = MembersUploadFields.CreateNonEmpty(memberData);

        // Act
        HashSet<string> result = await _service.ExtractPrimaryMemberIdentifiersAsync(membersUploadFields);

        // Assert
        result.Should().HaveCount(1);
        result.Should().Contain("MEM001");
    }

    [Fact]
    public async Task GatherMemberSpecificDataAsync_WithEmptyMemberData_ShouldReturnEmptyResults()
    {
        // Arrange
        MembersUploadFields emptyMemberData = MembersUploadFields.Empty();
        PolicyDto policy = CreateValidPolicy();
        var mockUsersService = new Mock<IUsersService>();
        var mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();

        // Act
        MemberDataResults result = await _service.GatherMemberSpecificDataAsync(
            emptyMemberData, policy, null, mockUsersService.Object, mockPolicyMemberQueryService.Object, CancellationToken.None);

        // Assert
        result.Should().BeEquivalentTo(MemberDataResults.Empty());
    }

    [Fact]
    public async Task GatherMemberSpecificDataAsync_WithValidMemberData_ShouldReturnCompleteResults()
    {
        // Arrange
        var memberData = new List<MemberUploadFields>
        {
            new(new Dictionary<string, string?> { ["memberId"] = "MEM001" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM002" })
        };
        MembersUploadFields membersUploadFields = MembersUploadFields.CreateNonEmpty(memberData);
        PolicyDto policy = CreateValidPolicy();

        var mockUsersService = new Mock<IUsersService>();
        var mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();

        // Setup mock responses
        var individuals = new List<Individual>
        {
            new() { InternalCode = "MEM001" },
            new() { InternalCode = "MEM002" }
        };

        var existingMembers = new Dictionary<string, PolicyMember?>
        {
            ["MEM001"] = CreateMockPolicyMember("MEM001"),
            ["MEM002"] = null
        };

        var memberValidationStates = new Dictionary<string, List<PolicyMember>>
        {
            ["MEM001"] = [CreateMockPolicyMember("MEM001")],
            ["MEM002"] = []
        };

        var dependentMembersCache = new Dictionary<string, PolicyMember?>
        {
            ["MEM001"] = CreateMockPolicyMember("MEM001")
        };

        mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individuals);

        mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingMembers);

        mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(memberValidationStates);

        // Setup dependent members cache call - this is called with an empty list when no primary members exist
        mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(
                It.Is<List<string>>(ids => ids.Count == 0), 
                It.IsAny<PolicyId>(), 
                It.IsAny<List<EndorsementId>>(), 
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(dependentMembersCache);

        // Act
        MemberDataResults result = await _service.GatherMemberSpecificDataAsync(
            membersUploadFields, policy, null, mockUsersService.Object, mockPolicyMemberQueryService.Object, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IndividualExistenceMap.Should().HaveCount(2);
        result.IndividualExistenceMap["MEM001"].Should().BeTrue();
        result.IndividualExistenceMap["MEM002"].Should().BeTrue();
        result.ExistingPolicyMembers.Should().HaveCount(2);
        result.MemberValidationStates.Should().HaveCount(2);
        result.DependentMembersCache.Should().BeEmpty(); // No dependent members in this test data
    }

    [Fact]
    public async Task GatherMemberSpecificDataAsync_WithDependentMembers_ShouldIncludeDependentCache()
    {
        // Arrange
        var memberData = new List<MemberUploadFields>
        {
            new(new Dictionary<string, string?> { ["memberId"] = "MEM001", ["memberType"] = "employee" }),
            new(new Dictionary<string, string?> { ["memberId"] = "MEM002", ["memberType"] = "dependent", ["dependentOfId"] = "MEM001" })
        };
        MembersUploadFields membersUploadFields = MembersUploadFields.CreateNonEmpty(memberData);
        PolicyDto policy = CreateValidPolicy();

        var mockUsersService = new Mock<IUsersService>();
        var mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();

        // Setup mock responses
        var individuals = new List<Individual>
        {
            new() { InternalCode = "MEM001" },
            new() { InternalCode = "MEM002" }
        };

        var existingMembers = new Dictionary<string, PolicyMember?>
        {
            ["MEM001"] = CreateMockPolicyMember("MEM001"),
            ["MEM002"] = CreateMockPolicyMember("MEM002")
        };

        var memberValidationStates = new Dictionary<string, List<PolicyMember>>
        {
            ["MEM001"] = [CreateMockPolicyMember("MEM001")],
            ["MEM002"] = [CreateMockPolicyMember("MEM002")]
        };

        var dependentMembersCache = new Dictionary<string, PolicyMember?>
        {
            ["MEM001"] = CreateMockPolicyMember("MEM001")
        };

        mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individuals);

        mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingMembers);

        mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(memberValidationStates);

        // Setup dependent members cache call
        mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(
                It.Is<List<string>>(ids => ids.Contains("MEM001")), 
                It.IsAny<PolicyId>(), 
                It.IsAny<List<EndorsementId>>(), 
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(dependentMembersCache);

        // Act
        MemberDataResults result = await _service.GatherMemberSpecificDataAsync(
            membersUploadFields, policy, null, mockUsersService.Object, mockPolicyMemberQueryService.Object, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.DependentMembersCache.Should().HaveCount(1);
        result.DependentMembersCache.Should().ContainKey("MEM001");
    }

    [Fact]
    public async Task GatherMemberSpecificDataAsync_WithCancellationToken_ShouldCancelGracefully()
    {
        // Arrange
        var memberData = new List<MemberUploadFields>
        {
            new(new Dictionary<string, string?> { ["memberId"] = "MEM001" })
        };
        MembersUploadFields membersUploadFields = MembersUploadFields.CreateNonEmpty(memberData);
        PolicyDto policy = CreateValidPolicy();

        var mockUsersService = new Mock<IUsersService>();
        var mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();

        // Setup long-running operation
        mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .Returns<QueryArgumentsOfIndividualWhere, CancellationToken>(async (_, ct) =>
            {
                await Task.Delay(1000, ct); // Long operation
                return new List<Individual>();
            });

        using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(100));

        // Act & Assert
        await Assert.ThrowsAsync<TaskCanceledException>(async () =>
            await _service.GatherMemberSpecificDataAsync(
                membersUploadFields, policy, null, mockUsersService.Object, mockPolicyMemberQueryService.Object, cts.Token));
    }

    [Fact]
    public async Task GatherMemberSpecificDataAsync_WithLargeDataset_ShouldProcessEfficiently()
    {
        // Arrange
        var memberData = new List<MemberUploadFields>();
        for (int i = 1; i <= 1000; i++)
        {
            memberData.Add(new(new Dictionary<string, string?> { ["memberId"] = $"MEM{i:D4}" }));
        }
        MembersUploadFields membersUploadFields = MembersUploadFields.CreateNonEmpty(memberData);
        PolicyDto policy = CreateValidPolicy();

        var mockUsersService = new Mock<IUsersService>();
        var mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();

        // Setup mock responses
        var individuals = memberData.Select(m => new Individual { InternalCode = m.Value["memberId"] }).ToList();
        var existingMembers = memberData.ToDictionary(m => m.Value["memberId"]!, _ => (PolicyMember?)null);
        var memberValidationStates = memberData.ToDictionary(m => m.Value["memberId"]!, _ => new List<PolicyMember>());

        mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individuals);

        mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingMembers);

        mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(memberValidationStates);

        mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(
                It.Is<List<string>>(ids => ids.Count == 0), 
                It.IsAny<PolicyId>(), 
                It.IsAny<List<EndorsementId>>(), 
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, PolicyMember?>());

        var stopwatch = Stopwatch.StartNew();

        // Act
        MemberDataResults result = await _service.GatherMemberSpecificDataAsync(
            membersUploadFields, policy, null, mockUsersService.Object, mockPolicyMemberQueryService.Object, CancellationToken.None);

        stopwatch.Stop();

        // Assert
        result.Should().NotBeNull();
        result.IndividualExistenceMap.Should().HaveCount(1000);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should complete within 5 seconds
    }

    #endregion

    #region Helper Methods for Batch Processing Tests

    private PolicyMember CreateMockPolicyMember(string memberId)
    {
        return new PolicyMember
        {
            MemberId = memberId,
            PolicyId = PolicyId.New
        };
    }

    #endregion
}