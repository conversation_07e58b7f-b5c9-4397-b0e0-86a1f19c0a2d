using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Products;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.DTOs.LegacyPolicy;

public class ProductIdDtoTests
{
    #region ToDomainProductId Tests

    [Fact]
    public void ToDomainProductId_WithValidProductIdDto_ShouldReturnDomainProductId()
    {
        // Arrange
        var productIdDto = new ProductIdDto
        {
            Type = "health",
            Plan = "basic",
            Version = "v1"
        };

        // Act
        ProductId result = productIdDto.ToDomainProductId();

        // Assert
        Assert.NotNull(result);
        Assert.Equal("basic", result.Plan);
        Assert.Equal("health", result.Type);
        Assert.Equal("v1", result.Version);
    }

    [Fact]
    public void ToDomainProductId_WithNullDto_ShouldThrowNullReferenceException()
    {
        // Arrange
        ProductIdDto? productIdDto = null;

        // Act & Assert
        Assert.Throws<NullReferenceException>(() => productIdDto!.ToDomainProductId());
    }

    [Theory]
    [InlineData("health", null, "v1", "ProductId Plan is required")]
    [InlineData("health", "", "v1", "ProductId Plan is required")]
    [InlineData("health", "   ", "v1", "ProductId Plan is required")]
    [InlineData(null, "basic", "v1", "ProductId Type is required")]
    [InlineData("", "basic", "v1", "ProductId Type is required")]
    [InlineData("   ", "basic", "v1", "ProductId Type is required")]
    [InlineData("health", "basic", null, "ProductId Version is required")]
    [InlineData("health", "basic", "", "ProductId Version is required")]
    [InlineData("health", "basic", "   ", "ProductId Version is required")]
    public void ToDomainProductId_WithInvalidProperties_ShouldThrowInvalidOperationException(
        string? type, string? plan, string? version, string expectedMessage)
    {
        // Arrange
        var productIdDto = new ProductIdDto
        {
            Type = type!,
            Plan = plan!,
            Version = version!
        };

        // Act & Assert
        InvalidOperationException exception = Assert.Throws<InvalidOperationException>(() => productIdDto.ToDomainProductId());
        Assert.Equal(expectedMessage, exception.Message);
    }

    #endregion
}
