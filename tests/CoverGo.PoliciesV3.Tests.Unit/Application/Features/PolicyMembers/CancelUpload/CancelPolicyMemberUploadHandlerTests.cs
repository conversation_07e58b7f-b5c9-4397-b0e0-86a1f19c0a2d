using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using System.Reflection;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.CancelUpload;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.CancelUpload;

public class CancelPolicyMemberUploadHandlerTests
{
    private readonly Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>> _mockPolicyMemberUploadRepository;
    private readonly Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> _mockPolicyMemberRepository;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly CancelPolicyMemberUploadHandler _handler;
    private readonly Fixture _fixture;

    public CancelPolicyMemberUploadHandlerTests()
    {
        _mockPolicyMemberUploadRepository = new Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>>();
        _mockPolicyMemberRepository = new Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        var mockLogger = new Mock<ILogger<CancelPolicyMemberUploadHandler>>();

        _handler = new CancelPolicyMemberUploadHandler(
            _mockPolicyMemberUploadRepository.Object,
            _mockPolicyMemberRepository.Object,
            _mockLegacyPolicyService.Object,
            mockLogger.Object);

        _fixture = new Fixture();
        _fixture.Customize<PolicyId>(c => c.FromFactory(() => PolicyId.New));
        _fixture.Customize<PolicyMemberUploadId>(c => c.FromFactory(() => PolicyMemberUploadId.New));
        _fixture.Customize<PolicyMemberId>(c => c.FromFactory(() => PolicyMemberId.New));
    }

    [Fact]
    public async Task Handle_WithValidatedUpload_ShouldReturnSuccessResponse()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload(PolicyMemberUploadStatus.VALIDATED);
        PolicyDto policy = CreateValidPolicy();
        var command = new CancelPolicyMemberUploadCommand
        {
            PolicyId = _fixture.Create<PolicyId>(),
            UploadId = upload.Id
        };

        SetupSuccessfulMocks(upload, policy);

        // Act
        Result<CancelPolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        if (!result.IsSuccess)
        {
            string errors = string.Join(", ", result.Errors.Select(e => $"{e.Code}: {e.Message}"));
            throw new Exception($"Expected success but got errors: {errors}");
        }
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBeNull();
        result.Value.PolicyMemberUpload.Id.Should().Be(command.UploadId);
        result.Value.PolicyMemberUpload.Status.Should().Be(PolicyMemberUploadStatus.CANCELED);

        _mockPolicyMemberUploadRepository.Verify(x => x.UpdateAsync(It.IsAny<PolicyMemberUpload>(), It.IsAny<CancellationToken>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithValidValidatedUpload_ShouldReturnSuccessResponse()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload(PolicyMemberUploadStatus.VALIDATED);
        PolicyDto policy = CreateValidPolicy();
        var command = new CancelPolicyMemberUploadCommand
        {
            PolicyId = _fixture.Create<PolicyId>(),
            UploadId = upload.Id
        };

        SetupSuccessfulMocks(upload, policy);

        // Act
        Result<CancelPolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBeNull();
        result.Value.PolicyMemberUpload.Id.Should().Be(command.UploadId);
        result.Value.PolicyMemberUpload.Status.Should().Be(PolicyMemberUploadStatus.CANCELED);
    }

    [Fact]
    public async Task Handle_WithAlreadyCanceledUpload_ShouldReturnValidationError()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload(PolicyMemberUploadStatus.CANCELED);
        PolicyDto policy = CreateValidPolicy();
        var command = new CancelPolicyMemberUploadCommand
        {
            PolicyId = _fixture.Create<PolicyId>(),
            UploadId = upload.Id
        };

        SetupSuccessfulMocks(upload, policy);

        // Act
        Result<CancelPolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().HaveCount(1);
        result.Errors.First().Code.Should().Be(ErrorCodes.InvalidState);
        result.Errors.First().Message.Should().Contain("Upload cannot be canceled due to status: CANCELED");

        // Verify that no business logic processing occurred (no member deletion)
        _mockPolicyMemberRepository.Verify(x => x.FindAllAsync(It.IsAny<List<PolicyMemberId>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WithNonExistentUpload_ShouldReturnValidationError()
    {
        // Arrange
        CancelPolicyMemberUploadCommand command = CreateValidCommand();

        _mockPolicyMemberUploadRepository
            .Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new PolicyMemberUploadNotFoundException(command.UploadId.Value.ToString()));

        // Act
        Result<CancelPolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().HaveCount(1);
        result.Errors.First().Code.Should().Be(ErrorCodes.NotFound);
        result.Errors.First().PropertyPath.Should().Be("upload.id");
    }

    [Fact]
    public async Task Handle_WithInvalidUploadStatus_ShouldReturnValidationError()
    {
        // Arrange
        CancelPolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUpload(PolicyMemberUploadStatus.IMPORTED);

        _mockPolicyMemberUploadRepository
            .Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockPolicyMemberUploadRepository
            .Setup(x => x.UpdateAsync(It.IsAny<PolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidPolicyMemberUploadStatusException(upload.Id.Value.ToString(), PolicyMemberUploadStatus.IMPORTED.Value, [PolicyMemberUploadStatus.CANCELING.Value]));

        // Act
        Result<CancelPolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().HaveCount(1);
        result.Errors.First().Code.Should().Be(ErrorCodes.InvalidState);
        result.Errors.First().PropertyPath.Should().Be("upload.status");
    }

    [Fact]
    public async Task Handle_WithNonExistentPolicy_ShouldReturnValidationError()
    {
        // Arrange
        CancelPolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUpload(PolicyMemberUploadStatus.VALIDATING);

        _mockPolicyMemberUploadRepository
            .Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyDto?)null);

        // Act
        Result<CancelPolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().HaveCount(1);
        result.Errors.First().Code.Should().Be(ErrorCodes.NotFound);
        result.Errors.First().PropertyPath.Should().Be("policy.id");
    }

    [Fact]
    public async Task Handle_WithIssuedPolicy_ShouldReturnValidationError()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload(PolicyMemberUploadStatus.VALIDATING);
        PolicyDto policy = CreateIssuedPolicy(); // Create an issued policy that will fail validation
        var command = new CancelPolicyMemberUploadCommand
        {
            PolicyId = _fixture.Create<PolicyId>(),
            UploadId = upload.Id
        };

        _mockPolicyMemberUploadRepository
            .Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        // Act
        Result<CancelPolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().HaveCount(1);
        if (result.Errors.First().Code != ErrorCodes.InvalidState)
        {
            ValidationError actualError = result.Errors.First();
            throw new Exception($"Expected error code '{ErrorCodes.InvalidState}' but got '{actualError.Code}' with message: {actualError.Message}");
        }
        result.Errors.First().Code.Should().Be(ErrorCodes.InvalidState);
        result.Errors.First().PropertyPath.Should().Be("policy.status");
    }

    [Fact]
    public async Task Handle_WithUnexpectedError_ShouldReturnValidationError()
    {
        // Arrange
        CancelPolicyMemberUploadCommand command = CreateValidCommand();

        _mockPolicyMemberUploadRepository
            .Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        // Act
        Result<CancelPolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().HaveCount(1);
        result.Errors.First().Code.Should().Be(ErrorCodes.UnexpectedError);
        result.Errors.First().PropertyPath.Should().Be("operation");
    }

    private CancelPolicyMemberUploadCommand CreateValidCommand()
    {
        return new CancelPolicyMemberUploadCommand
        {
            PolicyId = _fixture.Create<PolicyId>(),
            UploadId = _fixture.Create<PolicyMemberUploadId>()
        };
    }

    private PolicyMemberUpload CreateValidUpload(PolicyMemberUploadStatus status)
    {
        var upload = PolicyMemberUpload.Create(
            _fixture.Create<PolicyId>(),
            "uploads/test-file.csv",
            100);

        // Use reflection to set the status directly for testing
        PropertyInfo? statusProperty = typeof(PolicyMemberUpload).GetProperty("Status");
        statusProperty!.SetValue(upload, status);

        // Set up ImportedResults collection
        upload.ImportedResults.Clear();
        if (status == PolicyMemberUploadStatus.CANCELING)
        {
            // Add some successful imported results for testing deletion
            var importedResult1 = PolicyMemberUploadImportedResult.CreateSuccess(
                upload.Id,
                1,
                _fixture.Create<PolicyMemberId>());
            var importedResult2 = PolicyMemberUploadImportedResult.CreateSuccess(
                upload.Id,
                2,
                _fixture.Create<PolicyMemberId>());
            upload.ImportedResults.Add(importedResult1);
            upload.ImportedResults.Add(importedResult2);
        }

        return upload;
    }

    private PolicyDto CreateValidPolicy()
    {
        return new PolicyDto
        {
            Id = _fixture.Create<PolicyId>().Value.ToString(),
            Status = "ACTIVE",
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1))
        };
    }

    private PolicyDto CreateIssuedPolicy()
    {
        return new PolicyDto
        {
            Id = _fixture.Create<PolicyId>().Value.ToString(),
            Status = "ISSUED",
            IsIssued = true, // This will cause the validation to fail
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1))
        };
    }

    private void SetupSuccessfulMocks(PolicyMemberUpload upload, PolicyDto policy)
    {
        _mockPolicyMemberUploadRepository
            .Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        _mockPolicyMemberUploadRepository
            .Setup(x => x.UpdateAsync(It.IsAny<PolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMemberUpload upload, CancellationToken _) => upload);

        // Setup for policy member deletion
        if (upload.ImportedResults.Any(r => r.Success))
        {
            var policyMembers = upload.ImportedResults
                .Where(r => r.Success && r.PolicyMemberId != null)
                .Select(r => CreatePolicyMember(r.PolicyMemberId!.Value))
                .ToList();

            _mockPolicyMemberRepository
                .Setup(x => x.FindAllAsync(It.IsAny<List<PolicyMemberId>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(policyMembers);

            _mockPolicyMemberRepository
                .Setup(x => x.UpdateBatchAsync(It.IsAny<List<PolicyMember>>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);
        }
    }

    private PolicyMember CreatePolicyMember(PolicyMemberId id)
    {
        var member = PolicyMember.Create(
            _fixture.Create<PolicyId>(),
            $"MEMBER-{id.Value}",
            DateOnly.FromDateTime(DateTime.Today),
            DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            "TEST-PLAN");

        // Use reflection to set the ID to match the requested ID
        PropertyInfo? idProperty = typeof(PolicyMember).BaseType?.GetProperty("Id");
        if (idProperty != null && idProperty.CanWrite)
        {
            idProperty.SetValue(member, id);
        }
        else
        {
            // Try to access the private field if property is not writable
            FieldInfo? idField = typeof(PolicyMember).BaseType?.GetField("_id",
                BindingFlags.NonPublic | BindingFlags.Instance);
            idField?.SetValue(member, id);
        }

        return member;
    }
}

// Additional test class for business logic specific tests
public class CancelPolicyMemberUploadHandlerBusinessLogicTests
{
    private readonly Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>> _mockPolicyMemberUploadRepository;
    private readonly Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> _mockPolicyMemberRepository;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<ILogger<CancelPolicyMemberUploadHandler>> _mockLogger;
    private readonly CancelPolicyMemberUploadHandler _handler;
    private readonly Fixture _fixture;

    public CancelPolicyMemberUploadHandlerBusinessLogicTests()
    {
        _mockPolicyMemberUploadRepository = new Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>>();
        _mockPolicyMemberRepository = new Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockLogger = new Mock<ILogger<CancelPolicyMemberUploadHandler>>();

        _handler = new CancelPolicyMemberUploadHandler(
            _mockPolicyMemberUploadRepository.Object,
            _mockPolicyMemberRepository.Object,
            _mockLegacyPolicyService.Object,
            _mockLogger.Object);

        _fixture = new Fixture();
        _fixture.Customize<PolicyId>(c => c.FromFactory(() => PolicyId.New));
        _fixture.Customize<PolicyMemberUploadId>(c => c.FromFactory(() => PolicyMemberUploadId.New));
        _fixture.Customize<PolicyMemberId>(c => c.FromFactory(() => PolicyMemberId.New));
    }

    [Fact]
    public async Task ExecuteCancellationBusinessLogic_WithCancelingStatus_ShouldDeleteImportedMembers()
    {
        // Arrange
        PolicyMemberUpload upload = CreateUploadWithImportedMembers(PolicyMemberUploadStatus.IMPORTING);
        PolicyDto policy = CreateValidPolicy();
        var command = new CancelPolicyMemberUploadCommand
        {
            PolicyId = _fixture.Create<PolicyId>(),
            UploadId = upload.Id
        };

        List<PolicyMember> policyMembers = CreatePolicyMembersForImportedResults(upload.ImportedResults);

        SetupMocksForBusinessLogic(upload, policy, policyMembers);

        // Act
        Result<CancelPolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        if (!result.IsSuccess)
        {
            string errors = string.Join(", ", result.Errors.Select(e => $"{e.Code}: {e.Message}"));
            throw new Exception($"Expected success but got errors: {errors}");
        }
        result.IsSuccess.Should().BeTrue();

        // Verify that policy members were retrieved and marked as removed
        _mockPolicyMemberRepository.Verify(
            x => x.FindAllAsync(It.IsAny<List<PolicyMemberId>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _mockPolicyMemberRepository.Verify(
            x => x.UpdateBatchAsync(It.Is<List<PolicyMember>>(list => list.All(pm => pm.IsRemoved) && list.Count == policyMembers.Count), It.IsAny<CancellationToken>()),
            Times.Once);

        // Verify that imported results were cleared
        upload.ImportedResults.Should().BeEmpty();
    }

    [Fact]
    public async Task ExecuteCancellationBusinessLogic_WithCanceledStatus_ShouldReturnValidationError()
    {
        // Arrange
        PolicyMemberUpload upload = CreateUploadWithImportedMembers(PolicyMemberUploadStatus.CANCELED);
        PolicyDto policy = CreateValidPolicy();
        var command = new CancelPolicyMemberUploadCommand
        {
            PolicyId = _fixture.Create<PolicyId>(),
            UploadId = upload.Id
        };

        SetupMocksForBusinessLogic(upload, policy, new List<PolicyMember>());

        // Act
        Result<CancelPolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().HaveCount(1);
        result.Errors.First().Code.Should().Be(ErrorCodes.InvalidState);

        // Verify that no policy member operations were performed
        _mockPolicyMemberRepository.Verify(
            x => x.FindAllAsync(It.IsAny<List<PolicyMemberId>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _mockPolicyMemberRepository.Verify(
            x => x.UpdateBatchAsync(It.IsAny<List<PolicyMember>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task ExecuteCancellationBusinessLogic_WithOtherStatus_ShouldSkipBusinessLogic()
    {
        // Arrange
        PolicyMemberUpload upload = CreateUploadWithoutImportedMembers(PolicyMemberUploadStatus.VALIDATING);
        PolicyDto policy = CreateValidPolicy();
        var command = new CancelPolicyMemberUploadCommand
        {
            PolicyId = _fixture.Create<PolicyId>(),
            UploadId = upload.Id
        };

        SetupMocksForBusinessLogic(upload, policy, new List<PolicyMember>());

        // Act
        Result<CancelPolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        // Verify that no policy member operations were performed
        _mockPolicyMemberRepository.Verify(
            x => x.FindAllAsync(It.IsAny<List<PolicyMemberId>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task DeleteImportedMembers_WithNoImportedMembers_ShouldReturnEarly()
    {
        // Arrange
        PolicyMemberUpload upload = CreateUploadWithoutImportedMembers(PolicyMemberUploadStatus.IMPORTING);
        PolicyDto policy = CreateValidPolicy();
        var command = new CancelPolicyMemberUploadCommand
        {
            PolicyId = _fixture.Create<PolicyId>(),
            UploadId = upload.Id
        };

        SetupMocksForBusinessLogic(upload, policy, new List<PolicyMember>());

        // Act
        Result<CancelPolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        // Verify that no policy member operations were performed
        _mockPolicyMemberRepository.Verify(
            x => x.FindAllAsync(It.IsAny<List<PolicyMemberId>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task DeleteImportedMembers_WithMixedSuccessResults_ShouldOnlyDeleteSuccessfulOnes()
    {
        // Arrange
        PolicyMemberUpload upload = CreateUploadWithMixedImportedResults(PolicyMemberUploadStatus.IMPORTING);
        PolicyDto policy = CreateValidPolicy();
        var command = new CancelPolicyMemberUploadCommand
        {
            PolicyId = _fixture.Create<PolicyId>(),
            UploadId = upload.Id
        };
        var successfulResults = upload.ImportedResults.Where(r => r.Success && r.PolicyMemberId != null).ToList();
        List<PolicyMember> policyMembers = CreatePolicyMembersForImportedResults(successfulResults);

        SetupMocksForBusinessLogic(upload, policy, policyMembers);

        // Act
        Result<CancelPolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        // Verify that only successful policy members were processed
        _mockPolicyMemberRepository.Verify(
            x => x.FindAllAsync(
                It.Is<List<PolicyMemberId>>(list => list.Count == successfulResults.Count),
                It.IsAny<CancellationToken>()),
            Times.Once);

        _mockPolicyMemberRepository.Verify(
            x => x.UpdateBatchAsync(It.Is<List<PolicyMember>>(list => list.All(pm => pm.IsRemoved) && list.Count == successfulResults.Count), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    private CancelPolicyMemberUploadCommand CreateValidCommand()
    {
        return new CancelPolicyMemberUploadCommand
        {
            PolicyId = _fixture.Create<PolicyId>(),
            UploadId = _fixture.Create<PolicyMemberUploadId>()
        };
    }

    private PolicyMemberUpload CreateUploadWithImportedMembers(PolicyMemberUploadStatus status)
    {
        var upload = PolicyMemberUpload.Create(
            _fixture.Create<PolicyId>(),
            "uploads/test-file.csv",
            100);

        // Use reflection to set the status directly for testing
        PropertyInfo? statusProperty = typeof(PolicyMemberUpload).GetProperty("Status");
        statusProperty!.SetValue(upload, status);

        upload.ImportedResults.Clear();

        // Add successful imported results
        for (int i = 0; i < 3; i++)
        {
            var importedResult = PolicyMemberUploadImportedResult.CreateSuccess(
                upload.Id,
                i + 1,
                _fixture.Create<PolicyMemberId>());
            upload.ImportedResults.Add(importedResult);
        }

        return upload;
    }

    private PolicyMemberUpload CreateUploadWithoutImportedMembers(PolicyMemberUploadStatus status)
    {
        var upload = PolicyMemberUpload.Create(
            _fixture.Create<PolicyId>(),
            "uploads/test-file.csv",
            100);

        // Use reflection to set the status directly for testing
        PropertyInfo? statusProperty = typeof(PolicyMemberUpload).GetProperty("Status");
        statusProperty!.SetValue(upload, status);

        upload.ImportedResults.Clear();
        return upload;
    }

    private PolicyMemberUpload CreateUploadWithMixedImportedResults(PolicyMemberUploadStatus status)
    {
        var upload = PolicyMemberUpload.Create(
            _fixture.Create<PolicyId>(),
            "uploads/test-file.csv",
            100);

        // Use reflection to set the status directly for testing
        PropertyInfo? statusProperty = typeof(PolicyMemberUpload).GetProperty("Status");
        statusProperty!.SetValue(upload, status);

        upload.ImportedResults.Clear();

        // Add successful imported results
        for (int i = 0; i < 2; i++)
        {
            var successResult = PolicyMemberUploadImportedResult.CreateSuccess(
                upload.Id,
                i + 1,
                _fixture.Create<PolicyMemberId>());
            upload.ImportedResults.Add(successResult);
        }

        // Add failed imported results
        for (int i = 0; i < 2; i++)
        {
            var failedResult = PolicyMemberUploadImportedResult.CreateFailure(
                upload.Id,
                i + 3,
                "VALIDATION_ERROR",
                "Validation failed");
            upload.ImportedResults.Add(failedResult);
        }

        return upload;
    }

    private PolicyDto CreateValidPolicy()
    {
        return new PolicyDto
        {
            Id = _fixture.Create<PolicyId>().Value.ToString(),
            Status = "ACTIVE",
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1))
        };
    }

    private List<PolicyMember> CreatePolicyMembersForImportedResults(IEnumerable<PolicyMemberUploadImportedResult> importedResults)
    {
        return importedResults
            .Where(r => r.Success && r.PolicyMemberId != null)
            .Select(r => CreatePolicyMemberForId(r.PolicyMemberId!))
            .ToList();
    }

    private PolicyMember CreatePolicyMemberForId(PolicyMemberId id)
    {
        var member = PolicyMember.Create(
            _fixture.Create<PolicyId>(),
            $"MEMBER-{id.Value}",
            DateOnly.FromDateTime(DateTime.Today),
            DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            "TEST-PLAN");

        // Use reflection to set the ID to match the requested ID
        PropertyInfo? idProperty = typeof(PolicyMember).BaseType?.GetProperty("Id");
        if (idProperty != null && idProperty.CanWrite)
        {
            idProperty.SetValue(member, id);
        }
        else
        {
            // Try to access the private field if property is not writable
            FieldInfo? idField = typeof(PolicyMember).BaseType?.GetField("_id",
                BindingFlags.NonPublic | BindingFlags.Instance);
            idField?.SetValue(member, id);
        }

        return member;
    }

    private void SetupMocksForBusinessLogic(PolicyMemberUpload upload, PolicyDto policy, List<PolicyMember> policyMembers)
    {
        _mockPolicyMemberUploadRepository
            .Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        _mockPolicyMemberUploadRepository
            .Setup(x => x.UpdateAsync(It.IsAny<PolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMemberUpload upload, CancellationToken _) => upload);

        if (policyMembers.Any())
        {
            _mockPolicyMemberRepository
                .Setup(x => x.FindAllAsync(It.IsAny<List<PolicyMemberId>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(policyMembers);

            _mockPolicyMemberRepository
                .Setup(x => x.UpdateBatchAsync(It.IsAny<List<PolicyMember>>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);
        }
    }
}
