using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using System.Text.Json;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Common.DTOs;
using CoverGo.PoliciesV3.Application.PolicyMembers.CreatePolicyMember;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Products;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.Users.Client;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.CreatePolicyMember;

public class CreatePolicyMemberHandlerTests
{
    private readonly Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> _mockPolicyMemberRepository;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IndividualMemberValidationSpecification> _mockIndividualMemberValidationSpec;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IMultiTenantFeatureManager> _mockFeatureManager;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _mockSchemaProvider;
    private readonly CreatePolicyMemberHandler _handler;

    public CreatePolicyMemberHandlerTests()
    {
        // Mocks for dependencies of PolicyMemberValidationDataService
        var mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        var mockProductService = new Mock<IProductService>();
        var mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        var tenantId = new TenantId("test-tenant");
        var mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        var mockValidationLogger = new Mock<ILogger<PolicyMemberValidationDataService>>();

        _mockPolicyMemberRepository = new Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>>();
        _mockLegacyPolicyService = mockLegacyPolicyService;
        _mockProductService = mockProductService;
        _mockFeatureManager = mockFeatureManager;
        _mockSchemaProvider = mockSchemaProvider;
        _mockIndividualMemberValidationSpec = new Mock<IndividualMemberValidationSpecification>(
            new Mock<MemberMustHaveUniqueEmailSpecification>(
                Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>()).Object,
            new Mock<MemberMustHaveUniqueHKIDSpecification>(
                Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>()).Object,
            new Mock<MemberMustHaveUniquePassportSpecification>(
                Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>()).Object,
            new Mock<MemberMustHaveUniqueStaffNumberSpecification>(
                Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>()).Object,
            new Mock<MemberIdMustFollowBusinessRulesSpecification>(
                Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>()).Object,
            new Mock<MemberFieldsMustMatchSchemaSpecification>(
                Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>()).Object,
            new Mock<MemberEffectiveDateMustBeValidSpecification>(
                Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>()).Object,
            new Mock<DependentMustHaveValidPrimaryMemberSpecification>(
                Mock.Of<IPolicyMemberQueryService>(), Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>()).Object,
            new Mock<MemberMustHaveValidPlanIdSpecification>(
                Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>()).Object,
            Mock.Of<IConcurrentMemberProcessor>(),
            Mock.Of<ILogger<IndividualMemberValidationSpecification>>());
        _mockUsersService = new Mock<IUsersService>();
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        var mockLogger = new Mock<ILogger<CreatePolicyMemberHandler>>();
        // Use a real PolicyMemberValidationDataService with mocked dependencies
        var realValidationDataService = new PolicyMemberValidationDataService(
            mockLegacyPolicyService.Object,
            mockProductService.Object,
            mockFeatureManager.Object,
            tenantId,
            mockSchemaProvider.Object,
            mockValidationLogger.Object);

        _handler = new CreatePolicyMemberHandler(
            _mockPolicyMemberRepository.Object,
            _mockLegacyPolicyService.Object,
            _mockIndividualMemberValidationSpec.Object,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            mockLogger.Object,
            realValidationDataService);

        var fixture = new Fixture();
        fixture.Customize<PolicyId>(c => c.FromFactory(() => PolicyId.New));
        fixture.Customize<PolicyMemberId>(c => c.FromFactory(() => PolicyMemberId.New));
    }

    [Fact]
    public async Task Handle_WithValidRequest_ShouldReturnSuccessResponse()
    {
        // Arrange
        CreatePolicyMemberCommand command = CreateValidCommand();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateValidSchema();
        CreateValidResolvedValidationData();

        SetupSuccessfulMocks(policy, schema);

        // Act
        CreatePolicyMemberResponse result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PolicyMemberId.Should().NotBeEmpty();
        result.MemberId.Should().Be(command.MemberId);
        result.PolicyId.Should().Be(command.PolicyId);

        _mockPolicyMemberRepository.Verify(x => x.InsertAsync(It.IsAny<PolicyMember>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithNonExistentPolicy_ShouldThrowPolicyNotFoundException()
    {
        // Arrange
        CreatePolicyMemberCommand command = CreateValidCommand();

        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyDto?)null);

        // Act & Assert
        PolicyNotFoundException exception = await Assert.ThrowsAsync<PolicyNotFoundException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.PolicyId.Should().Be(command.PolicyId.ToString());
    }

    [Fact]
    public async Task Handle_WithPolicyMissingId_ShouldThrowPolicyNotFoundException()
    {
        // Arrange
        CreatePolicyMemberCommand command = CreateValidCommand();
        PolicyDto policyWithoutId = CreateValidPolicy() with { Id = null! };

        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyWithoutId);

        // Act & Assert
        PolicyNotFoundException exception = await Assert.ThrowsAsync<PolicyNotFoundException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.PolicyId.Should().Be(command.PolicyId.ToString());
    }

    [Fact]
    public async Task Handle_WithValidationFailure_ShouldThrowException()
    {
        // Arrange
        CreatePolicyMemberCommand command = CreateValidCommand();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateValidSchema();
        CreateValidResolvedValidationData();

        SetupSuccessfulMocks(policy, schema);

        _mockIndividualMemberValidationSpec
            .Setup(x => x.ValidateBatchAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Validation failed"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(
            () => _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithDependentMember_ShouldCreatePolicyMemberWithDependentOfId()
    {
        // Arrange
        CreatePolicyMemberCommand command = CreateValidCommandWithDependent();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateValidSchema();
        CreateValidResolvedValidationData();

        SetupSuccessfulMocks(policy, schema);

        // Act
        CreatePolicyMemberResponse result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PolicyMemberId.Should().NotBeEmpty();

        _mockPolicyMemberRepository.Verify(x => x.InsertAsync(
            It.Is<PolicyMember>(pm => pm.DependentOfId != null && pm.DependentOfId.Value == command.DependentOfId),
            It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithCustomFields_ShouldCreatePolicyMemberWithFields()
    {
        // Arrange
        CreatePolicyMemberCommand command = CreateValidCommandWithFields();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateValidSchema();
        CreateValidResolvedValidationData();

        SetupSuccessfulMocks(policy, schema);

        // Act
        CreatePolicyMemberResponse result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PolicyMemberId.Should().NotBeEmpty();

        _mockPolicyMemberRepository.Verify(x => x.InsertAsync(
            It.Is<PolicyMember>(pm => pm.States.Any(s => s.Fields.Count == command.Fields.Count)),
            It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithIndividualId_ShouldCreatePolicyMemberWithIndividualId()
    {
        // Arrange
        CreatePolicyMemberCommand command = CreateValidCommandWithIndividualId();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateValidSchema();
        CreateValidResolvedValidationData();

        SetupSuccessfulMocks(policy, schema);

        // Act
        CreatePolicyMemberResponse result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PolicyMemberId.Should().NotBeEmpty();

        _mockPolicyMemberRepository.Verify(x => x.InsertAsync(
            It.Is<PolicyMember>(pm => pm.IndividualId == command.IndividualId),
            It.IsAny<CancellationToken>()),
            Times.Once);
    }

    #region Helper Methods

    private void SetupSuccessfulMocks(PolicyDto policy, PolicyMemberFieldsSchema schema)
    {
        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        // Set up the mocked dependencies for PolicyMemberValidationDataService
        _mockProductService
            .Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);

        _mockProductService
            .Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("standard");

        _mockFeatureManager
            .Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", It.IsAny<string>()))
            .ReturnsAsync(false);

        _mockFeatureManager
            .Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", It.IsAny<string>()))
            .ReturnsAsync(false);

        _mockFeatureManager
            .Setup(x => x.IsEnabled("AllowMembersFromOtherContractHolders", It.IsAny<string>()))
            .ReturnsAsync(false);

        _mockSchemaProvider
            .Setup(x => x.GetCustomFieldsSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _mockSchemaProvider
            .Setup(x => x.GetMemberUploadSchema(
                It.IsAny<string?>(),
                It.IsAny<ProductId>(),
                It.IsAny<EndorsementId?>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _mockIndividualMemberValidationSpec
            .Setup(x => x.ValidateBatchAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(BatchValidationResult.Success(1));

        _mockPolicyMemberRepository
            .Setup(x => x.InsertAsync(It.IsAny<PolicyMember>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMember pm, CancellationToken _) => pm);

        _mockUsersService
            .Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Individual>());

        _mockUsersService
            .Setup(x => x.QueryIndividualsByEmails(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Individual>());

        _mockUsersService
            .Setup(x => x.GetDuplicatedIndividualsByEmails(It.IsAny<Dictionary<string, string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<DuplicatedIndividual>());

        _mockUsersService
            .Setup(x => x.GetCompanyContractHolderMembersFieldsById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((JsonElement)default);

        _mockPolicyMemberQueryService
            .Setup(x => x.GetActiveMembersAsync(It.IsAny<List<PolicyId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<PolicyMember>());

        _mockPolicyMemberQueryService
            .Setup(x => x.GetMemberValidationStatesAsync(It.IsAny<string>(), It.IsAny<List<PolicyId>>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<PolicyMember>());

        _mockPolicyMemberQueryService
            .Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, List<PolicyMember>>
            {
                { "MEMBER001", new List<PolicyMember>() },
                { "MEMBER002", new List<PolicyMember>() },
                { "MEMBER003", new List<PolicyMember>() },
                { "MEMBER004", new List<PolicyMember>() }
            });

        _mockPolicyMemberQueryService
            .Setup(x => x.GetPolicyMemberCurrentStateAsync(It.IsAny<string>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMember?)null);

        _mockPolicyMemberQueryService
            .Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, PolicyMember?>());

        _mockUsersService
            .Setup(x => x.GetMemberIdsWithoutExistingIndividual(It.IsAny<HashSet<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new HashSet<string>());
    }

    private static CreatePolicyMemberCommand CreateValidCommand() => new()
    {
        PolicyId = PolicyId.New,
        MemberId = "MEMBER001",
        StartDate = DateOnly.FromDateTime(DateTime.Today),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
        PlanId = "PLAN-001",
        DependentOfId = null,
        IndividualId = null,
        Fields = Array.Empty<PolicyField>()
    };

    private static CreatePolicyMemberCommand CreateValidCommandWithDependent() => new()
    {
        PolicyId = PolicyId.New,
        MemberId = "MEMBER002",
        StartDate = DateOnly.FromDateTime(DateTime.Today),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
        PlanId = "PLAN-001",
        DependentOfId = PolicyMemberId.New,
        IndividualId = null,
        Fields = Array.Empty<PolicyField>()
    };

    private static CreatePolicyMemberCommand CreateValidCommandWithFields() => new()
    {
        PolicyId = PolicyId.New,
        MemberId = "MEMBER003",
        StartDate = DateOnly.FromDateTime(DateTime.Today),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
        PlanId = "PLAN-001",
        DependentOfId = null,
        IndividualId = null,
        Fields = new List<PolicyField>
        {
            new() { Key = "firstName", Value = "John" },
            new() { Key = "lastName", Value = "Doe" },
            new() { Key = "email", Value = "<EMAIL>" }
        }
    };

    private static CreatePolicyMemberCommand CreateValidCommandWithIndividualId() => new()
    {
        PolicyId = PolicyId.New,
        MemberId = "MEMBER004",
        StartDate = DateOnly.FromDateTime(DateTime.Today),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
        PlanId = "PLAN-001",
        DependentOfId = null,
        IndividualId = Guid.NewGuid(),
        Fields = Array.Empty<PolicyField>()
    };

    private static PolicyDto CreateValidPolicy() => new()
    {
        Id = Guid.NewGuid().ToString(),
        ContractHolderId = "CONTRACT001",
        ProductId = new ProductIdDto { Plan = "BASIC", Type = "HEALTH", Version = "1.0" },
        Endorsements = new List<EndorsementDto>(),
        IsV2 = false,
        StartDate = DateOnly.FromDateTime(DateTime.Today),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1))
    };

    private static PolicyMemberFieldsSchema CreateValidSchema() => new(
        new List<PolicyMemberFieldDefinition>
        {
            new() { Name = "firstName", Label = "First Name", Type = new StringFieldType(), IsRequired = true },
            new() { Name = "lastName", Label = "Last Name", Type = new StringFieldType(), IsRequired = true },
            new() { Name = "email", Label = "Email", Type = new StringFieldType(), IsRequired = true, IsUnique = true }
        });

    private static ResolvedValidationData CreateValidResolvedValidationData() => new()
    {
        IsProductSme = false,
        AvailablePlans = new List<string> { "PLAN-001", "PLAN-002" }.ToHashSet(),
        ContractHolderScopeEndorsements = new List<EndorsementId>(),
        IndividualExistenceMap = new Dictionary<string, bool> { { "MEMBER001", true } },
        ExistingPolicyMembers = new Dictionary<string, PolicyMember?>(),
        MemberValidationStates = new Dictionary<string, IReadOnlyList<PolicyMember>>(),
        DependentMembersCache = new Dictionary<string, PolicyMember?>()
    };

    #endregion
}