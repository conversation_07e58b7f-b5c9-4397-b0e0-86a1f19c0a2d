using CoverGo.PoliciesV3.Application.PolicyMemberUploads.RegisterPolicyMemberUpload;
using FluentValidation.TestHelper;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.RegisterUpload;

public class RegisterPolicyMemberUploadValidatorTests
{
    private readonly RegisterPolicyMemberUploadValidator _validator;

    public RegisterPolicyMemberUploadValidatorTests()
    {
        _validator = new RegisterPolicyMemberUploadValidator();
    }

    [Fact]
    public void Validate_WithValidRequest_ShouldNotHaveValidationErrors()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand request = CreateValidRequest();

        // Act
        TestValidationResult<RegisterPolicyMemberUploadCommand> result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void Validate_WithEmptyPolicyId_ShouldHaveValidationError()
    {
        // Arrange
        var request = new RegisterPolicyMemberUploadCommand
        {
            PolicyId = Guid.Empty,
            EndorsementId = null,
            Path = "uploads/test-file.csv"
        };

        // Act
        TestValidationResult<RegisterPolicyMemberUploadCommand> result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.PolicyId.Value)
            .WithErrorMessage("PolicyId is required");
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("   ")]
    [InlineData("\t")]
    [InlineData("\n")]
    public void Validate_WithEmptyOrWhitespacePath_ShouldHaveValidationError(string invalidPath)
    {
        // Arrange
        var request = new RegisterPolicyMemberUploadCommand
        {
            PolicyId = Guid.NewGuid(),
            EndorsementId = null,
            Path = invalidPath
        };

        // Act
        TestValidationResult<RegisterPolicyMemberUploadCommand> result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Path)
            .WithErrorMessage("Path is required");
    }

    [Fact]
    public void Validate_WithNullPath_ShouldHaveValidationError()
    {
        // Arrange
        var request = new RegisterPolicyMemberUploadCommand
        {
            PolicyId = Guid.NewGuid(),
            EndorsementId = null,
            Path = null!
        };

        // Act
        TestValidationResult<RegisterPolicyMemberUploadCommand> result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Path)
            .WithErrorMessage("Path is required");
    }

    [Fact]
    public void Validate_WithPathExceeding500Characters_ShouldHaveValidationError()
    {
        // Arrange
        var request = new RegisterPolicyMemberUploadCommand
        {
            PolicyId = Guid.NewGuid(),
            EndorsementId = null,
            Path = new string('a', 501) // 501 characters
        };

        // Act
        TestValidationResult<RegisterPolicyMemberUploadCommand> result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Path)
            .WithErrorMessage("Path cannot exceed 500 characters");
    }

    [Fact]
    public void Validate_WithPathExactly500Characters_ShouldNotHaveValidationError()
    {
        // Arrange
        var request = new RegisterPolicyMemberUploadCommand
        {
            PolicyId = Guid.NewGuid(),
            EndorsementId = null,
            Path = new string('a', 500) // Exactly 500 characters
        };

        // Act
        TestValidationResult<RegisterPolicyMemberUploadCommand> result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Path);
    }

    [Fact]
    public void Validate_WithValidEndorsementId_ShouldNotHaveValidationError()
    {
        // Arrange
        var request = new RegisterPolicyMemberUploadCommand
        {
            PolicyId = Guid.NewGuid(),
            EndorsementId = Guid.NewGuid(),
            Path = "uploads/test-file.csv"
        };

        // Act
        TestValidationResult<RegisterPolicyMemberUploadCommand> result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void Validate_WithNullEndorsementId_ShouldNotHaveValidationError()
    {
        // Arrange
        var request = new RegisterPolicyMemberUploadCommand
        {
            PolicyId = Guid.NewGuid(),
            EndorsementId = null,
            Path = "uploads/test-file.csv"
        };

        // Act
        TestValidationResult<RegisterPolicyMemberUploadCommand> result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData("uploads/file.csv")]
    [InlineData("uploads/subfolder/file.xlsx")]
    [InlineData("a")]
    [InlineData("very/long/path/to/some/file/in/deep/directory/structure/file.csv")]
    public void Validate_WithValidPaths_ShouldNotHaveValidationError(string validPath)
    {
        // Arrange
        var request = new RegisterPolicyMemberUploadCommand
        {
            PolicyId = Guid.NewGuid(),
            EndorsementId = null,
            Path = validPath
        };

        // Act
        TestValidationResult<RegisterPolicyMemberUploadCommand> result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Path);
    }

    [Fact]
    public void Validate_WithMultipleErrors_ShouldHaveAllValidationErrors()
    {
        // Arrange
        var request = new RegisterPolicyMemberUploadCommand
        {
            PolicyId = Guid.Empty, // Invalid
            EndorsementId = null,
            Path = "" // Invalid
        };

        // Act
        TestValidationResult<RegisterPolicyMemberUploadCommand> result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.PolicyId.Value)
            .WithErrorMessage("PolicyId is required");
        result.ShouldHaveValidationErrorFor(x => x.Path)
            .WithErrorMessage("Path is required");
    }

    private static RegisterPolicyMemberUploadCommand CreateValidRequest() => new()
    {
        PolicyId = Guid.NewGuid(),
        EndorsementId = null,
        Path = "uploads/test-file.csv"
    };
}
