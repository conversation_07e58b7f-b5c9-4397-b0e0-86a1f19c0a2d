using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using System.Text;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.RegisterPolicyMemberUpload;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Validators;
using CoverGo.PoliciesV3.Domain.CustomFields.Validation;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Domain.Services;
using Microsoft.Extensions.Logging;
using DomainPolicyMemberUpload = CoverGo.PoliciesV3.Domain.PolicyMemberUploads.PolicyMemberUpload;
using PolicyDto = CoverGo.PoliciesV3.Domain.Policies.PolicyDto;
using ProductIdDto = CoverGo.PoliciesV3.Domain.Products.ProductIdDto;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.RegisterUpload;

[Trait("Category", "Unit")]
[Trait("Component", "Application")]
[Trait("Feature", "RegisterPolicyMemberUpload")]
public class RegisterPolicyMemberUploadHandlerTests
{
    private readonly Mock<IPaginatedRepository<DomainPolicyMemberUpload, PolicyMemberUploadId>> _policyMemberUploadRepository;
    private readonly Mock<ILegacyPolicyService> _legacyPolicyService;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _schemaProvider;
    private readonly Mock<IFileSystemService> _fileSystemService;
    private readonly Mock<IFileParserFactory> _fileParserFactory;
    private readonly Mock<IFileParser> _fileParser;
    private readonly Mock<ILogger<RegisterPolicyMemberUploadHandler>> _logger;
    private readonly RegisterPolicyMemberUploadHandler _handler;
    private readonly Fixture _fixture;

    public RegisterPolicyMemberUploadHandlerTests()
    {
        _policyMemberUploadRepository = new Mock<IPaginatedRepository<DomainPolicyMemberUpload, PolicyMemberUploadId>>();
        _legacyPolicyService = new Mock<ILegacyPolicyService>();
        _schemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _fileSystemService = new Mock<IFileSystemService>();
        _fileParserFactory = new Mock<IFileParserFactory>();
        _fileParser = new Mock<IFileParser>();
        _logger = new Mock<ILogger<RegisterPolicyMemberUploadHandler>>();

        _handler = new RegisterPolicyMemberUploadHandler(
            _policyMemberUploadRepository.Object,
            _legacyPolicyService.Object,
            _schemaProvider.Object,
            _fileSystemService.Object,
            _fileParserFactory.Object,
            _logger.Object);

        _fixture = new Fixture();
        _fixture.Customize<PolicyId>(c => c.FromFactory(() => PolicyId.New));
        _fixture.Customize<PolicyMemberUploadId>(c => c.FromFactory(() => PolicyMemberUploadId.New));
    }

    [Fact]
    public async Task Handle_WithValidRequest_ShouldReturnSuccessResponse()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] fileContent = CreateValidCsvFileContent();
        string[] headers = ["memberId", "planId", "effectiveDate"];
        int memberCount = 2;
        PolicyMemberFieldsSchema schema = CreateValidSchema();

        SetupSuccessfulMocks(fileContent, headers, memberCount, schema);

        // Act
        DomainPolicyMemberUpload result = (await _handler.Handle(command, CancellationToken.None)).PolicyMemberUpload;

        // Assert
        result.Should().NotBeNull();
        result.PolicyId.Value.Should().Be(command.PolicyId);
        result.EndorsementId.Should().Be(command.EndorsementId);
        result.Path.Should().Be(command.Path);
        result.MembersCount.Should().Be(memberCount);
        result.Status.Value.Should().Be(PolicyMemberUploadStatus.REGISTERED.Value);
        result.Id.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithNonExistentPolicy_ShouldThrowPolicyNotFoundException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyDto?)null);

        // Act & Assert
        PolicyNotFoundException exception = await Assert.ThrowsAsync<PolicyNotFoundException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.PolicyId.Should().Be(command.PolicyId.Value.ToString());
        exception.Code.Should().Be(ErrorCodes.PolicyNotFound);
    }

    [Fact]
    public async Task Handle_WithPolicyMissingId_ShouldThrowPolicyNotFoundException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();

        PolicyDto policyDtoWithoutId = CreateValidPolicyDto() with { Id = null! }; // Missing essential data

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDtoWithoutId);

        // Act & Assert
        PolicyNotFoundException exception = await Assert.ThrowsAsync<PolicyNotFoundException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.PolicyId.Should().Be(command.PolicyId.Value.ToString());
        exception.Code.Should().Be("POLICY_NOT_FOUND");
    }

    [Fact]
    public async Task Handle_WithPolicyEmptyId_ShouldThrowPolicyNotFoundException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();

        PolicyDto policyDtoWithEmptyId = CreateValidPolicyDto() with { Id = "" }; // Empty essential data

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDtoWithEmptyId);

        // Act & Assert
        PolicyNotFoundException exception = await Assert.ThrowsAsync<PolicyNotFoundException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.PolicyId.Should().Be(command.PolicyId.Value.ToString());
        exception.Code.Should().Be(ErrorCodes.PolicyNotFound);
    }

    [Fact]
    public async Task Handle_WithNullRequest_ShouldThrowNullReferenceException() =>
        // Arrange

        // Act & Assert
        // Handler doesn't have null checks, so it throws NullReferenceException when accessing command.PolicyId
        await Assert.ThrowsAsync<NullReferenceException>(
            () => _handler.Handle(null!, CancellationToken.None));

    [Fact]
    public async Task Handle_WithEmptyFilePath_ShouldThrowUploadFileNotFoundException()
    {
        // Arrange
        var command = new RegisterPolicyMemberUploadCommand
        {
            PolicyId = Guid.NewGuid(),
            EndorsementId = null,
            Path = "" // Empty path
        };

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        // File system will return null for empty path
        _fileSystemService
            .Setup(x => x.GetFileByPath("", It.IsAny<CancellationToken>()))
            .ReturnsAsync((byte[]?)null);

        // Act & Assert
        // Handler validates policy first, then tries to get file, which throws UploadFileNotFoundException for empty path
        UploadFileNotFoundException exception = await Assert.ThrowsAsync<UploadFileNotFoundException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.PolicyId.Should().Be(command.PolicyId.Value.ToString());
        exception.Path.Should().Be("");
    }

    [Fact]
    public async Task Handle_WithEmptyFileContent_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] emptyFileContent = [];

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyFileContent);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ErrorCode.Should().Be(ErrorCodes.EmptyFile);
    }

    [Fact]
    public async Task Handle_WithPolicyMissingProductId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto policyDtoWithoutProductId = CreateValidPolicyDto() with { ProductId = null! }; // Missing ProductId

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDtoWithoutProductId);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(validFileContent, parseResult);

        // Act & Assert
        PolicyProductIdMissingException exception = await Assert.ThrowsAsync<PolicyProductIdMissingException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Contain("is missing ProductId");
    }

    [Fact]
    public async Task Handle_WithPolicyMissingProductPlan_ShouldThrowInvalidOperationException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto basePolicy = CreateValidPolicyDto();
        PolicyDto policyDtoWithoutPlan = basePolicy with
        {
            ProductId = basePolicy.ProductId! with { Plan = null! }
        }; // Missing Plan

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDtoWithoutPlan);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(validFileContent, parseResult);

        // Act & Assert
        InvalidProductIdComponentException exception = await Assert.ThrowsAsync<InvalidProductIdComponentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Contain("is missing Plan");
    }

    [Fact]
    public async Task Handle_WithPolicyMissingProductType_ShouldThrowInvalidOperationException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto basePolicy2 = CreateValidPolicyDto();
        PolicyDto policyDtoWithoutType = basePolicy2 with
        {
            ProductId = basePolicy2.ProductId! with { Type = null! }
        }; // Missing Type

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDtoWithoutType);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(validFileContent, parseResult);

        // Act & Assert
        InvalidProductIdComponentException exception = await Assert.ThrowsAsync<InvalidProductIdComponentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Contain("is missing Type");
    }

    [Fact]
    public async Task Handle_WithFileNotFound_ShouldThrowUploadFileNotFoundException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((byte[]?)null);

        // Act & Assert
        UploadFileNotFoundException exception = await Assert.ThrowsAsync<UploadFileNotFoundException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.PolicyId.Should().Be(command.PolicyId.Value.ToString());
        exception.Path.Should().Be(command.Path);
        exception.Code.Should().Be(ErrorCodes.UploadFileNotFound);
    }

    [Fact]
    public async Task Handle_WithEmptyFile_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] emptyFileContent = [];

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(emptyFileContent))
            .Throws(new BadFileContentException(ErrorCodes.EmptyFile, "File content is empty"));

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ErrorCode.Should().Be(ErrorCodes.EmptyFile);
        exception.Code.Should().Be(ErrorCodes.EmptyFile);
    }

    [Fact]
    public async Task Handle_WithNullParseResult_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns((FileParseResult)null!); // Return null to trigger exception

        _fileParser
            .Setup(x => x.ParseFileAsync(validFileContent, It.IsAny<CancellationToken>()))
            .ReturnsAsync((FileParseResult)null!); // Return null to trigger exception

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ErrorCode.Should().Be(ErrorCodes.UnexpectedError);
        exception.Message.Should().Be("Failed to parse file content");
    }

    [Fact]
    public async Task Handle_WithNullHeaders_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = null!, // Null headers to trigger exception
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };

        SetupFileParserMock(validFileContent, parseResult);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ErrorCode.Should().Be(ErrorCodes.NoColumn);
        exception.Message.Should().Be("No headers found in file");
    }

    [Fact]
    public async Task Handle_WithEmptyHeaders_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = [], // Empty headers to trigger exception
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };

        SetupFileParserMock(validFileContent, parseResult);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ErrorCode.Should().Be(ErrorCodes.NoColumn);
        exception.Message.Should().Be("No headers found in file");
    }

    [Fact]
    public async Task Handle_WithZeroMemberCount_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [] // Empty contents to trigger zero count
        };

        SetupFileParserMock(validFileContent, parseResult);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ErrorCode.Should().Be(ErrorCodes.NoMember);
        exception.Message.Should().Be("No member data found in file");
    }

    [Fact]
    public async Task Handle_WithPolicyMissingProductPlanWhitespace_ShouldThrowInvalidOperationException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto basePolicy3 = CreateValidPolicyDto();
        PolicyDto policyDtoWithWhitespacePlan = basePolicy3 with
        {
            ProductId = basePolicy3.ProductId! with { Plan = "   " }
        }; // Whitespace Plan

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDtoWithWhitespacePlan);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(validFileContent, parseResult);

        // Act & Assert
        InvalidProductIdComponentException exception = await Assert.ThrowsAsync<InvalidProductIdComponentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Contain("is missing Plan");
    }

    [Fact]
    public async Task Handle_WithPolicyMissingProductTypeWhitespace_ShouldThrowInvalidOperationException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto basePolicy4 = CreateValidPolicyDto();
        PolicyDto policyDtoWithWhitespaceType = basePolicy4 with
        {
            ProductId = basePolicy4.ProductId! with { Type = "   " }
        }; // Whitespace Type

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDtoWithWhitespaceType);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(validFileContent, parseResult);

        // Act & Assert
        InvalidProductIdComponentException exception = await Assert.ThrowsAsync<InvalidProductIdComponentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Contain("is missing Type");
    }

    [Fact]
    public async Task Handle_WithNullSchema_ShouldThrowInvalidOperationException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(validFileContent, parseResult);

        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMemberFieldsSchema)null!); // Return null schema

        // Act & Assert
        BadSchemaConfigException exception = await Assert.ThrowsAsync<BadSchemaConfigException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Be("Failed to retrieve valid schema for policy member upload validation");
        exception.Code.Should().Be(ErrorCodes.BadSchemaConfig);
    }



    [Fact]
    public async Task Handle_WithMissingOneOfMandatoryColumns_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"], // Missing both required fields from OneOf validation
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(validFileContent, parseResult);

        PolicyMemberFieldsSchema schema = CreateSchemaWithOneOfValidation();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ErrorCode.Should().Be(ErrorCodes.MissingOneOfMandatoryColumns);
        exception.Message.Should().Contain("Missing one of mandatory columns");
    }

    [Fact]
    public async Task Handle_WithMissingMandatoryColumns_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId"], // Missing required field "firstName"
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(validFileContent, parseResult);

        PolicyMemberFieldsSchema schema = CreateSchemaWithMandatoryFields();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ErrorCode.Should().Be(ErrorCodes.MissingMandatoryColumns);
        exception.Message.Should().Contain("Mandatory column(s) are missing");
        exception.Message.Should().Contain("First Name"); // Uses Label, not Name
    }

    [Fact]
    public async Task Handle_WithExtraFormulaColumns_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["Calculated Field"], // Contains formula field that should not be uploaded (using Label)
            Contents = [new Dictionary<string, string?> { { "Calculated Field", "100" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(validFileContent, parseResult);

        PolicyMemberFieldsSchema schema = CreateSchemaWithFormulaFields();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ErrorCode.Should().Be(ErrorCodes.ExtraColumns);
        exception.Message.Should().Contain("Please remove column(s): Calculated Field"); // Uses Label, not Name
    }

    [Fact]
    public async Task Handle_WithMissingMandatoryObjectSubfields_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId"], // Missing required object subfield
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(validFileContent, parseResult);

        PolicyMemberFieldsSchema schema = CreateSchemaWithObjectFields();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ErrorCode.Should().Be(ErrorCodes.MissingMandatoryColumns);
        exception.Message.Should().Contain("Mandatory column(s) are missing");
    }

    [Fact]
    public async Task Handle_WithValidSchemaButNoValidationErrors_ShouldSucceed()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId"], // Contains all required fields (no mandatory fields in this schema)
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(validFileContent, parseResult);

        PolicyMemberFieldsSchema schema = CreateValidSchema(); // Use schema with no mandatory fields
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<DomainPolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<DomainPolicyMemberUpload>());

        // Act
        DomainPolicyMemberUpload result = (await _handler.Handle(command, CancellationToken.None)).PolicyMemberUpload;

        // Assert
        result.Should().NotBeNull();
        result.PolicyId.Value.Should().Be(command.PolicyId);
        result.MembersCount.Should().Be(1);
    }

    [Fact]
    public async Task Handle_WithNullOneOfValidations_ShouldNotThrowException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(validFileContent, parseResult);

        var schema = new PolicyMemberFieldsSchema(
            memberFields: [new PolicyMemberFieldDefinition
            {
                Name = "memberId",
                Label = "Member ID",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            }],
            oneOfValidations: null // Null OneOfValidations should not cause issues
        );

        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<DomainPolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<DomainPolicyMemberUpload>());

        // Act
        DomainPolicyMemberUpload result = (await _handler.Handle(command, CancellationToken.None)).PolicyMemberUpload;

        // Assert
        result.Should().NotBeNull();
        result.PolicyId.Value.Should().Be(command.PolicyId);
    }

    [Fact]
    public async Task Handle_WithMultipleValidationErrors_ShouldThrowBadFileContentExceptionWithMultipleErrors()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["Calculated Field"], // Contains formula field (should be removed) and missing mandatory field "firstName"
            Contents = [new Dictionary<string, string?> { { "Calculated Field", "100" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(validFileContent, parseResult);

        PolicyMemberFieldsSchema schema = CreateSchemaWithMultipleValidationIssues();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(command, CancellationToken.None));

        // Should contain multiple error messages
        exception.Message.Should().Contain("Mandatory column(s) are missing");
        exception.Message.Should().Contain("Please remove column(s): Calculated Field"); // Uses Label, not Name
    }

    private static RegisterPolicyMemberUploadCommand CreateValidRequest() => new()
    {
        PolicyId = Guid.NewGuid(),
        EndorsementId = null,
        Path = "uploads/test-file.csv"
    };

    private static PolicyDto CreateValidPolicyDto() => new()
    {
        Id = Guid.NewGuid().ToString(),
        StartDate = DateOnly.FromDateTime(DateTime.Today),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
        IsIssued = false,
        ContractHolderId = Guid.NewGuid().ToString(),
        ProductId = new ProductIdDto
        {
            Plan = "TEST_PLAN",
            Version = "1.0",
            Type = "INDIVIDUAL"
        }
    };

    private static byte[] CreateValidCsvFileContent()
    {
        string csvContent = "memberId,planId,effectiveDate\nMEM001,PLAN001,2024-01-01\nMEM002,PLAN001,2024-01-01";
        return Encoding.UTF8.GetBytes(csvContent);
    }

    private static PolicyMemberFieldsSchema CreateValidSchema()
    {
        // Create a simple schema that will work with the current implementation
        // We need to provide the required MemberFields property as IReadOnlyList
        var memberFields = new List<PolicyMemberFieldDefinition>();
        return new PolicyMemberFieldsSchema(memberFields);
    }

    private static PolicyMemberFieldsSchema CreateSchemaWithOneOfValidation()
    {
        var field1 = new PolicyMemberFieldDefinition
        {
            Name = "email",
            Label = "Email",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };

        var field2 = new PolicyMemberFieldDefinition
        {
            Name = "phone",
            Label = "Phone",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };

        var oneOfValidation = new CustomFieldOneOfValidation
        {
            Validations = [
                new CustomFieldRequiredValidation { Field = field1, IsRequired = true },
                new CustomFieldRequiredValidation { Field = field2, IsRequired = true }
            ]
        };

        return new PolicyMemberFieldsSchema(
            memberFields: [field1, field2],
            oneOfValidations: [oneOfValidation]
        );
    }

    private static PolicyMemberFieldsSchema CreateSchemaWithMandatoryFields()
    {
        var mandatoryField = new PolicyMemberFieldDefinition
        {
            Name = "firstName",
            Label = "First Name",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };

        var optionalField = new PolicyMemberFieldDefinition
        {
            Name = "memberId",
            Label = "Member ID",
            Type = new StringFieldType(),
            IsRequired = false,
            IsUnique = false
        };

        return new PolicyMemberFieldsSchema([mandatoryField, optionalField]);
    }

    private static PolicyMemberFieldsSchema CreateSchemaWithFormulaFields()
    {
        var formulaField = new PolicyMemberFieldDefinition
        {
            Name = "calculatedField",
            Label = "Calculated Field",
            Type = new FormulaFieldType(),
            IsRequired = false,
            IsUnique = false
        };

        return new PolicyMemberFieldsSchema([formulaField]);
    }

    private static PolicyMemberFieldsSchema CreateSchemaWithObjectFields()
    {
        var requiredSubfield = new PolicyMemberFieldDefinition
        {
            Name = "address.street",
            Label = "Street Address",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };

        var objectField = new PolicyMemberFieldDefinition
        {
            Name = "address",
            Label = "Address",
            Type = new ObjectFieldType([requiredSubfield]),
            IsRequired = true,
            IsUnique = false
        };

        return new PolicyMemberFieldsSchema([objectField]);
    }

    private static PolicyMemberFieldsSchema CreateSchemaWithMultipleValidationIssues()
    {
        var mandatoryField = new PolicyMemberFieldDefinition
        {
            Name = "firstName",
            Label = "First Name",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };

        var formulaField = new PolicyMemberFieldDefinition
        {
            Name = "calculatedField",
            Label = "Calculated Field",
            Type = new FormulaFieldType(),
            IsRequired = false,
            IsUnique = false
        };

        return new PolicyMemberFieldsSchema([mandatoryField, formulaField]);
    }

    #region File Size Validation Tests

    [Fact]
    public async Task Handle_WithFileSizeTooLarge_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();

        // Create oversized file content (50MB + 1 byte for CSV)
        long oversizedLength = ValidationConstants.FileSizeLimits.MaxCsvFileSizeBytes + 1;
        byte[] oversizedFileContent = new byte[oversizedLength];

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(oversizedFileContent);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ErrorCode.Should().Be(ErrorCodes.FileTooLarge);
        exception.Message.Should().Contain("Upload File validation failed");
    }

    [Theory]
    [InlineData("test.csv", 50L * 1024 * 1024)] // MaxCsvFileSizeBytes
    [InlineData("test.xlsx", 100L * 1024 * 1024)] // MaxXlsxFileSizeBytes  
    [InlineData("test.xls", 100L * 1024 * 1024)] // MaxXlsxFileSizeBytes
    [InlineData("test.txt", 100L * 1024 * 1024)] // MaxGeneralFileSizeBytes
    [InlineData("test.unknown", 100L * 1024 * 1024)] // MaxGeneralFileSizeBytes
    public async Task Handle_WithDifferentFileTypesAtMaxSize_ShouldSucceed(string fileName, long maxSize)
    {
        // Arrange
        var command = new RegisterPolicyMemberUploadCommand
        {
            PolicyId = Guid.NewGuid(),
            EndorsementId = null,
            Path = $"uploads/{fileName}"
        };

        // Create file content at exactly max size
        byte[] fileContentAtMaxSize = new byte[maxSize];
        string csvContent = "memberId,planId\nMEM001,PLAN001";
        byte[] csvBytes = Encoding.UTF8.GetBytes(csvContent);
        Array.Copy(csvBytes, fileContentAtMaxSize, Math.Min(csvBytes.Length, fileContentAtMaxSize.Length));

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fileContentAtMaxSize);

        _fileParserFactory
            .Setup(x => x.CreateParser(fileContentAtMaxSize))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(fileContentAtMaxSize, parseResult);

        PolicyMemberFieldsSchema schema = CreateValidSchema();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<DomainPolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<DomainPolicyMemberUpload>());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PolicyMemberUpload.Should().NotBeNull();
    }

    [Theory]
    [InlineData("test.csv", 50L * 1024 * 1024 + 1)] // Just over CSV limit
    [InlineData("test.xlsx", 100L * 1024 * 1024 + 1)] // Just over XLSX limit
    [InlineData("test.unknown", 100L * 1024 * 1024 + 1)] // Just over general limit
    public async Task Handle_WithFileExceedingTypeSpecificLimit_ShouldThrowBadFileContentException(string fileName, long oversizedLength)
    {
        // Arrange
        var command = new RegisterPolicyMemberUploadCommand
        {
            PolicyId = Guid.NewGuid(),
            EndorsementId = null,
            Path = $"uploads/{fileName}"
        };

        byte[] oversizedFileContent = new byte[oversizedLength];

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(oversizedFileContent);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ErrorCode.Should().Be(ErrorCodes.FileTooLarge);
    }

    [Fact]
    public async Task Handle_WithMinimumValidFileSize_ShouldSucceed()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();

        // Create minimum valid file (1 byte)
        byte[] minFileContent = [1];

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(minFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(minFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(minFileContent, parseResult);

        PolicyMemberFieldsSchema schema = CreateValidSchema();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<DomainPolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<DomainPolicyMemberUpload>());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PolicyMemberUpload.Should().NotBeNull();
    }

    #endregion

    #region Parallel Validation Tests

    [Fact]
    public async Task Handle_WithParallelValidation_ShouldExecutePolicyAndFileValidationConcurrently()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] fileContent = CreateValidCsvFileContent();
        var executionOrder = new List<string>();
        var policyDelayMs = 100;
        var fileDelayMs = 50;

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(async () =>
            {
                executionOrder.Add("PolicyStart");
                await Task.Delay(policyDelayMs);
                executionOrder.Add("PolicyEnd");
                return policyDto;
            });

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(async () =>
            {
                executionOrder.Add("FileStart");
                await Task.Delay(fileDelayMs);
                executionOrder.Add("FileEnd");
                return fileContent;
            });

        _fileParserFactory
            .Setup(x => x.CreateParser(fileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(fileContent, parseResult);

        PolicyMemberFieldsSchema schema = CreateValidSchema();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<DomainPolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<DomainPolicyMemberUpload>());

        // Act
        var startTime = DateTime.UtcNow;
        var result = await _handler.Handle(command, CancellationToken.None);
        var endTime = DateTime.UtcNow;

        // Assert
        result.Should().NotBeNull();

        // Verify parallel execution by checking that total time is less than sequential time
        var totalTime = (endTime - startTime).TotalMilliseconds;
        var sequentialTime = policyDelayMs + fileDelayMs;
        totalTime.Should().BeLessThan(sequentialTime, "because operations should run in parallel");

        // Verify both operations started before either completed
        executionOrder.Should().ContainInOrder("PolicyStart", "FileStart");
        executionOrder.Should().Contain("PolicyEnd");
        executionOrder.Should().Contain("FileEnd");
    }

    [Fact]
    public async Task Handle_WithPolicyValidationFailure_ShouldNotAffectFileValidation()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] fileContent = CreateValidCsvFileContent();

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new PolicyNotFoundException(command.PolicyId.Value.ToString()));

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fileContent);

        // Act & Assert
        PolicyNotFoundException exception = await Assert.ThrowsAsync<PolicyNotFoundException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.PolicyId.Should().Be(command.PolicyId.Value.ToString());

        // Verify that both tasks were attempted
        _legacyPolicyService.Verify(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
        _fileSystemService.Verify(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithFileValidationFailure_ShouldNotAffectPolicyValidation()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new UploadFileNotFoundException(command.PolicyId.Value.ToString(), command.Path));

        // Act & Assert
        UploadFileNotFoundException exception = await Assert.ThrowsAsync<UploadFileNotFoundException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.PolicyId.Should().Be(command.PolicyId.Value.ToString());
        exception.Path.Should().Be(command.Path);

        // Verify that both tasks were attempted
        _legacyPolicyService.Verify(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
        _fileSystemService.Verify(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region Enhanced Schema Validation Tests

    [Fact]
    public async Task Handle_WithSchemaRetrievalFailure_ShouldThrowBadSchemaConfigException()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] fileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(fileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(fileContent, parseResult);

        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Schema service unavailable"));

        // Act & Assert
        BadSchemaConfigException exception = await Assert.ThrowsAsync<BadSchemaConfigException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Contain("Failed to retrieve schema configuration");
        exception.InnerException.Should().BeOfType<InvalidOperationException>();
    }

    [Fact]
    public async Task Handle_WithComplexSchemaValidation_ShouldValidateAllFieldTypes()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] fileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(fileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "First Name", "email", "Phone"], // Use field labels that match GetFullLabel() results
            Contents = [new Dictionary<string, string?>
            {
                { "memberId", "MEM001" },
                { "First Name", "John" },
                { "email", "<EMAIL>" },
                { "Phone", "************" }
            }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(fileContent, parseResult);

        PolicyMemberFieldsSchema schema = CreateComplexSchema();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<DomainPolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<DomainPolicyMemberUpload>());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PolicyMemberUpload.Should().NotBeNull();
    }

    #endregion

    #region Enhanced File Processing Tests

    [Fact]
    public async Task Handle_WithSingleDownloadStrategy_ShouldOnlyDownloadFileOnce()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] fileContent = CreateValidCsvFileContent();

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(fileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(fileContent, parseResult);

        PolicyMemberFieldsSchema schema = CreateValidSchema();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<DomainPolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<DomainPolicyMemberUpload>());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Verify file was downloaded exactly once
        _fileSystemService.Verify(
            x => x.GetFileByPath(command.Path, It.IsAny<CancellationToken>()),
            Times.Once,
            "File should only be downloaded once to avoid performance issues");
    }

    [Fact]
    public async Task Handle_WithLargeValidFile_ShouldProcessEfficiently()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();

        // Create a large but valid file (10MB)
        var largeValidSize = 10L * 1024 * 1024;
        byte[] largeFileContent = new byte[largeValidSize];
        string csvContent = "memberId,planId\nMEM001,PLAN001\nMEM002,PLAN002";
        byte[] csvBytes = Encoding.UTF8.GetBytes(csvContent);
        Array.Copy(csvBytes, largeFileContent, csvBytes.Length);

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(largeFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(largeFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [
                new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } },
                new Dictionary<string, string?> { { "memberId", "MEM002" }, { "planId", "PLAN002" } }
            ]
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(largeFileContent, parseResult);

        PolicyMemberFieldsSchema schema = CreateValidSchema();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<DomainPolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<DomainPolicyMemberUpload>());

        // Act
        var startTime = DateTime.UtcNow;
        var result = await _handler.Handle(command, CancellationToken.None);
        var endTime = DateTime.UtcNow;

        // Assert
        result.Should().NotBeNull();
        result.PolicyMemberUpload.MembersCount.Should().Be(2);

        // Verify processing completed in reasonable time (less than 5 seconds for this test)
        var processingTime = (endTime - startTime).TotalSeconds;
        processingTime.Should().BeLessThan(5, "Large file processing should be efficient");
    }

    #endregion

    #region Helper Methods for New Tests

    private static PolicyMemberFieldsSchema CreateComplexSchema()
    {
        var stringField = new PolicyMemberFieldDefinition
        {
            Name = "memberId",
            Label = "Member ID",
            Type = new StringFieldType(),
            IsRequired = false,
            IsUnique = false
        };

        var mandatoryField = new PolicyMemberFieldDefinition
        {
            Name = "firstName",
            Label = "First Name",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };

        var emailField = new PolicyMemberFieldDefinition
        {
            Name = "email",
            Label = "Email",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };

        var phoneField = new PolicyMemberFieldDefinition
        {
            Name = "phone",
            Label = "Phone",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };

        var oneOfValidation = new CustomFieldOneOfValidation
        {
            Validations = [
                new CustomFieldRequiredValidation { Field = emailField, IsRequired = true },
                new CustomFieldRequiredValidation { Field = phoneField, IsRequired = true }
            ]
        };

        return new PolicyMemberFieldsSchema(
            memberFields: [stringField, mandatoryField, emailField, phoneField],
            oneOfValidations: [oneOfValidation]
        );
    }

    #endregion

    #region Async File Processing Tests

    [Fact]
    public async Task Handle_WithLargeFile_ShouldUseAsyncProcessing()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();

        // Create a large file (2MB) to trigger async processing
        byte[] largeFileContent = new byte[2 * 1024 * 1024];
        string csvContent = "memberId,planId\nMEM001,PLAN001";
        byte[] csvBytes = Encoding.UTF8.GetBytes(csvContent);
        Array.Copy(csvBytes, largeFileContent, csvBytes.Length);

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(largeFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(largeFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        // Verify that async method is called for large files
        _fileParser
            .Setup(x => x.ParseFileAsync(largeFileContent, It.IsAny<CancellationToken>()))
            .ReturnsAsync(parseResult);

        PolicyMemberFieldsSchema schema = CreateValidSchema();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<DomainPolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<DomainPolicyMemberUpload>());

        // Act
        RegisterPolicyMemberUploadResponse result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PolicyMemberUpload.Should().NotBeNull();

        // Verify async method was called
        _fileParser.Verify(x => x.ParseFileAsync(largeFileContent, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] fileContent = CreateValidCsvFileContent();

        using var cts = new CancellationTokenSource();
        cts.Cancel(); // Cancel immediately

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(fileContent))
            .Returns(_fileParser.Object);

        _fileParser
            .Setup(x => x.ParseFileAsync(fileContent, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _handler.Handle(command, cts.Token));
    }

    [Fact]
    public async Task Handle_WithParallelValidation_ShouldExecuteConcurrently()
    {
        // Arrange
        RegisterPolicyMemberUploadCommand command = CreateValidRequest();
        byte[] fileContent = CreateValidCsvFileContent();
        var executionOrder = new List<string>();
        int policyDelayMs = 100;
        int fileDelayMs = 50;

        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(async () =>
            {
                executionOrder.Add("Policy Start");
                await Task.Delay(policyDelayMs);
                executionOrder.Add("Policy End");
                return policyDto;
            });

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(fileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFileAsync(fileContent, It.IsAny<CancellationToken>()))
            .Returns(async () =>
            {
                executionOrder.Add("File Start");
                await Task.Delay(fileDelayMs);
                executionOrder.Add("File End");
                return parseResult;
            });

        PolicyMemberFieldsSchema schema = CreateValidSchema();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<DomainPolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<DomainPolicyMemberUpload>());

        // Act
        DateTime startTime = DateTime.UtcNow;
        RegisterPolicyMemberUploadResponse result = await _handler.Handle(command, CancellationToken.None);
        DateTime endTime = DateTime.UtcNow;

        // Assert
        result.Should().NotBeNull();
        result.PolicyMemberUpload.Should().NotBeNull();

        // Verify parallel execution - both should start before either ends
        executionOrder.Should().Contain("Policy Start");
        executionOrder.Should().Contain("File Start");
        executionOrder.Should().Contain("Policy End");
        executionOrder.Should().Contain("File End");

        // File should complete first (shorter delay)
        int fileEndIndex = executionOrder.IndexOf("File End");
        int policyEndIndex = executionOrder.IndexOf("Policy End");
        fileEndIndex.Should().BeLessThan(policyEndIndex);

        // Total time should be closer to the longer delay (parallel execution)
        TimeSpan totalTime = endTime - startTime;
        totalTime.TotalMilliseconds.Should().BeLessThan(policyDelayMs + fileDelayMs + 30); // Allow more margin for test execution overhead
    }

    /// <summary>
    /// Helper method to setup file parser mock for both sync and async methods
    /// </summary>
    private void SetupFileParserMock(byte[] fileContent, FileParseResult parseResult)
    {
        _fileParser
            .Setup(x => x.ParseFile(fileContent))
            .Returns(parseResult);

        _fileParser
            .Setup(x => x.ParseFileAsync(fileContent, It.IsAny<CancellationToken>()))
            .ReturnsAsync(parseResult);
    }

    private void SetupSuccessfulMocks(byte[] fileContent, string[] headers, int memberCount, PolicyMemberFieldsSchema schema)
    {
        PolicyDto policyDto = CreateValidPolicyDto();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(fileContent))
            .Returns(_fileParser.Object);

        var memberData = Enumerable.Range(0, memberCount)
            .Select(_ => new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } } as IReadOnlyDictionary<string, string?>)
            .ToList();

        var parseResult = new FileParseResult
        {
            Headers = [.. headers],
            Contents = memberData
        };
        parseResult.InitializeHeadersSet();

        SetupFileParserMock(fileContent, parseResult);

        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<DomainPolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<DomainPolicyMemberUpload>());
    }

    #endregion
}