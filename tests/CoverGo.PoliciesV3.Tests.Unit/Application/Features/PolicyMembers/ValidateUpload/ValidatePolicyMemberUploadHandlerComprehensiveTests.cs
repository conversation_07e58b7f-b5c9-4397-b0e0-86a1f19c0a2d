using System.Reflection;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Tests.Unit.TestData;
using CoverGo.Users.Client;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.ValidateUpload;

public class ValidatePolicyMemberUploadHandlerComprehensiveTests
{
    #region Test Setup and Dependencies

    private readonly Mock<IPolicyMemberUploadRepository> _mockUploadRepository;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IFileProcessingService> _mockFileProcessingService;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _mockSchemaProvider;
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<IPolicyMemberUniquenessService> _mockPolicyMemberUniquenessService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IMultiTenantFeatureManager> _mockFeatureManager;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<ILogger<ValidatePolicyMemberUploadHandler>> _mockLogger;
    private readonly Mock<IMemoryCache> _mockMemoryCache;

    // Specification mocks
    private readonly Mock<CompleteUploadValidationSpecification> _mockCompleteValidationSpec;

    private readonly ValidatePolicyMemberUploadHandler _handler;
    private readonly TenantId _tenantId;
    private readonly Fixture _fixture;

    public ValidatePolicyMemberUploadHandlerComprehensiveTests()
    {
        // Initialize mocks
        _mockUploadRepository = new Mock<IPolicyMemberUploadRepository>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockFileProcessingService = new Mock<IFileProcessingService>();
        _mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        _mockPolicyMemberUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        _mockProductService = new Mock<IProductService>();
        _mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockUsersService = new Mock<IUsersService>();
        // Create real instances of specifications with mock dependencies
        var uploadUniqueEmailsSpec = new UploadMustHaveUniqueEmailsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueEmailsSpecification>>());
        var uploadUniqueIdSpec = new UploadMustHaveUniqueIdentificationSpecification(Mock.Of<ILogger<UploadMustHaveUniqueIdentificationSpecification>>());
        var uploadUniqueMemberIdsSpec = new UploadMustHaveUniqueMemberIdsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>());
        var dependentPlanSpec = new DependentAndEmployeeMustBeOnSamePlanSpecification(Mock.Of<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>());
        // Setup mock for IUploadValidationOrchestrator to return empty errors dictionary
        var mockUploadValidationOrchestrator = new Mock<IUploadValidationOrchestrator>();
        mockUploadValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var uploadWideSpec = new UploadWideValidationSpecification(
            uploadUniqueEmailsSpec,
            uploadUniqueIdSpec,
            uploadUniqueMemberIdsSpec,
            dependentPlanSpec,
            mockUploadValidationOrchestrator.Object,
            Mock.Of<ILogger<UploadWideValidationSpecification>>());

        var memberUniqueEmailSpec = new MemberMustHaveUniqueEmailSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>());
        var memberUniqueHkidSpec = new MemberMustHaveUniqueHKIDSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>());
        var memberUniquePassportSpec = new MemberMustHaveUniquePassportSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>());
        var memberUniqueStaffSpec = new MemberMustHaveUniqueStaffNumberSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>());
        var memberIdBusinessRulesSpec = new MemberIdMustFollowBusinessRulesSpecification(Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>());
        var memberFieldsSchemaSpec = new MemberFieldsMustMatchSchemaSpecification(Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>());
        var memberEffectiveDateSpec = new MemberEffectiveDateMustBeValidSpecification(Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>());
        var dependentValidationSpec = new DependentMustHaveValidPrimaryMemberSpecification(Mock.Of<IPolicyMemberQueryService>(), Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>());
        var memberValidPlanIdSpec = new MemberMustHaveValidPlanIdSpecification(Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>());

        // Setup mock for IConcurrentMemberProcessor to return empty errors dictionary
        var mockConcurrentMemberProcessor = new Mock<IConcurrentMemberProcessor>();
        mockConcurrentMemberProcessor.Setup(x => x.ProcessMembersAsync(
                It.IsAny<int>(),
                It.IsAny<Func<int, Task<List<ValidationError>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var individualMemberSpec = new IndividualMemberValidationSpecification(
            memberUniqueEmailSpec,
            memberUniqueHkidSpec,
            memberUniquePassportSpec,
            memberUniqueStaffSpec,
            memberIdBusinessRulesSpec,
            memberFieldsSchemaSpec,
            memberEffectiveDateSpec,
            dependentValidationSpec,
            memberValidPlanIdSpec,
            mockConcurrentMemberProcessor.Object,
            Mock.Of<ILogger<IndividualMemberValidationSpecification>>());

        // Setup mock for IValidationErrorAggregator to return valid BatchValidationResult
        var mockErrorAggregator = new Mock<IValidationErrorAggregator>();
        mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns((List<BatchValidationResult> results, int _) =>
            {
                int totalValid = results.Sum(r => r.ValidCount);
                int totalInvalid = results.Sum(r => r.InvalidCount);
                var allErrors = results.SelectMany(r => r.RowErrors).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                return BatchValidationResult.WithErrors(totalValid, totalInvalid, allErrors);
            });

        _mockCompleteValidationSpec = new Mock<CompleteUploadValidationSpecification>(
            uploadWideSpec,
            individualMemberSpec,
            mockErrorAggregator.Object,
            Mock.Of<ILogger<CompleteUploadValidationSpecification>>());

        // Setup the ValidateAsync method to return a successful result by default
        _mockCompleteValidationSpec.Setup(x => x.ValidateAsync(It.IsAny<CompleteValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(ValidationOrchestrationResult.Success(0, 0, []));
        _mockLogger = new Mock<ILogger<ValidatePolicyMemberUploadHandler>>();
        _mockMemoryCache = new Mock<IMemoryCache>();

        // Setup configuration
        var mockSection = new Mock<IConfigurationSection>();
        mockSection.Setup(s => s.Value).Returns("10");
        _mockConfiguration.Setup(c => c.GetSection("ValidationSettings:MaxConcurrentValidations")).Returns(mockSection.Object);

        // Initialize specifications with mock loggers

        _tenantId = new TenantId("test-tenant");

        // Initialize handler
        // Create mock for PolicyMemberValidationDataService
        var mockValidationDataService = new Mock<PolicyMemberValidationDataService>(
            _mockLegacyPolicyService.Object,
            _mockProductService.Object,
            _mockFeatureManager.Object,
            _tenantId,
            _mockSchemaProvider.Object,
            new Mock<ILogger<PolicyMemberValidationDataService>>().Object);

        _handler = new ValidatePolicyMemberUploadHandler(
            _mockUploadRepository.Object,
            _mockLegacyPolicyService.Object,
            _mockCompleteValidationSpec.Object,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            _mockFileProcessingService.Object,
            _mockLogger.Object,
            mockValidationDataService.Object);

        // Initialize test data fixture
        _fixture = new Fixture();
        _fixture.Customize<PolicyId>(c => c.FromFactory(() => PolicyId.New));
        _fixture.Customize<PolicyMemberUploadId>(c => c.FromFactory(() => PolicyMemberUploadId.New));

        SetupDefaultMocks();
    }

    private void SetupDefaultMocks()
    {
        // Setup memory cache mock
        var mockCacheEntry = new Mock<ICacheEntry>();
        _mockMemoryCache.Setup(x => x.CreateEntry(It.IsAny<object>())).Returns(mockCacheEntry.Object);
        _mockMemoryCache.Setup(x => x.TryGetValue(It.IsAny<object>(), out It.Ref<object?>.IsAny)).Returns(false);

        // Setup file processing service
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success([], 0));

        // Setup feature manager defaults
        _mockFeatureManager.Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);

        // Setup users service to return that all members exist (no missing individuals)
        _mockUsersService.Setup(x => x.GetMemberIdsWithoutExistingIndividual(It.IsAny<HashSet<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup QueryIndividuals to return empty list by default
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup legacy policy service to return valid contract holder policy IDs (must be valid GUIDs)
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([Guid.NewGuid().ToString()]);

        // Setup legacy policy service to return a default policy
        PolicyDto defaultPolicy = MemberUploadTestDataBuilder.Create().BuildPolicyDto();
        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(defaultPolicy);

        // Setup policy member uniqueness service to return no conflicts
        SetupPolicyMemberUniquenessService();

        // Setup policy member query service
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMemberCurrentStateAsync(It.IsAny<string>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMember?)null);

        _mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup schema provider to return a default schema
        PolicyMemberFieldsSchema defaultSchema = MemberUploadTestDataBuilder.Create().BuildSchema();
        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(defaultSchema);

        // Setup repository operations that are required by the handler
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockUploadRepository.Setup(x => x.CompleteValidationIfNotLockedAsync(
                It.IsAny<PolicyMemberUploadId>(),
                It.IsAny<PolicyMemberUploadStatus>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
    }

    private void SetupPolicyMemberUniquenessService()
    {
        // Setup uniqueness service to return empty results (no conflicts)
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
                It.IsAny<string>(), It.IsAny<PolicyMemberId?>(), It.IsAny<List<PolicyId>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);


    }

    #endregion

    #region Helper Methods

    private ValidatePolicyMemberUploadCommand CreateValidCommand() => new()
    {
        PolicyId = Guid.NewGuid(),
        UploadId = Guid.NewGuid()
    };

    private PolicyMemberUpload CreateUploadWithStatus(PolicyMemberUploadStatus status)
    {
        PolicyMemberUpload upload = MemberUploadTestDataBuilder.Create().BuildPolicyMemberUpload();
        // Set the status using reflection to access the private setter
        PropertyInfo? statusProperty = typeof(PolicyMemberUpload).GetProperty("Status");
        statusProperty?.SetValue(upload, status);
        return upload;
    }

    #endregion

    #region Success Scenario Tests

    [Fact]
    public async Task Handle_WithValidRequest_ShouldReturnSuccess()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateUploadWithStatus(PolicyMemberUploadStatus.REGISTERED);
        PolicyDto policy = MemberUploadTestDataBuilder.Create().BuildPolicyDto();
        PolicyMemberFieldsSchema schema = MemberUploadTestDataBuilder.Create().BuildSchema();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([policy.Id]); // Return the current policy ID for contract holder

        _mockUsersService.Setup(x => x.GetMemberIdsWithoutExistingIndividual(It.IsAny<HashSet<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]); // Return empty set - all members exist

        // Setup QueryIndividuals to return empty list by default
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Setup file processing service to return test member data - create valid data without dependents or duplicates
        MembersUploadFields membersUploadFields = MemberUploadTestDataBuilder.Create()
            .WithMemberCount(100)  // Smaller dataset for faster testing
            .WithDuplicatePercentage(0.0)  // No duplicates for valid test
            .WithDependents(false)  // No dependents to avoid relationship validation errors
            .BuildMembersUploadFields();
        var memberDataList = membersUploadFields.AsReadOnlyList().Select(m => m.Value).ToList();

        var fileProcessingResult = FileProcessingResult.Success(memberDataList, 1024);
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fileProcessingResult);

        // Setup repository operations that are required by the handler
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockUploadRepository.Setup(x => x.CompleteValidationIfNotLockedAsync(
                It.IsAny<PolicyMemberUploadId>(),
                It.IsAny<PolicyMemberUploadStatus>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBeNull();
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    #endregion

    #region Upload State Validation Tests

    [Theory]
    [InlineData("REGISTERED")]
    [InlineData("VALIDATING_ERROR")]
    [InlineData("VALIDATED")]
    [InlineData("FAILED")]
    public async Task Handle_WithValidUploadStates_ShouldProceedWithValidation(string statusValue)
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        var status = new PolicyMemberUploadStatus(statusValue);
        PolicyMemberUpload upload = CreateUploadWithStatus(status);
        PolicyDto policy = MemberUploadTestDataBuilder.Create().BuildPolicyDto();
        PolicyMemberFieldsSchema schema = MemberUploadTestDataBuilder.Create().BuildSchema();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Create valid test data without duplicates, dependents, or existing members
        MembersUploadFields membersData = MemberUploadTestDataBuilder.Create()
            .WithMemberCount(2)
            .WithDuplicatePercentage(0.0)  // No duplicates
            .WithDependents(false)  // No dependents
            .WithExistingMembers(false)  // No existing members (avoids GUID parsing issues)
            .BuildMembersUploadFields();

        // Setup file processing with the generated valid data
        var memberDataList = membersData.AsReadOnlyList().Select(m => m.Value).ToList();
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberDataList, memberDataList.Count));

        // Setup feature manager defaults
        _mockFeatureManager.Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);

        // Setup users service to return that all members exist (no missing individuals)
        _mockUsersService.Setup(x => x.GetMemberIdsWithoutExistingIndividual(It.IsAny<HashSet<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup QueryIndividuals to return empty list by default
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup legacy policy service to return valid contract holder policy IDs (must be valid GUIDs)
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([Guid.NewGuid().ToString()]);

        // Setup policy member uniqueness service to return no conflicts
        SetupPolicyMemberUniquenessService();

        // Setup repository operations that are required by the handler
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockUploadRepository.Setup(x => x.CompleteValidationIfNotLockedAsync(
                It.IsAny<PolicyMemberUploadId>(),
                It.IsAny<PolicyMemberUploadStatus>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("Upload state {0} should be valid for validation", status.Value);
    }

    [Theory]
    [InlineData("VALIDATING")]
    [InlineData("CANCELED")]
    public async Task Handle_WithInvalidUploadStates_ShouldReturnFailure(string statusValue)
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        var status = new PolicyMemberUploadStatus(statusValue);
        PolicyMemberUpload upload = CreateUploadWithStatus(status);

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().ContainSingle()
            .Which.Code.Should().Be("INVALID_POLICY_MEMBER_UPLOAD_STATUS");
    }

    #endregion

    #region Upload Not Found Tests

    [Fact]
    public async Task Handle_WithNonExistentUpload_ShouldReturnFailure()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(default(PolicyMemberUpload)!);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().ContainSingle()
            .Which.Code.Should().Be("POLICY_MEMBER_UPLOAD_NOT_FOUND");
    }

    #endregion

    #region Policy Validation Tests

    [Fact]
    public async Task Handle_WithNonExistentPolicy_ShouldReturnFailure()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = MemberUploadTestDataBuilder.Create().BuildPolicyMemberUpload();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyDto?)null);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().ContainSingle()
            .Which.Code.Should().Be("NOT_FOUND");
    }

    [Fact]
    public async Task Handle_WithIssuedPolicy_ShouldReturnFailure()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = MemberUploadTestDataBuilder.Create().BuildPolicyMemberUpload();
        PolicyDto policy = MemberUploadTestDataBuilder.Create().BuildPolicyDto();

        // Create a new policy instance with IsIssued = true
        var issuedPolicy = new PolicyDto
        {
            Id = policy.Id,
            ProductId = policy.ProductId,
            ContractHolderId = policy.ContractHolderId,
            StartDate = policy.StartDate,
            EndDate = policy.EndDate,
            IsIssued = true,
            Endorsements = policy.Endorsements,
            ApprovedEndorsementIds = policy.ApprovedEndorsementIds
        };

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(issuedPolicy);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().ContainSingle()
            .Which.Code.Should().Be("POLICY_ISSUED");
    }

    #endregion

    #region File Processing Tests

    [Fact]
    public async Task Handle_WithEmptyFile_ShouldReturnSuccess()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = MemberUploadTestDataBuilder.Create().BuildPolicyMemberUpload();
        PolicyDto policy = MemberUploadTestDataBuilder.Create().BuildPolicyDto();
        PolicyMemberFieldsSchema schema = MemberUploadTestDataBuilder.Create().BuildSchema();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Setup empty file processing result
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success([], 0));

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("empty files are valid - there's nothing to validate but the upload structure is correct");
        result.Value.Should().NotBeNull();
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithValidFileData_ShouldProcessMembers()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();

        // Create upload and policy with matching PolicyIds
        var policyId = new PolicyId(command.PolicyId);
        PolicyMemberUpload upload = MemberUploadTestDataBuilder.Create().BuildPolicyMemberUpload(policyId);
        PolicyDto policy = MemberUploadTestDataBuilder.Create().BuildPolicyDto(command.PolicyId.ToString());
        PolicyMemberFieldsSchema schema = MemberUploadTestDataBuilder.Create().BuildSchema();

        // Create valid test data without duplicates, dependents, or existing members
        MembersUploadFields membersData = MemberUploadTestDataBuilder.Create()
            .WithMemberCount(2)
            .WithDuplicatePercentage(0.0)  // No duplicates
            .WithDependents(false)  // No dependents
            .WithExistingMembers(false)  // No existing members (avoids GUID parsing issues)
            .BuildMembersUploadFields();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Setup file processing with the generated valid data
        var memberDataList = membersData.AsReadOnlyList().Select(m => m.Value).ToList();
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberDataList, memberDataList.Count));



        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);



        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify file processing was called
        _mockFileProcessingService.Verify(x => x.ProcessUploadFileAsync(
            It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region Concurrency and Performance Tests

    [Fact]
    public async Task Handle_WithConcurrentRequests_ShouldHandleGracefully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateUploadWithStatus(PolicyMemberUploadStatus.REGISTERED);
        PolicyDto policy = MemberUploadTestDataBuilder.Create().BuildPolicyDto();
        PolicyMemberFieldsSchema schema = MemberUploadTestDataBuilder.Create().BuildSchema();

        // Return a new upload instance for each call to avoid state conflicts in concurrent scenarios
        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(() => CreateUploadWithStatus(PolicyMemberUploadStatus.REGISTERED));

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Setup file processing to return non-empty data for concurrent requests
        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Member ID", "12345" }, { "Name", "John Doe" } }
        };
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, 1024));

        // Setup validation specifications to return success for concurrent requests
        _mockCompleteValidationSpec.Setup(x => x.ValidateAsync(It.IsAny<CompleteValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(ValidationOrchestrationResult.Success(1, 0, []));

        // Act - Run multiple concurrent requests
        Task<Result<ValidatePolicyMemberUploadResponse>>[] tasks = [.. Enumerable.Range(0, 5)
            .Select(_ => _handler.Handle(command, CancellationToken.None))];

        Result<ValidatePolicyMemberUploadResponse>[] results = await Task.WhenAll(tasks);

        // Assert
        results.Should().AllSatisfy(result => result.IsSuccess.Should().BeTrue());
    }

    #endregion
}