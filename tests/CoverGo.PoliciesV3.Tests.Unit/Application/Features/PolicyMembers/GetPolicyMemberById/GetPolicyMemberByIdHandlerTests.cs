using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Application.PolicyMembers.GetPolicyMemberById;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.GetPolicyMemberById;

public class GetPolicyMemberByIdHandlerTests
{
    private readonly Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> _repository;
    private readonly GetPolicyMemberByIdHandler _handler;

    public GetPolicyMemberByIdHandlerTests()
    {
        _repository = new Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>>();
        _handler = new GetPolicyMemberByIdHandler(_repository.Object);
    }

    [Fact]
    public async Task Handle_WithExistingPolicyMember_ShouldReturnCurrentState()
    {
        // Arrange
        var policyMemberId = Guid.NewGuid();
        var query = new GetPolicyMemberByIdQuery { PolicyMemberId = policyMemberId };

        var policyId = new PolicyId(Guid.NewGuid());
        var policyMember = PolicyMember.Create(
            policyId,
            "TEST001",
            DateOnly.FromDateTime(DateTime.UtcNow),
            DateOnly.FromDateTime(DateTime.UtcNow.AddDays(365)),
            "PLAN001");

        _repository
            .Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyMember);

        // Act
        GetPolicyMemberByIdResponse result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.CurrentState.Should().NotBeNull();
        result.CurrentState!.PlanId.Should().Be("PLAN001");

        _repository.Verify(x => x.FindByIdAsync(
            It.Is<PolicyMemberId>(id => id.Value == policyMemberId),
            It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithNonExistentPolicyMember_ShouldReturnNullState()
    {
        // Arrange
        var policyMemberId = Guid.NewGuid();
        var query = new GetPolicyMemberByIdQuery { PolicyMemberId = policyMemberId };

        _repository
            .Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(() => null!);

        // Act
        GetPolicyMemberByIdResponse result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.CurrentState.Should().BeNull();

        _repository.Verify(x => x.FindByIdAsync(
            It.Is<PolicyMemberId>(id => id.Value == policyMemberId),
            It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithPolicyMemberWithoutCurrentState_ShouldReturnNullState()
    {
        // Arrange
        var policyMemberId = Guid.NewGuid();
        var query = new GetPolicyMemberByIdQuery { PolicyMemberId = policyMemberId };

        var policyId = new PolicyId(Guid.NewGuid());
        var policyMember = PolicyMember.Create(
            policyId,
            "TEST001",
            null, // No start date
            null, // No end date
            "PLAN001");
        // No states will be created because start/end dates are null

        _repository
            .Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyMember);

        // Act
        GetPolicyMemberByIdResponse result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.CurrentState.Should().BeNull();

        _repository.Verify(x => x.FindByIdAsync(
            It.Is<PolicyMemberId>(id => id.Value == policyMemberId),
            It.IsAny<CancellationToken>()),
            Times.Once);
    }
}