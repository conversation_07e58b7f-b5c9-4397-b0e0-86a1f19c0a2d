using CoverGo.PoliciesV3.Application.Common.Interfaces;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Common.Interfaces;

[Trait("Category", "Unit")]
[Trait("Component", "Application")]
[Trait("Feature", "FileParseResult")]
public class FileParseResultTests
{
    #region Constructor and Properties Tests

    [Fact]
    public void FileParseResult_WithValidData_ShouldInitializeCorrectly()
    {
        // Arrange
        var headers = new List<string> { "Name", "Age", "Email" };
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John" }, { "Age", "30" }, { "Email", "<EMAIL>" } },
            new Dictionary<string, string?> { { "Name", "Jane" }, { "Age", "25" }, { "Email", "<EMAIL>" } }
        };

        // Act
        var result = new FileParseResult
        {
            Headers = headers,
            Contents = contents
        };
        result.InitializeHeadersSet();

        // Assert
        result.Headers.Should().BeEquivalentTo(headers);
        result.Contents.Should().BeEquivalentTo(contents);
        result.Count.Should().Be(2);
        result.HeadersSet.Should().NotBeNull();
    }

    [Fact]
    public void Count_ShouldReturnContentsCount()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John" } },
            new Dictionary<string, string?> { { "Name", "Jane" } },
            new Dictionary<string, string?> { { "Name", "Bob" } }
        };

        var result = new FileParseResult
        {
            Headers = ["Name"],
            Contents = contents
        };

        // Act & Assert
        result.Count.Should().Be(3);
    }

    [Fact]
    public void Count_WithEmptyContents_ShouldReturnZero()
    {
        // Arrange
        var result = new FileParseResult
        {
            Headers = ["Name", "Age"],
            Contents = []
        };

        // Act & Assert
        result.Count.Should().Be(0);
    }

    #endregion

    #region HeadersSet Tests

    [Fact]
    public void InitializeHeadersSet_ShouldCreateCaseInsensitiveHashSet()
    {
        // Arrange
        var headers = new List<string> { "Name", "Age", "Email" };
        var result = new FileParseResult
        {
            Headers = headers,
            Contents = []
        };

        // Act
        result.InitializeHeadersSet();

        // Assert
        result.HeadersSet.Should().HaveCount(3);
        result.HeadersSet.Contains("Name").Should().BeTrue();
        result.HeadersSet.Contains("name").Should().BeTrue(); // Case insensitive
        result.HeadersSet.Contains("NAME").Should().BeTrue(); // Case insensitive
        result.HeadersSet.Contains("Age").Should().BeTrue();
        result.HeadersSet.Contains("Email").Should().BeTrue();
    }

    [Fact]
    public void InitializeHeadersSet_WithDuplicateHeaders_ShouldHandleCorrectly()
    {
        // Arrange
        var headers = new List<string> { "Name", "name", "NAME", "Age" };
        var result = new FileParseResult
        {
            Headers = headers,
            Contents = []
        };

        // Act
        result.InitializeHeadersSet();

        // Assert
        result.HeadersSet.Should().HaveCount(2); // "Name" and "Age" (duplicates removed due to case insensitivity)
        result.HeadersSet.Contains("Name").Should().BeTrue();
        result.HeadersSet.Contains("Age").Should().BeTrue();
    }

    [Fact]
    public void InitializeHeadersSet_WithEmptyHeaders_ShouldCreateEmptyHashSet()
    {
        // Arrange
        var result = new FileParseResult
        {
            Headers = [],
            Contents = []
        };

        // Act
        result.InitializeHeadersSet();

        // Assert
        result.HeadersSet.Should().BeEmpty();
    }

    [Fact]
    public void InitializeHeadersSet_CalledMultipleTimes_ShouldReinitializeCorrectly()
    {
        // Arrange
        var initialHeaders = new List<string> { "Name", "Age" };
        var result = new FileParseResult
        {
            Headers = initialHeaders,
            Contents = []
        };

        result.InitializeHeadersSet();
        result.HeadersSet.Should().HaveCount(2);

        // Act - Change headers and reinitialize
        var newHeaders = new List<string> { "Email", "Phone", "Address" };
        result = new FileParseResult
        {
            Headers = newHeaders,
            Contents = []
        };
        result.InitializeHeadersSet();

        // Assert
        result.HeadersSet.Should().HaveCount(3);
        result.HeadersSet.Contains("Email").Should().BeTrue();
        result.HeadersSet.Contains("Phone").Should().BeTrue();
        result.HeadersSet.Contains("Address").Should().BeTrue();
        result.HeadersSet.Contains("Name").Should().BeFalse();
        result.HeadersSet.Contains("Age").Should().BeFalse();
    }

    #endregion

    #region Performance Tests

    [Fact]
    public void HeadersSet_Contains_ShouldBeOptimizedForLookups()
    {
        // Arrange
        var headers = Enumerable.Range(0, 1000).Select(i => $"Header{i}").ToList();
        var result = new FileParseResult
        {
            Headers = headers,
            Contents = []
        };
        result.InitializeHeadersSet();

        // Act & Assert - These should be fast O(1) operations
        result.HeadersSet.Contains("Header500").Should().BeTrue();
        result.HeadersSet.Contains("header500").Should().BeTrue(); // Case insensitive
        result.HeadersSet.Contains("HEADER500").Should().BeTrue(); // Case insensitive
        result.HeadersSet.Contains("NonExistentHeader").Should().BeFalse();
    }

    [Fact]
    public void HeadersSet_CaseInsensitiveComparison_ShouldWorkCorrectly()
    {
        // Arrange
        var headers = new List<string> { "MemberId", "PlanId", "EffectiveDate" };
        var result = new FileParseResult
        {
            Headers = headers,
            Contents = []
        };
        result.InitializeHeadersSet();

        // Act & Assert - Test various case combinations
        result.HeadersSet.Contains("memberid").Should().BeTrue();
        result.HeadersSet.Contains("MEMBERID").Should().BeTrue();
        result.HeadersSet.Contains("MemberId").Should().BeTrue();
        result.HeadersSet.Contains("planid").Should().BeTrue();
        result.HeadersSet.Contains("PLANID").Should().BeTrue();
        result.HeadersSet.Contains("PlanId").Should().BeTrue();
        result.HeadersSet.Contains("effectivedate").Should().BeTrue();
        result.HeadersSet.Contains("EFFECTIVEDATE").Should().BeTrue();
        result.HeadersSet.Contains("EffectiveDate").Should().BeTrue();
    }

    #endregion

    #region Edge Cases

    [Fact]
    public void InitializeHeadersSet_WithNullHeaders_ShouldHandleGracefully()
    {
        // Arrange
        var result = new FileParseResult
        {
            Headers = null!,
            Contents = []
        };

        // Act
        result.InitializeHeadersSet();

        // Assert
        result.HeadersSet.Should().BeEmpty();
    }

    [Fact]
    public void InitializeHeadersSet_WithWhitespaceHeaders_ShouldIncludeWhitespace()
    {
        // Arrange
        var headers = new List<string> { "Name", " ", "\t", "Age", "" };
        var result = new FileParseResult
        {
            Headers = headers,
            Contents = []
        };

        // Act
        result.InitializeHeadersSet();

        // Assert
        result.HeadersSet.Should().HaveCount(5); // All headers including whitespace ones
        result.HeadersSet.Contains("Name").Should().BeTrue();
        result.HeadersSet.Contains(" ").Should().BeTrue();
        result.HeadersSet.Contains("\t").Should().BeTrue();
        result.HeadersSet.Contains("Age").Should().BeTrue();
        result.HeadersSet.Contains("").Should().BeTrue();
    }

    #endregion
}
