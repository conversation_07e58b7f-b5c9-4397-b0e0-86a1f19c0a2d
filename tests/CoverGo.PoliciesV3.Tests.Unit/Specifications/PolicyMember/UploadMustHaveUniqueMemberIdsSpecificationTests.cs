using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.PolicyMember;

public class UploadMustHaveUniqueMemberIdsSpecificationTests
{
    private readonly Mock<ILogger<UploadMustHaveUniqueMemberIdsSpecification>> _mockLogger;
    private readonly UploadMustHaveUniqueMemberIdsSpecification _specification;

    public UploadMustHaveUniqueMemberIdsSpecificationTests()
    {
        _mockLogger = new Mock<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>();
        _specification = new UploadMustHaveUniqueMemberIdsSpecification(_mockLogger.Object);
    }

    [Fact]
    public void BusinessRuleName_ShouldReturnReadableName()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        Assert.Equal("Upload Must Have Unique Member IDs", businessRuleName);
    }

    [Fact]
    public void Description_ShouldReturnMeaningfulDescription()
    {
        // Act
        string description = _specification.Description;

        // Assert
        Assert.Contains("member IDs are unique within the upload file", description);
        Assert.Contains("prevent duplicate member ID entries", description);
        Assert.Contains("existing members", description);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _specification.IsSatisfiedBy(null!));

    [Fact]
    public async Task IsSatisfiedBy_WithUniqueMemberIds_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP002" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP003" }
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithDuplicateMemberIds_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP002" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" } // Duplicate
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(2, result.Errors.Count); // Both duplicate entries should have errors
        Assert.All(result.Errors, error =>
        {
            Assert.Equal(ErrorCodes.UniqueViolation, error.Code);
            Assert.Equal("memberId", error.PropertyPath);
            Assert.Equal("Member ID", error.PropertyLabel);
            Assert.Equal("upload", error.Context["Scope"]);
        });
    }

    [Fact]
    public async Task IsSatisfiedBy_WithMultipleDuplicateGroups_ShouldReturnAllErrors()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP002" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" }, // Duplicate of EMP001
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP003" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP002" } // Duplicate of EMP002
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(4, result.Errors.Count); // 2 errors for EMP001 + 2 errors for EMP002
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNullMemberIds_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = null },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = null },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" }
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess); // Null values are not validated for uniqueness
    }

    [Fact]
    public async Task IsSatisfiedBy_WithEmptyMemberIds_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" }
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess); // Empty values are not validated for uniqueness
    }

    [Fact]
    public async Task IsSatisfiedBy_WithWhitespaceMemberIds_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "   " },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "\t" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" }
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess); // Whitespace values are not validated for uniqueness
    }

    [Fact]
    public async Task IsSatisfiedBy_WithCaseInsensitiveDuplicates_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "emp001" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" } // Case insensitive - should be treated as duplicates
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess); // Member IDs are case insensitive, so these are duplicates
        Assert.Equal(2, result.Errors.Count); // Both members should have errors
        Assert.All(result.Errors, error =>
        {
            Assert.Equal(ErrorCodes.UniqueViolation, error.Code);
            Assert.Equal("memberId", error.PropertyPath);
            Assert.Equal("Member ID", error.PropertyLabel);
        });
    }

    [Fact]
    public async Task IsSatisfiedBy_WithMixedNullAndDuplicateValues_ShouldOnlyValidateNonNullValues()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = null },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" } // Duplicate
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(2, result.Errors.Count); // Only the two EMP001 entries should have errors
    }

    [Fact]
    public async Task IsSatisfiedBy_WithSpecialCharactersInMemberIds_ShouldValidateCorrectly()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP-001" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP_002" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP.003" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP@004" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP-001" } // Duplicate
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(2, result.Errors.Count); // Only the EMP-001 duplicates should have errors
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNumericMemberIds_ShouldValidateCorrectly()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "001" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "002" },
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "001" } // Duplicate
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(2, result.Errors.Count);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithSingleMember_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" }
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithEmptyUpload_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(Array.Empty<Dictionary<string, string?>>());
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public void ValidateWithRowIndexedErrors_WithDuplicates_ShouldReturnRowIndexedErrors()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" }, // Row 1
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP002" }, // Row 2
            new Dictionary<string, string?> { [PolicyMemberUploadWellKnowFields.MemberIdField] = "EMP001" }  // Row 3 - duplicate
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Dictionary<int, List<ValidationError>> rowErrors = _specification.ValidateWithRowIndexedErrors(context);

        // Assert
        Assert.Equal(2, rowErrors.Count); // Rows 0 and 2 should have errors (0-based indexing)
        Assert.True(rowErrors.ContainsKey(0)); // Row 0 (first duplicate)
        Assert.True(rowErrors.ContainsKey(2)); // Row 2 (second duplicate)
        Assert.False(rowErrors.ContainsKey(1)); // Row 1 should not have errors

        // Verify error details
        Assert.Single(rowErrors[0]);
        Assert.Single(rowErrors[2]);
        Assert.All(rowErrors.Values.SelectMany(errors => errors), error =>
        {
            Assert.Equal(ErrorCodes.UniqueViolation, error.Code);
            Assert.Equal("memberId", error.PropertyPath);
            Assert.Equal("Member ID", error.PropertyLabel);
        });
    }

    private static PolicyMemberFieldsSchema CreateMockSchema()
    {
        var fields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = PolicyMemberUploadWellKnowFields.MemberIdField,
                Label = "Member ID",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(fields);
    }

    private static MembersUploadFields CreateMembersUploadFields(IEnumerable<Dictionary<string, string?>> memberFieldsList)
    {
        var memberUploadFields = memberFieldsList
            .Select(fields => new MemberUploadFields(fields))
            .ToList();

        return memberUploadFields.Count == 0
            ? MembersUploadFields.Empty()
            : new MembersUploadFields(memberUploadFields);
    }
}