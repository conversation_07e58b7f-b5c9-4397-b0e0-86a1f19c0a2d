using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.PolicyMember;

public class MemberEffectiveDateMustBeValidSpecificationTests
{
    private readonly Mock<ILogger<MemberEffectiveDateMustBeValidSpecification>> _mockLogger;
    private readonly MemberEffectiveDateMustBeValidSpecification _specification;

    public MemberEffectiveDateMustBeValidSpecificationTests()
    {
        _mockLogger = new Mock<ILogger<MemberEffectiveDateMustBeValidSpecification>>();
        _specification = new MemberEffectiveDateMustBeValidSpecification(_mockLogger.Object);
    }

    [Fact]
    public void BusinessRuleName_ShouldReturnReadableName()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        Assert.Equal("Member Effective Date Must Be Valid", businessRuleName);
    }

    [Fact]
    public void Description_ShouldReturnMeaningfulDescription()
    {
        // Act
        string description = _specification.Description;

        // Assert
        Assert.Contains("effective dates", description);
        Assert.Contains("policy date ranges", description);
        Assert.Contains("endorsement movement rules", description);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _specification.IsSatisfiedBy(null!));

    [Fact]
    public async Task IsSatisfiedBy_WithNoEffectiveDate_ShouldReturnSuccess()
    {
        // Arrange
        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["name"] = "John Doe" },
            policyStartDate: new DateOnly(2024, 1, 1),
            policyEndDate: new DateOnly(2024, 12, 31));

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNullEffectiveDate_ShouldReturnSuccess()
    {
        // Arrange
        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["effectiveDate"] = null },
            policyStartDate: new DateOnly(2024, 1, 1),
            policyEndDate: new DateOnly(2024, 12, 31));

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithValidEffectiveDateWithinPolicyRange_ShouldReturnSuccess()
    {
        // Arrange
        var effectiveDate = new DateOnly(2024, 6, 15);
        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["effectiveDate"] = effectiveDate.ToString() },
            policyStartDate: new DateOnly(2024, 1, 1),
            policyEndDate: new DateOnly(2024, 12, 31));

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithEffectiveDateBeforePolicyStart_ShouldReturnFailure()
    {
        // Arrange
        var effectiveDate = new DateOnly(2023, 12, 31); // Before policy start
        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["effectiveDate"] = effectiveDate.ToString("yyyy-MM-dd") },
            policyStartDate: new DateOnly(2024, 1, 1),
            policyEndDate: new DateOnly(2024, 12, 31));

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        Assert.Equal("EFFECTIVE_DATE_OUTSIDE_POLICY_DATES", result.Errors.First().Code);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithEffectiveDateAfterPolicyEnd_ShouldReturnFailure()
    {
        // Arrange
        var effectiveDate = new DateOnly(2025, 1, 1); // After policy end
        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["effectiveDate"] = effectiveDate.ToString("yyyy-MM-dd") },
            policyStartDate: new DateOnly(2024, 1, 1),
            policyEndDate: new DateOnly(2024, 12, 31));

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        Assert.Equal("EFFECTIVE_DATE_OUTSIDE_POLICY_DATES", result.Errors.First().Code);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithEffectiveDateOnPolicyStartDate_ShouldReturnSuccess()
    {
        // Arrange
        var effectiveDate = new DateOnly(2024, 1, 1); // Exactly on policy start
        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["effectiveDate"] = effectiveDate.ToString() },
            policyStartDate: new DateOnly(2024, 1, 1),
            policyEndDate: new DateOnly(2024, 12, 31));

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithEffectiveDateOnPolicyEndDate_ShouldReturnSuccess()
    {
        // Arrange
        var effectiveDate = new DateOnly(2024, 12, 31); // Exactly on policy end
        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["effectiveDate"] = effectiveDate.ToString() },
            policyStartDate: new DateOnly(2024, 1, 1),
            policyEndDate: new DateOnly(2024, 12, 31));

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithEndorsementIdAndValidMovement_ShouldReturnSuccess()
    {
        // Arrange
        var effectiveDate = new DateOnly(2024, 6, 15);
        var endorsementId = Guid.NewGuid();
        PolicyDto mockPolicy = CreateMockPolicy(
            startDate: new DateOnly(2024, 1, 1),
            endDate: new DateOnly(2024, 12, 31),
            canChangeMembersViaMovement: true,
            endorsementId: endorsementId);

        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["effectiveDate"] = effectiveDate.ToString() },
            policy: mockPolicy,
            endorsementId: endorsementId);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithEndorsementIdAndInvalidMovement_ShouldReturnFailure()
    {
        // Arrange
        var effectiveDate = new DateOnly(2024, 6, 15);
        var endorsementId = Guid.NewGuid();
        PolicyDto mockPolicy = CreateMockPolicy(
            startDate: new DateOnly(2024, 1, 1),
            endDate: new DateOnly(2024, 12, 31),
            canChangeMembersViaMovement: false,
            endorsementId: endorsementId);

        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["effectiveDate"] = effectiveDate.ToString() },
            policy: mockPolicy,
            endorsementId: endorsementId);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        Assert.Equal("EFFECTIVE_DATE_OUTSIDE_POLICY_DATES", result.Errors.First().Code);
    }

    private static FieldValidationContext CreateValidationContext(
        Dictionary<string, string?> memberFields,
        DateOnly policyStartDate,
        DateOnly policyEndDate,
        Guid? endorsementId = null)
    {
        PolicyDto policy = CreateMockPolicy(policyStartDate, policyEndDate);
        return CreateValidationContext(memberFields, policy, endorsementId);
    }

    private static FieldValidationContext CreateValidationContext(
        Dictionary<string, string?> memberFields,
        PolicyDto? policy = null,
        Guid? endorsementId = null)
    {
        var memberUploadFields = new MemberUploadFields(memberFields);
        var schema = new PolicyMemberFieldsSchema([]);
        policy ??= CreateMockPolicy(new DateOnly(2024, 1, 1), new DateOnly(2024, 12, 31));

        return FieldValidationContext.Create(
            memberUploadFields,
            policy,
            schema,
            memberId: null,
            endorsementId: endorsementId);
    }

    private static PolicyDto CreateMockPolicy(
        DateOnly startDate,
        DateOnly endDate,
        bool canChangeMembersViaMovement = true,
        Guid? endorsementId = null)
    {
        // Create endorsements based on whether movement should be allowed
        var endorsements = new List<EndorsementDto>();
        string actualEndorsementId = endorsementId?.ToString() ?? Guid.NewGuid().ToString();

        if (canChangeMembersViaMovement)
        {
            endorsements.Add(new EndorsementDto
            {
                Id = actualEndorsementId,
                Status = "PENDING" // PENDING status allows changes (CanBeChanged = true)
            });
        }
        else
        {
            endorsements.Add(new EndorsementDto
            {
                Id = actualEndorsementId,
                Status = "APPROVED" // APPROVED status doesn't allow changes (CanBeChanged = false)
            });
        }

        return new PolicyDto
        {
            Id = "policy-123",
            StartDate = startDate,
            EndDate = endDate,
            IsIssued = true,
            CanUploadMembers = true,
            ContractHolderId = "contract-holder-123", // Required for CanChangeMembersViaMovement to work
            Endorsements = endorsements
        };
    }
}