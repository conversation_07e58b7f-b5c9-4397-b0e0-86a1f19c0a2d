using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.CustomFields;
using Microsoft.Extensions.Logging;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.Products;
using PolicyMemberEntity = CoverGo.PoliciesV3.Domain.PolicyMembers.PolicyMember;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.PolicyMember;

public class MemberIdMustFollowBusinessRulesSpecificationTests
{
    private readonly Mock<ILogger<MemberIdMustFollowBusinessRulesSpecification>> _mockLogger;
    private readonly MemberIdMustFollowBusinessRulesSpecification _specification;

    public MemberIdMustFollowBusinessRulesSpecificationTests()
    {
        _mockLogger = new Mock<ILogger<MemberIdMustFollowBusinessRulesSpecification>>();
        _specification = new MemberIdMustFollowBusinessRulesSpecification(_mockLogger.Object);
    }

    [Fact]
    public void BusinessRuleName_ShouldReturnReadableName()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        Assert.Equal("Member ID Must Follow Business Rules", businessRuleName);
    }

    [Fact]
    public void Description_ShouldReturnMeaningfulDescription()
    {
        // Act
        string description = _specification.Description;

        // Assert
        Assert.Contains("member IDs comply with business rules", description);
        Assert.Contains("member existence", description);
        Assert.Contains("policy membership", description);
        Assert.Contains("contract holder constraints", description);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _specification.IsSatisfiedBy(null!));

    [Fact]
    public async Task IsSatisfiedBy_WithNullMemberId_ShouldReturnSuccess()
    {
        // Arrange
        MemberIdValidationContext context = CreateValidationContext(memberId: null);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithEmptyMemberId_ShouldReturnSuccess()
    {
        // Arrange
        MemberIdValidationContext context = CreateValidationContext(memberId: "");

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithWhitespaceMemberId_ShouldReturnSuccess()
    {
        // Arrange
        MemberIdValidationContext context = CreateValidationContext(memberId: "   ");

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithValidMemberId_ShouldReturnSuccess()
    {
        // Arrange
        string memberId = "EMP001";
        MemberIdValidationContext context = CreateValidationContext(
            memberId: memberId,
            individualExists: true,
            allowMembersFromOtherContractHolders: false,
            existingPolicyMember: null,
            memberValidationStates: []);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNonExistentIndividual_ShouldReturnFailureWithMemberNotFoundError()
    {
        // Arrange
        string memberId = "NONEXISTENT001";
        MemberIdValidationContext context = CreateValidationContext(
            memberId: memberId,
            individualExists: false, // Individual does not exist
            allowMembersFromOtherContractHolders: false,
            existingPolicyMember: null,
            memberValidationStates: []);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        Assert.Equal("MEMBER_NOT_FOUND", result.Errors.First().Code);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithExistingPolicyMember_ShouldReturnFailureWithMemberIdTakenError()
    {
        // Arrange
        string memberId = "EMP001";
        PolicyMemberEntity existingPolicyMember = CreateTestPolicyMember(memberId, Guid.Parse("12345678-1234-1234-1234-123456789012"));
        MemberIdValidationContext context = CreateValidationContext(
            memberId: memberId,
            individualExists: true,
            allowMembersFromOtherContractHolders: false,
            existingPolicyMember: existingPolicyMember,
            memberValidationStates: []);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        Assert.Equal("MEMBER_ID_TAKEN", result.Errors.First().Code);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithMemberInAnotherContractHolder_ShouldReturnFailureWithContractHolderError()
    {
        // Arrange
        string memberId = "EMP001";
        var conflictingPolicyId = Guid.Parse("*************-2222-2222-************");
        PolicyMemberEntity memberInOtherPolicy = CreateTestPolicyMember(memberId, conflictingPolicyId);
        var memberValidationStates = new List<PolicyMemberEntity> { memberInOtherPolicy };

        MemberIdValidationContext context = CreateValidationContext(
            memberId: memberId,
            individualExists: true,
            allowMembersFromOtherContractHolders: false, // Feature flag disabled
            existingPolicyMember: null,
            memberValidationStates: memberValidationStates);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        Assert.Equal("MEMBER_NOT_IN_CONTRACT_HOLDER", result.Errors.First().Code);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithAllowMembersFromOtherContractHoldersEnabled_ShouldReturnSuccess()
    {
        // Arrange
        string memberId = "EMP001";
        var conflictingPolicyId = Guid.Parse("*************-2222-2222-************");
        PolicyMemberEntity memberInOtherPolicy = CreateTestPolicyMember(memberId, conflictingPolicyId);
        var memberValidationStates = new List<PolicyMemberEntity> { memberInOtherPolicy };

        MemberIdValidationContext context = CreateValidationContext(
            memberId: memberId,
            individualExists: true,
            allowMembersFromOtherContractHolders: true, // Feature flag enabled
            existingPolicyMember: null,
            memberValidationStates: memberValidationStates);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithValidContext_ShouldReturnSuccess()
    {
        // Arrange
        string memberId = "EMP001";
        MemberIdValidationContext context = CreateValidationContext(
            memberId: memberId,
            individualExists: true,
            allowMembersFromOtherContractHolders: false,
            existingPolicyMember: null,
            memberValidationStates: []);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    private static MemberIdValidationContext CreateValidationContext(
        string? memberId = "EMP001",
        bool individualExists = true,
        bool allowMembersFromOtherContractHolders = false,
        PolicyMemberEntity? existingPolicyMember = null,
        List<PolicyMemberEntity>? memberValidationStates = null)
    {
        var memberFields = new Dictionary<string, string?>();
        if (memberId != null)
        {
            memberFields[PolicyMemberUploadWellKnowFields.MemberIdField] = memberId;
        }

        var memberUploadFields = new MemberUploadFields(memberFields);
        var policy = new PolicyDto
        {
            Id = "POL001",
            ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1))
        };
        var schema = new PolicyMemberFieldsSchema([]);
        var contractHolderPolicyIds = new List<string> { "POL001", "POL002" };

        return MemberIdValidationContext.Create(
            memberFields: memberUploadFields,
            policy: policy,
            schema: schema,
            contractHolderPolicyIds: contractHolderPolicyIds,
            individualExists: individualExists,
            allowMembersFromOtherContractHolders: allowMembersFromOtherContractHolders,
            existingPolicyMember: existingPolicyMember,
            memberValidationStates: memberValidationStates ?? [],
            endorsementId: Guid.Parse("12345678-1234-1234-1234-123456789012"));
    }

    private static PolicyMemberEntity CreateTestPolicyMember(string memberId, Guid policyId)
    {
        var member = PolicyMemberEntity.Create(
            policyId,
            memberId,
            DateOnly.FromDateTime(DateTime.Today),
            DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            "TEST-PLAN");

        // Set audit information using reflection since it's likely a private setter
        var auditInfo = new BuildingBlocks.Domain.Core.Audit.EntityAuditInfo("test-user", DateTime.UtcNow);
        System.Reflection.PropertyInfo? auditProperty = typeof(PolicyMemberEntity).GetProperty(nameof(PolicyMemberEntity.EntityAuditInfo));
        auditProperty?.SetValue(member, auditInfo);

        return member;
    }
}