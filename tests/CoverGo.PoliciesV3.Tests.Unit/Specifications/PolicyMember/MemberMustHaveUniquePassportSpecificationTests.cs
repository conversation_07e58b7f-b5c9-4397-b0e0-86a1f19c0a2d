using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Services;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.PolicyMember;

public class MemberMustHaveUniquePassportSpecificationTests
{
    private readonly Mock<IPolicyMemberUniquenessService> _mockUniquenessService;
    private readonly Mock<ILogger<MemberMustHaveUniquePassportSpecification>> _mockLogger;
    private readonly MemberMustHaveUniquePassportSpecification _specification;

    public MemberMustHaveUniquePassportSpecificationTests()
    {
        _mockUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        _mockLogger = new Mock<ILogger<MemberMustHaveUniquePassportSpecification>>();
        _specification = new MemberMustHaveUniquePassportSpecification(_mockUniquenessService.Object, _mockLogger.Object);
    }

    [Fact]
    public void BusinessRuleName_ShouldReturnReadableName()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        Assert.Equal("Member Must Have Unique Passport", businessRuleName);
    }

    [Fact]
    public void Description_ShouldReturnMeaningfulDescription()
    {
        // Act
        string description = _specification.Description;

        // Assert
        Assert.Contains("passport numbers are unique", description);
        Assert.Contains("policy or contract holder", description);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _specification.IsSatisfiedBy(null!, CancellationToken.None));

    [Fact]
    public async Task IsSatisfiedByAsync_WithNoPassportField_ShouldReturnSuccess()
    {
        // Arrange
        UniquenessValidationContext context = CreateValidationContext(fieldValues: new Dictionary<string, object?> { ["name"] = "John Doe" });

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithEmptyPassport_ShouldReturnSuccess()
    {
        // Arrange
        UniquenessValidationContext context = CreateValidationContext(fieldValues: new Dictionary<string, object?> { ["passportNo"] = "" });

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithNullPassport_ShouldReturnSuccess()
    {
        // Arrange
        UniquenessValidationContext context = CreateValidationContext(fieldValues: new Dictionary<string, object?> { ["passportNo"] = null });

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithPassportFieldNotInSchema_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasPassportField: false);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["passportNo"] = "P123456789" },
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithValidPassport_ShouldAlwaysValidatePolicyScope()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasPassportField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["passportNo"] = "P123456789" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.Is<Dictionary<string, object>>(d => d.ContainsKey("passportNo") && d["passportNo"].ToString() == "P123456789"),
            It.Is<List<string>>(l => l.Contains("passportNo")),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithContractHolderPolicies_ShouldValidateContractHolderScope()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasPassportField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["passportNo"] = "P123456789" },
            schema: schema,
            contractHolderId: "contract-holder-123",
            contractHolderPolicyIds: [Guid.NewGuid().ToString(), Guid.NewGuid().ToString()]);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockUniquenessService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
                It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<List<PolicyId>>(), It.IsAny<List<EndorsementId>>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidateContractHolderScopeUniquenessAsync(
            It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<List<PolicyId>>(), It.IsAny<List<EndorsementId>>(),
            It.Is<Dictionary<string, object>>(d => d.ContainsKey("passportNo") && d["passportNo"].ToString() == "P123456789"),
            It.Is<List<string>>(l => l.Contains("passportNo")),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithoutContractHolderPolicies_ShouldSkipContractHolderScopeValidation()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasPassportField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["passportNo"] = "P123456789" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidateContractHolderScopeUniquenessAsync(
            It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<List<PolicyId>>(), It.IsAny<List<EndorsementId>>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithDuplicatePassportInPolicyScope_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasPassportField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["passportNo"] = "P123456789" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["passportNo"]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Single(result.Errors);
        Assert.Contains("policy", result.Errors[0].Message);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithDuplicatePassportInContractHolderScope_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasPassportField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["passportNo"] = "P123456789" },
            schema: schema,
            contractHolderId: "contract-holder-123",
            contractHolderPolicyIds: [Guid.NewGuid().ToString(), Guid.NewGuid().ToString()]);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockUniquenessService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
                It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<List<PolicyId>>(), It.IsAny<List<EndorsementId>>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(["passportNo"]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Single(result.Errors);
        Assert.Contains("contract holder", result.Errors[0].Message);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithInvalidContractHolderPolicyIds_ShouldSkipContractHolderValidation()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasPassportField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["passportNo"] = "P123456789" },
            schema: schema,
            contractHolderId: "contract-holder-123",
            contractHolderPolicyIds: ["invalid-guid", "not-a-guid"]);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidateContractHolderScopeUniquenessAsync(
            It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<List<PolicyId>>(), It.IsAny<List<EndorsementId>>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithBothScopesHavingDuplicates_ShouldReturnMultipleErrors()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasPassportField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["passportNo"] = "P123456789" },
            schema: schema,
            contractHolderId: "contract-holder-123",
            contractHolderPolicyIds: [Guid.NewGuid().ToString(), Guid.NewGuid().ToString()]);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["passportNo"]);

        _mockUniquenessService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
                It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<List<PolicyId>>(), It.IsAny<List<EndorsementId>>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(["passportNo"]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Equal(2, result.Errors.Count);
        Assert.Contains(result.Errors, e => e.Message.Contains("policy"));
        Assert.Contains(result.Errors, e => e.Message.Contains("contract holder"));
    }

    private static UniquenessValidationContext CreateValidationContext(
        Dictionary<string, object?> fieldValues,
        PolicyMemberFieldsSchema? schema = null,
        string? contractHolderId = null,
        IReadOnlyList<string>? contractHolderPolicyIds = null)
    {
        string policyId = Guid.NewGuid().ToString();
        var policy = new PolicyDto
        {
            Id = policyId,
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
            Endorsements =
            [
                new() { Id = "endorsement-1", Status = "APPROVED" },
                new() { Id = "endorsement-2", Status = "PENDING" }
            ]
        };

        var memberUploadFields = new MemberUploadFields(fieldValues.ToDictionary(kvp => kvp.Key, kvp => kvp.Value?.ToString()));

        return UniquenessValidationContext.Builder()
            .WithMemberFields(memberUploadFields)
            .WithPolicy(policy)
            .WithSchema(schema ?? CreateMockSchema())
            .WithPolicyId(policyId)
            .WithPlanId("test-plan-id")
            .WithContractHolderId(contractHolderId)
            .WithContractHolderPolicyIds(contractHolderPolicyIds ?? [])
            .Build();
    }

    private static PolicyMemberFieldsSchema CreateMockSchema(bool hasPassportField = true)
    {
        var fields = new List<PolicyMemberFieldDefinition>();

        if (hasPassportField)
        {
            var passportField = new PolicyMemberFieldDefinition
            {
                Name = "passportNo",
                Label = "Passport Number",
                IsUnique = false, // Passport uniqueness is handled by policy holder scope, not schema uniqueness
                IsRequired = false,
                IsRequiredForDependent = false,
                Type = new StringFieldType()
            };
            fields.Add(passportField);
        }

        return new PolicyMemberFieldsSchema(fields);
    }
}