using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Services;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.PolicyMember;

public class MemberMustHaveUniqueHKIDSpecificationTests
{
    private readonly Mock<IPolicyMemberUniquenessService> _mockUniquenessService;
    private readonly Mock<ILogger<MemberMustHaveUniqueHKIDSpecification>> _mockLogger;
    private readonly MemberMustHaveUniqueHKIDSpecification _specification;

    public MemberMustHaveUniqueHKIDSpecificationTests()
    {
        _mockUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        _mockLogger = new Mock<ILogger<MemberMustHaveUniqueHKIDSpecification>>();
        _specification = new MemberMustHaveUniqueHKIDSpecification(_mockUniquenessService.Object, _mockLogger.Object);
    }

    [Fact]
    public void BusinessRuleName_ShouldReturnReadableName()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        Assert.Equal("Member Must Have Unique HKID", businessRuleName);
    }

    [Fact]
    public void Description_ShouldReturnMeaningfulDescription()
    {
        // Act
        string description = _specification.Description;

        // Assert
        Assert.Contains("HKID", description);
        Assert.Contains("Hong Kong Identity Document", description);
        Assert.Contains("policy or contract holder", description);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _specification.IsSatisfiedBy(null!, CancellationToken.None));

    [Fact]
    public async Task IsSatisfiedByAsync_WithNoHKIDField_ShouldReturnSuccess()
    {
        // Arrange
        UniquenessValidationContext context = CreateValidationContext(fieldValues: new Dictionary<string, object?> { ["name"] = "John Doe" });

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithEmptyHKID_ShouldReturnSuccess()
    {
        // Arrange
        UniquenessValidationContext context = CreateValidationContext(fieldValues: new Dictionary<string, object?> { ["hkid"] = "" });

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithNullHKID_ShouldReturnSuccess()
    {
        // Arrange
        UniquenessValidationContext context = CreateValidationContext(fieldValues: new Dictionary<string, object?> { ["hkid"] = null });

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithHKIDFieldNotInSchema_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: false);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["hkid"] = "A123456(7)" },
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithValidHKID_ShouldAlwaysValidatePolicyScope()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["hkid"] = "A123456(7)" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.Is<Dictionary<string, object>>(d => d.ContainsKey("hkid") && d["hkid"].ToString() == "A123456(7)"),
            It.Is<List<string>>(l => l.Contains("hkid")),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithContractHolderPolicies_ShouldValidateContractHolderScope()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["hkid"] = "A123456(7)" },
            schema: schema,
            contractHolderId: "contract-holder-123",
            contractHolderPolicyIds: [Guid.NewGuid().ToString(), Guid.NewGuid().ToString()]);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockUniquenessService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
                It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<List<PolicyId>>(), It.IsAny<List<EndorsementId>>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidateContractHolderScopeUniquenessAsync(
            It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<List<PolicyId>>(), It.IsAny<List<EndorsementId>>(),
            It.Is<Dictionary<string, object>>(d => d.ContainsKey("hkid") && d["hkid"].ToString() == "A123456(7)"),
            It.Is<List<string>>(l => l.Contains("hkid")),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithoutContractHolderPolicies_ShouldSkipContractHolderScopeValidation()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["hkid"] = "A123456(7)" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidateContractHolderScopeUniquenessAsync(
            It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<List<PolicyId>>(), It.IsAny<List<EndorsementId>>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithDuplicateHKIDInPolicyScope_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["hkid"] = "A123456(7)" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["hkid"]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Single(result.Errors);
        Assert.Contains("policy", result.Errors.First().Message);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithDuplicateHKIDInContractHolderScope_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["hkid"] = "A123456(7)" },
            schema: schema,
            contractHolderId: "contract-holder-123",
            contractHolderPolicyIds: [Guid.NewGuid().ToString(), Guid.NewGuid().ToString()]);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockUniquenessService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
                It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<List<PolicyId>>(), It.IsAny<List<EndorsementId>>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(["hkid"]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Single(result.Errors);
        Assert.Contains("contract holder", result.Errors.First().Message);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithInvalidContractHolderPolicyIds_ShouldSkipContractHolderValidation()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["hkid"] = "A123456(7)" },
            schema: schema,
            contractHolderId: "contract-holder-123",
            contractHolderPolicyIds: ["invalid-guid", "not-a-guid"]);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidateContractHolderScopeUniquenessAsync(
            It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<List<PolicyId>>(), It.IsAny<List<EndorsementId>>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<CancellationToken>()), Times.Never);
    }

    private static UniquenessValidationContext CreateValidationContext(
        Dictionary<string, object?> fieldValues,
        PolicyMemberFieldsSchema? schema = null,
        string? contractHolderId = null,
        IReadOnlyList<string>? contractHolderPolicyIds = null)
    {
        var policy = new PolicyDto
        {
            Id = "test-policy-id",
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
            Endorsements =
            [
                new() { Id = "endorsement-1", Status = "APPROVED" },
                new() { Id = "endorsement-2", Status = "PENDING" }
            ]
        };

        var memberUploadFields = new MemberUploadFields(fieldValues.ToDictionary(kvp => kvp.Key, kvp => kvp.Value?.ToString()));

        return UniquenessValidationContext.Builder()
            .WithMemberFields(memberUploadFields)
            .WithPolicy(policy)
            .WithSchema(schema ?? CreateMockSchema())
            .WithPolicyId(Guid.NewGuid().ToString())
            .WithPlanId("test-plan-id")
            .WithContractHolderId(contractHolderId)
            .WithContractHolderPolicyIds(contractHolderPolicyIds ?? [])
            .Build();
    }

    private static PolicyMemberFieldsSchema CreateMockSchema(bool hasHKIDField = true)
    {
        var fields = new List<PolicyMemberFieldDefinition>();

        if (hasHKIDField)
        {
            var hkidField = new PolicyMemberFieldDefinition
            {
                Name = "hkid",
                Label = "HKID Number",
                IsUnique = false, // HKID uniqueness is handled by policy holder scope, not schema uniqueness
                IsRequired = false,
                IsRequiredForDependent = false,
                Type = new StringFieldType()
            };
            fields.Add(hkidField);
        }

        return new PolicyMemberFieldsSchema(fields);
    }
}