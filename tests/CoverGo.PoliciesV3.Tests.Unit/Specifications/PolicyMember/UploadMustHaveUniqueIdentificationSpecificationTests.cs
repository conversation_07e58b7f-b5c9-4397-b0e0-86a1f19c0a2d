using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.PolicyMember;

public class UploadMustHaveUniqueIdentificationSpecificationTests
{
    private readonly Mock<ILogger<UploadMustHaveUniqueIdentificationSpecification>> _mockLogger;
    private readonly UploadMustHaveUniqueIdentificationSpecification _specification;

    public UploadMustHaveUniqueIdentificationSpecificationTests()
    {
        _mockLogger = new Mock<ILogger<UploadMustHaveUniqueIdentificationSpecification>>();
        _specification = new UploadMustHaveUniqueIdentificationSpecification(_mockLogger.Object);
    }

    [Fact]
    public void BusinessRuleName_ShouldReturnReadableName()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        Assert.Equal("Upload Must Have Unique Identification", businessRuleName);
    }



    [Fact]
    public async Task IsSatisfiedBy_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _specification.IsSatisfiedBy(null!));

    [Fact]
    public async Task IsSatisfiedBy_WithUniqueHKIDs_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["hkid"] = "A123456(7)" },
            new Dictionary<string, string?> { ["hkid"] = "B234567(8)" },
            new Dictionary<string, string?> { ["hkid"] = "C345678(9)" }
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithDuplicateHKIDs_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["hkid"] = "A123456(7)" },
            new Dictionary<string, string?> { ["hkid"] = "B234567(8)" },
            new Dictionary<string, string?> { ["hkid"] = "A123456(7)" } // Duplicate
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(2, result.Errors.Count); // Two errors for the duplicate HKID
        Assert.All(result.Errors, error =>
        {
            Assert.Contains("hkid", error.PropertyPath);
            Assert.Contains("upload", error.Message);
        });
    }

    [Fact]
    public async Task IsSatisfiedBy_WithDuplicatePassports_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasPassportField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["passportNo"] = "P123456789" },
            new Dictionary<string, string?> { ["passportNo"] = "P987654321" },
            new Dictionary<string, string?> { ["passportNo"] = "P123456789" } // Duplicate
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(2, result.Errors.Count);
        Assert.All(result.Errors, error =>
        {
            Assert.Contains("passportNo", error.PropertyPath);
            Assert.Contains("upload", error.Message);
        });
    }

    [Fact]
    public async Task IsSatisfiedBy_WithDuplicateStaffNumbers_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasStaffNumberField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["staffNo"] = "EMP001" },
            new Dictionary<string, string?> { ["staffNo"] = "EMP002" },
            new Dictionary<string, string?> { ["staffNo"] = "EMP001" } // Duplicate
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(2, result.Errors.Count);
        Assert.All(result.Errors, error =>
        {
            Assert.Contains("staffNo", error.PropertyPath);
            Assert.Contains("upload", error.Message);
        });
    }

    [Fact]
    public async Task IsSatisfiedBy_WithCaseInsensitiveDuplicates_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["hkid"] = "a123456(7)" },
            new Dictionary<string, string?> { ["hkid"] = "A123456(7)" } // Case insensitive duplicate
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(2, result.Errors.Count);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNullAndEmptyValues_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: true, hasPassportField: true, hasStaffNumberField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["hkid"] = null, ["passportNo"] = "", ["staffNo"] = "   " },
            new Dictionary<string, string?> { ["hkid"] = "A123456(7)" }
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithExistingMembersWithDuplicates_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["hkid"] = "A123456(7)", ["memberId"] = "existing-1" },
            new Dictionary<string, string?> { ["hkid"] = "A123456(7)", ["memberId"] = "existing-2" }, // Existing members with same HKID - should be ignored
            new Dictionary<string, string?> { ["hkid"] = "B234567(8)" } // New member
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess); // Existing members are excluded from upload uniqueness validation
    }

    [Fact]
    public async Task IsSatisfiedBy_WithMultipleFieldTypeDuplicates_ShouldReturnAllErrors()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: true, hasPassportField: true, hasStaffNumberField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["hkid"] = "A123456(7)", ["passportNo"] = "P123456789", ["staffNo"] = "EMP001" },
            new Dictionary<string, string?> { ["hkid"] = "B234567(8)", ["passportNo"] = "P987654321", ["staffNo"] = "EMP002" },
            new Dictionary<string, string?> { ["hkid"] = "A123456(7)", ["passportNo"] = "P123456789", ["staffNo"] = "EMP001" } // All duplicates
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(6, result.Errors.Count); // 2 errors each for hkid, passportNo, and staffNo
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNoIdentificationFields_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasHKIDField: false, hasPassportField: false, hasStaffNumberField: false);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["name"] = "John Doe" },
            new Dictionary<string, string?> { ["name"] = "Jane Doe" }
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    private static PolicyMemberFieldsSchema CreateMockSchema(
        bool hasHKIDField = false,
        bool hasPassportField = false,
        bool hasStaffNumberField = false)
    {
        var fields = new List<PolicyMemberFieldDefinition>();

        if (hasHKIDField)
        {
            fields.Add(new PolicyMemberFieldDefinition
            {
                Name = "hkid",
                Label = "Hong Kong ID",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            });
        }

        if (hasPassportField)
        {
            fields.Add(new PolicyMemberFieldDefinition
            {
                Name = "passportNo",
                Label = "Passport Number",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            });
        }

        if (hasStaffNumberField)
        {
            fields.Add(new PolicyMemberFieldDefinition
            {
                Name = "staffNo",
                Label = "Staff Number",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            });
        }

        return new PolicyMemberFieldsSchema(fields);
    }

    private static MembersUploadFields CreateMembersUploadFields(IEnumerable<Dictionary<string, string?>> memberData)
    {
        var memberFields = memberData.Select(data => new MemberUploadFields(data)).ToList();
        return memberFields.Count == 0
            ? MembersUploadFields.Empty()
            : new MembersUploadFields(memberFields);
    }
}