using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Services;
using Microsoft.Extensions.Logging;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Products;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.PolicyMember;

public class MemberMustHaveUniqueStaffNumberSpecificationTests
{
    private readonly Mock<IPolicyMemberUniquenessService> _mockUniquenessService;
    private readonly Mock<ILogger<MemberMustHaveUniqueStaffNumberSpecification>> _mockLogger;
    private readonly MemberMustHaveUniqueStaffNumberSpecification _specification;

    public MemberMustHaveUniqueStaffNumberSpecificationTests()
    {
        _mockUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        _mockLogger = new Mock<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>();
        _specification = new MemberMustHaveUniqueStaffNumberSpecification(_mockUniquenessService.Object, _mockLogger.Object);
    }

    [Fact]
    public void BusinessRuleName_ShouldReturnReadableName()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        Assert.Equal("Member Must Have Unique Staff Number", businessRuleName);
    }

    [Fact]
    public void Description_ShouldReturnMeaningfulDescription()
    {
        // Act
        string description = _specification.Description;

        // Assert
        Assert.Contains("staff numbers are unique", description);
        Assert.Contains("appropriate scope", description);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _specification.IsSatisfiedBy(null!));

    [Fact]
    public async Task IsSatisfiedByAsync_WithNoStaffNumberField_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasStaffNumberField: false);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["email"] = "<EMAIL>" },
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithNullStaffNumber_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasStaffNumberField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["staffNo"] = null },
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithEmptyStaffNumber_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasStaffNumberField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["staffNo"] = "" },
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithWhitespaceStaffNumber_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasStaffNumberField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["staffNo"] = "   " },
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithStaffNumberNotInSchema_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasStaffNumberField: false);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["staffNo"] = "EMP001" },
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithUniqueStaffNumber_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasStaffNumberField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["staffNo"] = "EMP001" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithDuplicateStaffNumber_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasStaffNumberField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["staffNo"] = "EMP001" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["staffNo"]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        Assert.Contains("staffNo", result.Errors.First().PropertyPath);
        Assert.Contains("policy", result.Errors.First().Message);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithValidationServiceException_ShouldPropagateException()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasStaffNumberField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["staffNo"] = "EMP001" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _specification.IsSatisfiedBy(context));
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithValidStaffNumberAndValidEndorsements_ShouldCallValidationService()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasStaffNumberField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["staffNo"] = "EMP001" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(),
            null,
            null,
            It.Is<Dictionary<string, object>>(d => d.ContainsKey("staffNo") && d["staffNo"].ToString() == "EMP001"),
            It.Is<List<string>>(l => l.Contains("staffNo")),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithMultipleFieldsButOnlyStaffNumberValidated_ShouldOnlyValidateStaffNumber()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasStaffNumberField: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?>
            {
                ["staffNo"] = "EMP001",
                ["email"] = "<EMAIL>",
                ["hkid"] = "A123456(7)"
            },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.Is<Dictionary<string, object>>(d => d.Count == 1 && d.ContainsKey("staffNo")),
            It.Is<List<string>>(l => l.Count == 1 && l.Contains("staffNo")),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    private static PolicyMemberFieldsSchema CreateMockSchema(bool hasStaffNumberField = true)
    {
        var fields = new List<PolicyMemberFieldDefinition>();

        if (hasStaffNumberField)
        {
            fields.Add(new PolicyMemberFieldDefinition
            {
                Name = "staffNo",
                Label = "Staff Number",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            });
        }

        return new PolicyMemberFieldsSchema(fields);
    }

    private static UniquenessValidationContext CreateValidationContext(
        IDictionary<string, object?> fieldValues,
        PolicyMemberFieldsSchema schema,
        string? contractHolderId = null,
        IReadOnlyList<string>? contractHolderPolicyIds = null)
    {
        string policyId = Guid.NewGuid().ToString();
        var policy = new PolicyDto
        {
            Id = policyId,
            ContractHolderId = contractHolderId ?? "CH001",
            ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            Endorsements =
            [
                new() { Id = "endorsement-1", Status = EndorsementStatus.Approved },
                new() { Id = "endorsement-2", Status = EndorsementStatus.InProgress },
                new() { Id = "endorsement-3", Status = EndorsementStatus.Canceled } // Should be excluded
            ]
        };

        var memberUploadFields = new MemberUploadFields(fieldValues.ToDictionary(kvp => kvp.Key, kvp => kvp.Value?.ToString()));

        return UniquenessValidationContext.Builder()
            .WithMemberFields(memberUploadFields)
            .WithPolicy(policy)
            .WithSchema(schema)
            .WithPolicyId(policyId)
            .WithPlanId("plan-456")
            .WithContractHolderId(contractHolderId)
            .WithContractHolderPolicyIds(contractHolderPolicyIds)
            .Build();
    }
}