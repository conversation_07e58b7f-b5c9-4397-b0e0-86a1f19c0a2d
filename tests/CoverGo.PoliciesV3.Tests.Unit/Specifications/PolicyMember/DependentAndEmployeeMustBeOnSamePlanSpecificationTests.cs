using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.PolicyMember;

public class DependentAndEmployeeMustBeOnSamePlanSpecificationTests
{
    private readonly Mock<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>> _mockLogger;
    private readonly DependentAndEmployeeMustBeOnSamePlanSpecification _specification;

    public DependentAndEmployeeMustBeOnSamePlanSpecificationTests()
    {
        _mockLogger = new Mock<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>();
        _specification = new DependentAndEmployeeMustBeOnSamePlanSpecification(_mockLogger.Object);
    }

    [Fact]
    public void BusinessRuleName_ShouldReturnReadableName()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        Assert.Equal("Dependent And Employee Must Be On Same Plan", businessRuleName);
    }

    [Fact]
    public void Description_ShouldReturnMeaningfulDescription()
    {
        // Act
        string description = _specification.Description;

        // Assert
        Assert.Contains("dependent members", description);
        Assert.Contains("same plan ID", description);
        Assert.Contains("primary member", description);
        Assert.Contains("upload file", description);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _specification.IsSatisfiedBy(null!));

    [Fact]
    public async Task IsSatisfiedBy_WithNoMembers_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new Dictionary<string, string?>[0]);
        var context = DependentPlanValidationContext.Create(membersFields, schema, shouldApplyDependentPlanValidation: true);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithOnlyEmployees_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["memberType"] = "employee", ["planId"] = "plan-1" },
            new Dictionary<string, string?> { ["memberType"] = "employee", ["planId"] = "plan-2" }
        });
        var context = DependentPlanValidationContext.Create(membersFields, schema, shouldApplyDependentPlanValidation: true);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithDependentAndEmployeeOnSamePlan_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["memberType"] = "employee", ["planId"] = "plan-1" },
            new Dictionary<string, string?> { ["memberType"] = "dependent", ["planId"] = "plan-1" }
        });
        var context = DependentPlanValidationContext.Create(membersFields, schema, shouldApplyDependentPlanValidation: true);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithDependentAndEmployeeOnDifferentPlans_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["memberType"] = "employee", ["planId"] = "plan-1" },
            new Dictionary<string, string?> { ["memberType"] = "dependent", ["planId"] = "plan-2" } // Different plan
        });
        var context = DependentPlanValidationContext.Create(membersFields, schema, shouldApplyDependentPlanValidation: true);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        Assert.Equal("DEPENDENT_PLAN_MISMATCH", result.Errors.First().Code);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithMultipleDependentsOnSamePlan_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["memberType"] = "employee", ["planId"] = "plan-1" },
            new Dictionary<string, string?> { ["memberType"] = "dependent", ["planId"] = "plan-1" },
            new Dictionary<string, string?> { ["memberType"] = "dependent", ["planId"] = "plan-1" }
        });
        var context = DependentPlanValidationContext.Create(membersFields, schema, shouldApplyDependentPlanValidation: true);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithMultipleDependentsOnDifferentPlans_ShouldReturnMultipleErrors()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["memberType"] = "employee", ["planId"] = "plan-1" },
            new Dictionary<string, string?> { ["memberType"] = "dependent", ["planId"] = "plan-2" }, // Different plan
            new Dictionary<string, string?> { ["memberType"] = "dependent", ["planId"] = "plan-3" }  // Different plan
        });
        var context = DependentPlanValidationContext.Create(membersFields, schema, shouldApplyDependentPlanValidation: true);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(2, result.Errors.Count); // Two dependents with wrong plans
        Assert.All(result.Errors, error => Assert.Equal("DEPENDENT_PLAN_MISMATCH", error.Code));
    }

    [Fact]
    public async Task IsSatisfiedBy_WithDependentHavingNullPlanId_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["memberType"] = "employee", ["planId"] = "plan-1" },
            new Dictionary<string, string?> { ["memberType"] = "dependent", ["planId"] = null } // Null plan
        });
        var context = DependentPlanValidationContext.Create(membersFields, schema, shouldApplyDependentPlanValidation: true);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithEmployeeHavingNullPlanId_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["memberType"] = "employee", ["planId"] = null }, // Null plan
            new Dictionary<string, string?> { ["memberType"] = "dependent", ["planId"] = "plan-1" }
        });
        var context = DependentPlanValidationContext.Create(membersFields, schema, shouldApplyDependentPlanValidation: true);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithComplexUploadStructure_ShouldValidateCorrectly()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            // Employee 1 with dependents
            new Dictionary<string, string?> { ["memberType"] = "employee", ["planId"] = "plan-1" },
            new Dictionary<string, string?> { ["memberType"] = "dependent", ["planId"] = "plan-1" }, // Correct
            new Dictionary<string, string?> { ["memberType"] = "dependent", ["planId"] = "plan-1" }, // Correct
            
            // Employee 2 with dependents
            new Dictionary<string, string?> { ["memberType"] = "employee", ["planId"] = "plan-2" },
            new Dictionary<string, string?> { ["memberType"] = "dependent", ["planId"] = "plan-2" }, // Correct
            new Dictionary<string, string?> { ["memberType"] = "dependent", ["planId"] = "plan-1" }, // Wrong - should be plan-2
        });
        var context = DependentPlanValidationContext.Create(membersFields, schema, shouldApplyDependentPlanValidation: true);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors); // Only the last dependent should have an error
    }

    [Fact]
    public async Task IsSatisfiedBy_WithDependentHavingNoPrimaryMember_ShouldReturnSuccess()
    {
        // Arrange - Dependent at the beginning with no primary member before it
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["memberType"] = "dependent", ["planId"] = "plan-1" } // No primary member
        });
        var context = DependentPlanValidationContext.Create(membersFields, schema, shouldApplyDependentPlanValidation: true);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess); // Should succeed because no primary member to compare against
    }

    private static PolicyMemberFieldsSchema CreateMockSchema() => new([]);

    private static MembersUploadFields CreateMembersUploadFields(IEnumerable<Dictionary<string, string?>> memberData)
    {
        var memberFields = memberData.Select(data => new MemberUploadFields(data)).ToList();
        return memberFields.Count == 0
            ? MembersUploadFields.Empty()
            : new MembersUploadFields(memberFields);
    }
}