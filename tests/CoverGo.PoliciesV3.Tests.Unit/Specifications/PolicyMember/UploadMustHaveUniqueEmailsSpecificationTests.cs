using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.PolicyMember;

public class UploadMustHaveUniqueEmailsSpecificationTests
{
    private readonly Mock<ILogger<UploadMustHaveUniqueEmailsSpecification>> _mockLogger;
    private readonly UploadMustHaveUniqueEmailsSpecification _specification;

    public UploadMustHaveUniqueEmailsSpecificationTests()
    {
        _mockLogger = new Mock<ILogger<UploadMustHaveUniqueEmailsSpecification>>();
        _specification = new UploadMustHaveUniqueEmailsSpecification(_mockLogger.Object);
    }

    [Fact]
    public void BusinessRuleName_ShouldReturnReadableName()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        Assert.Equal("Upload Must Have Unique Emails", businessRuleName);
    }

    [Fact]
    public void Description_ShouldReturnMeaningfulDescription()
    {
        // Act
        string description = _specification.Description;

        // Assert
        Assert.Contains("email addresses are unique", description);
        Assert.Contains("upload file", description);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _specification.IsSatisfiedBy(null!));

    [Fact]
    public async Task IsSatisfiedBy_WithNoEmailField_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: false);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["name"] = "John Doe" }
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithUniqueEmails_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" },
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" },
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" }
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithDuplicateEmails_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" },
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" },
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" } // Duplicate
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(2, result.Errors.Count); // Two errors for the duplicate email
        Assert.All(result.Errors, error =>
        {
            Assert.Equal(ErrorCodes.UniqueViolation, error.Code);
            Assert.Contains("upload", error.Message);
        });
    }

    [Fact]
    public async Task IsSatisfiedBy_WithCaseInsensitiveDuplicateEmails_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" },
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" } // Case insensitive duplicate
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(2, result.Errors.Count);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNullAndEmptyEmails_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["email"] = null },
            new Dictionary<string, string?> { ["email"] = "" },
            new Dictionary<string, string?> { ["email"] = "   " },
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" }
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithExistingMembersWithDuplicateEmails_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["email"] = "<EMAIL>", ["memberId"] = "existing-1" },
            new Dictionary<string, string?> { ["email"] = "<EMAIL>", ["memberId"] = "existing-2" }, // Existing members with same email - should be ignored
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" } // New member
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess); // Existing members are excluded from upload uniqueness validation
    }

    [Fact]
    public async Task IsSatisfiedBy_WithMixedNewAndExistingMembers_ShouldOnlyValidateNewMembers()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" }, // New member
            new Dictionary<string, string?> { ["email"] = "<EMAIL>", ["memberId"] = "existing-1" }, // Existing member
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" } // New member - duplicate
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(2, result.Errors.Count); // Only new members should be validated
    }

    [Fact]
    public async Task IsSatisfiedBy_WithMultipleDuplicateGroups_ShouldReturnAllErrors()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: true);
        MembersUploadFields membersFields = CreateMembersUploadFields(new[]
        {
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" },
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" },
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" }, // Duplicate group 1
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" }, // Duplicate group 2
            new Dictionary<string, string?> { ["email"] = "<EMAIL>" }   // Unique
        });
        var context = UploadUniquenessValidationContext.Create(membersFields, schema);

        // Act
        Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(4, result.Errors.Count); // 2 <NAME_EMAIL> + 2 <NAME_EMAIL>
    }

    private static PolicyMemberFieldsSchema CreateMockSchema(bool hasEmailField = true)
    {
        var fields = new List<PolicyMemberFieldDefinition>();

        if (hasEmailField)
        {
            fields.Add(new PolicyMemberFieldDefinition
            {
                Name = "email",
                Label = "Email Address",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            });
        }

        return new PolicyMemberFieldsSchema(fields);
    }

    private static MembersUploadFields CreateMembersUploadFields(IEnumerable<Dictionary<string, string?>> memberData)
    {
        var memberFields = memberData.Select(data => new MemberUploadFields(data)).ToList();
        return memberFields.Count == 0
            ? MembersUploadFields.Empty()
            : new MembersUploadFields(memberFields);
    }
}