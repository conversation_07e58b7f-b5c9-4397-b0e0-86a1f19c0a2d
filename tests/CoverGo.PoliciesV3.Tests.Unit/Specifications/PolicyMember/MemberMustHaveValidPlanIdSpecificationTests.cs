using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Products;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.PolicyMember;

public class MemberMustHaveValidPlanIdSpecificationTests
{
    private readonly Mock<ILogger<MemberMustHaveValidPlanIdSpecification>> _mockLogger;
    private readonly MemberMustHaveValidPlanIdSpecification _specification;

    public MemberMustHaveValidPlanIdSpecificationTests()
    {
        _mockLogger = new Mock<ILogger<MemberMustHaveValidPlanIdSpecification>>();
        _specification = new MemberMustHaveValidPlanIdSpecification(_mockLogger.Object);
    }

    [Fact]
    public void BusinessRuleName_ShouldReturnReadableName()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        Assert.Equal("Member Must Have Valid Plan ID", businessRuleName);
    }

    [Fact]
    public void Description_ShouldReturnMeaningfulDescription()
    {
        // Act
        string description = _specification.Description;

        // Assert
        Assert.Contains("plan IDs exist in the product's available plans", description);
        Assert.Contains("plan availability", description);
        Assert.Contains("prevent invalid plan assignments", description);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _specification.IsSatisfiedBy(null!));

    [Fact]
    public async Task IsSatisfiedBy_WithNullPlanId_ShouldReturnSuccess()
    {
        // Arrange
        PlanValidationContext context = CreateValidationContext(planId: null);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithEmptyPlanId_ShouldReturnSuccess()
    {
        // Arrange
        PlanValidationContext context = CreateValidationContext(planId: "");

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithWhitespacePlanId_ShouldReturnSuccess()
    {
        // Arrange
        PlanValidationContext context = CreateValidationContext(planId: "   ");

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNullProductId_ShouldReturnSuccess()
    {
        // Arrange
        PlanValidationContext context = CreateValidationContext(planId: "PLAN_A", productId: null);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNoAvailablePlans_ShouldReturnSuccess()
    {
        // Arrange
        PlanValidationContext context = CreateValidationContext(planId: "PLAN_A", availablePlans: null);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithValidPlanId_ShouldReturnSuccess()
    {
        // Arrange
        var availablePlans = new HashSet<string> { "PLAN_A", "PLAN_B", "PLAN_C" };
        PlanValidationContext context = CreateValidationContext(planId: "PLAN_A", availablePlans: availablePlans);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithInvalidPlanId_ShouldReturnFailureWithInvalidPlanIdError()
    {
        // Arrange
        var availablePlans = new HashSet<string> { "PLAN_A", "PLAN_B", "PLAN_C" };
        PlanValidationContext context = CreateValidationContext(planId: "INVALID_PLAN", availablePlans: availablePlans);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        PoliciesV3.Domain.Common.Validation.ValidationError error = result.Errors[0];
        Assert.Equal("PRODUCT_PLAN_NOT_FOUND", error.Code);
        Assert.Equal("planId", error.PropertyPath);
        Assert.Equal("Plan ID", error.PropertyLabel);
        Assert.Equal("INVALID_PLAN", error.Context["PlanId"]);
        Assert.Equal(availablePlans.ToList(), error.Context["AvailablePlans"]);
        Assert.Equal("health/basic/v1", error.Context["ProductId"]);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithCaseInsensitivePlanId_ShouldReturnFailure()
    {
        // Arrange - Plan IDs are case sensitive
        var availablePlans = new HashSet<string> { "PLAN_A", "PLAN_B", "PLAN_C" };
        PlanValidationContext context = CreateValidationContext(planId: "plan_a", availablePlans: availablePlans);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        PoliciesV3.Domain.Common.Validation.ValidationError error = result.Errors[0];
        Assert.Equal("PRODUCT_PLAN_NOT_FOUND", error.Code);
        Assert.Equal("plan_a", error.Context["PlanId"]);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithEmptyAvailablePlans_ShouldReturnFailure()
    {
        // Arrange
        var availablePlans = new HashSet<string>();
        PlanValidationContext context = CreateValidationContext(planId: "PLAN_A", availablePlans: availablePlans);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        PoliciesV3.Domain.Common.Validation.ValidationError error = result.Errors[0];
        Assert.Equal("PRODUCT_PLAN_NOT_FOUND", error.Code);
        Assert.Equal("PLAN_A", error.Context["PlanId"]);
        Assert.Empty((List<string>)error.Context["AvailablePlans"]!);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithMultipleAvailablePlans_ShouldValidateCorrectly()
    {
        // Arrange
        var availablePlans = new HashSet<string> { "BASIC", "PREMIUM", "ENTERPRISE", "FAMILY" };
        PlanValidationContext context = CreateValidationContext(planId: "PREMIUM", availablePlans: availablePlans);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithExceptionDuringValidation_ShouldReturnFailureWithDefaultError()
    {
        // Arrange - Create a context that will cause an exception during ProductId.ToDomainProductId()
        // Use a plan that's NOT in available plans to force the validation to fail and call ToDomainProductId()
        var memberFields = new Dictionary<string, string?> { ["planId"] = "PLAN_A" };
        var memberUploadFields = new MemberUploadFields(memberFields);
        var policy = new PolicyDto
        {
            Id = "POL001",
            ProductId = new ProductIdDto { Plan = null!, Type = "health", Version = "v1" }, // Invalid - will cause exception
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1))
        };
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        var availablePlans = new HashSet<string> { "PLAN_B", "PLAN_C" }; // PLAN_A is not in this list

        var context = PlanValidationContext.Create(
            memberUploadFields,
            policy,
            schema,
            availablePlans);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        PoliciesV3.Domain.Common.Validation.ValidationError error = result.Errors[0];
        Assert.Equal("PRODUCT_PLAN_NOT_FOUND", error.Code);
        Assert.Equal("planId", error.PropertyPath);
        Assert.Equal("Plan ID", error.PropertyLabel);
        Assert.Equal("PLAN_A", error.Context["PlanId"]);
        Assert.Empty((IEnumerable<string>)error.Context["AvailablePlans"]!);
        Assert.Equal("unknown", error.Context["ProductId"]);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithSpecialCharactersInPlanId_ShouldValidateCorrectly()
    {
        // Arrange
        var availablePlans = new HashSet<string> { "PLAN-A", "PLAN_B", "PLAN.C", "PLAN@D" };
        PlanValidationContext context = CreateValidationContext(planId: "PLAN-A", availablePlans: availablePlans);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNumericPlanId_ShouldValidateCorrectly()
    {
        // Arrange
        var availablePlans = new HashSet<string> { "1", "2", "3", "100" };
        PlanValidationContext context = CreateValidationContext(planId: "2", availablePlans: availablePlans);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    private static PlanValidationContext CreateValidationContext(
        string? planId = "PLAN_A",
        string? productId = "PROD001",
        IReadOnlySet<string>? availablePlans = null)
    {
        var memberFields = new Dictionary<string, string?>();
        if (planId != null)
        {
            memberFields["planId"] = planId;
        }

        var memberUploadFields = new MemberUploadFields(memberFields);
        var policy = new PolicyDto
        {
            Id = "POL001",
            ProductId = productId != null ? new ProductIdDto
            {
                Plan = "basic",
                Type = "health",
                Version = "v1"
            } : null,
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1))
        };
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        return PlanValidationContext.Create(
            memberUploadFields,
            policy,
            schema,
            availablePlans);
    }

    private static PolicyMemberFieldsSchema CreateMockSchema()
    {
        var fields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "planId",
                Label = "Plan ID",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(fields);
    }
}