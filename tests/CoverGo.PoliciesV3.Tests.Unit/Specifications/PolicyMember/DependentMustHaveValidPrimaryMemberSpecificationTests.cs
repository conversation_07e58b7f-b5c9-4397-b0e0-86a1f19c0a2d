using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Services;
using Microsoft.Extensions.Logging;
using PolicyMemberEntity = CoverGo.PoliciesV3.Domain.PolicyMembers.PolicyMember;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.PolicyMember;

public class DependentMustHaveValidPrimaryMemberSpecificationTests
{
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<ILogger<DependentMustHaveValidPrimaryMemberSpecification>> _mockLogger;
    private readonly DependentMustHaveValidPrimaryMemberSpecification _specification;

    public DependentMustHaveValidPrimaryMemberSpecificationTests()
    {
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        _mockLogger = new Mock<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>();
        _specification = new DependentMustHaveValidPrimaryMemberSpecification(_mockPolicyMemberQueryService.Object, _mockLogger.Object);
    }

    [Fact]
    public void BusinessRuleName_ShouldReturnReadableName()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        Assert.Equal("Dependent Must Have Valid Primary Member", businessRuleName);
    }

    [Fact]
    public void Description_ShouldReturnMeaningfulDescription()
    {
        // Act
        string description = _specification.Description;

        // Assert
        Assert.Contains("dependent members", description);
        Assert.Contains("valid primary member", description);
        Assert.Contains("policy or in the upload", description);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _specification.IsSatisfiedBy(null!));

    [Fact]
    public async Task IsSatisfiedByAsync_WithNonDependentMemberAndNoDependentOfField_ShouldReturnSuccess()
    {
        // Arrange - Non-dependent member with no dependentOf field
        DependentValidationContext context = CreateValidationContext(
            dependentMemberFields: new Dictionary<string, string?> { ["memberType"] = "employee" },
            dependentMembersCache: []);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithNonDependentMemberButHasDependentOfField_ShouldValidate()
    {
        // Arrange - Non-dependent member with dependentOf field (behavioral compatibility test)
        PolicyMemberEntity primaryMember = new Mock<PolicyMemberEntity>().Object;
        var dependentMembersCache = new Dictionary<string, PolicyMemberEntity?>
        {
            ["EMP001"] = primaryMember
        };

        DependentValidationContext context = CreateValidationContext(
            dependentMemberFields: new Dictionary<string, string?>
            {
                ["memberType"] = "employee", // Not a dependent
                ["dependentOf"] = "EMP001"   // But has dependentOf field
            },
            dependentMembersCache: dependentMembersCache);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert - Should validate and succeed because primary member exists
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithDependentOfFieldAndValidPrimaryMemberInCache_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberEntity primaryMember = new Mock<PolicyMemberEntity>().Object;
        var dependentMembersCache = new Dictionary<string, PolicyMemberEntity?>
        {
            ["primary-member-123"] = primaryMember
        };

        DependentValidationContext context = CreateValidationContext(
            dependentMemberFields: new Dictionary<string, string?>
            {
                ["memberType"] = "dependent",
                ["dependentOf"] = "primary-member-123"
            },
            dependentMembersCache: dependentMembersCache);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithDependentOfFieldAndNullPrimaryMemberInCache_ShouldReturnFailure()
    {
        // Arrange
        var dependentMembersCache = new Dictionary<string, PolicyMemberEntity?>
        {
            ["primary-member-123"] = null // Primary member exists in cache but is null
        };

        DependentValidationContext context = CreateValidationContext(
            dependentMemberFields: new Dictionary<string, string?>
            {
                ["memberType"] = "dependent",
                ["dependentOf"] = "primary-member-123"
            },
            dependentMembersCache: dependentMembersCache);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        Assert.Equal("NOT_FOUND", result.Errors.First().Code);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithDependentOfFieldAndPrimaryMemberNotInCache_ShouldReturnFailure()
    {
        // Arrange
        var dependentMembersCache = new Dictionary<string, PolicyMemberEntity?>(); // Empty cache

        DependentValidationContext context = CreateValidationContext(
            dependentMemberFields: new Dictionary<string, string?>
            {
                ["memberType"] = "dependent",
                ["dependentOf"] = "primary-member-123"
            },
            dependentMembersCache: dependentMembersCache);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        Assert.Equal("NOT_FOUND", result.Errors.First().Code);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithNoDependentOfFieldAndPrimaryMemberInUpload_ShouldReturnSuccess()
    {
        // Arrange
        var primaryMemberFields = new MemberUploadFields(new Dictionary<string, string?>
        {
            ["memberType"] = "employee",
            ["name"] = "Primary Member"
        });

        DependentValidationContext context = CreateValidationContext(
            dependentMemberFields: new Dictionary<string, string?>
            {
                ["memberType"] = "dependent",
                ["name"] = "Dependent Member"
                // No "dependentOf" field
            },
            dependentMembersCache: [],
            primaryMemberFields: primaryMemberFields);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithNoDependentOfFieldAndNoPrimaryMemberInUpload_ShouldReturnFailure()
    {
        // Arrange
        DependentValidationContext context = CreateValidationContext(
            dependentMemberFields: new Dictionary<string, string?>
            {
                ["memberType"] = "dependent",
                ["name"] = "Dependent Member"
                // No "dependentOf" field
            },
            dependentMembersCache: [],
            primaryMemberFields: null); // No primary member in upload

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Single(result.Errors);
        Assert.Equal("NOT_FOUND", result.Errors.First().Code);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithEmptyDependentOfField_ShouldTreatAsNoDependentOfField()
    {
        // Arrange
        DependentValidationContext context = CreateValidationContext(
            dependentMemberFields: new Dictionary<string, string?>
            {
                ["memberType"] = "dependent",
                ["dependentOf"] = "", // Empty string
                ["name"] = "Dependent Member"
            },
            dependentMembersCache: [],
            primaryMemberFields: null);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess); // Should fail because no primary member in upload
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithWhitespaceDependentOfField_ShouldTreatAsNoDependentOfField()
    {
        // Arrange
        DependentValidationContext context = CreateValidationContext(
            dependentMemberFields: new Dictionary<string, string?>
            {
                ["memberType"] = "dependent",
                ["dependentOf"] = "   ", // Whitespace
                ["name"] = "Dependent Member"
            },
            dependentMembersCache: [],
            primaryMemberFields: null);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess); // Should fail because no primary member in upload
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithValidDependentOfFieldAndCacheHit_ShouldLogDebugMessage()
    {
        // Arrange
        PolicyMemberEntity primaryMember = new Mock<PolicyMemberEntity>().Object;
        var dependentMembersCache = new Dictionary<string, PolicyMemberEntity?>
        {
            ["primary-member-123"] = primaryMember
        };

        DependentValidationContext context = CreateValidationContext(
            dependentMemberFields: new Dictionary<string, string?>
            {
                ["memberType"] = "dependent",
                ["dependentOf"] = "primary-member-123"
            },
            dependentMembersCache: dependentMembersCache);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
        // Note: In a real test, you might verify the logger was called with specific messages
    }

    private static DependentValidationContext CreateValidationContext(
        Dictionary<string, string?> dependentMemberFields,
        Dictionary<string, PolicyMemberEntity?> dependentMembersCache,
        MemberUploadFields? primaryMemberFields = null)
    {
        var dependentFields = new MemberUploadFields(dependentMemberFields);
        var policy = new PolicyDto
        {
            Id = "policy-123",
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
            IsIssued = true,
            CanUploadMembers = true
        };
        var schema = new PolicyMemberFieldsSchema([]);
        var validEndorsementIds = new List<string?> { "endorsement-1", "endorsement-2" };

        return DependentValidationContext.Create(
            dependentFields,
            "policy-123",
            policy,
            schema,
            dependentMembersCache,
            validEndorsementIds,
            primaryMemberFields);
    }
}