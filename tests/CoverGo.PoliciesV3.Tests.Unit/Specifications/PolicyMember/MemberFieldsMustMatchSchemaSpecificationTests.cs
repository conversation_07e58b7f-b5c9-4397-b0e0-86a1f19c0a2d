using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.PolicyMember;

public class MemberFieldsMustMatchSchemaSpecificationTests
{
    private readonly Mock<ILogger<MemberFieldsMustMatchSchemaSpecification>> _mockLogger;
    private readonly MemberFieldsMustMatchSchemaSpecification _specification;

    public MemberFieldsMustMatchSchemaSpecificationTests()
    {
        _mockLogger = new Mock<ILogger<MemberFieldsMustMatchSchemaSpecification>>();
        _specification = new MemberFieldsMustMatchSchemaSpecification(_mockLogger.Object);
    }

    [Fact]
    public void BusinessRuleName_ShouldReturnReadableName()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        Assert.Equal("Member Fields Must Match Schema", businessRuleName);
    }

    [Fact]
    public void Description_ShouldReturnMeaningfulDescription()
    {
        // Act
        string description = _specification.Description;

        // Assert
        Assert.Contains("member fields conform", description);
        Assert.Contains("schema definitions", description);
        Assert.Contains("field types", description);
        Assert.Contains("required fields", description);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _specification.IsSatisfiedBy(null!));

    [Fact]
    public async Task IsSatisfiedBy_WithValidFields_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchemaWithValidation(hasValidationErrors: false);
        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["firstName"] = "John Doe", ["test_field"] = "test value" },
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithInvalidFields_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchemaWithValidation(hasValidationErrors: true);
        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["name"] = "", ["email"] = "invalid-email" },
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotEmpty(result.Errors);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithNewMember_ShouldPassMemberIdAsNull()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateSchemaForValidationTest();
        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["name"] = "John Doe" },
            schema: schema,
            memberId: null);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithExistingMember_ShouldPassMemberId()
    {
        // Arrange
        string memberId = "existing-member-123";
        PolicyMemberFieldsSchema schema = CreateSchemaForValidationTest();
        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["name"] = "John Doe" },
            schema: schema,
            memberId: memberId);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
        // Note: We can't directly verify the memberId parameter with real schema,
        // but the test validates that existing members (with memberId) work correctly
    }

    [Fact]
    public async Task IsSatisfiedBy_WithInvalidFieldData_ShouldReturnFailureWithValidationErrors()
    {
        // Arrange - Create a schema that requires a name field, but provide empty data
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "name",
                Label = "Name",
                Type = new StringFieldType(),
                IsRequired = true, // This will cause validation to fail for empty/missing name
                IsUnique = false
            }
        };
        var schema = new PolicyMemberFieldsSchema(memberFields);

        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?> { ["name"] = "" }, // Empty name should fail validation
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotEmpty(result.Errors);
    }

    [Fact]
    public async Task IsSatisfiedBy_WithMultipleValidationErrors_ShouldReturnAllErrors()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateSchemaWithMultipleValidationErrors();
        FieldValidationContext context = CreateValidationContext(
            memberFields: new Dictionary<string, string?>
            {
                ["name"] = "", // Required field, will fail
                ["email"] = "invalid", // Invalid email format, will fail
                ["age"] = "abc" // Invalid number format, will fail
            },
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotEmpty(result.Errors);
        // Note: The exact number of errors may vary based on validation implementation,
        // but we expect multiple errors for the invalid data
    }

    [Fact]
    public async Task IsSatisfiedBy_WithEmptyFields_ShouldCallSchemaValidation()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateSchemaForValidationTest();
        FieldValidationContext context = CreateValidationContext(
            memberFields: [],
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
        // Note: Empty fields should pass validation if no fields are required
    }

    [Fact]
    public async Task IsSatisfiedBy_ShouldPassFieldsAsBothFieldValuesAndOtherFields()
    {
        // Arrange
        var memberFields = new Dictionary<string, string?> { ["name"] = "John Doe", ["memberType"] = "employee" };
        PolicyMemberFieldsSchema schema = CreateSchemaForValidationTest();
        FieldValidationContext context = CreateValidationContext(
            memberFields: memberFields,
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context);

        // Assert
        Assert.True(result.IsSuccess);
        // Note: We can't directly verify the parameter passing with real schema,
        // but the test validates that the fields are processed correctly
    }

    private static FieldValidationContext CreateValidationContext(
        Dictionary<string, string?> memberFields,
        PolicyMemberFieldsSchema? schema = null,
        string? memberId = null)
    {
        var memberUploadFields = new MemberUploadFields(memberFields);
        schema ??= new PolicyMemberFieldsSchema([]);
        var policy = new PolicyDto
        {
            Id = "test-policy-123",
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1))
        };

        return FieldValidationContext.Create(
            memberUploadFields,
            policy,
            schema,
            memberId: memberId);
    }

    private static PolicyMemberFieldsSchema CreateMockSchemaWithValidation(bool hasValidationErrors)
    {
        // Create a simple schema with basic field definitions
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "firstName",
                Label = "First Name",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            },
            new()
            {
                Name = "test_field",
                Label = "Test Field",
                Type = new StringFieldType(),
                IsRequired = hasValidationErrors, // Make it required if we want validation errors
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(memberFields);
    }

    private static PolicyMemberFieldsSchema CreateSchemaForValidationTest()
    {
        // Create a schema that will validate successfully for the test data
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "name",
                Label = "Name",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            },
            new()
            {
                Name = "memberType",
                Label = "Member Type",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(memberFields);
    }

    private static PolicyMemberFieldsSchema CreateSchemaWithMultipleValidationErrors()
    {
        // Create a schema that will produce multiple validation errors
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "name",
                Label = "Name",
                Type = new StringFieldType(),
                IsRequired = true, // This will cause a required field error for empty values
                IsUnique = false
            },
            new()
            {
                Name = "email",
                Label = "Email",
                Type = new StringFieldType { Validations = "regex:^[^@]+@[^@]+\\.[^@]+$" }, // Email regex validation
                IsRequired = true,
                IsUnique = false
            },
            new()
            {
                Name = "age",
                Label = "Age",
                Type = new StringFieldType { Validations = "regex:^[0-9]+$" }, // Number validation
                IsRequired = false,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(memberFields);
    }
}