using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Services;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.PolicyMember;

public class MemberMustHaveUniqueEmailSpecificationTests
{
    private readonly Mock<IPolicyMemberUniquenessService> _mockUniquenessService;
    private readonly Mock<ILogger<MemberMustHaveUniqueEmailSpecification>> _mockLogger;
    private readonly MemberMustHaveUniqueEmailSpecification _specification;

    public MemberMustHaveUniqueEmailSpecificationTests()
    {
        _mockUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        _mockLogger = new Mock<ILogger<MemberMustHaveUniqueEmailSpecification>>();
        _specification = new MemberMustHaveUniqueEmailSpecification(_mockUniquenessService.Object, _mockLogger.Object);
    }

    [Fact]
    public void BusinessRuleName_ShouldReturnReadableName()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        Assert.Equal("Member Must Have Unique Email", businessRuleName);
    }

    [Fact]
    public void Description_ShouldReturnMeaningfulDescription()
    {
        // Act
        string description = _specification.Description;

        // Assert
        Assert.Contains("email addresses are unique", description);
        Assert.Contains("DDD compliant", description);
        Assert.Contains("domain objects", description);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _specification.IsSatisfiedBy(null!, CancellationToken.None));

    [Fact]
    public async Task IsSatisfiedByAsync_WithNoEmailField_ShouldReturnSuccess()
    {
        // Arrange
        UniquenessValidationContext context = CreateValidationContext(fieldValues: new Dictionary<string, object?> { ["name"] = "John Doe" });

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidateTenantScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithEmptyEmail_ShouldReturnSuccess()
    {
        // Arrange
        UniquenessValidationContext context = CreateValidationContext(fieldValues: new Dictionary<string, object?> { ["email"] = "" });

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithNullEmail_ShouldReturnSuccess()
    {
        // Arrange
        UniquenessValidationContext context = CreateValidationContext(fieldValues: new Dictionary<string, object?> { ["email"] = null });

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithEmailFieldNotInSchema_ShouldReturnSuccess()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: false);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["email"] = "<EMAIL>" },
            schema: schema);

        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithUniqueEmailInTenantScope_ShouldValidateTenantScope()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: true, isEmailUnique: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["email"] = "<EMAIL>" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);



        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidateTenantScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.Is<Dictionary<string, object>>(d => d.ContainsKey("email") && d["email"].ToString() == "<EMAIL>"),
            It.Is<List<string>>(l => l.Contains("email")),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithNonUniqueEmailInTenantScope_ShouldSkipTenantScopeValidation()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: true, isEmailUnique: false);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["email"] = "<EMAIL>" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);



        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidateTenantScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithValidEmail_ShouldAlwaysValidatePolicyScope()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: true, isEmailUnique: false);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["email"] = "<EMAIL>" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);



        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _mockUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.Is<Dictionary<string, object>>(d => d.ContainsKey("email") && d["email"].ToString() == "<EMAIL>"),
            It.Is<List<string>>(l => l.Contains("email")),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithDuplicateEmailInTenantScope_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: true, isEmailUnique: true);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["email"] = "<EMAIL>" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["email"]);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);



        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Single(result.Errors);
        Assert.Contains("tenant", result.Errors.First().Message);
    }

    [Fact]
    public async Task IsSatisfiedByAsync_WithDuplicateEmailInPolicyScope_ShouldReturnFailure()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchema(hasEmailField: true, isEmailUnique: false);
        UniquenessValidationContext context = CreateValidationContext(
            fieldValues: new Dictionary<string, object?> { ["email"] = "<EMAIL>" },
            schema: schema);

        _mockUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["email"]);



        // Act
        PoliciesV3.Domain.Common.Validation.Result result = await _specification.IsSatisfiedBy(context, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Single(result.Errors);
        Assert.Contains("policy", result.Errors.First().Message);
    }

    private static UniquenessValidationContext CreateValidationContext(
        Dictionary<string, object?> fieldValues,
        PolicyMemberFieldsSchema? schema = null)
    {
        string policyId = Guid.NewGuid().ToString();
        var policy = new PolicyDto
        {
            Id = policyId,
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
            Endorsements =
            [
                new() { Id = Guid.NewGuid().ToString(), Status = "APPROVED" },
                new() { Id = Guid.NewGuid().ToString(), Status = "PENDING" }
            ]
        };

        var memberUploadFields = new MemberUploadFields(fieldValues.ToDictionary(kvp => kvp.Key, kvp => kvp.Value?.ToString()));

        return UniquenessValidationContext.Builder()
            .WithMemberFields(memberUploadFields)
            .WithPolicy(policy)
            .WithSchema(schema ?? CreateMockSchema())
            .WithPolicyId(policyId)
            .WithPlanId("test-plan-id")
            .WithContractHolderPolicyIds([])
            .Build();
    }

    private static PolicyMemberFieldsSchema CreateMockSchema(bool hasEmailField = true, bool isEmailUnique = false)
    {
        var memberFields = new List<PolicyMemberFieldDefinition>();

        if (hasEmailField)
        {
            var emailField = new PolicyMemberFieldDefinition
            {
                Name = "email",
                Label = "Email Address",
                IsUnique = isEmailUnique,
                IsRequired = false,
                IsRequiredForDependent = false,
                Type = new StringFieldType()
            };

            memberFields.Add(emailField);
        }

        return new PolicyMemberFieldsSchema(memberFields);
    }
}