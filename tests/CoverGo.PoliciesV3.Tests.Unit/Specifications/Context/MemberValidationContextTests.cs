using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.Context;

/// <summary>
/// Unit tests for MemberValidationContext base class functionality.
/// Tests the common properties and helper methods shared by all member validation contexts.
/// </summary>
public class MemberValidationContextTests
{
    #region Test Context Implementation

    /// <summary>
    /// Concrete implementation of MemberValidationContext for testing purposes.
    /// </summary>
    private sealed record TestMemberValidationContext : MemberValidationContext
    {
        public static TestMemberValidationContext Create(
            MemberUploadFields memberFields,
            PolicyDto policy,
            PolicyMemberFieldsSchema schema,
            Guid? endorsementId = null)
        {
            Validate(memberFields, policy, schema);

            return new TestMemberValidationContext
            {
                MemberFields = memberFields,
                Policy = policy,
                Schema = schema,
                EndorsementId = endorsementId
            };
        }
    }

    #endregion

    #region Helper Method Tests

    [Fact]
    public void GetMemberId_WithMemberIdField_ShouldReturnMemberId()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["memberId"] = "member-123" });
        TestMemberValidationContext context = CreateTestContext(memberFields);

        // Act
        string? result = context.GetMemberId();

        // Assert
        Assert.Equal("member-123", result);
    }

    [Fact]
    public void GetMemberId_WithoutMemberIdField_ShouldReturnNull()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["name"] = "John Doe" });
        TestMemberValidationContext context = CreateTestContext(memberFields);

        // Act
        string? result = context.GetMemberId();

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void GetPlanId_WithPlanIdField_ShouldReturnPlanId()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["planId"] = "plan-456" });
        TestMemberValidationContext context = CreateTestContext(memberFields);

        // Act
        string? result = context.GetPlanId();

        // Assert
        Assert.Equal("plan-456", result);
    }

    [Fact]
    public void GetPlanId_WithoutPlanIdField_ShouldReturnNull()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["name"] = "John Doe" });
        TestMemberValidationContext context = CreateTestContext(memberFields);

        // Act
        string? result = context.GetPlanId();

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void GetEmail_WithEmailField_ShouldReturnEmail()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["email"] = "<EMAIL>" });
        TestMemberValidationContext context = CreateTestContext(memberFields);

        // Act
        string? result = context.GetEmail();

        // Assert
        Assert.Equal("<EMAIL>", result);
    }

    [Fact]
    public void GetEmail_WithoutEmailField_ShouldReturnNull()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["name"] = "John Doe" });
        TestMemberValidationContext context = CreateTestContext(memberFields);

        // Act
        string? result = context.GetEmail();

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void GetFieldValue_WithExistingField_ShouldReturnFieldValue()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["customField"] = "customValue" });
        TestMemberValidationContext context = CreateTestContext(memberFields);

        // Act
        string? result = context.GetFieldValue("customField");

        // Assert
        Assert.Equal("customValue", result);
    }

    [Fact]
    public void GetFieldValue_WithNonExistingField_ShouldReturnNull()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["name"] = "John Doe" });
        TestMemberValidationContext context = CreateTestContext(memberFields);

        // Act
        string? result = context.GetFieldValue("nonExistingField");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void GetFieldValue_WithNullFieldValue_ShouldReturnNull()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["nullField"] = null });
        TestMemberValidationContext context = CreateTestContext(memberFields);

        // Act
        string? result = context.GetFieldValue("nullField");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region Validation Tests

    [Fact]
    public void Validate_WithValidParameters_ShouldNotThrow()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act & Assert
        Exception exception = Record.Exception(() => TestMemberValidationContext.Create(memberFields, policy, schema));
        Assert.Null(exception);
    }

    [Fact]
    public void Validate_WithNullMemberFields_ShouldThrowArgumentNullException()
    {
        // Arrange
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => TestMemberValidationContext.Create(null!, policy, schema));
    }

    [Fact]
    public void Validate_WithNullPolicy_ShouldThrowArgumentNullException()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => TestMemberValidationContext.Create(memberFields, null!, schema));
    }

    [Fact]
    public void Validate_WithNullSchema_ShouldThrowArgumentNullException()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => TestMemberValidationContext.Create(memberFields, policy, null!));
    }

    #endregion

    #region Context Property Tests

    [Fact]
    public void Context_WithEndorsementId_ShouldSetEndorsementId()
    {
        // Arrange
        var endorsementId = Guid.NewGuid();
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act
        var context = TestMemberValidationContext.Create(memberFields, policy, schema, endorsementId);

        // Assert
        Assert.Equal(endorsementId, context.EndorsementId);
    }

    [Fact]
    public void Context_WithoutEndorsementId_ShouldHaveNullEndorsementId()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act
        var context = TestMemberValidationContext.Create(memberFields, policy, schema);

        // Assert
        Assert.Null(context.EndorsementId);
    }

    #endregion

    #region Helper Methods

    private static TestMemberValidationContext CreateTestContext(
        MemberUploadFields? memberFields = null,
        PolicyDto? policy = null,
        PolicyMemberFieldsSchema? schema = null,
        Guid? endorsementId = null) => TestMemberValidationContext.Create(
            memberFields ?? CreateMemberUploadFields(),
            policy ?? CreateMockPolicy(),
            schema ?? CreateMockSchema(),
            endorsementId);

    private static MemberUploadFields CreateMemberUploadFields(Dictionary<string, string?>? fields = null)
    {
        var defaultFields = new Dictionary<string, string?> { ["name"] = "John Doe" };
        return new MemberUploadFields(fields ?? defaultFields);
    }

    private static PolicyDto CreateMockPolicy() => new()
    {
        Id = Guid.NewGuid().ToString(),
        StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
        Endorsements =
            [
                new() { Id = Guid.NewGuid().ToString(), Status = "APPROVED" }
            ]
    };

    private static PolicyMemberFieldsSchema CreateMockSchema()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "name",
                Label = "Full Name",
                IsUnique = false,
                IsRequired = true,
                IsRequiredForDependent = false,
                Type = new StringFieldType()
            }
        };

        return new PolicyMemberFieldsSchema(memberFields);
    }

    #endregion
}
