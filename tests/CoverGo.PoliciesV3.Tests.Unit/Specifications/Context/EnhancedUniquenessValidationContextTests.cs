using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.Context;

/// <summary>
/// Unit tests for UniquenessValidationContext following existing test patterns.
/// Tests the enhanced context with pre-resolved external data and additional properties.
/// </summary>
public class EnhancedUniquenessValidationContextTests
{
    [Fact]
    public void Create_WithValidParameters_ShouldCreateContextSuccessfully()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        string policyId = "policy-123";
        string planId = "plan-456";
        string contractHolderId = "contract-holder-789";
        string memberClass = "employee";
        var contractHolderPolicyIds = new List<string> { "policy-1", "policy-2" };
        bool shouldSkipContractHolderValidation = true;
        var contractHolderValidEndorsementIds = new List<string?> { "endorsement-1", "endorsement-2", null };
        var endorsementId = Guid.NewGuid();
        string tenantId = "tenant-123";
        var featureFlags = new Dictionary<string, bool> { ["TestFlag"] = true, ["AnotherFlag"] = false };
        string memberId = "member-456";
        string policyMemberId = "policy-member-789";

        // Act
        UniquenessValidationContext context = UniquenessValidationContext.Builder()
            .WithMemberFields(memberFields)
            .WithPolicy(policy)
            .WithSchema(schema)
            .WithPolicyId(policyId)
            .WithPlanId(planId)
            .WithContractHolderId(contractHolderId)
            .WithMemberClass(memberClass)
            .WithContractHolderPolicyIds(contractHolderPolicyIds)
            .WithSkipContractHolderValidation(shouldSkipContractHolderValidation)
            .WithContractHolderValidEndorsementIds(contractHolderValidEndorsementIds)
            .WithEndorsementId(endorsementId)
            .WithTenantId(tenantId)
            .WithFeatureFlags(featureFlags)
            .WithMemberId(memberId)
            .WithPolicyMemberId(policyMemberId)
            .Build();

        // Assert
        Assert.NotNull(context);
        Assert.Equal(memberFields, context.MemberFields);
        Assert.Equal(policy, context.Policy);
        Assert.Equal(schema, context.Schema);
        Assert.Equal(policyId, context.PolicyId);
        Assert.Equal(planId, context.PlanId);
        Assert.Equal(contractHolderId, context.ContractHolderId);
        Assert.Equal(memberClass, context.Class);
        Assert.Equal(contractHolderPolicyIds, context.ContractHolderPolicyIds);
        Assert.Equal(shouldSkipContractHolderValidation, context.ShouldSkipContractHolderValidation);
        Assert.Equal(contractHolderValidEndorsementIds, context.ContractHolderValidEndorsementIds);
        Assert.Equal(endorsementId, context.EndorsementId);
        Assert.Equal(tenantId, context.TenantId);
        Assert.Equal(featureFlags, context.FeatureFlags);
        Assert.Equal(memberId, context.MemberId);
        Assert.Equal(policyMemberId, context.PolicyMemberId);
    }

    [Fact]
    public void Create_WithMinimalParameters_ShouldCreateContextWithDefaults()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        string policyId = "policy-123";
        string planId = "plan-456";

        // Act
        UniquenessValidationContext context = UniquenessValidationContext.Builder()
            .WithMemberFields(memberFields)
            .WithPolicy(policy)
            .WithSchema(schema)
            .WithPolicyId(policyId)
            .WithPlanId(planId)
            .Build();

        // Assert
        Assert.NotNull(context);
        Assert.Equal(memberFields, context.MemberFields);
        Assert.Equal(policy, context.Policy);
        Assert.Equal(schema, context.Schema);
        Assert.Equal(policyId, context.PolicyId);
        Assert.Equal(planId, context.PlanId);
        Assert.Null(context.ContractHolderId);
        Assert.Null(context.Class);
        Assert.Empty(context.ContractHolderPolicyIds);
        Assert.False(context.ShouldSkipContractHolderValidation);
        Assert.Null(context.ContractHolderValidEndorsementIds);
        Assert.Null(context.EndorsementId);
        Assert.Null(context.TenantId);
        Assert.Null(context.FeatureFlags);
        Assert.Null(context.MemberId);
        Assert.Null(context.PolicyMemberId);
    }





    [Fact]
    public void GetFeatureFlag_WithExistingFlag_ShouldReturnCorrectValue()
    {
        // Arrange
        var featureFlags = new Dictionary<string, bool> { ["TestFlag"] = true, ["AnotherFlag"] = false };
        UniquenessValidationContext context = CreateEnhancedContext(featureFlags: featureFlags);

        // Act & Assert
        Assert.True(context.GetFeatureFlag("TestFlag"));
        Assert.False(context.GetFeatureFlag("AnotherFlag"));
    }

    [Fact]
    public void GetFeatureFlag_WithNonExistingFlag_ShouldReturnDefaultValue()
    {
        // Arrange
        var featureFlags = new Dictionary<string, bool> { ["TestFlag"] = true };
        UniquenessValidationContext context = CreateEnhancedContext(featureFlags: featureFlags);

        // Act & Assert
        Assert.False(context.GetFeatureFlag("NonExistingFlag")); // Default is false
        Assert.True(context.GetFeatureFlag("NonExistingFlag", true)); // Custom default
    }

    [Fact]
    public void GetFeatureFlag_WithNullFeatureFlags_ShouldReturnDefaultValue()
    {
        // Arrange
        UniquenessValidationContext context = CreateEnhancedContext(featureFlags: null);

        // Act & Assert
        Assert.False(context.GetFeatureFlag("AnyFlag"));
        Assert.True(context.GetFeatureFlag("AnyFlag", true));
    }

    [Fact]
    public void HasContractHolderEndorsementIds_WithEndorsementIds_ShouldReturnTrue()
    {
        // Arrange
        var endorsementIds = new List<string?> { "endorsement-1", "endorsement-2" };
        UniquenessValidationContext context = CreateEnhancedContext(contractHolderValidEndorsementIds: endorsementIds);

        // Act & Assert
        Assert.True(context.HasContractHolderEndorsementIds());
    }

    [Fact]
    public void HasContractHolderEndorsementIds_WithEmptyList_ShouldReturnFalse()
    {
        // Arrange
        UniquenessValidationContext context = CreateEnhancedContext(contractHolderValidEndorsementIds: []);

        // Act & Assert
        Assert.False(context.HasContractHolderEndorsementIds());
    }

    [Fact]
    public void HasContractHolderEndorsementIds_WithNull_ShouldReturnFalse()
    {
        // Arrange
        UniquenessValidationContext context = CreateEnhancedContext(contractHolderValidEndorsementIds: null);

        // Act & Assert
        Assert.False(context.HasContractHolderEndorsementIds());
    }

    [Fact]
    public void IsExistingMemberUpdate_WithMemberId_ShouldReturnTrue()
    {
        // Arrange
        UniquenessValidationContext context = CreateEnhancedContext(memberId: "member-123");

        // Act & Assert
        Assert.True(context.IsExistingMemberUpdate());
    }

    [Fact]
    public void IsExistingMemberUpdate_WithoutMemberId_ShouldReturnFalse()
    {
        // Arrange
        UniquenessValidationContext context = CreateEnhancedContext(memberId: null);

        // Act & Assert
        Assert.False(context.IsExistingMemberUpdate());
    }

    [Fact]
    public void GetEffectiveMemberId_WithPreResolvedMemberId_ShouldReturnPreResolvedValue()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["memberId"] = "field-member-id" });
        UniquenessValidationContext context = CreateEnhancedContext(memberFields: memberFields, memberId: "pre-resolved-member-id");

        // Act
        string? effectiveMemberId = context.GetEffectiveMemberId();

        // Assert
        Assert.Equal("pre-resolved-member-id", effectiveMemberId);
    }

    [Fact]
    public void GetEffectiveMemberId_WithoutPreResolvedMemberId_ShouldReturnFieldValue()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["memberId"] = "field-member-id" });
        UniquenessValidationContext context = CreateEnhancedContext(memberFields: memberFields, memberId: null);

        // Act
        string? effectiveMemberId = context.GetEffectiveMemberId();

        // Assert
        Assert.Equal("field-member-id", effectiveMemberId);
    }

    [Fact]
    public void GetEffectivePolicyMemberId_ShouldReturnPolicyMemberId()
    {
        // Arrange
        UniquenessValidationContext context = CreateEnhancedContext(policyMemberId: "policy-member-123");

        // Act
        string? effectivePolicyMemberId = context.GetEffectivePolicyMemberId();

        // Assert
        Assert.Equal("policy-member-123", effectivePolicyMemberId);
    }

    private static UniquenessValidationContext CreateEnhancedContext(
        MemberUploadFields? memberFields = null,
        IReadOnlyDictionary<string, bool>? featureFlags = null,
        IReadOnlyList<string?>? contractHolderValidEndorsementIds = null,
        string? memberId = null,
        string? policyMemberId = null) => UniquenessValidationContext.Builder()
            .WithMemberFields(memberFields ?? CreateMemberUploadFields())
            .WithPolicy(CreateMockPolicy())
            .WithSchema(CreateMockSchema())
            .WithPolicyId("policy-123")
            .WithPlanId("plan-456")
            .WithFeatureFlags(featureFlags)
            .WithContractHolderValidEndorsementIds(contractHolderValidEndorsementIds)
            .WithMemberId(memberId)
            .WithPolicyMemberId(policyMemberId)
            .Build();

    private static MemberUploadFields CreateMemberUploadFields(Dictionary<string, string?>? fieldValues = null) => new(fieldValues ?? new Dictionary<string, string?> { ["email"] = "<EMAIL>" });

    private static PolicyDto CreateMockPolicy() => new()
    {
        Id = Guid.NewGuid().ToString(),
        StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
        Endorsements =
            [
                new() { Id = Guid.NewGuid().ToString(), Status = "APPROVED" },
                new() { Id = Guid.NewGuid().ToString(), Status = "PENDING" }
            ]
    };

    private static PolicyMemberFieldsSchema CreateMockSchema()
    {
        var emailField = new PolicyMemberFieldDefinition
        {
            Name = "email",
            Label = "Email Address",
            IsUnique = false,
            IsRequired = false,
            IsRequiredForDependent = false,
            Type = new StringFieldType()
        };

        return new PolicyMemberFieldsSchema([emailField]);
    }
}
