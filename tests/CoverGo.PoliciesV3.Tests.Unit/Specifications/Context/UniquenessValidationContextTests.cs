using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.Context;

/// <summary>
/// Unit tests for UniquenessValidationContext class.
/// Tests uniqueness validation context creation, validation, and specialized helper methods.
/// </summary>
public class UniquenessValidationContextTests
{
    #region Builder Pattern Tests

    [Fact]
    public void Builder_WithValidParameters_ShouldCreateContext()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        string policyId = "policy-123";
        string planId = "plan-456";

        // Act
        UniquenessValidationContext context = UniquenessValidationContext.Builder()
            .WithMemberFields(memberFields)
            .WithPolicy(policy)
            .WithSchema(schema)
            .WithPolicyId(policyId)
            .WithPlanId(planId)
            .Build();

        // Assert
        Assert.NotNull(context);
        Assert.Equal(memberFields, context.MemberFields);
        Assert.Equal(policy, context.Policy);
        Assert.Equal(schema, context.Schema);
        Assert.Equal(policyId, context.PolicyId);
        Assert.Equal(planId, context.PlanId);
        Assert.Null(context.ContractHolderId);
        Assert.Null(context.Class);
        Assert.Empty(context.ContractHolderPolicyIds);
        Assert.False(context.ShouldSkipContractHolderValidation);
        Assert.Null(context.EndorsementId);
    }

    [Fact]
    public void Builder_WithAllParameters_ShouldSetAllProperties()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        string policyId = "policy-123";
        string planId = "plan-456";
        string contractHolderId = "contract-789";
        string memberClass = "class-A";
        var contractHolderPolicyIds = new List<string> { "policy-1", "policy-2" };
        bool shouldSkipContractHolderValidation = true;
        var endorsementId = Guid.NewGuid();
        var contractHolderValidEndorsementIds = new List<string?> { endorsementId.ToString() };

        // Act
        UniquenessValidationContext context = UniquenessValidationContext.Builder()
            .WithMemberFields(memberFields)
            .WithPolicy(policy)
            .WithSchema(schema)
            .WithPolicyId(policyId)
            .WithPlanId(planId)
            .WithContractHolderId(contractHolderId)
            .WithMemberClass(memberClass)
            .WithContractHolderPolicyIds(contractHolderPolicyIds)
            .WithSkipContractHolderValidation(shouldSkipContractHolderValidation)
            .WithContractHolderValidEndorsementIds(contractHolderValidEndorsementIds)
            .WithTenantId("test-tenant")
            .WithFeatureFlags(new Dictionary<string, bool>())
            .WithMemberId("member-1")
            .WithPolicyMemberId("policy-member-1")
            .WithValidationRules([])
            .WithFieldValues(new Dictionary<string, object>())
            .WithEndorsementId(endorsementId)
            .Build();

        // Assert
        Assert.Equal(contractHolderId, context.ContractHolderId);
        Assert.Equal(memberClass, context.Class);
        Assert.Equal(contractHolderPolicyIds, context.ContractHolderPolicyIds);
        Assert.Equal(shouldSkipContractHolderValidation, context.ShouldSkipContractHolderValidation);
        Assert.Equal(endorsementId, context.EndorsementId);
    }




    #endregion

    #region Specialized Helper Method Tests

    [Fact]
    public void GetRawFieldValue_WithExistingField_ShouldReturnFieldValue()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["email"] = "<EMAIL>" });
        UniquenessValidationContext context = CreateUniquenessValidationContext(memberFields);

        // Act
        object? result = context.GetRawFieldValue("email");

        // Assert
        Assert.Equal("<EMAIL>", result);
    }

    [Fact]
    public void GetRawFieldValue_WithNonExistingField_ShouldReturnNull()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["name"] = "John Doe" });
        UniquenessValidationContext context = CreateUniquenessValidationContext(memberFields);

        // Act
        object? result = context.GetRawFieldValue("email");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void GetFieldStringValue_WithExistingField_ShouldReturnStringValue()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["email"] = "<EMAIL>" });
        UniquenessValidationContext context = CreateUniquenessValidationContext(memberFields);

        // Act
        string? result = context.GetFieldStringValue("email");

        // Assert
        Assert.Equal("<EMAIL>", result);
    }

    [Fact]
    public void GetFieldStringValue_WithNullField_ShouldReturnNull()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["email"] = null });
        UniquenessValidationContext context = CreateUniquenessValidationContext(memberFields);

        // Act
        string? result = context.GetFieldStringValue("email");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void HasNonEmptyFieldValue_WithNonEmptyField_ShouldReturnTrue()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["email"] = "<EMAIL>" });
        UniquenessValidationContext context = CreateUniquenessValidationContext(memberFields);

        // Act
        bool result = context.HasNonEmptyFieldValue("email");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void HasNonEmptyFieldValue_WithEmptyField_ShouldReturnFalse()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["email"] = "" });
        UniquenessValidationContext context = CreateUniquenessValidationContext(memberFields);

        // Act
        bool result = context.HasNonEmptyFieldValue("email");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void HasNonEmptyFieldValue_WithWhitespaceField_ShouldReturnFalse()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["email"] = "   " });
        UniquenessValidationContext context = CreateUniquenessValidationContext(memberFields);

        // Act
        bool result = context.HasNonEmptyFieldValue("email");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void HasNonEmptyFieldValue_WithNullField_ShouldReturnFalse()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["email"] = null });
        UniquenessValidationContext context = CreateUniquenessValidationContext(memberFields);

        // Act
        bool result = context.HasNonEmptyFieldValue("email");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void GetFieldDefinition_WithExistingField_ShouldReturnFieldDefinition()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchemaWithEmailField(isUnique: true);
        UniquenessValidationContext context = CreateUniquenessValidationContext(schema: schema);

        // Act
        PolicyMemberFieldDefinition? result = context.GetFieldDefinition("email");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("email", result.Name);
        Assert.Equal("Email Address", result.Label);
        Assert.True(result.IsUnique);
    }

    [Fact]
    public void GetFieldDefinition_WithNonExistingField_ShouldReturnNull()
    {
        // Arrange
        UniquenessValidationContext context = CreateUniquenessValidationContext();

        // Act
        PolicyMemberFieldDefinition? result = context.GetFieldDefinition("nonExistingField");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void IsFieldUnique_WithUniqueField_ShouldReturnTrue()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchemaWithEmailField(isUnique: true);
        UniquenessValidationContext context = CreateUniquenessValidationContext(schema: schema);

        // Act
        bool result = context.IsFieldUnique("email");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsFieldUnique_WithNonUniqueField_ShouldReturnFalse()
    {
        // Arrange
        PolicyMemberFieldsSchema schema = CreateMockSchemaWithEmailField(isUnique: false);
        UniquenessValidationContext context = CreateUniquenessValidationContext(schema: schema);

        // Act
        bool result = context.IsFieldUnique("email");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsFieldUnique_WithNonExistingField_ShouldReturnFalse()
    {
        // Arrange
        UniquenessValidationContext context = CreateUniquenessValidationContext();

        // Act
        bool result = context.IsFieldUnique("nonExistingField");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void GetFieldValuesForUniquenessValidation_ShouldReturnDictionaryWithObjectValues()
    {
        // Arrange
        var fields = new Dictionary<string, string?>
        {
            ["email"] = "<EMAIL>",
            ["name"] = "John Doe",
            ["age"] = null
        };
        MemberUploadFields memberFields = CreateMemberUploadFields(fields);
        UniquenessValidationContext context = CreateUniquenessValidationContext(memberFields);

        // Act
        IDictionary<string, object?> result = context.GetFieldValuesForUniquenessValidation();

        // Assert
        Assert.Equal(3, result.Count);
        Assert.Equal("<EMAIL>", result["email"]);
        Assert.Equal("John Doe", result["name"]);
        Assert.Null(result["age"]);
    }

    [Fact]
    public void GetRawFieldValue_ShouldReturnObjectType_WhileBaseGetFieldValue_ShouldReturnStringType()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["email"] = "<EMAIL>" });
        UniquenessValidationContext context = CreateUniquenessValidationContext(memberFields);

        // Act - Call the new method directly
        object? rawResult = context.GetRawFieldValue("email");

        // Act - Call base method through polymorphic reference
        MemberValidationContext baseContext = context;
        string? baseResult = baseContext.GetFieldValue("email");

        // Assert - Both should return the same value but different declared types
        Assert.Equal("<EMAIL>", rawResult);
        Assert.Equal("<EMAIL>", baseResult);
        Assert.IsType<string>(rawResult); // Actual type is still string
        Assert.IsType<string>(baseResult);
    }

    [Fact]
    public void GetFieldStringValue_ShouldUseGetRawFieldValue_AndMaintainBehavior()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["email"] = "<EMAIL>" });
        UniquenessValidationContext context = CreateUniquenessValidationContext(memberFields);

        // Act
        string? stringResult = context.GetFieldStringValue("email");
        object? rawResult = context.GetRawFieldValue("email");

        // Assert - GetFieldStringValue should return same as GetRawFieldValue?.ToString()
        Assert.Equal("<EMAIL>", stringResult);
        Assert.Equal(rawResult?.ToString(), stringResult);
    }

    [Fact]
    public void PolymorphicBehavior_ShouldNotBreak_AfterMethodRenaming()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?>
        {
            ["memberId"] = "member-123",
            ["email"] = "<EMAIL>",
            ["planId"] = "plan-456"
        });
        UniquenessValidationContext context = CreateUniquenessValidationContext(memberFields);

        // Act - Call base class methods through polymorphic reference
        MemberValidationContext baseContext = context;
        string? memberId = baseContext.GetMemberId();
        string? email = baseContext.GetEmail();
        string? planId = baseContext.GetPlanId();

        // Assert - Base class methods should work unchanged
        Assert.Equal("member-123", memberId);
        Assert.Equal("<EMAIL>", email);
        Assert.Equal("plan-456", planId);
    }

    #endregion

    #region Builder Pattern Tests

    [Fact]
    public void Builder_WithRequiredParameters_ShouldCreateContext()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        string policyId = "policy-123";
        string planId = "plan-456";

        // Act
        UniquenessValidationContext context = UniquenessValidationContext.Builder()
            .WithMemberFields(memberFields)
            .WithPolicy(policy)
            .WithSchema(schema)
            .WithPolicyId(policyId)
            .WithPlanId(planId)
            .Build();

        // Assert
        Assert.Equal(memberFields, context.MemberFields);
        Assert.Equal(policy, context.Policy);
        Assert.Equal(schema, context.Schema);
        Assert.Equal(policyId, context.PolicyId);
        Assert.Equal(planId, context.PlanId);
    }



    [Fact]
    public void Builder_WithMissingMemberFields_ShouldThrowInvalidOperationException()
    {
        // Arrange
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act & Assert
        InvalidOperationException exception = Assert.Throws<InvalidOperationException>(() =>
            UniquenessValidationContext.Builder()
                .WithPolicy(policy)
                .WithSchema(schema)
                .WithPolicyId("policy-123")
                .WithPlanId("plan-456")
                .Build());

        Assert.Contains("MemberFields is required", exception.Message);
    }

    [Fact]
    public void Builder_WithMissingPolicy_ShouldThrowInvalidOperationException()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act & Assert
        InvalidOperationException exception = Assert.Throws<InvalidOperationException>(() =>
            UniquenessValidationContext.Builder()
                .WithMemberFields(memberFields)
                .WithSchema(schema)
                .WithPolicyId("policy-123")
                .WithPlanId("plan-456")
                .Build());

        Assert.Contains("Policy is required", exception.Message);
    }

    [Fact]
    public void Builder_WithMissingSchema_ShouldThrowInvalidOperationException()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();

        // Act & Assert
        InvalidOperationException exception = Assert.Throws<InvalidOperationException>(() =>
            UniquenessValidationContext.Builder()
                .WithMemberFields(memberFields)
                .WithPolicy(policy)
                .WithPolicyId("policy-123")
                .WithPlanId("plan-456")
                .Build());

        Assert.Contains("Schema is required", exception.Message);
    }

    [Fact]
    public void Builder_WithMissingPolicyId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act & Assert
        InvalidOperationException exception = Assert.Throws<InvalidOperationException>(() =>
            UniquenessValidationContext.Builder()
                .WithMemberFields(memberFields)
                .WithPolicy(policy)
                .WithSchema(schema)
                .WithPlanId("plan-456")
                .Build());

        Assert.Contains("PolicyId is required", exception.Message);
    }

    [Fact]
    public void Builder_WithMissingPlanId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act & Assert
        InvalidOperationException exception = Assert.Throws<InvalidOperationException>(() =>
            UniquenessValidationContext.Builder()
                .WithMemberFields(memberFields)
                .WithPolicy(policy)
                .WithSchema(schema)
                .WithPolicyId("policy-123")
                .Build());

        Assert.Contains("PlanId is required", exception.Message);
    }

    [Fact]
    public void Builder_FluentInterface_ShouldAllowMethodChaining()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act - Test fluent interface
        UniquenessValidationContext context = UniquenessValidationContext.Builder()
            .WithMemberFields(memberFields)
            .WithPolicy(policy)
            .WithSchema(schema)
            .WithPolicyId("policy-123")
            .WithPlanId("plan-456")
            .WithContractHolderId("contract-789")
            .WithMemberClass("employee")
            .WithSkipContractHolderValidation(true)
            .WithTenantId("tenant-123")
            .WithMemberId("member-456")
            .Build();

        // Assert
        Assert.NotNull(context);
        Assert.Equal("contract-789", context.ContractHolderId);
        Assert.Equal("employee", context.Class);
        Assert.True(context.ShouldSkipContractHolderValidation);
        Assert.Equal("tenant-123", context.TenantId);
        Assert.Equal("member-456", context.MemberId);
    }

    #endregion

    #region Helper Methods

    private static UniquenessValidationContext CreateUniquenessValidationContext(
        MemberUploadFields? memberFields = null,
        PolicyDto? policy = null,
        PolicyMemberFieldsSchema? schema = null,
        string policyId = "policy-123",
        string planId = "plan-456") => UniquenessValidationContext.Builder()
            .WithMemberFields(memberFields ?? CreateMemberUploadFields())
            .WithPolicy(policy ?? CreateMockPolicy())
            .WithSchema(schema ?? CreateMockSchema())
            .WithPolicyId(policyId)
            .WithPlanId(planId)
            .Build();

    private static MemberUploadFields CreateMemberUploadFields(Dictionary<string, string?>? fields = null)
    {
        var defaultFields = new Dictionary<string, string?> { ["name"] = "John Doe" };
        return new MemberUploadFields(fields ?? defaultFields);
    }

    private static PolicyDto CreateMockPolicy() => new()
    {
        Id = Guid.NewGuid().ToString(),
        StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
        Endorsements =
            [
                new() { Id = Guid.NewGuid().ToString(), Status = "APPROVED" }
            ]
    };

    private static PolicyMemberFieldsSchema CreateMockSchema()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "name",
                Label = "Full Name",
                IsUnique = false,
                IsRequired = true,
                IsRequiredForDependent = false,
                Type = new StringFieldType()
            }
        };

        return new PolicyMemberFieldsSchema(memberFields);
    }

    private static PolicyMemberFieldsSchema CreateMockSchemaWithEmailField(bool isUnique = false)
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "email",
                Label = "Email Address",
                IsUnique = isUnique,
                IsRequired = false,
                IsRequiredForDependent = false,
                Type = new StringFieldType()
            }
        };

        return new PolicyMemberFieldsSchema(memberFields);
    }

    #endregion
}
