using System.Reflection;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Products;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Tests.Unit.Common;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.Context;

public class CompleteValidationContextTests
{
    #region Create With Processed Data Tests

    [Fact]
    public void CreateWithProcessedData_WithValidParameters_ShouldCreateContext()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        ResolvedValidationData resolvedData = CreateValidResolvedData();
        FileProcessingResult fileResult = CreateValidFileResult();
        PolicyMemberFieldsSchema schema = CreateValidSchema();
        MembersUploadFields transformedData = CreateValidTransformedData();

        // Act
        CompleteValidationContext context = CompleteValidationContext.CreateWithProcessedData(
            upload, policy, resolvedData, fileResult, schema, transformedData);

        // Assert
        context.Should().NotBeNull();
        context.Upload.Should().Be(upload);
        context.Policy.Should().Be(policy);
        context.ResolvedData.Should().Be(resolvedData);
        context.FileResult.Should().Be(fileResult);
        context.Schema.Should().Be(schema);
        context.TransformedMemberData.Should().Be(transformedData);
        context.HasPreProcessedData.Should().BeTrue();
    }

    [Fact]
    public void CreateWithProcessedData_WithNullUpload_ShouldThrowArgumentNullException()
    {
        // Arrange
        PolicyDto policy = CreateValidPolicy();
        ResolvedValidationData resolvedData = CreateValidResolvedData();
        FileProcessingResult fileResult = CreateValidFileResult();
        PolicyMemberFieldsSchema schema = CreateValidSchema();
        MembersUploadFields transformedData = CreateValidTransformedData();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() =>
            CompleteValidationContext.CreateWithProcessedData(
                null!, policy, resolvedData, fileResult, schema, transformedData));
    }

    [Fact]
    public void CreateWithProcessedData_WithEmptyUploadPath_ShouldThrowValidationException()
    {
        // Arrange
        PolicyMemberUpload upload = CreateUploadWithEmptyPath();
        PolicyDto policy = CreateValidPolicy();
        ResolvedValidationData resolvedData = CreateValidResolvedData();
        FileProcessingResult fileResult = CreateValidFileResult();
        PolicyMemberFieldsSchema schema = CreateValidSchema();
        MembersUploadFields transformedData = CreateValidTransformedData();

        // Act & Assert
        ValidationException exception = Assert.Throws<ValidationException>(() =>
            CompleteValidationContext.CreateWithProcessedData(
                upload, policy, resolvedData, fileResult, schema, transformedData));

        // Assert exception details
        exception.Errors.Should().HaveCount(1);
        exception.Errors[0].Code.Should().Be(ErrorCodes.Required);
        exception.Errors[0].PropertyPath.Should().Be("upload.Path");
        exception.Errors[0].PropertyLabel.Should().Be("Upload Path");
    }

    [Fact]
    public void CreateWithProcessedData_WithNullUploadPath_ShouldThrowValidationException()
    {
        // Arrange
        PolicyMemberUpload upload = CreateUploadWithNullPath();
        PolicyDto policy = CreateValidPolicy();
        ResolvedValidationData resolvedData = CreateValidResolvedData();
        FileProcessingResult fileResult = CreateValidFileResult();
        PolicyMemberFieldsSchema schema = CreateValidSchema();
        MembersUploadFields transformedData = CreateValidTransformedData();

        // Act & Assert
        ValidationException exception = Assert.Throws<ValidationException>(() =>
            CompleteValidationContext.CreateWithProcessedData(
                upload, policy, resolvedData, fileResult, schema, transformedData));

        // Assert exception details
        exception.Errors.Should().HaveCount(1);
        exception.Errors[0].Code.Should().Be(ErrorCodes.Required);
        exception.Errors[0].PropertyPath.Should().Be("upload.Path");
        exception.Errors[0].PropertyLabel.Should().Be("Upload Path");
    }

    [Fact]
    public void CreateWithProcessedData_WithEmptyContractHolderId_ShouldThrowValidationException()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto policy = CreatePolicyWithEmptyContractHolderId();
        ResolvedValidationData resolvedData = CreateValidResolvedData();
        FileProcessingResult fileResult = CreateValidFileResult();
        PolicyMemberFieldsSchema schema = CreateValidSchema();
        MembersUploadFields transformedData = CreateValidTransformedData();

        // Act & Assert
        ValidationException exception = Assert.Throws<ValidationException>(() =>
            CompleteValidationContext.CreateWithProcessedData(
                upload, policy, resolvedData, fileResult, schema, transformedData));

        // Assert exception details
        exception.Errors.Should().HaveCount(1);
        exception.Errors[0].Code.Should().Be(ErrorCodes.Required);
        exception.Errors[0].PropertyPath.Should().Be("policy.ContractHolderId");
        exception.Errors[0].PropertyLabel.Should().Be("Contract Holder ID");
    }

    [Fact]
    public void CreateWithProcessedData_WithNullContractHolderId_ShouldThrowValidationException()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto policy = CreatePolicyWithNullContractHolderId();
        ResolvedValidationData resolvedData = CreateValidResolvedData();
        FileProcessingResult fileResult = CreateValidFileResult();
        PolicyMemberFieldsSchema schema = CreateValidSchema();
        MembersUploadFields transformedData = CreateValidTransformedData();

        // Act & Assert
        ValidationException exception = Assert.Throws<ValidationException>(() =>
            CompleteValidationContext.CreateWithProcessedData(
                upload, policy, resolvedData, fileResult, schema, transformedData));

        // Assert exception details
        exception.Errors.Should().HaveCount(1);
        exception.Errors[0].Code.Should().Be(ErrorCodes.Required);
        exception.Errors[0].PropertyPath.Should().Be("policy.ContractHolderId");
        exception.Errors[0].PropertyLabel.Should().Be("Contract Holder ID");
    }

    #endregion

    #region GetProcessedDataOrThrow Tests

    [Fact]
    public void GetProcessedDataOrThrow_WithPreProcessedData_ShouldReturnData()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();

        // Act
        (FileProcessingResult fileResult, PolicyMemberFieldsSchema schema, MembersUploadFields transformedData) = context.GetProcessedDataOrThrow();

        // Assert
        fileResult.Should().NotBeNull();
        schema.Should().NotBeNull();
        transformedData.Should().NotBeNull();
    }

    [Fact]
    public void GetProcessedDataOrThrow_WithoutPreProcessedData_ShouldThrowValidationException()
    {
        // Arrange
        CompleteValidationContext context = CreateContextWithoutPreProcessedData();

        // Act & Assert
        ValidationException exception = Assert.Throws<ValidationException>(() => context.GetProcessedDataOrThrow());

        // Assert exception details
        exception.Errors.Should().HaveCount(1);
        exception.Errors[0].Code.Should().Be(ErrorCodes.InvalidState);
        exception.Errors[0].PropertyPath.Should().Be("context.PreProcessedData");
        exception.Errors[0].PropertyLabel.Should().Be("Pre-processed Data");
    }

    #endregion

    #region ValidateForProcessing Tests

    [Fact]
    public void ValidateForProcessing_WithPreProcessedData_ShouldNotThrow()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();

        // Act & Assert
        Exception exception = Record.Exception(() => context.ValidateForProcessing());
        exception.Should().BeNull();
    }

    [Fact]
    public void ValidateForProcessing_WithoutPreProcessedData_ShouldThrowValidationException()
    {
        // Arrange
        CompleteValidationContext context = CreateContextWithoutPreProcessedData();

        // Act & Assert
        ValidationException exception = Assert.Throws<ValidationException>(() => context.ValidateForProcessing());

        // Assert exception details
        exception.Errors.Should().HaveCount(1);
        exception.Errors[0].Code.Should().Be(ErrorCodes.InvalidState);
        exception.Errors[0].PropertyPath.Should().Be("context.PreProcessedData");
        exception.Errors[0].PropertyLabel.Should().Be("Pre-processed Data");
    }

    #endregion

    #region Helper Methods

    private static PolicyMemberUpload CreateValidUpload() =>
        PolicyMemberUpload.Create(PolicyId.New, "test-upload.xlsx", 10, null);

    private static PolicyMemberUpload CreateUploadWithEmptyPath() =>
        PolicyMemberUpload.Create(PolicyId.New, "", 10, null);

    private static PolicyMemberUpload CreateUploadWithNullPath() =>
        PolicyMemberUpload.Create(PolicyId.New, null!, 10, null);

    private static PolicyDto CreateValidPolicy() => new()
    {
        Id = Guid.NewGuid().ToString(),
        ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
        ContractHolderId = Guid.NewGuid().ToString(),
        StartDate = TestDateConstants.PolicyDates.PolicyStart,
        EndDate = TestDateConstants.PolicyDates.PolicyEnd,
        IsIssued = false,
        Endorsements = [],
        ApprovedEndorsementIds = []
    };

    private static PolicyDto CreatePolicyWithEmptyContractHolderId() => new()
    {
        Id = Guid.NewGuid().ToString(),
        ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
        ContractHolderId = "",
        StartDate = DateOnly.FromDateTime(DateTime.Today),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
        IsIssued = false,
        Endorsements = [],
        ApprovedEndorsementIds = []
    };

    private static PolicyDto CreatePolicyWithNullContractHolderId() => new()
    {
        Id = Guid.NewGuid().ToString(),
        ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
        ContractHolderId = null!,
        StartDate = DateOnly.FromDateTime(DateTime.Today),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
        IsIssued = false,
        Endorsements = [],
        ApprovedEndorsementIds = []
    };

    private static ResolvedValidationData CreateValidResolvedData() => new();

    private static FileProcessingResult CreateValidFileResult() =>
        FileProcessingResult.Success([], 0);

    private static PolicyMemberFieldsSchema CreateValidSchema() =>
        new([]);

    private static MembersUploadFields CreateValidTransformedData() =>
        MembersUploadFields.Empty();

    private static CompleteValidationContext CreateValidCompleteValidationContext() =>
        CompleteValidationContext.CreateWithProcessedData(
            CreateValidUpload(),
            CreateValidPolicy(),
            CreateValidResolvedData(),
            CreateValidFileResult(),
            CreateValidSchema(),
            CreateValidTransformedData());

    private static CompleteValidationContext CreateContextWithoutPreProcessedData()
    {
        // Create context using private constructor via reflection to bypass validation
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        ResolvedValidationData resolvedData = CreateValidResolvedData();

        // Use reflection to create context without pre-processed data
        ConstructorInfo constructor = typeof(CompleteValidationContext)
            .GetConstructors(BindingFlags.NonPublic | BindingFlags.Instance)[0];

        return (CompleteValidationContext)constructor.Invoke([upload, policy, resolvedData, null, null, null]);
    }

    #endregion
}
