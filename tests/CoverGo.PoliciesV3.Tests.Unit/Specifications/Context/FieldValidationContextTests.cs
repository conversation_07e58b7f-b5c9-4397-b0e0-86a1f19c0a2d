using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.Context;

/// <summary>
/// Unit tests for FieldValidationContext class.
/// Tests field validation context creation, validation, and helper methods.
/// </summary>
public class FieldValidationContextTests
{
    #region Factory Method Tests

    [Fact]
    public void Create_WithValidParameters_ShouldCreateContext()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act
        var context = FieldValidationContext.Create(memberFields, policy, schema);

        // Assert
        Assert.NotNull(context);
        Assert.Equal(memberFields, context.MemberFields);
        Assert.Equal(policy, context.Policy);
        Assert.Equal(schema, context.Schema);
        Assert.Null(context.MemberId);
        Assert.Null(context.EndorsementId);
    }

    [Fact]
    public void Create_WithMemberId_ShouldSetMemberId()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        string memberId = "member-123";

        // Act
        var context = FieldValidationContext.Create(memberFields, policy, schema, memberId: memberId);

        // Assert
        Assert.Equal(memberId, context.MemberId);
    }

    [Fact]
    public void Create_WithEndorsementId_ShouldSetEndorsementId()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        var endorsementId = Guid.NewGuid();

        // Act
        var context = FieldValidationContext.Create(memberFields, policy, schema, endorsementId: endorsementId);

        // Assert
        Assert.Equal(endorsementId, context.EndorsementId);
    }

    [Fact]
    public void Create_WithAllParameters_ShouldSetAllProperties()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        string memberId = "member-123";
        var endorsementId = Guid.NewGuid();

        // Act
        var context = FieldValidationContext.Create(memberFields, policy, schema, memberId: memberId, endorsementId: endorsementId);

        // Assert
        Assert.Equal(memberFields, context.MemberFields);
        Assert.Equal(policy, context.Policy);
        Assert.Equal(schema, context.Schema);
        Assert.Equal(memberId, context.MemberId);
        Assert.Equal(endorsementId, context.EndorsementId);
    }

    [Fact]
    public void Create_WithNullMemberFields_ShouldThrowArgumentNullException()
    {
        // Arrange
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => FieldValidationContext.Create(null!, policy, schema));
    }

    [Fact]
    public void Create_WithNullPolicy_ShouldThrowArgumentNullException()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => FieldValidationContext.Create(memberFields, null!, schema));
    }

    [Fact]
    public void Create_WithNullSchema_ShouldThrowArgumentNullException()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => FieldValidationContext.Create(memberFields, policy, null!));
    }

    #endregion

    #region Helper Method Tests

    [Fact]
    public void IsNewMember_WithNullMemberId_ShouldReturnTrue()
    {
        // Arrange
        FieldValidationContext context = CreateFieldValidationContext(memberId: null);

        // Act
        bool result = context.IsNewMember();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsNewMember_WithEmptyMemberId_ShouldReturnTrue()
    {
        // Arrange
        FieldValidationContext context = CreateFieldValidationContext(memberId: "");

        // Act
        bool result = context.IsNewMember();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsNewMember_WithWhitespaceMemberId_ShouldReturnTrue()
    {
        // Arrange
        FieldValidationContext context = CreateFieldValidationContext(memberId: "   ");

        // Act
        bool result = context.IsNewMember();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsNewMember_WithValidMemberId_ShouldReturnFalse()
    {
        // Arrange
        FieldValidationContext context = CreateFieldValidationContext(memberId: "member-123");

        // Act
        bool result = context.IsNewMember();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsExistingMemberUpdate_WithNullMemberId_ShouldReturnFalse()
    {
        // Arrange
        FieldValidationContext context = CreateFieldValidationContext(memberId: null);

        // Act
        bool result = context.IsExistingMemberUpdate();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsExistingMemberUpdate_WithValidMemberId_ShouldReturnTrue()
    {
        // Arrange
        FieldValidationContext context = CreateFieldValidationContext(memberId: "member-123");

        // Act
        bool result = context.IsExistingMemberUpdate();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void GetEffectiveMemberId_WithExplicitMemberId_ShouldReturnExplicitMemberId()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["memberId"] = "field-member-456" });
        FieldValidationContext context = CreateFieldValidationContext(memberFields, memberId: "explicit-member-123");

        // Act
        string? result = context.GetEffectiveMemberId();

        // Assert
        Assert.Equal("explicit-member-123", result);
    }

    [Fact]
    public void GetEffectiveMemberId_WithoutExplicitMemberId_ShouldReturnFieldMemberId()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["memberId"] = "field-member-456" });
        FieldValidationContext context = CreateFieldValidationContext(memberFields, memberId: null);

        // Act
        string? result = context.GetEffectiveMemberId();

        // Assert
        Assert.Equal("field-member-456", result);
    }

    [Fact]
    public void GetEffectiveMemberId_WithoutAnyMemberId_ShouldReturnNull()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["name"] = "John Doe" });
        FieldValidationContext context = CreateFieldValidationContext(memberFields, memberId: null);

        // Act
        string? result = context.GetEffectiveMemberId();

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region Inherited Helper Method Tests

    [Fact]
    public void GetMemberId_ShouldReturnMemberIdFromFields()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["memberId"] = "member-789" });
        FieldValidationContext context = CreateFieldValidationContext(memberFields);

        // Act
        string? result = context.GetMemberId();

        // Assert
        Assert.Equal("member-789", result);
    }

    [Fact]
    public void GetPlanId_ShouldReturnPlanIdFromFields()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["planId"] = "plan-789" });
        FieldValidationContext context = CreateFieldValidationContext(memberFields);

        // Act
        string? result = context.GetPlanId();

        // Assert
        Assert.Equal("plan-789", result);
    }

    [Fact]
    public void GetEmail_ShouldReturnEmailFromFields()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["email"] = "<EMAIL>" });
        FieldValidationContext context = CreateFieldValidationContext(memberFields);

        // Act
        string? result = context.GetEmail();

        // Assert
        Assert.Equal("<EMAIL>", result);
    }

    #endregion

    #region Helper Methods

    private static FieldValidationContext CreateFieldValidationContext(
        MemberUploadFields? memberFields = null,
        PolicyDto? policy = null,
        PolicyMemberFieldsSchema? schema = null,
        string? memberId = null,
        Guid? endorsementId = null) => FieldValidationContext.Create(
            memberFields: memberFields ?? CreateMemberUploadFields(),
            policy: policy ?? CreateMockPolicy(),
            schema: schema ?? CreateMockSchema(),
            memberId: memberId,
            endorsementId: endorsementId);

    private static MemberUploadFields CreateMemberUploadFields(Dictionary<string, string?>? fields = null)
    {
        var defaultFields = new Dictionary<string, string?> { ["name"] = "John Doe" };
        return new MemberUploadFields(fields ?? defaultFields);
    }

    private static PolicyDto CreateMockPolicy() => new()
    {
        Id = Guid.NewGuid().ToString(),
        StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
        Endorsements =
            [
                new() { Id = Guid.NewGuid().ToString(), Status = "APPROVED" }
            ]
    };

    private static PolicyMemberFieldsSchema CreateMockSchema()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "name",
                Label = "Full Name",
                IsUnique = false,
                IsRequired = true,
                IsRequiredForDependent = false,
                Type = new StringFieldType()
            }
        };

        return new PolicyMemberFieldsSchema(memberFields);
    }

    #endregion
}
