using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Products;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.Context;

/// <summary>
/// Unit tests for PlanValidationContext class.
/// Tests plan validation context creation, validation, and specialized helper methods.
/// </summary>
public class PlanValidationContextTests
{
    #region Factory Method Tests

    [Fact]
    public void Create_WithValidParameters_ShouldCreateContext()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act
        var context = PlanValidationContext.Create(memberFields, policy, schema);

        // Assert
        Assert.NotNull(context);
        Assert.Equal(memberFields, context.MemberFields);
        Assert.Equal(policy, context.Policy);
        Assert.Equal(schema, context.Schema);
        Assert.Null(context.AvailablePlans);
        Assert.Null(context.EndorsementId);
    }

    [Fact]
    public void Create_WithAvailablePlans_ShouldSetAvailablePlans()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        var availablePlans = new HashSet<string> { "plan-1", "plan-2", "plan-3" };

        // Act
        var context = PlanValidationContext.Create(memberFields, policy, schema, availablePlans);

        // Assert
        Assert.Equal(availablePlans, context.AvailablePlans);
    }

    [Fact]
    public void Create_WithEndorsementId_ShouldSetEndorsementId()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        var endorsementId = Guid.NewGuid();

        // Act
        var context = PlanValidationContext.Create(memberFields, policy, schema, endorsementId: endorsementId);

        // Assert
        Assert.Equal(endorsementId, context.EndorsementId);
    }

    [Fact]
    public void Create_WithAllParameters_ShouldSetAllProperties()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();
        var availablePlans = new HashSet<string> { "plan-1", "plan-2" };
        var endorsementId = Guid.NewGuid();

        // Act
        var context = PlanValidationContext.Create(memberFields, policy, schema, availablePlans, endorsementId);

        // Assert
        Assert.Equal(memberFields, context.MemberFields);
        Assert.Equal(policy, context.Policy);
        Assert.Equal(schema, context.Schema);
        Assert.Equal(availablePlans, context.AvailablePlans);
        Assert.Equal(endorsementId, context.EndorsementId);
    }

    [Fact]
    public void Create_WithNullMemberFields_ShouldThrowArgumentNullException()
    {
        // Arrange
        PolicyDto policy = CreateMockPolicy();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => PlanValidationContext.Create(null!, policy, schema));
    }

    [Fact]
    public void Create_WithNullPolicy_ShouldThrowArgumentNullException()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyMemberFieldsSchema schema = CreateMockSchema();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => PlanValidationContext.Create(memberFields, null!, schema));
    }

    [Fact]
    public void Create_WithNullSchema_ShouldThrowArgumentNullException()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields();
        PolicyDto policy = CreateMockPolicy();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => PlanValidationContext.Create(memberFields, policy, null!));
    }

    #endregion

    #region Specialized Helper Method Tests

    [Fact]
    public void HasValidProductId_WithValidProductId_ShouldReturnTrue()
    {
        // Arrange
        PolicyDto policy = CreateMockPolicyWithProductId();
        PlanValidationContext context = CreatePlanValidationContext(policy: policy);

        // Act
        bool result = context.HasValidProductId();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void HasValidProductId_WithNullProductId_ShouldReturnFalse()
    {
        // Arrange
        PolicyDto policy = CreateMockPolicyWithoutProductId();
        PlanValidationContext context = CreatePlanValidationContext(policy: policy);

        // Act
        bool result = context.HasValidProductId();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void CanValidatePlans_WithValidProductIdAndAvailablePlans_ShouldReturnTrue()
    {
        // Arrange
        PolicyDto policy = CreateMockPolicyWithProductId();
        var availablePlans = new HashSet<string> { "plan-1", "plan-2" };
        PlanValidationContext context = CreatePlanValidationContext(policy: policy, availablePlans: availablePlans);

        // Act
        bool result = context.CanValidatePlans();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void CanValidatePlans_WithNullProductId_ShouldReturnFalse()
    {
        // Arrange
        PolicyDto policy = CreateMockPolicyWithoutProductId();
        var availablePlans = new HashSet<string> { "plan-1", "plan-2" };
        PlanValidationContext context = CreatePlanValidationContext(policy: policy, availablePlans: availablePlans);

        // Act
        bool result = context.CanValidatePlans();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void CanValidatePlans_WithNullAvailablePlans_ShouldReturnFalse()
    {
        // Arrange
        PolicyDto policy = CreateMockPolicyWithProductId();
        PlanValidationContext context = CreatePlanValidationContext(policy: policy, availablePlans: null);

        // Act
        bool result = context.CanValidatePlans();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsPlanAvailable_WithAvailablePlan_ShouldReturnTrue()
    {
        // Arrange
        var availablePlans = new HashSet<string> { "plan-1", "plan-2", "plan-3" };
        PlanValidationContext context = CreatePlanValidationContext(availablePlans: availablePlans);

        // Act
        bool result = context.IsPlanAvailable("plan-2");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsPlanAvailable_WithUnavailablePlan_ShouldReturnFalse()
    {
        // Arrange
        var availablePlans = new HashSet<string> { "plan-1", "plan-2", "plan-3" };
        PlanValidationContext context = CreatePlanValidationContext(availablePlans: availablePlans);

        // Act
        bool result = context.IsPlanAvailable("plan-4");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsPlanAvailable_WithNullAvailablePlans_ShouldReturnFalse()
    {
        // Arrange
        PlanValidationContext context = CreatePlanValidationContext(availablePlans: null);

        // Act
        bool result = context.IsPlanAvailable("plan-1");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsPlanAvailable_WithNullPlanId_ShouldReturnFalse()
    {
        // Arrange
        var availablePlans = new HashSet<string> { "plan-1", "plan-2" };
        PlanValidationContext context = CreatePlanValidationContext(availablePlans: availablePlans);

        // Act
        bool result = context.IsPlanAvailable(null!);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsPlanAvailable_WithEmptyPlanId_ShouldReturnFalse()
    {
        // Arrange
        var availablePlans = new HashSet<string> { "plan-1", "plan-2" };
        PlanValidationContext context = CreatePlanValidationContext(availablePlans: availablePlans);

        // Act
        bool result = context.IsPlanAvailable("");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsPlanAvailable_WithWhitespacePlanId_ShouldReturnFalse()
    {
        // Arrange
        var availablePlans = new HashSet<string> { "plan-1", "plan-2" };
        PlanValidationContext context = CreatePlanValidationContext(availablePlans: availablePlans);

        // Act
        bool result = context.IsPlanAvailable("   ");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void GetAvailablePlansList_WithAvailablePlans_ShouldReturnPlansList()
    {
        // Arrange
        var availablePlans = new HashSet<string> { "plan-1", "plan-2", "plan-3" };
        PlanValidationContext context = CreatePlanValidationContext(availablePlans: availablePlans);

        // Act
        List<string> result = context.GetAvailablePlansList();

        // Assert
        Assert.Equal(3, result.Count);
        Assert.Contains("plan-1", result);
        Assert.Contains("plan-2", result);
        Assert.Contains("plan-3", result);
    }

    [Fact]
    public void GetAvailablePlansList_WithNullAvailablePlans_ShouldReturnEmptyList()
    {
        // Arrange
        PlanValidationContext context = CreatePlanValidationContext(availablePlans: null);

        // Act
        List<string> result = context.GetAvailablePlansList();

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public void IsMemberPlanValid_WithValidPlan_ShouldReturnTrue()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["planId"] = "plan-2" });
        var availablePlans = new HashSet<string> { "plan-1", "plan-2", "plan-3" };
        PlanValidationContext context = CreatePlanValidationContext(memberFields: memberFields, availablePlans: availablePlans);

        // Act
        bool result = context.IsMemberPlanValid();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsMemberPlanValid_WithInvalidPlan_ShouldReturnFalse()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["planId"] = "plan-4" });
        var availablePlans = new HashSet<string> { "plan-1", "plan-2", "plan-3" };
        PlanValidationContext context = CreatePlanValidationContext(memberFields: memberFields, availablePlans: availablePlans);

        // Act
        bool result = context.IsMemberPlanValid();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsMemberPlanValid_WithNullPlanId_ShouldReturnFalse()
    {
        // Arrange
        MemberUploadFields memberFields = CreateMemberUploadFields(new Dictionary<string, string?> { ["name"] = "John Doe" });
        var availablePlans = new HashSet<string> { "plan-1", "plan-2" };
        PlanValidationContext context = CreatePlanValidationContext(memberFields: memberFields, availablePlans: availablePlans);

        // Act
        bool result = context.IsMemberPlanValid();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void GetAvailablePlansCount_WithAvailablePlans_ShouldReturnCount()
    {
        // Arrange
        var availablePlans = new HashSet<string> { "plan-1", "plan-2", "plan-3" };
        PlanValidationContext context = CreatePlanValidationContext(availablePlans: availablePlans);

        // Act
        int result = context.GetAvailablePlansCount();

        // Assert
        Assert.Equal(3, result);
    }

    [Fact]
    public void GetAvailablePlansCount_WithNullAvailablePlans_ShouldReturnZero()
    {
        // Arrange
        PlanValidationContext context = CreatePlanValidationContext(availablePlans: null);

        // Act
        int result = context.GetAvailablePlansCount();

        // Assert
        Assert.Equal(0, result);
    }

    [Fact]
    public void HasAvailablePlans_WithAvailablePlans_ShouldReturnTrue()
    {
        // Arrange
        var availablePlans = new HashSet<string> { "plan-1" };
        PlanValidationContext context = CreatePlanValidationContext(availablePlans: availablePlans);

        // Act
        bool result = context.HasAvailablePlans();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void HasAvailablePlans_WithEmptyAvailablePlans_ShouldReturnFalse()
    {
        // Arrange
        var availablePlans = new HashSet<string>();
        PlanValidationContext context = CreatePlanValidationContext(availablePlans: availablePlans);

        // Act
        bool result = context.HasAvailablePlans();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void HasAvailablePlans_WithNullAvailablePlans_ShouldReturnFalse()
    {
        // Arrange
        PlanValidationContext context = CreatePlanValidationContext(availablePlans: null);

        // Act
        bool result = context.HasAvailablePlans();

        // Assert
        Assert.False(result);
    }

    #endregion

    #region Helper Methods

    private static PlanValidationContext CreatePlanValidationContext(
        MemberUploadFields? memberFields = null,
        PolicyDto? policy = null,
        PolicyMemberFieldsSchema? schema = null,
        IReadOnlySet<string>? availablePlans = null) => PlanValidationContext.Create(
            memberFields ?? CreateMemberUploadFields(),
            policy ?? CreateMockPolicy(),
            schema ?? CreateMockSchema(),
            availablePlans);

    private static MemberUploadFields CreateMemberUploadFields(Dictionary<string, string?>? fields = null)
    {
        var defaultFields = new Dictionary<string, string?> { ["name"] = "John Doe" };
        return new MemberUploadFields(fields ?? defaultFields);
    }

    private static PolicyDto CreateMockPolicy() => new()
    {
        Id = Guid.NewGuid().ToString(),
        StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
        Endorsements =
            [
                new() { Id = Guid.NewGuid().ToString(), Status = "APPROVED" }
            ]
    };

    private static PolicyDto CreateMockPolicyWithProductId()
    {
        PolicyDto policy = CreateMockPolicy();
        return policy with
        {
            ProductId = new ProductIdDto
            {
                Plan = "basic-plan",
                Type = "health",
                Version = "v1"
            }
        };
    }

    private static PolicyDto CreateMockPolicyWithoutProductId()
    {
        PolicyDto policy = CreateMockPolicy();
        return policy with { ProductId = null };
    }

    private static PolicyMemberFieldsSchema CreateMockSchema()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "name",
                Label = "Full Name",
                IsUnique = false,
                IsRequired = true,
                IsRequiredForDependent = false,
                Type = new StringFieldType()
            }
        };

        return new PolicyMemberFieldsSchema(memberFields);
    }

    #endregion
}
