using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.Common;

public class SpecificationResultTests
{
    #region Factory Method Tests

    [Fact]
    public void Satisfied_WithBusinessRuleName_ShouldCreateSatisfiedResult()
    {
        // Arrange
        const string businessRuleName = "Test Business Rule";
        const string description = "Test description";

        // Act
        var result = SpecificationResult.Satisfied(businessRuleName, description);

        // Assert
        result.IsSatisfied.Should().BeTrue();
        result.IsNotSatisfied.Should().BeFalse();
        result.BusinessRuleName.Should().Be(businessRuleName);
        result.Description.Should().Be(description);
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void Satisfied_WithoutParameters_ShouldCreateSatisfiedResult()
    {
        // Act
        var result = SpecificationResult.Satisfied();

        // Assert
        result.IsSatisfied.Should().BeTrue();
        result.IsNotSatisfied.Should().BeFalse();
        result.BusinessRuleName.Should().BeNull();
        result.Description.Should().BeNull();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void NotSatisfied_WithSingleError_ShouldCreateNotSatisfiedResult()
    {
        // Arrange
        var error = new ValidationError("TEST_ERROR", "testField", "Test Field");
        const string businessRuleName = "Test Business Rule";

        // Act
        var result = SpecificationResult.NotSatisfied(error, businessRuleName);

        // Assert
        result.IsSatisfied.Should().BeFalse();
        result.IsNotSatisfied.Should().BeTrue();
        result.BusinessRuleName.Should().Be(businessRuleName);
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Should().Be(error);
    }

    [Fact]
    public void NotSatisfied_WithMultipleErrors_ShouldCreateNotSatisfiedResult()
    {
        // Arrange
        ValidationError[] errors =
        [
            new ValidationError("ERROR1", "field1", "Field 1"),
            new ValidationError("ERROR2", "field2", "Field 2")
        ];
        const string businessRuleName = "Test Business Rule";

        // Act
        var result = SpecificationResult.NotSatisfied(errors, businessRuleName);

        // Assert
        result.IsSatisfied.Should().BeFalse();
        result.IsNotSatisfied.Should().BeTrue();
        result.BusinessRuleName.Should().Be(businessRuleName);
        result.Errors.Should().HaveCount(2);
        result.Errors.Should().BeEquivalentTo(errors);
    }

    [Fact]
    public void FromResult_WithSuccessfulResult_ShouldCreateSatisfiedSpecificationResult()
    {
        // Arrange
        var result = Result.Success();
        const string businessRuleName = "Test Rule";

        // Act
        var specResult = SpecificationResult.FromResult(result, businessRuleName);

        // Assert
        specResult.IsSatisfied.Should().BeTrue();
        specResult.BusinessRuleName.Should().Be(businessRuleName);
    }

    [Fact]
    public void FromResult_WithFailedResult_ShouldCreateNotSatisfiedSpecificationResult()
    {
        // Arrange
        var error = new ValidationError("TEST_ERROR", "testField", "Test Field");
        var result = Result.Failure(error);
        const string businessRuleName = "Test Rule";

        // Act
        var specResult = SpecificationResult.FromResult(result, businessRuleName);

        // Assert
        specResult.IsNotSatisfied.Should().BeTrue();
        specResult.BusinessRuleName.Should().Be(businessRuleName);
        specResult.Errors.Should().HaveCount(1);
    }

    #endregion

    #region Utility Method Tests

    [Fact]
    public void OnSatisfied_WhenSatisfied_ShouldExecuteAction()
    {
        // Arrange
        var result = SpecificationResult.Satisfied();
        bool actionExecuted = false;

        // Act
        SpecificationResult returnedResult = result.OnSatisfied(() => actionExecuted = true);

        // Assert
        actionExecuted.Should().BeTrue();
        returnedResult.Should().Be(result); // Should return same instance for chaining
    }

    [Fact]
    public void OnSatisfied_WhenNotSatisfied_ShouldNotExecuteAction()
    {
        // Arrange
        var error = new ValidationError("TEST_ERROR", "testField", "Test Field");
        var result = SpecificationResult.NotSatisfied(error);
        bool actionExecuted = false;

        // Act
        SpecificationResult returnedResult = result.OnSatisfied(() => actionExecuted = true);

        // Assert
        actionExecuted.Should().BeFalse();
        returnedResult.Should().Be(result);
    }

    [Fact]
    public void OnNotSatisfied_WhenNotSatisfied_ShouldExecuteAction()
    {
        // Arrange
        var error = new ValidationError("TEST_ERROR", "testField", "Test Field");
        var result = SpecificationResult.NotSatisfied(error);
        IReadOnlyList<ValidationError>? capturedErrors = null;

        // Act
        SpecificationResult returnedResult = result.OnNotSatisfied(errors => capturedErrors = errors);

        // Assert
        capturedErrors.Should().NotBeNull();
        capturedErrors.Should().HaveCount(1);
        capturedErrors![0].Should().Be(error);
        returnedResult.Should().Be(result);
    }

    [Fact]
    public void OnNotSatisfied_WhenSatisfied_ShouldNotExecuteAction()
    {
        // Arrange
        var result = SpecificationResult.Satisfied();
        bool actionExecuted = false;

        // Act
        SpecificationResult returnedResult = result.OnNotSatisfied(_ => actionExecuted = true);

        // Assert
        actionExecuted.Should().BeFalse();
        returnedResult.Should().Be(result);
    }

    #endregion

    #region Logical Combination Tests

    [Fact]
    public void And_WhenBothSatisfied_ShouldReturnSatisfied()
    {
        // Arrange
        var result1 = SpecificationResult.Satisfied("Rule 1");
        var result2 = SpecificationResult.Satisfied("Rule 2");

        // Act
        SpecificationResult combined = result1.And(result2);

        // Assert
        combined.IsSatisfied.Should().BeTrue();
        combined.BusinessRuleName.Should().Be("Rule 1 AND Rule 2");
    }

    [Fact]
    public void And_WhenFirstNotSatisfied_ShouldReturnNotSatisfied()
    {
        // Arrange
        var error = new ValidationError("ERROR1", "field1", "Field 1");
        var result1 = SpecificationResult.NotSatisfied(error, "Rule 1");
        var result2 = SpecificationResult.Satisfied("Rule 2");

        // Act
        SpecificationResult combined = result1.And(result2);

        // Assert
        combined.IsNotSatisfied.Should().BeTrue();
        combined.Errors.Should().HaveCount(1);
        combined.Errors[0].Should().Be(error);
    }

    [Fact]
    public void And_WhenBothNotSatisfied_ShouldCombineErrors()
    {
        // Arrange
        var error1 = new ValidationError("ERROR1", "field1", "Field 1");
        var error2 = new ValidationError("ERROR2", "field2", "Field 2");
        var result1 = SpecificationResult.NotSatisfied(error1, "Rule 1");
        var result2 = SpecificationResult.NotSatisfied(error2, "Rule 2");

        // Act
        SpecificationResult combined = result1.And(result2);

        // Assert
        combined.IsNotSatisfied.Should().BeTrue();
        combined.Errors.Should().HaveCount(2);
        combined.Errors.Should().Contain(error1);
        combined.Errors.Should().Contain(error2);
    }

    [Fact]
    public void Or_WhenBothSatisfied_ShouldReturnSatisfied()
    {
        // Arrange
        var result1 = SpecificationResult.Satisfied("Rule 1");
        var result2 = SpecificationResult.Satisfied("Rule 2");

        // Act
        SpecificationResult combined = result1.Or(result2);

        // Assert
        combined.IsSatisfied.Should().BeTrue();
        combined.BusinessRuleName.Should().Be("Rule 1 OR Rule 2");
    }

    [Fact]
    public void Or_WhenFirstSatisfied_ShouldReturnSatisfied()
    {
        // Arrange
        var error = new ValidationError("ERROR2", "field2", "Field 2");
        var result1 = SpecificationResult.Satisfied("Rule 1");
        var result2 = SpecificationResult.NotSatisfied(error, "Rule 2");

        // Act
        SpecificationResult combined = result1.Or(result2);

        // Assert
        combined.IsSatisfied.Should().BeTrue();
    }

    [Fact]
    public void Or_WhenBothNotSatisfied_ShouldCombineErrors()
    {
        // Arrange
        var error1 = new ValidationError("ERROR1", "field1", "Field 1");
        var error2 = new ValidationError("ERROR2", "field2", "Field 2");
        var result1 = SpecificationResult.NotSatisfied(error1, "Rule 1");
        var result2 = SpecificationResult.NotSatisfied(error2, "Rule 2");

        // Act
        SpecificationResult combined = result1.Or(result2);

        // Assert
        combined.IsNotSatisfied.Should().BeTrue();
        combined.Errors.Should().HaveCount(2);
        combined.Errors.Should().Contain(error1);
        combined.Errors.Should().Contain(error2);
    }

    #endregion

    #region Conversion Tests

    [Fact]
    public void ImplicitConversion_FromResult_ShouldWork()
    {
        // Arrange
        var result = Result.Success();

        // Act
        SpecificationResult specResult = result;

        // Assert
        specResult.IsSatisfied.Should().BeTrue();
    }

    [Fact]
    public void ImplicitConversion_ToResult_ShouldWork()
    {
        // Arrange
        var specResult = SpecificationResult.Satisfied();

        // Act
        Result result = specResult;

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Fact]
    public void AsResult_ShouldReturnUnderlyingResult()
    {
        // Arrange
        var error = new ValidationError("TEST_ERROR", "testField", "Test Field");
        var specResult = SpecificationResult.NotSatisfied(error);

        // Act
        Result result = specResult.AsResult();

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Should().Be(error);
    }

    #endregion

    #region ToString and Equality Tests

    [Fact]
    public void ToString_WithSatisfiedResult_ShouldShowCorrectFormat()
    {
        // Arrange
        var result = SpecificationResult.Satisfied("Test Rule", "Test description");

        // Act
        string stringResult = result.ToString();

        // Assert
        stringResult.Should().Contain("Satisfied");
        stringResult.Should().Contain("Test Rule");
    }

    [Fact]
    public void ToString_WithNotSatisfiedResult_ShouldShowErrorCount()
    {
        // Arrange
        ValidationError[] errors =
        [
            new ValidationError("ERROR1", "field1", "Field 1"),
            new ValidationError("ERROR2", "field2", "Field 2")
        ];
        var result = SpecificationResult.NotSatisfied(errors, "Test Rule");

        // Act
        string stringResult = result.ToString();

        // Assert
        stringResult.Should().Contain("Not Satisfied");
        stringResult.Should().Contain("Test Rule");
        stringResult.Should().Contain("2 error");
    }

    [Fact]
    public void Equals_WithSameResults_ShouldBeEqual()
    {
        // Arrange
        var result1 = SpecificationResult.Satisfied("Test Rule");
        var result2 = SpecificationResult.Satisfied("Test Rule");

        // Act & Assert
        result1.Should().Be(result2);
        (result1 == result2).Should().BeTrue();
        (result1 != result2).Should().BeFalse();
    }

    [Fact]
    public void Equals_WithDifferentBusinessRuleNames_ShouldNotBeEqual()
    {
        // Arrange
        var result1 = SpecificationResult.Satisfied("Rule 1");
        var result2 = SpecificationResult.Satisfied("Rule 2");

        // Act & Assert
        result1.Should().NotBe(result2);
        (result1 == result2).Should().BeFalse();
        (result1 != result2).Should().BeTrue();
    }

    #endregion
}