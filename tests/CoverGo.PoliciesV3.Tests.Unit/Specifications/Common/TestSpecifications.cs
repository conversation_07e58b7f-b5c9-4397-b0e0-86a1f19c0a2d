using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.Common;

/// <summary>
/// Sample specifications for testing the specification framework.
/// These are used to verify that the core specification infrastructure works correctly.
/// </summary>
/// <summary>
/// Test entity for specification testing.
/// </summary>
public record TestEntity(string Name, int Value, bool IsActive);

/// <summary>
/// Sample specification that validates entity name is not empty.
/// </summary>
public class NameMustNotBeEmptyAsyncSpecification : CompositeSpecification<TestEntity>
{
    public override string BusinessRuleName => "Entity Name Must Not Be Empty";

    public override string Description => "The entity name must be provided and cannot be empty or whitespace";

    public override async Task<Result> IsSatisfiedBy(TestEntity entity, CancellationToken cancellationToken = default)
    {
        // Use Task.CompletedTask for in-memory operations to maintain async pattern consistency
        await Task.CompletedTask;

        if (string.IsNullOrWhiteSpace(entity.Name))
        {
            var error = new ValidationError(
                ErrorCodes.Required,
                "name",
                "Name",
                new Dictionary<string, object?> { ["EntityType"] = nameof(TestEntity) }
            );
            return Result.Failure(error);
        }

        return Result.Success();
    }
}

/// <summary>
/// Sample specification that validates entity value is positive.
/// </summary>
public class ValueMustBePositiveSpecification : CompositeSpecification<TestEntity>
{
    public override string BusinessRuleName => "Entity Value Must Be Positive";

    public override string Description => "The entity value must be greater than zero";

    public override async Task<Result> IsSatisfiedBy(TestEntity entity, CancellationToken cancellationToken = default)
    {
        // Use Task.CompletedTask for in-memory operations to maintain async pattern consistency
        await Task.CompletedTask;

        if (entity.Value <= 0)
        {
            var error = new ValidationError(
                "OUT_OF_RANGE",
                "value",
                "Value",
                new Dictionary<string, object?>
                {
                    ["MinValue"] = 1,
                    ["ActualValue"] = entity.Value
                }
            );
            return Result.Failure(error);
        }

        return Result.Success();
    }
}

/// <summary>
/// Sample specification that validates entity is active.
/// </summary>
public class EntityMustBeActiveSpecification : CompositeSpecification<TestEntity>
{
    public override string BusinessRuleName => "Entity Must Be Active";

    public override string Description => "The entity must be in an active state";

    public override async Task<Result> IsSatisfiedBy(TestEntity entity, CancellationToken cancellationToken = default)
    {
        // Use Task.CompletedTask for in-memory operations to maintain async pattern consistency
        await Task.CompletedTask;

        if (!entity.IsActive)
        {
            var error = new ValidationError(
                "INVALID_STATE",
                "isActive",
                "Active Status",
                new Dictionary<string, object?> { ["Message"] = "Entity is not active" }
            );
            return Result.Failure(error);
        }

        return Result.Success();
    }
}

/// <summary>
/// Sample async specification that validates entity name uniqueness (simulated with delay).
/// </summary>
public class NameMustBeUniqueAsyncSpecification : CompositeSpecification<TestEntity>
{
    private readonly HashSet<string> _existingNames;

    public NameMustBeUniqueAsyncSpecification(HashSet<string>? existingNames = null)
    {
        _existingNames = existingNames ?? ["duplicate", "taken", "existing"];
    }

    public override string BusinessRuleName => "Entity Name Must Be Unique";

    public override string Description => "The entity name must be unique across all entities";

    public override async Task<Result> IsSatisfiedBy(TestEntity entity, CancellationToken cancellationToken = default)
    {
        // Check for cancellation first for deterministic cancellation testing
        cancellationToken.ThrowIfCancellationRequested();

        // Simulate async database check without actual delay for fast, deterministic tests
        await Task.CompletedTask;

        if (_existingNames.Contains(entity.Name.ToLowerInvariant()))
        {
            var error = new ValidationError(
                ErrorCodes.UniqueViolation,
                "name",
                "Name",
                new Dictionary<string, object?>
                {
                    ["Scope"] = "system",
                    ["ConflictingValue"] = entity.Name
                }
            );
            return Result.Failure(error);
        }

        return Result.Success();
    }
}