using System.Diagnostics;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using Result = CoverGo.PoliciesV3.Domain.Common.Validation.Result;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.Common;

public class AsyncCompositeSpecificationTests
{
    private readonly NameMustBeUniqueAsyncSpecification _nameUniqueSpec;
    private readonly NameMustBeUniqueAsyncSpecification _nameUniqueSpecWithConflicts;

    public AsyncCompositeSpecificationTests()
    {
        _nameUniqueSpec = new NameMustBeUniqueAsyncSpecification([]);
        _nameUniqueSpecWithConflicts = new NameMustBeUniqueAsyncSpecification(
            ["duplicate", "taken", "existing"]);
    }

    #region Individual Async Specification Tests

    [Fact]
    public async Task NameMustBeUniqueAsyncSpecification_WithUniqueName_ShouldBeSatisfied()
    {
        // Arrange
        var entity = new TestEntity("UniqueName", 10, true);

        // Act
        Result result = await _nameUniqueSpec.IsSatisfiedBy(entity);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Theory]
    [InlineData("duplicate")]
    [InlineData("taken")]
    [InlineData("existing")]
    [InlineData("DUPLICATE")] // Case insensitive
    public async Task NameMustBeUniqueAsyncSpecification_WithDuplicateName_ShouldNotBeSatisfied(string duplicateName)
    {
        // Arrange
        var entity = new TestEntity(duplicateName, 10, true);

        // Act
        Result result = await _nameUniqueSpecWithConflicts.IsSatisfiedBy(entity);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.UniqueViolation);
        result.Errors[0].PropertyPath.Should().Be("name");
    }

    [Fact]
    public async Task AsyncSpecification_ShouldSupportCancellation()
    {
        // Arrange
        var entity = new TestEntity("TestName", 10, true);
        using var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert - OperationCanceledException is the base class for cancellation
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _nameUniqueSpec.IsSatisfiedBy(entity, cts.Token));
    }

    #endregion

    #region Async AND Composition Tests

    [Fact]
    public async Task AsyncAndSpecification_WhenBothSpecificationsSatisfied_ShouldBeSatisfied()
    {
        // Arrange
        var entity = new TestEntity("UniqueName", 10, true);
        var spec1 = new NameMustBeUniqueAsyncSpecification([]);
        var spec2 = new NameMustBeUniqueAsyncSpecification([]);
        ISpecification<TestEntity> andSpec = spec1.And(spec2);

        // Act
        Result result = await andSpec.IsSatisfiedBy(entity);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public async Task AsyncAndSpecification_WhenFirstSpecificationFails_ShouldNotBeSatisfied()
    {
        // Arrange
        var entity = new TestEntity("duplicate", 10, true);
        var spec1 = new NameMustBeUniqueAsyncSpecification(["duplicate"]);
        var spec2 = new NameMustBeUniqueAsyncSpecification([]);
        ISpecification<TestEntity> andSpec = spec1.And(spec2);

        // Act
        Result result = await andSpec.IsSatisfiedBy(entity);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.UniqueViolation);
    }

    [Fact]
    public async Task AsyncAndSpecification_WhenBothSpecificationsFail_ShouldCombineErrors()
    {
        // Arrange
        var entity = new TestEntity("duplicate", 10, true);
        var spec1 = new NameMustBeUniqueAsyncSpecification(["duplicate"]);
        var spec2 = new NameMustBeUniqueAsyncSpecification(["duplicate"]);
        ISpecification<TestEntity> andSpec = spec1.And(spec2);

        // Act
        Result result = await andSpec.IsSatisfiedBy(entity);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(2);
        result.Errors.Should().OnlyContain(e => e.Code == ErrorCodes.UniqueViolation);
    }

    #endregion

    #region Async OR Composition Tests

    [Fact]
    public async Task AsyncOrSpecification_WhenBothSpecificationsSatisfied_ShouldBeSatisfied()
    {
        // Arrange
        var entity = new TestEntity("UniqueName", 10, true);
        var spec1 = new NameMustBeUniqueAsyncSpecification([]);
        var spec2 = new NameMustBeUniqueAsyncSpecification([]);
        ISpecification<TestEntity> orSpec = spec1.Or(spec2);

        // Act
        Result result = await orSpec.IsSatisfiedBy(entity);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Fact]
    public async Task AsyncOrSpecification_WhenFirstSpecificationSatisfied_ShouldBeSatisfied()
    {
        // Arrange
        var entity = new TestEntity("UniqueName", 10, true);
        var spec1 = new NameMustBeUniqueAsyncSpecification([]);
        var spec2 = new NameMustBeUniqueAsyncSpecification(["uniquename"]);
        ISpecification<TestEntity> orSpec = spec1.Or(spec2);

        // Act
        Result result = await orSpec.IsSatisfiedBy(entity);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Fact]
    public async Task AsyncOrSpecification_WhenBothSpecificationsFail_ShouldCombineErrors()
    {
        // Arrange
        var entity = new TestEntity("duplicate", 10, true);
        var spec1 = new NameMustBeUniqueAsyncSpecification(["duplicate"]);
        var spec2 = new NameMustBeUniqueAsyncSpecification(["duplicate"]);
        ISpecification<TestEntity> orSpec = spec1.Or(spec2);

        // Act
        Result result = await orSpec.IsSatisfiedBy(entity);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(2);
        result.Errors.Should().OnlyContain(e => e.Code == ErrorCodes.UniqueViolation);
    }

    #endregion

    #region Async NOT Composition Tests

    [Fact]
    public async Task AsyncNotSpecification_WhenOriginalSpecificationSatisfied_ShouldNotBeSatisfied()
    {
        // Arrange
        var entity = new TestEntity("UniqueName", 10, true);
        ISpecification<TestEntity> notSpec = _nameUniqueSpec.Not();

        // Act
        Result result = await notSpec.IsSatisfiedBy(entity);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().HaveCount(1);
        result.Errors[0].Code.Should().Be(ErrorCodes.NotSatisfied);
    }

    [Fact]
    public async Task AsyncNotSpecification_WhenOriginalSpecificationFails_ShouldBeSatisfied()
    {
        // Arrange
        var entity = new TestEntity("duplicate", 10, true);
        ISpecification<TestEntity> notSpec = _nameUniqueSpecWithConflicts.Not();

        // Act
        Result result = await notSpec.IsSatisfiedBy(entity);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    #endregion

    #region Performance and Concurrency Tests

    [Fact]
    public async Task AsyncAndSpecification_ShouldExecuteBothSpecifications()
    {
        // Arrange
        var entity = new TestEntity("TestName", 10, true);
        var spec1 = new NameMustBeUniqueAsyncSpecification([]);
        var spec2 = new NameMustBeUniqueAsyncSpecification([]);
        ISpecification<TestEntity> andSpec = spec1.And(spec2);

        // Act
        Result result = await andSpec.IsSatisfiedBy(entity);

        // Assert - Focus on correctness, not timing
        result.IsSuccess.Should().BeTrue();

        // Verify that both specifications were evaluated by testing the AND logic
        // If either spec fails, the AND should fail (using lowercase to match the specification logic)
        var failingSpec = new NameMustBeUniqueAsyncSpecification(["testname"]);
        ISpecification<TestEntity> andSpecWithFailure = spec1.And(failingSpec);
        Result failureResult = await andSpecWithFailure.IsSatisfiedBy(entity);
        failureResult.IsFailure.Should().BeTrue();
    }

    [Fact]
    public async Task AsyncOrSpecification_ShouldExecuteConcurrently()
    {
        // Arrange
        var entity = new TestEntity("TestName", 10, true);
        var spec1 = new NameMustBeUniqueAsyncSpecification([]);
        var spec2 = new NameMustBeUniqueAsyncSpecification([]);
        ISpecification<TestEntity> orSpec = spec1.Or(spec2);

        var stopwatch = Stopwatch.StartNew();

        // Act
        Result result = await orSpec.IsSatisfiedBy(entity);

        // Assert
        stopwatch.Stop();
        result.IsSuccess.Should().BeTrue();
        stopwatch.ElapsedMilliseconds
            .Should().BeLessThan(2000, "concurrent execution should complete in reasonable time");
    }
    #endregion

    #region Business Rule Names Tests

    [Fact]
    public void AsyncSpecificationBusinessRuleNames_ShouldBeDescriptive()
    {
        // Arrange & Act
        var spec1 = new NameMustBeUniqueAsyncSpecification();
        var spec2 = new NameMustBeUniqueAsyncSpecification();
        ISpecification<TestEntity> andSpec = spec1.And(spec2);
        ISpecification<TestEntity> orSpec = spec1.Or(spec2);
        ISpecification<TestEntity> notSpec = spec1.Not();

        // Assert
        spec1.BusinessRuleName.Should().Be("Entity Name Must Be Unique");
        spec1.Description.Should().Contain("unique across all entities");

        andSpec.BusinessRuleName.Should().Contain("AND");
        orSpec.BusinessRuleName.Should().Contain("OR");
        notSpec.BusinessRuleName.Should().Contain("NOT");
    }

    #endregion
}
