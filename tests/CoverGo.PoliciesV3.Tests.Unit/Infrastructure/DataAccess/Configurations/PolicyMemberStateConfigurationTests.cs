using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using System.Text.Json;
using CoverGo.PoliciesV3.Domain.Common.Enums;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;
using CoverGo.PoliciesV3.Infrastructure.DataAccess;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using PolicyMemberState = CoverGo.PoliciesV3.Domain.ValueObjects.PolicyMemberState;
using PolicyMember = CoverGo.PoliciesV3.Domain.PolicyMembers.PolicyMember;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.DataAccess.Configurations;

public class PolicyMemberStateConfigurationTests
{
    private readonly Fixture _fixture;
    private readonly DbContextOptions<ApplicationDbContext> _dbContextOptions;

    public PolicyMemberStateConfigurationTests()
    {
        _fixture = new Fixture();

        // Configure in-memory database for testing
        _dbContextOptions = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
    }

    #region Configuration Tests

    [Fact]
    public void Configure_ShouldSetCorrectTableName()
    {
        // Arrange & Act
        using var context = new ApplicationDbContext(_dbContextOptions);
        IEntityType entityType = context.Model.FindEntityType(typeof(PolicyMemberState))!;

        // Assert
        entityType.Should().NotBeNull();
        entityType.GetTableName().Should().Be(PostgreSqlHelpers.GetTableName(nameof(PolicyMemberState)));
    }

    [Fact]
    public void Configure_ShouldSetCorrectPrimaryKey()
    {
        // Arrange & Act
        using var context = new ApplicationDbContext(_dbContextOptions);
        IEntityType entityType = context.Model.FindEntityType(typeof(PolicyMemberState))!;
        IKey primaryKey = entityType.FindPrimaryKey()!;

        // Assert
        primaryKey.Should().NotBeNull();
        primaryKey.Properties.Should().HaveCount(1);
        primaryKey.Properties.First().Name.Should().Be(nameof(PolicyMemberState.Id));
    }

    [Fact]
    public void Configure_ShouldSetCorrectColumnNames()
    {
        // Arrange & Act
        using var context = new ApplicationDbContext(_dbContextOptions);
        IEntityType entityType = context.Model.FindEntityType(typeof(PolicyMemberState))!;

        // Assert - Check key properties
        IProperty idProperty = entityType.FindProperty(nameof(PolicyMemberState.Id))!;
        idProperty.GetColumnName().Should().Be(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.Id)));

        IProperty policyMemberIdProperty = entityType.FindProperty(nameof(PolicyMemberState.PolicyMemberId))!;
        policyMemberIdProperty.GetColumnName().Should().Be(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.PolicyMemberId)));

        IProperty planIdProperty = entityType.FindProperty(nameof(PolicyMemberState.PlanId))!;
        planIdProperty.GetColumnName().Should().Be(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.PlanId)));
    }

    [Fact]
    public void Configure_ShouldSetCorrectColumnTypes()
    {
        // Arrange & Act
        using var context = new ApplicationDbContext(_dbContextOptions);
        IEntityType entityType = context.Model.FindEntityType(typeof(PolicyMemberState))!;

        // Assert - Check that properties exist and have the expected configuration
        // Note: InMemory database doesn't support relational column types, so we check the configuration exists
        IProperty idProperty = entityType.FindProperty(nameof(PolicyMemberState.Id))!;
        idProperty.Should().NotBeNull();

        IProperty policyMemberIdProperty = entityType.FindProperty(nameof(PolicyMemberState.PolicyMemberId))!;
        policyMemberIdProperty.Should().NotBeNull();

        IProperty planIdProperty = entityType.FindProperty(nameof(PolicyMemberState.PlanId))!;
        planIdProperty.Should().NotBeNull();

        IProperty classProperty = entityType.FindProperty(nameof(PolicyMemberState.Class))!;
        classProperty.Should().NotBeNull();

        IProperty fieldsProperty = entityType.FindProperty(nameof(PolicyMemberState.Fields))!;
        fieldsProperty.Should().NotBeNull();

        IProperty premiumProperty = entityType.FindProperty(nameof(PolicyMemberState.Premium))!;
        premiumProperty.Should().NotBeNull();
    }

    [Fact]
    public void Configure_ShouldSetCorrectRequiredProperties()
    {
        // Arrange & Act
        using var context = new ApplicationDbContext(_dbContextOptions);
        IEntityType entityType = context.Model.FindEntityType(typeof(PolicyMemberState))!;

        // Assert - Required properties
        IProperty idProperty = entityType.FindProperty(nameof(PolicyMemberState.Id))!;
        idProperty.IsNullable.Should().BeFalse();

        IProperty policyMemberIdProperty = entityType.FindProperty(nameof(PolicyMemberState.PolicyMemberId))!;
        policyMemberIdProperty.IsNullable.Should().BeFalse();

        IProperty planIdProperty = entityType.FindProperty(nameof(PolicyMemberState.PlanId))!;
        planIdProperty.IsNullable.Should().BeFalse();

        IProperty fieldsProperty = entityType.FindProperty(nameof(PolicyMemberState.Fields))!;
        fieldsProperty.IsNullable.Should().BeFalse();

        // Assert - Optional properties
        IProperty classProperty = entityType.FindProperty(nameof(PolicyMemberState.Class))!;
        classProperty.IsNullable.Should().BeTrue();

        IProperty premiumProperty = entityType.FindProperty(nameof(PolicyMemberState.Premium))!;
        premiumProperty.IsNullable.Should().BeTrue();

        IProperty underwritingProperty = entityType.FindProperty(nameof(PolicyMemberState.Underwriting))!;
        underwritingProperty.IsNullable.Should().BeTrue();
    }

    #endregion

    #region Index Tests

    [Fact]
    public void Configure_ShouldCreateCorrectIndexes()
    {
        // Arrange & Act
        using var context = new ApplicationDbContext(_dbContextOptions);
        IEntityType entityType = context.Model.FindEntityType(typeof(PolicyMemberState))!;
        var indexes = entityType.GetIndexes().ToList();

        // Assert
        indexes.Should().NotBeEmpty();

        // Check PolicyMemberId index
        IIndex? policyMemberIdIndex = indexes.FirstOrDefault(i =>
            i.Properties.Count == 1 &&
            i.Properties.First().Name == nameof(PolicyMemberState.PolicyMemberId));
        policyMemberIdIndex.Should().NotBeNull();

        // Check composite index
        IIndex? compositeIndex = indexes.FirstOrDefault(i =>
            i.Properties.Count == 3 &&
            i.Properties.Any(p => p.Name == nameof(PolicyMemberState.PolicyMemberId)) &&
            i.Properties.Any(p => p.Name == nameof(PolicyMemberState.StartDate)) &&
            i.Properties.Any(p => p.Name == nameof(PolicyMemberState.EndDate)));
        compositeIndex.Should().NotBeNull();
    }

    #endregion

    #region Relationship Tests

    [Fact]
    public void Configure_ShouldSetCorrectRelationships()
    {
        // Arrange & Act
        using var context = new ApplicationDbContext(_dbContextOptions);
        IEntityType entityType = context.Model.FindEntityType(typeof(PolicyMemberState))!;
        var foreignKeys = entityType.GetForeignKeys().ToList();

        // Assert
        foreignKeys.Should().HaveCount(1);

        IForeignKey policyMemberForeignKey = foreignKeys.First();
        policyMemberForeignKey.PrincipalEntityType.ClrType.Should().Be(typeof(PolicyMember));
        policyMemberForeignKey.Properties.First().Name.Should().Be(nameof(PolicyMemberState.PolicyMemberId));
        policyMemberForeignKey.DeleteBehavior.Should().Be(DeleteBehavior.Restrict);
    }

    #endregion

    #region Audit Configuration Tests

    [Fact]
    public void Configure_ShouldConfigureEntityProperties()
    {
        // Arrange & Act
        using var context = new ApplicationDbContext(_dbContextOptions);
        IEntityType entityType = context.Model.FindEntityType(typeof(PolicyMemberState))!;

        // Assert - Verify the entity has the expected core properties
        var allProperties = entityType.GetProperties().Select(p => p.Name).ToList();

        allProperties.Should().Contain(nameof(PolicyMemberState.Id));
        allProperties.Should().Contain(nameof(PolicyMemberState.PolicyMemberId));
        allProperties.Should().Contain(nameof(PolicyMemberState.PlanId));
        allProperties.Should().Contain(nameof(PolicyMemberState.StartDate));
        allProperties.Should().Contain(nameof(PolicyMemberState.EndDate));
        allProperties.Should().Contain(nameof(PolicyMemberState.Fields));
        allProperties.Should().Contain(nameof(PolicyMemberState.Premium));
        allProperties.Should().Contain(nameof(PolicyMemberState.Underwriting));
        allProperties.Should().Contain(nameof(PolicyMemberState.Loadings));
        allProperties.Should().Contain(nameof(PolicyMemberState.BenefitsLoadings));
        allProperties.Should().Contain(nameof(PolicyMemberState.BenefitsUnderwritings));
    }

    #endregion

    #region JSON Serialization Tests

    [Fact]
    public void JsonConversion_Fields_ShouldSerializeAndDeserializeCorrectly()
    {
        // Arrange
        var originalFields = new List<PolicyField>
        {
            new() { Key = "firstName", Value = "John" },
            new() { Key = "lastName", Value = "Doe" },
            new() { Key = "age", Value = "30" }
        };

        // Act - Serialize
        string serialized = JsonSerializer.Serialize(originalFields, JsonSchemaProcessor.DatabaseOptions);

        // Act - Deserialize
        List<PolicyField> deserialized = JsonSerializer.Deserialize<List<PolicyField>>(serialized, JsonSchemaProcessor.DatabaseOptions) ?? new List<PolicyField>();

        // Assert
        deserialized.Should().NotBeNull();
        deserialized.Should().HaveCount(3);

        // Check keys match
        deserialized.Select(f => f.Key).Should().BeEquivalentTo(originalFields.Select(f => f.Key));

        // Check values (they may be JsonElement after deserialization)
        for (int i = 0; i < deserialized.Count; i++)
        {
            deserialized[i].Key.Should().Be(originalFields[i].Key);
            // Value might be JsonElement, so convert to string for comparison
            deserialized[i].Value?.ToString().Should().Be(originalFields[i].Value?.ToString());
        }
    }

    [Fact]
    public void JsonConversion_Premium_ShouldSerializeAndDeserializeCorrectly()
    {
        // Arrange
        var originalPremium = new MemberPremium
        {
            Id = "premium-123",
            EffectiveDate = DateOnly.FromDateTime(DateTime.UtcNow)
        };

        // Act - Serialize
        string serialized = JsonSerializer.Serialize(originalPremium, JsonSchemaProcessor.DatabaseOptions);

        // Act - Deserialize
        MemberPremium? deserialized = JsonSerializer.Deserialize<MemberPremium>(serialized, JsonSchemaProcessor.DatabaseOptions);

        // Assert
        deserialized.Should().NotBeNull();
        deserialized.Should().BeEquivalentTo(originalPremium);
    }

    [Fact]
    public void JsonConversion_Premium_ShouldHandleNullCorrectly()
    {
        // Arrange
        MemberPremium? originalPremium = null;

        // Act - Serialize
        string? serialized = originalPremium == null ? null : JsonSerializer.Serialize(originalPremium, JsonSchemaProcessor.DatabaseOptions);

        // Act - Deserialize
        MemberPremium? deserialized = serialized == null ? null : JsonSerializer.Deserialize<MemberPremium>(serialized, JsonSchemaProcessor.DatabaseOptions);

        // Assert
        deserialized.Should().BeNull();
    }

    [Fact]
    public void JsonConversion_Underwriting_ShouldSerializeAndDeserializeCorrectly()
    {
        // Arrange
        var originalUnderwriting = new MemberUnderwriting
        {
            Id = "underwriting-123",
            Name = "Standard Assessment",
            Status = MemberUnderwritingStatus.Approved
        };

        // Act - Serialize
        string serialized = JsonSerializer.Serialize(originalUnderwriting, JsonSchemaProcessor.DatabaseOptions);

        // Act - Deserialize
        MemberUnderwriting? deserialized = JsonSerializer.Deserialize<MemberUnderwriting>(serialized, JsonSchemaProcessor.DatabaseOptions);

        // Assert
        deserialized.Should().NotBeNull();
        deserialized.Should().BeEquivalentTo(originalUnderwriting);
    }

    [Fact]
    public void JsonConversion_Loadings_ShouldSerializeAndDeserializeCorrectly()
    {
        // Arrange
        var originalLoadings = new List<MemberLoading>
        {
            new() { Id = "loading-1", Name = "Age Loading", Value = 10.5m, Method = LoadingMethodType.Factor, Source = LoadingSourceType.Auto, Status = LoadingStatusType.Accepted, EffectiveDate = DateOnly.FromDateTime(DateTime.UtcNow) },
            new() { Id = "loading-2", Name = "Occupation Loading", Value = 5.0m, Method = LoadingMethodType.FixedAmount, Source = LoadingSourceType.Manual, Status = LoadingStatusType.Pending, EffectiveDate = DateOnly.FromDateTime(DateTime.UtcNow) }
        };

        // Act - Serialize
        string serialized = JsonSerializer.Serialize(originalLoadings, JsonSchemaProcessor.DatabaseOptions);

        // Act - Deserialize
        var deserialized = JsonSerializer.Deserialize<List<MemberLoading>>(serialized, JsonSchemaProcessor.DatabaseOptions) as ICollection<MemberLoading>;

        // Assert
        deserialized.Should().NotBeNull();
        deserialized.Should().HaveCount(2);
        deserialized.Should().BeEquivalentTo(originalLoadings);
    }

    [Fact]
    public void JsonConversion_BenefitsLoadings_ShouldSerializeAndDeserializeCorrectly()
    {
        // Arrange
        var originalBenefitsLoadings = new List<MemberBenefitsLoading>
        {
            new() { Id = "benefits-loading-1", BenefitId = "medical-benefit", BenefitName = "Medical Coverage" },
            new() { Id = "benefits-loading-2", BenefitId = "dental-benefit", BenefitName = "Dental Coverage" }
        };

        // Act - Serialize
        string serialized = JsonSerializer.Serialize(originalBenefitsLoadings, JsonSchemaProcessor.DatabaseOptions);

        // Act - Deserialize
        var deserialized = JsonSerializer.Deserialize<List<MemberBenefitsLoading>>(serialized, JsonSchemaProcessor.DatabaseOptions) as ICollection<MemberBenefitsLoading>;

        // Assert
        deserialized.Should().NotBeNull();
        deserialized.Should().HaveCount(2);
        deserialized.Should().BeEquivalentTo(originalBenefitsLoadings);
    }

    [Fact]
    public void JsonConversion_BenefitsUnderwritings_ShouldSerializeAndDeserializeCorrectly()
    {
        // Arrange
        var originalBenefitsUnderwritings = new List<MemberBenefitsUnderwriting>
        {
            new() { Id = "benefits-underwriting-1", BenefitId = "life-benefit", BenefitName = "Life Insurance", Status = MemberBenefitUnderwritingStatus.Approved },
            new() { Id = "benefits-underwriting-2", BenefitId = "disability-benefit", BenefitName = "Disability Insurance", Status = MemberBenefitUnderwritingStatus.Pending }
        };

        // Act - Serialize
        string serialized = JsonSerializer.Serialize(originalBenefitsUnderwritings, JsonSchemaProcessor.DatabaseOptions);

        // Act - Deserialize
        var deserialized = JsonSerializer.Deserialize<List<MemberBenefitsUnderwriting>>(serialized, JsonSchemaProcessor.DatabaseOptions) as ICollection<MemberBenefitsUnderwriting>;

        // Assert
        deserialized.Should().NotBeNull();
        deserialized.Should().HaveCount(2);
        deserialized.Should().BeEquivalentTo(originalBenefitsUnderwritings);
    }

    [Fact]
    public void JsonConversion_ShouldUseSnakeCaseNaming()
    {
        // Arrange
        var premium = new MemberPremium
        {
            Id = "premium-123",
            EffectiveDate = DateOnly.FromDateTime(DateTime.UtcNow)
        };

        // Act
        string serialized = JsonSerializer.Serialize(premium, JsonSchemaProcessor.DatabaseOptions);

        // Assert - Should use snake_case property names
        serialized.Should().Contain("\"id\":");
        serialized.Should().Contain("\"effective_date\":");
        serialized.Should().Contain("\"summaries\":");

        // Should not contain PascalCase
        serialized.Should().NotContain("\"Id\":");
        serialized.Should().NotContain("\"EffectiveDate\":");
        serialized.Should().NotContain("\"Summaries\":");
    }

    #endregion

    #region Database Naming Convention Tests

    [Fact]
    public void DatabaseNaming_ShouldFollowSnakeCaseConvention()
    {
        // Arrange & Act
        using var context = new ApplicationDbContext(_dbContextOptions);
        IEntityType entityType = context.Model.FindEntityType(typeof(PolicyMemberState))!;

        // Assert - Table name should be snake_case
        entityType.GetTableName().Should().Be("policy_member_states");

        // Assert - Column names should be snake_case
        var properties = entityType.GetProperties().ToList();

        IProperty idProperty = properties.First(p => p.Name == nameof(PolicyMemberState.Id));
        idProperty.GetColumnName().Should().Be("id");

        IProperty policyMemberIdProperty = properties.First(p => p.Name == nameof(PolicyMemberState.PolicyMemberId));
        policyMemberIdProperty.GetColumnName().Should().Be("policy_member_id");

        IProperty planIdProperty = properties.First(p => p.Name == nameof(PolicyMemberState.PlanId));
        planIdProperty.GetColumnName().Should().Be("plan_id");

        IProperty startDateProperty = properties.First(p => p.Name == nameof(PolicyMemberState.StartDate));
        startDateProperty.GetColumnName().Should().Be("start_date");

        IProperty endDateProperty = properties.First(p => p.Name == nameof(PolicyMemberState.EndDate));
        endDateProperty.GetColumnName().Should().Be("end_date");
    }

    [Fact]
    public void DatabaseNaming_ShouldFollowConstraintNamingConvention()
    {
        // Arrange & Act
        using var context = new ApplicationDbContext(_dbContextOptions);
        IEntityType entityType = context.Model.FindEntityType(typeof(PolicyMemberState))!;

        // Assert - Foreign key constraint naming
        var foreignKeys = entityType.GetForeignKeys().ToList();
        foreignKeys.Should().HaveCount(1);

        IForeignKey policyMemberForeignKey = foreignKeys.First();
        // The actual constraint name includes the full path, so we check it contains the expected parts
        string? constraintName = policyMemberForeignKey.GetConstraintName();
        constraintName.Should().StartWith("fk_policy_member_state_");
        constraintName.Should().Contain("policy_member");
    }

    #endregion
}
