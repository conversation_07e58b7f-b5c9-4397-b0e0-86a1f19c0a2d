using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.Extensions;
using CoverGo.PoliciesV3.Infrastructure.Schemas;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Providers;

/// <summary>
/// Shared test helpers and data builders for PolicyMemberFieldsSchemaProvider tests
/// </summary>
public static class PolicyMemberFieldsSchemaProviderTestHelpers
{
    /// <summary>
    /// Helper method to map custom fields dictionary to PolicyMemberFieldDefinition list
    /// This replicates the logic from PolicyMemberFieldsSchemaRepository.MapCustomFieldsToDefinitions
    /// </summary>
    public static List<PolicyMemberFieldDefinition> MapCustomFieldsToDefinitions(
        IDictionary<string, PolicyMemberCustomField> properties,
        bool includeObjectFieldParents = false)
    {
        var fieldDefinitions = new List<PolicyMemberFieldDefinition>(properties.Count);

        foreach ((string fieldName, PolicyMemberCustomField fieldType) in properties)
        {
            CustomFieldTypeBase? type = fieldType.ToCustomFieldType();
            if (type == null)
                continue;

            var fieldDefinition = new PolicyMemberFieldDefinition
            {
                Label = fieldType.Meta?.Label ?? fieldName,
                Name = fieldName,
                Type = type,
                IsRequired = fieldType.IsRequired,
                IsUnique = fieldType.IsUnique,
            };

            // Handle nested object fields with parent relationships (only for member fields)
            if (includeObjectFieldParents && fieldDefinition.Type is ObjectFieldType objType)
            {
                PolicyMemberFieldDefinition[] innerFieldsWithParent = [.. objType.InnerFieldDefinitions
                    .ToArray()
                    .Select(it => it with { Parent = fieldDefinition })];

                fieldDefinition = fieldDefinition with
                {
                    Type = objType with
                    {
                        InnerFieldDefinitions = [.. innerFieldsWithParent]
                    }
                };
            }

            fieldDefinitions.Add(fieldDefinition);
        }

        return fieldDefinitions;
    }

    #region Schema Builders

    public static PolicyMemberFieldsSchema CreateBaseSchema()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "firstName",
                Label = "First Name",
                Type = new StringFieldType { Options = null },
                IsRequired = true,
                IsUnique = false
            },
            new()
            {
                Name = "lastName",
                Label = "Last Name",
                Type = new StringFieldType { Options = null },
                IsRequired = true,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(
            memberFields,
            productFields: null,
            censusFields: null,
            oneOfValidations: null
        );
    }

    public static PolicyMemberFieldsSchema CreateSchemaWithRelationshipField()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "relationshipToEmployee",
                Label = "Relationship to Employee",
                Type = new StringFieldType { Options = null },
                IsRequired = false,
                IsRequiredForDependent = false,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(
            memberFields,
            productFields: null,
            censusFields: null,
            oneOfValidations: null
        );
    }

    public static PolicyMemberFieldsSchema CreateSchemaWithIdentityFields()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "passportNo",
                Label = "Passport Number",
                Type = new StringFieldType { Options = null },
                IsRequired = false,
                IsUnique = false
            },
            new()
            {
                Name = "hkid",
                Label = "Hong Kong ID",
                Type = new StringFieldType { Options = null },
                IsRequired = false,
                IsUnique = false
            },
            new()
            {
                Name = "staffNo",
                Label = "Staff Number",
                Type = new StringFieldType { Options = null },
                IsRequired = false,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(
            memberFields,
            productFields: null,
            censusFields: null,
            oneOfValidations: null
        );
    }

    public static PolicyMemberFieldsSchema CreateSchemaWithEmptyAddressField()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "address",
                Label = "Home Address",
                Type = new ObjectFieldType([]), // Empty object field
                IsRequired = false,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(
            memberFields,
            productFields: null,
            censusFields: null,
            oneOfValidations: null
        );
    }

    public static PolicyMemberFieldsSchema CreateSchemaWithValidationRules()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "email",
                Label = "Email Address",
                Type = new StringFieldType { Validations = "email" },
                IsRequired = false,
                IsUnique = false
            },
            new()
            {
                Name = "requiredField",
                Label = "Required Field",
                Type = new StringFieldType { Validations = "required" },
                IsRequired = true,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(
            memberFields,
            productFields: null,
            censusFields: null,
            oneOfValidations: null
        );
    }

    public static PolicyMemberFieldsSchema CreateSchemaWithMixedFieldOrder()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "textField",
                Label = "Text Field",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            },
            new()
            {
                Name = "numberField",
                Label = "Number Field",
                Type = new NumberFieldType(),
                IsRequired = false,
                IsUnique = false
            },
            new()
            {
                Name = "booleanField",
                Label = "Boolean Field",
                Type = new BooleanFieldType(),
                IsRequired = false,
                IsUnique = false
            },
            new()
            {
                Name = "dateField",
                Label = "Date Field",
                Type = new DateFieldType(),
                IsRequired = false,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(
            memberFields,
            productFields: null,
            censusFields: null,
            oneOfValidations: null
        );
    }

    public static PolicyMemberFieldsSchema CreateSchemaWithExistingSystemFields()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = PolicyMemberUploadWellKnowFields.PlanIdField,
                Label = "Existing Plan",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            },
            new()
            {
                Name = PolicyMemberUploadWellKnowFields.ClassField,
                Label = "Existing Class",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            },
            new()
            {
                Name = "customField",
                Label = "Custom Field",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(
            memberFields,
            productFields: null,
            censusFields: null,
            oneOfValidations: null
        );
    }

    /// <summary>
    /// Creates a schema based on complex field types pattern to demonstrate field type parsing
    /// This simulates the structure that would result from parsing CasesService JSON
    /// </summary>
    public static PolicyMemberFieldsSchema CreateSchemaBasedOnComplexPattern()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            // Text field (givenName)
            new()
            {
                Name = "givenName",
                Label = "Given Name",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            },
            // Text field (surname)
            new()
            {
                Name = "surname",
                Label = "Surname",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            },
            // Formula field (fullName with fieldType: "dynamic")
            new()
            {
                Name = "fullName",
                Label = "Name",
                Type = new FormulaFieldType(),
                IsRequired = false,
                IsUnique = false
            },
            // Date field (dateOfBirth with fieldType: "date")
            new()
            {
                Name = "dateOfBirth",
                Label = "Date of Birth",
                Type = new DateFieldType(),
                IsRequired = false,
                IsUnique = false
            },
            // Select field with options (memberType)
            new()
            {
                Name = "memberType",
                Label = "Member Type",
                Type = new StringFieldType
                {
                    Options = [
                        new() { Value = "employee", Label = "Employee" },
                        new() { Value = "dependent", Label = "Dependent" }
                    ]
                },
                IsRequired = true,
                IsUnique = false
            },
            // Address field (type: "address" with properties)
            new()
            {
                Name = "address",
                Label = "Home Address",
                Type = new AddressFieldType([
                    new()
                    {
                        Name = "flatNumber",
                        Label = "Flat Number / Building",
                        Type = new StringFieldType(),
                        IsRequired = false,
                        IsUnique = false
                    },
                    new()
                    {
                        Name = "streetNumberAndName",
                        Label = "Street Number & Street Name",
                        Type = new StringFieldType(),
                        IsRequired = false,
                        IsUnique = false
                    }
                ]),
                IsRequired = false,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(
            memberFields,
            productFields: null,
            censusFields: null,
            oneOfValidations: null
        );
    }

    #endregion
}
