using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.Validation;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.CustomFields;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Providers;

/// <summary>
/// Tests for upload-specific functionality of PolicyMemberFieldsSchemaProvider
/// </summary>
public class PolicyMemberFieldsSchemaProviderUploadTests
{
    private readonly Mock<IPolicyMemberFieldsSchemaRepository> _schemaRepository;
    private readonly Mock<IMultiTenantFeatureManager> _featureManager;
    private readonly Mock<ILogger<PolicyMemberFieldsSchemaProvider>> _logger;
    private readonly PolicyMemberFieldsSchemaProvider _provider;
    private readonly IFixture _fixture;

    public PolicyMemberFieldsSchemaProviderUploadTests()
    {
        _schemaRepository = new Mock<IPolicyMemberFieldsSchemaRepository>();
        _featureManager = new Mock<IMultiTenantFeatureManager>();
        _logger = new Mock<ILogger<PolicyMemberFieldsSchemaProvider>>();

        var tenantId = new TenantId("test-tenant");
        _provider = new PolicyMemberFieldsSchemaProvider(
            _schemaRepository.Object,
            _featureManager.Object,
            tenantId,
            _logger.Object);

        _fixture = new Fixture();
        _fixture.Customize<ProductId>(c => c.FromFactory(() => new ProductId("PLAN-001", "HEALTH", "1.0")));
        _fixture.Customize<EndorsementId>(c => c.FromFactory(() => EndorsementId.New));

        // Setup default feature flag behavior
        _featureManager.Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);
    }

    [Fact]
    public async Task GetMemberUploadSchema_ShouldApplyUploadSpecificProcessing()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        EndorsementId endorsementId = _fixture.Create<EndorsementId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, endorsementId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.MemberFields.Should().NotBeEmpty();

        // Should contain system fields added by upload processing
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.PlanIdField);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.ClassField);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.MemberIdField);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.DependentOfField);

        // Should contain effective date field when endorsementId is provided
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);

        // Should have identity validations
        result.OneOfValidations.Should().NotBeNull();
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithoutEndorsement_ShouldNotIncludeEffectiveDate()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Should NOT contain effective date field when endorsementId is null
        result.MemberFields.Should().NotContain(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithRelationshipToEmployeeField_ShouldSetRequiredForDependent()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateSchemaWithRelationshipField();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        PolicyMemberFieldDefinition? relationshipField = result.MemberFields.FirstOrDefault(f => f.Name == "relationshipToEmployee");
        relationshipField.Should().NotBeNull();
        relationshipField!.IsRequiredForDependent.Should().BeTrue();
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithIdentityFields_ShouldCreateOneOfValidation()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateSchemaWithIdentityFields();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.OneOfValidations.Should().NotBeNull();
        result.OneOfValidations.Should().HaveCount(1);

        CustomFieldOneOfValidation oneOfValidation = result.OneOfValidations.First();
        oneOfValidation.Validations.Should().HaveCount(3); // passportNo, hkid, staffNo
        oneOfValidation.Validations.Should().Contain(v => v.Field.Name == "passportNo");
        oneOfValidation.Validations.Should().Contain(v => v.Field.Name == "hkid");
        oneOfValidation.Validations.Should().Contain(v => v.Field.Name == "staffNo");
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithDuplicateSystemFields_ShouldNotDuplicate()
    {
        // Arrange - Schema already contains some system fields
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateSchemaWithExistingSystemFields();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Should not duplicate system fields
        IEnumerable<PolicyMemberFieldDefinition> planIdFields = result.MemberFields.Where(f => f.Name == PolicyMemberUploadWellKnowFields.PlanIdField);
        planIdFields.Should().HaveCount(1);

        IEnumerable<PolicyMemberFieldDefinition> classFields = result.MemberFields.Where(f => f.Name == PolicyMemberUploadWellKnowFields.ClassField);
        classFields.Should().HaveCount(1);

        IEnumerable<PolicyMemberFieldDefinition> memberIdFields = result.MemberFields.Where(f => f.Name == PolicyMemberUploadWellKnowFields.MemberIdField);
        memberIdFields.Should().HaveCount(1);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithMixedFieldOrder_ShouldPreserveAllFields()
    {
        // Arrange - Test with fields in different orders and mixed types
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateSchemaWithMixedFieldOrder();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Should contain all original fields plus system fields
        result.MemberFields.Should().HaveCount(8); // Based on actual test output: 4 original + 4 system fields

        // Verify specific field types are preserved
        result.MemberFields.Should().Contain(f => f.Name == "textField" && f.Type is StringFieldType);
        result.MemberFields.Should().Contain(f => f.Name == "numberField" && f.Type is NumberFieldType);
        result.MemberFields.Should().Contain(f => f.Name == "booleanField" && f.Type is BooleanFieldType);
        result.MemberFields.Should().Contain(f => f.Name == "dateField" && f.Type is DateFieldType);

        // Verify system fields are added
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.PlanIdField);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.ClassField);
    }
}
