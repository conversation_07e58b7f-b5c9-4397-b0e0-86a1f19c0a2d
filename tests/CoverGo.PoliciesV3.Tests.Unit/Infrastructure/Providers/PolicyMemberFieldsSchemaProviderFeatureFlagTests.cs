using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.CustomFields;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Providers;

/// <summary>
/// Tests for feature flag behavior in PolicyMemberFieldsSchemaProvider
/// </summary>
public class PolicyMemberFieldsSchemaProviderFeatureFlagTests
{
    private readonly Mock<IPolicyMemberFieldsSchemaRepository> _schemaRepository;
    private readonly Mock<IMultiTenantFeatureManager> _featureManager;
    private readonly Mock<ILogger<PolicyMemberFieldsSchemaProvider>> _logger;
    private readonly PolicyMemberFieldsSchemaProvider _provider;
    private readonly IFixture _fixture;

    public PolicyMemberFieldsSchemaProviderFeatureFlagTests()
    {
        _schemaRepository = new Mock<IPolicyMemberFieldsSchemaRepository>();
        _featureManager = new Mock<IMultiTenantFeatureManager>();
        _logger = new Mock<ILogger<PolicyMemberFieldsSchemaProvider>>();

        var tenantId = new TenantId("test-tenant");
        _provider = new PolicyMemberFieldsSchemaProvider(
            _schemaRepository.Object,
            _featureManager.Object,
            tenantId,
            _logger.Object);

        _fixture = new Fixture();
        _fixture.Customize<ProductId>(c => c.FromFactory(() => new ProductId("PLAN-001", "HEALTH", "1.0")));
        _fixture.Customize<EndorsementId>(c => c.FromFactory(() => EndorsementId.New));

        // Setup default feature flag behavior
        _featureManager.Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithFeatureFlagEnabled_ShouldIncludeEffectiveDateWithoutEndorsement()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Setup feature flag to return true
        _featureManager
            .Setup(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"))
            .ReturnsAsync(true);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Should contain effective date field even without endorsement when feature flag is enabled
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);

        // Verify feature flag was called
        _featureManager.Verify(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"), Times.Once);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithFeatureFlagDisabled_ShouldExcludeEffectiveDateWithoutEndorsement()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Setup feature flag to return false (default behavior)
        _featureManager
            .Setup(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"))
            .ReturnsAsync(false);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Should NOT contain effective date field without endorsement when feature flag is disabled
        result.MemberFields.Should().NotContain(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);

        // Verify feature flag was called
        _featureManager.Verify(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"), Times.Once);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithFeatureFlagEnabledAndEndorsement_ShouldIncludeEffectiveDate()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        EndorsementId endorsementId = _fixture.Create<EndorsementId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Setup feature flag to return true
        _featureManager
            .Setup(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"))
            .ReturnsAsync(true);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, endorsementId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Should contain effective date field when both feature flag is enabled AND endorsement is provided
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);

        // Verify the effective date field is required when endorsement is provided
        PolicyMemberFieldDefinition? effectiveDateField = result.MemberFields.FirstOrDefault(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);
        effectiveDateField.Should().NotBeNull();
        effectiveDateField!.IsRequired.Should().BeTrue();
        effectiveDateField.Type.Should().BeOfType<DateFieldType>();
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithFeatureFlagDisabledButEndorsement_ShouldIncludeEffectiveDate()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        EndorsementId endorsementId = _fixture.Create<EndorsementId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Setup feature flag to return false
        _featureManager
            .Setup(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"))
            .ReturnsAsync(false);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, endorsementId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Should contain effective date field when endorsement is provided, regardless of feature flag
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);

        // Verify the effective date field is required when endorsement is provided
        PolicyMemberFieldDefinition? effectiveDateField = result.MemberFields.FirstOrDefault(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);
        effectiveDateField.Should().NotBeNull();
        effectiveDateField!.IsRequired.Should().BeTrue();
        effectiveDateField.Type.Should().BeOfType<DateFieldType>();
    }

    [Fact]
    public async Task GetMemberUploadSchema_ShouldAlwaysCheckFeatureFlag()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        _featureManager
            .Setup(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"))
            .ReturnsAsync(false);

        // Act
        await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        _featureManager.Verify(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"), Times.Once);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithFeatureFlagException_ShouldPropagateException()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateBaseSchema();
        var expectedException = new InvalidOperationException("Feature manager error");

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        _featureManager
            .Setup(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"))
            .ThrowsAsync(expectedException);

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None));

        exception.Should().Be(expectedException);
    }
}
