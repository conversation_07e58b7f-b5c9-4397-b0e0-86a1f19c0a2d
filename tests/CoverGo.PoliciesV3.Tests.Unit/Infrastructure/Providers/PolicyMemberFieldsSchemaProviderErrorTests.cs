using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Infrastructure.CustomFields;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Providers;

/// <summary>
/// Tests for error handling in PolicyMemberFieldsSchemaProvider
/// </summary>
public class PolicyMemberFieldsSchemaProviderErrorTests
{
    private readonly Mock<IPolicyMemberFieldsSchemaRepository> _schemaRepository;
    private readonly Mock<IMultiTenantFeatureManager> _featureManager;
    private readonly Mock<ILogger<PolicyMemberFieldsSchemaProvider>> _logger;
    private readonly PolicyMemberFieldsSchemaProvider _provider;
    private readonly IFixture _fixture;

    public PolicyMemberFieldsSchemaProviderErrorTests()
    {
        _schemaRepository = new Mock<IPolicyMemberFieldsSchemaRepository>();
        _featureManager = new Mock<IMultiTenantFeatureManager>();
        _logger = new Mock<ILogger<PolicyMemberFieldsSchemaProvider>>();

        var tenantId = new TenantId("test-tenant");
        _provider = new PolicyMemberFieldsSchemaProvider(
            _schemaRepository.Object,
            _featureManager.Object,
            tenantId,
            _logger.Object);

        _fixture = new Fixture();
        _fixture.Customize<ProductId>(c => c.FromFactory(() => new ProductId("PLAN-001", "HEALTH", "1.0")));
        _fixture.Customize<EndorsementId>(c => c.FromFactory(() => EndorsementId.New));

        // Setup default feature flag behavior
        _featureManager.Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WhenRepositoryThrows_ShouldLogErrorAndRethrow()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        var expectedException = new InvalidOperationException("Repository error");

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _provider.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None));

        exception.Should().Be(expectedException);

        // Verify error logging
        _logger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Error occurred")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WhenRepositoryThrowsArgumentException_ShouldRethrow()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        var expectedException = new ArgumentException("Invalid argument", nameof(productId));

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        ArgumentException exception = await Assert.ThrowsAsync<ArgumentException>(
            () => _provider.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None));

        exception.Should().Be(expectedException);
        exception.ParamName.Should().Be(nameof(productId));
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WhenRepositoryThrowsTimeoutException_ShouldRethrow()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        var expectedException = new TimeoutException("Repository timeout");

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        TimeoutException exception = await Assert.ThrowsAsync<TimeoutException>(
            () => _provider.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None));

        exception.Should().Be(expectedException);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WhenRepositoryThrows_ShouldPropagateException()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        var expectedException = new InvalidOperationException("Repository error");

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None));

        exception.Should().Be(expectedException);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WhenFeatureManagerThrows_ShouldPropagateException()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateBaseSchema();
        var expectedException = new InvalidOperationException("Feature manager error");

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        _featureManager
            .Setup(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"))
            .ThrowsAsync(expectedException);

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None));

        exception.Should().Be(expectedException);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithCancellationRequested_ShouldThrowOperationCanceledException()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _provider.GetMemberUploadSchema(contractHolderId, productId, null, cancellationTokenSource.Token));
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WithCancellationRequested_ShouldThrowOperationCanceledException()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _provider.GetCustomFieldsSchema(contractHolderId, productId, cancellationTokenSource.Token));
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithNullBaseSchema_ShouldHandleGracefully()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMemberFieldsSchema)null!);

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(
            () => _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None));
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WithNullProductId_ShouldThrowNullReferenceException()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(
            () => _provider.GetCustomFieldsSchema(contractHolderId, null!, CancellationToken.None));
    }
}
