using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Infrastructure.CustomFields;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Providers;

/// <summary>
/// Tests for basic functionality of PolicyMemberFieldsSchemaProvider
/// </summary>
public class PolicyMemberFieldsSchemaProviderBasicTests
{
    private readonly Mock<IPolicyMemberFieldsSchemaRepository> _schemaRepository;
    private readonly Mock<IMultiTenantFeatureManager> _featureManager;
    private readonly Mock<ILogger<PolicyMemberFieldsSchemaProvider>> _logger;
    private readonly PolicyMemberFieldsSchemaProvider _provider;
    private readonly IFixture _fixture;

    public PolicyMemberFieldsSchemaProviderBasicTests()
    {
        _schemaRepository = new Mock<IPolicyMemberFieldsSchemaRepository>();
        _featureManager = new Mock<IMultiTenantFeatureManager>();
        _logger = new Mock<ILogger<PolicyMemberFieldsSchemaProvider>>();

        var tenantId = new TenantId("test-tenant");
        _provider = new PolicyMemberFieldsSchemaProvider(
            _schemaRepository.Object,
            _featureManager.Object,
            tenantId,
            _logger.Object);

        _fixture = new Fixture();
        _fixture.Customize<ProductId>(c => c.FromFactory(() => new ProductId("PLAN-001", "HEALTH", "1.0")));
        _fixture.Customize<EndorsementId>(c => c.FromFactory(() => EndorsementId.New));

        // Setup default feature flag behavior
        _featureManager.Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);
    }

    [Fact]
    public async Task GetCustomFieldsSchema_ShouldReturnSchemaFromRepository()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema expectedSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result.Should().Be(expectedSchema);
        _schemaRepository.Verify(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WithNullContractHolderId_ShouldCallRepository()
    {
        // Arrange
        string? contractHolderId = null;
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema expectedSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result.Should().Be(expectedSchema);
        _schemaRepository.Verify(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WithEmptySchema_ShouldReturnEmptySchema()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        var emptySchema = new PolicyMemberFieldsSchema(
            memberFields: [],
            productFields: null,
            censusFields: null,
            oneOfValidations: null
        );

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptySchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result.Should().Be(emptySchema);
        result.MemberFields.Should().BeEmpty();
        result.ProductFields.Should().BeEmpty(); // Current implementation returns empty collections, not null
        result.CensusFields.Should().BeEmpty(); // Current implementation returns empty collections, not null
        result.OneOfValidations.Should().BeEmpty(); // Current implementation returns empty collections, not null
    }

    [Fact]
    public async Task GetCustomFieldsSchema_ShouldLogInformation()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema expectedSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedSchema);

        // Act
        await _provider.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        _logger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("GetCustomFieldsSchema - Base schema retrieved")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WithCancellationToken_ShouldPassTokenToRepository()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema expectedSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateBaseSchema();
        var cancellationToken = new CancellationToken();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, cancellationToken))
            .ReturnsAsync(expectedSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetCustomFieldsSchema(contractHolderId, productId, cancellationToken);

        // Assert
        result.Should().Be(expectedSchema);
        _schemaRepository.Verify(x => x.GetCustomFieldsSchema(contractHolderId, productId, cancellationToken), Times.Once);
    }
}
