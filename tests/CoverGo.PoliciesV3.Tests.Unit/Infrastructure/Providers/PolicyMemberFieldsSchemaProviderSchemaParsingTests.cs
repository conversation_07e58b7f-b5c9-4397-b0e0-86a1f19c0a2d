using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.CustomFields;
using CoverGo.PoliciesV3.Infrastructure.Schemas;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Providers;

/// <summary>
/// Tests for schema parsing functionality in PolicyMemberFieldsSchemaProvider
/// </summary>
public class PolicyMemberFieldsSchemaProviderSchemaParsingTests
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true,
        Converters = { new JsonStringEnumConverter() }
    };
    private readonly Mock<IPolicyMemberFieldsSchemaRepository> _schemaRepository;
    private readonly Mock<IMultiTenantFeatureManager> _featureManager;
    private readonly Mock<ILogger<PolicyMemberFieldsSchemaProvider>> _logger;
    private readonly PolicyMemberFieldsSchemaProvider _provider;
    private readonly IFixture _fixture;

    public PolicyMemberFieldsSchemaProviderSchemaParsingTests()
    {
        _schemaRepository = new Mock<IPolicyMemberFieldsSchemaRepository>();
        _featureManager = new Mock<IMultiTenantFeatureManager>();
        _logger = new Mock<ILogger<PolicyMemberFieldsSchemaProvider>>();

        var tenantId = new TenantId("test-tenant");
        _provider = new PolicyMemberFieldsSchemaProvider(
            _schemaRepository.Object,
            _featureManager.Object,
            tenantId,
            _logger.Object);

        _fixture = new Fixture();
        _fixture.Customize<ProductId>(c => c.FromFactory(() => new ProductId("PLAN-001", "HEALTH", "1.0")));
        _fixture.Customize<EndorsementId>(c => c.FromFactory(() => EndorsementId.New));

        // Setup default feature flag behavior
        _featureManager.Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithAddressFieldWithoutProperties_ShouldUseDefaultAddressStructure()
    {
        // Arrange - Test case where address field has no properties defined
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateSchemaWithEmptyAddressField();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        PolicyMemberFieldDefinition? addressField = result.MemberFields.FirstOrDefault(f => f.Name == "address");
        addressField.Should().NotBeNull();
        addressField!.Type.Should().BeOfType<ObjectFieldType>(); // Should be ObjectFieldType when no properties
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithValidationRules_ShouldPreserveValidations()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateSchemaWithValidationRules();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Verify email field with email validation
        PolicyMemberFieldDefinition? emailField = result.MemberFields.FirstOrDefault(f => f.Name == "email");
        emailField.Should().NotBeNull();
        emailField!.Type.Should().BeOfType<StringFieldType>();
        var emailStringType = (StringFieldType)emailField.Type;
        emailStringType.Validations.Should().Be("email");

        // Verify required field validation
        PolicyMemberFieldDefinition? requiredField = result.MemberFields.FirstOrDefault(f => f.Name == "requiredField");
        requiredField.Should().NotBeNull();
        requiredField!.IsRequired.Should().BeTrue();
        var requiredStringType = (StringFieldType)requiredField.Type;
        requiredStringType.Validations.Should().Be("required");
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithComplexFieldTypes_ShouldDemonstrateFieldTypeParsing()
    {
        // Arrange - Demonstrate how different field types from complex schema pattern are handled
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = PolicyMemberFieldsSchemaProviderTestHelpers.CreateSchemaBasedOnComplexPattern();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert - Test how the schema pattern works through the provider
        result.Should().NotBeNull();

        // Verify text fields (based on complex schema pattern)
        PolicyMemberFieldDefinition? givenNameField = result.MemberFields.FirstOrDefault(f => f.Name == "givenName");
        givenNameField.Should().NotBeNull();
        givenNameField!.Type.Should().BeOfType<StringFieldType>();
        givenNameField.Label.Should().Be("Given Name");
        givenNameField.IsRequired.Should().BeTrue();

        // Verify formula field (fieldType: "dynamic" becomes FormulaFieldType)
        PolicyMemberFieldDefinition? fullNameField = result.MemberFields.FirstOrDefault(f => f.Name == "fullName");
        fullNameField.Should().NotBeNull();
        fullNameField!.Type.Should().BeOfType<FormulaFieldType>();
        fullNameField.Label.Should().Be("Name");
        fullNameField.IsRequired.Should().BeFalse();

        // Verify date field (fieldType: "date" becomes DateFieldType)
        PolicyMemberFieldDefinition? dateOfBirthField = result.MemberFields.FirstOrDefault(f => f.Name == "dateOfBirth");
        dateOfBirthField.Should().NotBeNull();
        dateOfBirthField!.Type.Should().BeOfType<DateFieldType>();
        dateOfBirthField.Label.Should().Be("Date of Birth");
        dateOfBirthField.IsRequired.Should().BeFalse();

        // Verify select field with options
        PolicyMemberFieldDefinition? memberTypeField = result.MemberFields.FirstOrDefault(f => f.Name == "memberType");
        memberTypeField.Should().NotBeNull();
        memberTypeField!.Type.Should().BeOfType<StringFieldType>();
        memberTypeField.Label.Should().Be("Member Type");
        memberTypeField.IsRequired.Should().BeTrue();
        var memberTypeStringType = (StringFieldType)memberTypeField.Type;
        memberTypeStringType.Options.Should().NotBeNull();
        memberTypeStringType.Options!.Should().HaveCount(2);
        memberTypeStringType.Options.Should().Contain(o => o.Value == "employee" && o.Label == "Employee");
        memberTypeStringType.Options.Should().Contain(o => o.Value == "dependent" && o.Label == "Dependent");

        // Verify address field (type: "address" becomes AddressFieldType)
        PolicyMemberFieldDefinition? addressField = result.MemberFields.FirstOrDefault(f => f.Name == "address");
        addressField.Should().NotBeNull();
        addressField!.Type.Should().BeOfType<AddressFieldType>();
        addressField.Label.Should().Be("Home Address");
        addressField.IsRequired.Should().BeFalse();
        var addressType = (AddressFieldType)addressField.Type;
        addressType.InnerFieldDefinitions.Should().HaveCount(2);
        addressType.InnerFieldDefinitions.Should().Contain(f => f.Name == "flatNumber" && f.Label == "Flat Number / Building");
        addressType.InnerFieldDefinitions.Should().Contain(f => f.Name == "streetNumberAndName" && f.Label == "Street Number & Street Name");

        // Verify system fields are added by the provider
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.PlanIdField);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.ClassField);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.MemberIdField);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.DependentOfField);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithAddressFieldSchema_ShouldParseCorrectly()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();

        string schema = """
        {
            "properties": {
                "address": {
                    "type": "address",
                    "meta": {
                        "label": "Home Address",
                        "component": "CAddressField",
                        "required": false,
                        "fieldType": "address",
                        "validations": "",
                        "formula": [],
                        "order": 17,
                        "propertyName": "address"
                    },
                    "properties": {
                        "flatNumber": {
                            "type": "string",
                            "meta": {
                                "label": "Flat Number / Building",
                                "required": false
                            }
                        },
                        "streetNumberAndName": {
                            "type": "string",
                            "meta": {
                                "label": "Street Number & Street Name",
                                "required": false
                            }
                        },
                        "district": {
                            "type": "string",
                            "meta": {
                                "label": "District",
                                "required": false
                            }
                        },
                        "region": {
                            "type": "string",
                            "meta": {
                                "label": "Region",
                                "required": false
                            }
                        }
                    }
                }
            }
        }
        """;

        // Act (When)
        CasesDataSchema? casesSchema = JsonSerializer.Deserialize<CasesDataSchema>(schema, JsonOptions);
        List<PolicyMemberFieldDefinition> fieldsDefinitions = PolicyMemberFieldsSchemaProviderTestHelpers.MapCustomFieldsToDefinitions(casesSchema!.Properties!);

        // Create schema with the parsed field
        PolicyMemberFieldsSchema baseSchema = new(
            fieldsDefinitions,
            productFields: null,
            censusFields: null,
            oneOfValidations: null
        );

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert (Then)
        fieldsDefinitions.Should().NotBeEmpty();
        fieldsDefinitions[0].Type.Should().BeOfType<AddressFieldType>();
        (fieldsDefinitions[0].Type as AddressFieldType)!.InnerFieldDefinitions.Should().HaveCount(4);
        (fieldsDefinitions[0].Type as AddressFieldType)!.InnerFieldDefinitions[0].Type.Should().BeOfType<StringFieldType>();

        // Verify the result from provider includes system fields
        result.Should().NotBeNull();
        result.MemberFields.Should().Contain(f => f.Name == "address" && f.Type is AddressFieldType);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.PlanIdField);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.ClassField);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.MemberIdField);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.DependentOfField);
    }
}
