using System.Reflection;
using CoverGo.BuildingBlocks.Domain.Core.Audit;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;
using CoverGo.PoliciesV3.Infrastructure.PolicyMembers;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Services;

public class PolicyMemberUniquenessServiceTests
{
    private readonly Mock<IPolicyMemberDataRepository> _mockDataRepository;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<ILogger<PolicyMemberUniquenessService>> _mockLogger;
    private readonly PolicyMemberUniquenessService _service;

    private readonly PolicyId _testPolicyId = Guid.NewGuid();
    private readonly EndorsementId _testEndorsementId = Guid.NewGuid();

    public PolicyMemberUniquenessServiceTests()
    {
        _mockDataRepository = new Mock<IPolicyMemberDataRepository>();
        _mockUsersService = new Mock<IUsersService>();
        _mockLogger = new Mock<ILogger<PolicyMemberUniquenessService>>();

        _service = new PolicyMemberUniquenessService(
            _mockDataRepository.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task ValidateTenantScopeUniquenessAsync_WithEmptyFieldNames_ShouldReturnEmpty()
    {
        // Arrange
        var fieldValues = new Dictionary<string, object> { { "email", "<EMAIL>" } };
        var fieldNames = new List<string>();
        var validEndorsementIds = new List<EndorsementId> { _testEndorsementId };

        // Act
        List<string> result = await _service.ValidateTenantScopeUniquenessAsync(
            _testPolicyId, null, null, fieldValues, fieldNames, validEndorsementIds);

        // Assert
        result.Should().BeEmpty();
        _mockDataRepository.Verify(x => x.GetByExcludingPolicyAsync(It.IsAny<PolicyId>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ValidateTenantScopeUniquenessAsync_WithValidFieldValues_ShouldReturnDuplicateFields()
    {
        // Arrange
        var fieldValues = new Dictionary<string, object> { { "email", "<EMAIL>" } };
        var fieldNames = new List<string> { "email" };
        var validEndorsementIds = new List<EndorsementId> { _testEndorsementId };

        PolicyMember existingMember = CreateTestPolicyMember("existing-member", _testPolicyId);
        existingMember.States.First().Fields.Add(new PolicyField { Key = "email", Value = "<EMAIL>" });

        _mockDataRepository.Setup(x => x.GetByExcludingPolicyAsync(_testPolicyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync([existingMember]);

        // Act
        List<string> result = await _service.ValidateTenantScopeUniquenessAsync(
            _testPolicyId, null, null, fieldValues, fieldNames, validEndorsementIds);

        // Assert
        result.Should().Contain("email");
    }

    [Fact]
    public async Task ValidateTenantScopeUniquenessAsync_WithCurrentMemberExclusion_ShouldReturnEmpty()
    {
        // Arrange
        string currentMemberId = "current-member";
        var fieldValues = new Dictionary<string, object> { { "email", "<EMAIL>" } };
        var fieldNames = new List<string> { "email" };
        var validEndorsementIds = new List<EndorsementId> { _testEndorsementId };

        PolicyMember existingMember = CreateTestPolicyMember(currentMemberId, _testPolicyId);
        existingMember.States.First().Fields.Add(new PolicyField { Key = "email", Value = "<EMAIL>" });

        _mockDataRepository.Setup(x => x.GetByExcludingPolicyAsync(_testPolicyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync([existingMember]);

        // Act
        List<string> result = await _service.ValidateTenantScopeUniquenessAsync(
            _testPolicyId, currentMemberId, null, fieldValues, fieldNames, validEndorsementIds);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task ValidatePolicyScopeUniquenessAsync_WithMatchingFieldInSamePolicy_ShouldReturnDuplicateField()
    {
        // Arrange
        var fieldValues = new Dictionary<string, object> { { "email", "<EMAIL>" } };
        var fieldNames = new List<string> { "email" };
        var validEndorsementIds = new List<EndorsementId> { _testEndorsementId };

        PolicyMember existingMember = CreateTestPolicyMember("existing-member", _testPolicyId);
        existingMember.States.First().Fields.Add(new PolicyField { Key = "email", Value = "<EMAIL>" });

        _mockDataRepository.Setup(x => x.GetByPolicyIdAsync(_testPolicyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync([existingMember]);

        // Act
        List<string> result = await _service.ValidatePolicyScopeUniquenessAsync(
            _testPolicyId, null, null, fieldValues, fieldNames, validEndorsementIds);

        // Assert
        result.Should().Contain("email");
    }

    [Fact]
    public async Task ValidateContractHolderScopeUniquenessAsync_WithApprovedMembers_ShouldReturnDuplicateFields()
    {
        // Arrange
        var contractHolderPolicyIds = new List<PolicyId> { _testPolicyId };
        var fieldValues = new Dictionary<string, object> { { "email", "<EMAIL>" } };
        var fieldNames = new List<string> { "email" };
        var validEndorsementIds = new List<EndorsementId> { _testEndorsementId };

        PolicyMember approvedMember = CreateTestPolicyMember("approved-member", _testPolicyId);
        approvedMember.States.First().Fields.Add(new PolicyField { Key = "email", Value = "<EMAIL>" });
        // Set up approved state
        approvedMember.States.First().UnderwritingResult = PolicyMemberUnderwritingResult.Approved;

        _mockDataRepository.Setup(x => x.GetByPolicyIdsAsync(contractHolderPolicyIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync([approvedMember]);

        // Act
        List<string> result = await _service.ValidateContractHolderScopeUniquenessAsync(
            null, null, contractHolderPolicyIds, validEndorsementIds, fieldValues, fieldNames);

        // Assert
        result.Should().Contain("email");
    }



    private PolicyMember CreateTestPolicyMember(string memberId, PolicyId policyId)
    {
        var member = PolicyMember.Create(
            policyId,
            memberId,
            DateOnly.FromDateTime(DateTime.Today),
            DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            "TEST-PLAN");

        // Set audit information using reflection since it's likely a private setter
        var auditInfo = new EntityAuditInfo("test-user", DateTime.UtcNow);
        PropertyInfo? auditProperty = typeof(PolicyMember).GetProperty(nameof(PolicyMember.EntityAuditInfo));
        auditProperty?.SetValue(member, auditInfo);

        return member;
    }
}