using System.Reflection;
using CoverGo.BuildingBlocks.Domain.Core.Audit;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Infrastructure.PolicyMembers;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Services;

public class PolicyMemberQueryServiceTests
{
    private readonly Mock<IPolicyMemberDataRepository> _mockDataRepository;
    private readonly Mock<ILogger<PolicyMemberQueryService>> _mockLogger;
    private readonly PolicyMemberQueryService _service;

    private readonly PolicyId _testPolicyId = Guid.NewGuid();
    private readonly PolicyId _testPolicyId2 = Guid.NewGuid();
    private readonly EndorsementId _testEndorsementId = Guid.NewGuid();

    public PolicyMemberQueryServiceTests()
    {
        _mockDataRepository = new Mock<IPolicyMemberDataRepository>();
        _mockLogger = new Mock<ILogger<PolicyMemberQueryService>>();

        _service = new PolicyMemberQueryService(
            _mockDataRepository.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task GetActiveMembersAsync_WithEmptyPolicyIds_ShouldReturnEmpty()
    {
        // Arrange
        var policyIds = new List<PolicyId>();

        // Act
        List<PolicyMember> result = await _service.GetActiveMembersAsync(policyIds);

        // Assert
        result.Should().BeEmpty();
        _mockDataRepository.Verify(x => x.GetByPolicyIdsAsync(It.IsAny<List<PolicyId>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetActiveMembersAsync_WithValidPolicyIds_ShouldReturnActiveMembers()
    {
        // Arrange
        var policyIds = new List<PolicyId> { _testPolicyId };

        PolicyMember activeMember = CreateTestPolicyMember("active-member", _testPolicyId);
        activeMember.States.First().UnderwritingResult = PolicyMemberUnderwritingResult.Approved;
        // Set endorsement ID for active member
        PropertyInfo? activeEndorsementProperty = typeof(PolicyMemberState).GetProperty("EndorsementId");
        activeEndorsementProperty?.SetValue(activeMember.States.First(), _testEndorsementId);

        PolicyMember inactiveMember = CreateTestPolicyMember("inactive-member", _testPolicyId);
        // Ensure the inactive member has a rejected underwriting result and an endorsement ID
        inactiveMember.States.First().UnderwritingResult = PolicyMemberUnderwritingResult.Rejected;
        PropertyInfo? endorsementProperty = typeof(PolicyMemberState).GetProperty("EndorsementId");
        endorsementProperty?.SetValue(inactiveMember.States.First(), _testEndorsementId);

        PolicyMember removedMember = CreateTestPolicyMember("removed-member", _testPolicyId);
        // Mark as removed by setting IsRemoved property
        PropertyInfo? removeProperty = typeof(PolicyMember).GetProperty("IsRemoved");
        removeProperty?.SetValue(removedMember, true);

        _mockDataRepository.Setup(x => x.GetByPolicyIdsAsync(policyIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync([activeMember, inactiveMember, removedMember]);

        // Act
        List<PolicyMember> result = await _service.GetActiveMembersAsync(policyIds);

        // Assert
        result.Should().HaveCount(1);
        result.Should().Contain(activeMember);
        result.Should().NotContain(inactiveMember);
        result.Should().NotContain(removedMember);
    }

    [Fact]
    public async Task GetActiveMembersAsync_WithRemovedMembers_ShouldExcludeRemovedMembers()
    {
        // Arrange
        var policyIds = new List<PolicyId> { _testPolicyId };

        PolicyMember activeMember = CreateTestPolicyMember("active-member", _testPolicyId);
        PolicyMember removedMember = CreateTestPolicyMember("removed-member", _testPolicyId);
        // Mark as removed by setting IsRemoved property
        PropertyInfo? removeProperty = typeof(PolicyMember).GetProperty("IsRemoved");
        removeProperty?.SetValue(removedMember, true);

        _mockDataRepository.Setup(x => x.GetByPolicyIdsAsync(policyIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync([activeMember, removedMember]);

        // Act
        List<PolicyMember> result = await _service.GetActiveMembersAsync(policyIds);

        // Assert
        result.Should().HaveCount(1);
        result.Should().Contain(activeMember);
        result.Should().NotContain(removedMember);
    }

    [Fact]
    public async Task GetMemberValidationStatesAsync_WithNullMemberId_ShouldReturnNull()
    {
        // Arrange
        var policyIds = new List<PolicyId> { _testPolicyId };
        var validEndorsementIds = new List<EndorsementId> { _testEndorsementId };

        // Act
        List<PolicyMember>? result = await _service.GetMemberValidationStatesAsync(null!, policyIds, validEndorsementIds, CancellationToken.None);

        // Assert
        result.Should().BeNull();
        _mockDataRepository.Verify(x => x.GetByMemberIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetMemberValidationStatesAsync_WithValidParameters_ShouldReturnLatestMemberPerPolicy()
    {
        // Arrange
        string memberId = "test-member";
        var policyIds = new List<PolicyId> { _testPolicyId, _testPolicyId2 };
        var validEndorsementIds = new List<EndorsementId> { _testEndorsementId };

        PolicyMember member1Policy1 = CreateTestPolicyMember(memberId, _testPolicyId);
        PolicyMember member2Policy1 = CreateTestPolicyMember(memberId, _testPolicyId);

        PolicyMember member1Policy2 = CreateTestPolicyMember(memberId, _testPolicyId2);

        _mockDataRepository.Setup(x => x.GetByMemberIdAsync(memberId, It.IsAny<CancellationToken>()))
            .ReturnsAsync([member1Policy1, member2Policy1, member1Policy2]);

        // Act
        List<PolicyMember>? result = await _service.GetMemberValidationStatesAsync(memberId, policyIds, validEndorsementIds, CancellationToken.None);

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain(member2Policy1); // Latest for policy 1
        result.Should().Contain(member1Policy2); // Only one for policy 2
        result.Should().NotContain(member1Policy1); // Older version
    }

    [Fact]
    public async Task GetPolicyMemberCurrentStateAsync_WithValidParameters_ShouldReturnMostRecentMember()
    {
        // Arrange
        string memberId = "test-member";
        var validEndorsementIds = new List<EndorsementId> { _testEndorsementId };

        PolicyMember olderMember = CreateTestPolicyMember(memberId, _testPolicyId);
        PolicyMember newerMember = CreateTestPolicyMember(memberId, _testPolicyId);

        _mockDataRepository.Setup(x => x.GetByMemberIdAsync(memberId, It.IsAny<CancellationToken>()))
            .ReturnsAsync([olderMember, newerMember]);

        // Act
        PolicyMember? result = await _service.GetPolicyMemberCurrentStateAsync(memberId, _testPolicyId, validEndorsementIds, CancellationToken.None);

        // Assert
        result.Should().Be(newerMember);
    }

    [Fact]
    public async Task GetMemberValidationStatesByMemberIdAsync_WithValidMemberId_ShouldReturnLatestMemberPerPolicy()
    {
        // Arrange
        string memberId = "test-member";

        PolicyMember member1Policy1 = CreateTestPolicyMember(memberId, _testPolicyId);
        PolicyMember member2Policy1 = CreateTestPolicyMember(memberId, _testPolicyId);
        PolicyMember member1Policy2 = CreateTestPolicyMember(memberId, _testPolicyId2);

        _mockDataRepository.Setup(x => x.GetByMemberIdAsync(memberId, It.IsAny<CancellationToken>()))
            .ReturnsAsync([member1Policy1, member2Policy1, member1Policy2]);

        // Act
        List<PolicyMember> result = await _service.GetMemberValidationStatesByMemberIdAsync(memberId, CancellationToken.None);

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain(member2Policy1); // Latest for policy 1
        result.Should().Contain(member1Policy2); // Only one for policy 2
    }

    [Fact]
    public async Task GetPolicyMembersBatchAsync_WithEmptyMemberIds_ShouldReturnEmpty()
    {
        // Arrange
        var memberIds = new List<string>();
        var validEndorsementIds = new List<EndorsementId> { _testEndorsementId };

        // Act
        Dictionary<string, PolicyMember?> result = await _service.GetPolicyMembersBatchAsync(memberIds, _testPolicyId, validEndorsementIds);

        // Assert
        result.Should().BeEmpty();
        _mockDataRepository.Verify(x => x.GetByMemberIdsAndPolicyAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetPolicyMembersBatchAsync_WithValidMemberIds_ShouldReturnMostRecentForEachMember()
    {
        // Arrange
        var memberIds = new List<string> { "member1", "member2", "member3" };
        var validEndorsementIds = new List<EndorsementId> { _testEndorsementId };

        PolicyMember member1Old = CreateTestPolicyMember("member1", _testPolicyId);
        PolicyMember member1New = CreateTestPolicyMember("member1", _testPolicyId);

        PolicyMember member2 = CreateTestPolicyMember("member2", _testPolicyId);

        _mockDataRepository.Setup(x => x.GetByMemberIdsAndPolicyAsync(memberIds, _testPolicyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync([member1Old, member1New, member2]);

        // Act
        Dictionary<string, PolicyMember?> result = await _service.GetPolicyMembersBatchAsync(memberIds, _testPolicyId, validEndorsementIds);

        // Assert
        result.Should().HaveCount(3);
        result["member1"].Should().Be(member1New); // Most recent
        result["member2"].Should().Be(member2);
        result["member3"].Should().BeNull(); // Not found
    }

    private PolicyMember CreateTestPolicyMember(string memberId, PolicyId policyId)
    {
        var member = PolicyMember.Create(
            policyId,
            memberId,
            DateOnly.FromDateTime(DateTime.Today),
            DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            "TEST-PLAN");

        // Set audit information using reflection since it's likely a private setter
        var auditInfo = new EntityAuditInfo("test-user", DateTime.UtcNow);
        PropertyInfo? auditProperty = typeof(PolicyMember).GetProperty(nameof(PolicyMember.EntityAuditInfo));
        auditProperty?.SetValue(member, auditInfo);

        return member;
    }
}