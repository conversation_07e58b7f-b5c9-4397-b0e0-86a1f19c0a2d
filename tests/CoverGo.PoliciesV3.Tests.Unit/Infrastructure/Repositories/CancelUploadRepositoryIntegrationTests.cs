using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using System.Reflection;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using Microsoft.EntityFrameworkCore;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Repositories;

/// <summary>
/// Integration tests that verify repository methods work together correctly
/// in the context of the CancelPolicyMemberUploadHandler workflow
/// </summary>
public class CancelUploadRepositoryIntegrationTests
{
    private readonly Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>> _mockUploadRepository;
    private readonly Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> _mockMemberRepository;
    private readonly Fixture _fixture;

    public CancelUploadRepositoryIntegrationTests()
    {
        _mockUploadRepository = new Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>>();
        _mockMemberRepository = new Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>>();
        _fixture = new Fixture();
        _fixture.Customize<PolicyMemberId>(c => c.FromFactory(() => PolicyMemberId.New));
        _fixture.Customize<PolicyMemberUploadId>(c => c.FromFactory(() => PolicyMemberUploadId.New));
        _fixture.Customize<PolicyId>(c => c.FromFactory(() => PolicyId.New));
        _fixture.Customize<DateOnly>(c => c.FromFactory(() => DateOnly.FromDateTime(DateTime.Today.AddDays(_fixture.Create<int>() % 365))));
    }

    [Fact]
    public async Task CancelUploadWorkflow_WithSuccessfulImportedMembers_ShouldCompleteFullWorkflow()
    {
        // Arrange
        PolicyMemberUploadId? uploadId = _fixture.Create<PolicyMemberUploadId>();
        PolicyMemberUpload upload = CreateUploadWithImportedMembers(uploadId, PolicyMemberUploadStatus.CANCELING);
        var importedMemberIds = upload.ImportedResults
            .Where(r => r.Success && r.PolicyMemberId != null)
            .Select(r => r.PolicyMemberId!)
            .ToList();
        var policyMembers = importedMemberIds.Select(id => CreatePolicyMember(id, false)).ToList();

        SetupRepositoryMocks(upload, policyMembers, importedMemberIds);

        // Act - Simulate the cancellation workflow
        // Step 1: Find the upload
        PolicyMemberUpload foundUpload = await _mockUploadRepository.Object.FindByIdAsync(uploadId, CancellationToken.None);

        // Step 2: Get imported member IDs and find the members
        var memberIds = foundUpload.ImportedResults
            .Where(r => r.Success && r.PolicyMemberId != null)
            .Select(r => r.PolicyMemberId!)
            .ToList();
        List<PolicyMember> members = await _mockMemberRepository.Object.FindAllAsync(memberIds, CancellationToken.None);

        // Step 3: Mark members as removed and update them
        foreach (PolicyMember member in members)
        {
            member.MarkAsRemoved();
            await _mockMemberRepository.Object.UpdateAsync(member, CancellationToken.None);
        }

        // Step 4: Clear imported results and update upload
        foundUpload.ImportedResults.Clear();
        foundUpload.CompleteCancellation();
        await _mockUploadRepository.Object.UpdateAsync(foundUpload, CancellationToken.None);

        // Assert
        foundUpload.Status.Should().Be(PolicyMemberUploadStatus.CANCELED);
        foundUpload.ImportedResults.Should().BeEmpty();
        members.Should().AllSatisfy(member => member.IsRemoved.Should().BeTrue());

        // Verify all repository calls were made
        _mockUploadRepository.Verify(x => x.FindByIdAsync(uploadId, It.IsAny<CancellationToken>()), Times.Once);
        _mockMemberRepository.Verify(x => x.FindAllAsync(memberIds, It.IsAny<CancellationToken>()), Times.Once);
        _mockMemberRepository.Verify(x => x.UpdateAsync(It.IsAny<PolicyMember>(), It.IsAny<CancellationToken>()), Times.Exactly(members.Count));
        _mockUploadRepository.Verify(x => x.UpdateAsync(foundUpload, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CancelUploadWorkflow_WithNoImportedMembers_ShouldOnlyUpdateUploadStatus()
    {
        // Arrange
        PolicyMemberUploadId? uploadId = _fixture.Create<PolicyMemberUploadId>();
        PolicyMemberUpload upload = CreateUploadWithoutImportedMembers(uploadId, PolicyMemberUploadStatus.CANCELING);

        _mockUploadRepository.Setup(x => x.FindByIdAsync(uploadId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);
        _mockUploadRepository.Setup(x => x.UpdateAsync(upload, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMemberUpload u, CancellationToken _) => u);

        // Act - Simulate the cancellation workflow
        PolicyMemberUpload foundUpload = await _mockUploadRepository.Object.FindByIdAsync(uploadId, CancellationToken.None);

        var memberIds = foundUpload.ImportedResults
            .Where(r => r.Success && r.PolicyMemberId != null)
            .Select(r => r.PolicyMemberId!)
            .ToList();

        // No members to process since list is empty
        if (memberIds.Count > 0)
        {
            await _mockMemberRepository.Object.FindAllAsync(memberIds, CancellationToken.None);
        }

        foundUpload.CompleteCancellation();
        await _mockUploadRepository.Object.UpdateAsync(foundUpload, CancellationToken.None);

        // Assert
        foundUpload.Status.Should().Be(PolicyMemberUploadStatus.CANCELED);
        memberIds.Should().BeEmpty();

        // Verify repository calls
        _mockUploadRepository.Verify(x => x.FindByIdAsync(uploadId, It.IsAny<CancellationToken>()), Times.Once);
        _mockMemberRepository.Verify(x => x.FindAllAsync(It.IsAny<List<PolicyMemberId>>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockUploadRepository.Verify(x => x.UpdateAsync(foundUpload, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CancelUploadWorkflow_WithAlreadyCanceledUpload_ShouldSkipProcessing()
    {
        // Arrange
        PolicyMemberUploadId? uploadId = _fixture.Create<PolicyMemberUploadId>();
        PolicyMemberUpload upload = CreateUploadWithImportedMembers(uploadId, PolicyMemberUploadStatus.CANCELED);

        _mockUploadRepository.Setup(x => x.FindByIdAsync(uploadId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        // Act - Simulate the fail-fast behavior for already canceled uploads
        PolicyMemberUpload foundUpload = await _mockUploadRepository.Object.FindByIdAsync(uploadId, CancellationToken.None);

        // Check status and return early (fail-fast pattern)
        if (foundUpload.Status == PolicyMemberUploadStatus.CANCELED)
        {
            // No further processing needed
        }

        // Assert
        foundUpload.Status.Should().Be(PolicyMemberUploadStatus.CANCELED);

        // Verify only the find operation was called
        _mockUploadRepository.Verify(x => x.FindByIdAsync(uploadId, It.IsAny<CancellationToken>()), Times.Once);
        _mockMemberRepository.Verify(x => x.FindAllAsync(It.IsAny<List<PolicyMemberId>>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockUploadRepository.Verify(x => x.UpdateAsync(It.IsAny<PolicyMemberUpload>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task CancelUploadWorkflow_WithMixedSuccessAndFailedImports_ShouldOnlyProcessSuccessfulOnes()
    {
        // Arrange
        PolicyMemberUploadId? uploadId = _fixture.Create<PolicyMemberUploadId>();
        PolicyMemberUpload upload = CreateUploadWithMixedImportResults(uploadId, PolicyMemberUploadStatus.CANCELING);

        var successfulMemberIds = upload.ImportedResults
            .Where(r => r.Success && r.PolicyMemberId != null)
            .Select(r => r.PolicyMemberId!)
            .ToList();
        var successfulMembers = successfulMemberIds.Select(id => CreatePolicyMember(id, false)).ToList();

        SetupRepositoryMocks(upload, successfulMembers, successfulMemberIds);

        // Act - Simulate the cancellation workflow
        PolicyMemberUpload foundUpload = await _mockUploadRepository.Object.FindByIdAsync(uploadId, CancellationToken.None);

        var memberIds = foundUpload.ImportedResults
            .Where(r => r.Success && r.PolicyMemberId != null)
            .Select(r => r.PolicyMemberId!)
            .ToList();

        List<PolicyMember> members = await _mockMemberRepository.Object.FindAllAsync(memberIds, CancellationToken.None);

        foreach (PolicyMember member in members)
        {
            member.MarkAsRemoved();
            await _mockMemberRepository.Object.UpdateAsync(member, CancellationToken.None);
        }

        foundUpload.ImportedResults.Clear();
        foundUpload.CompleteCancellation();
        await _mockUploadRepository.Object.UpdateAsync(foundUpload, CancellationToken.None);

        // Assert
        foundUpload.Status.Should().Be(PolicyMemberUploadStatus.CANCELED);
        foundUpload.ImportedResults.Should().BeEmpty();
        members.Should().HaveCount(successfulMemberIds.Count);
        members.Should().AllSatisfy(member => member.IsRemoved.Should().BeTrue());

        // Verify repository calls
        _mockUploadRepository.Verify(x => x.FindByIdAsync(uploadId, It.IsAny<CancellationToken>()), Times.Once);
        _mockMemberRepository.Verify(x => x.FindAllAsync(successfulMemberIds, It.IsAny<CancellationToken>()), Times.Once);
        _mockMemberRepository.Verify(x => x.UpdateAsync(It.IsAny<PolicyMember>(), It.IsAny<CancellationToken>()), Times.Exactly(successfulMembers.Count));
        _mockUploadRepository.Verify(x => x.UpdateAsync(foundUpload, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CancelUploadWorkflow_WithConcurrencyConflictOnUploadUpdate_ShouldThrowException()
    {
        // Arrange
        PolicyMemberUploadId? uploadId = _fixture.Create<PolicyMemberUploadId>();
        PolicyMemberUpload upload = CreateUploadWithImportedMembers(uploadId, PolicyMemberUploadStatus.CANCELING);

        _mockUploadRepository.Setup(x => x.FindByIdAsync(uploadId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);
        _mockUploadRepository.Setup(x => x.UpdateAsync(upload, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DbUpdateConcurrencyException("Concurrency conflict"));

        // Act & Assert
        PolicyMemberUpload foundUpload = await _mockUploadRepository.Object.FindByIdAsync(uploadId, CancellationToken.None);
        foundUpload.CompleteCancellation();

        await Assert.ThrowsAsync<DbUpdateConcurrencyException>(
            () => _mockUploadRepository.Object.UpdateAsync(foundUpload, CancellationToken.None));
    }

    [Fact]
    public async Task CancelUploadWorkflow_WithConcurrencyConflictOnMemberUpdate_ShouldThrowException()
    {
        // Arrange
        PolicyMemberUploadId? uploadId = _fixture.Create<PolicyMemberUploadId>();
        PolicyMemberUpload upload = CreateUploadWithImportedMembers(uploadId, PolicyMemberUploadStatus.CANCELING);
        var memberIds = upload.ImportedResults
            .Where(r => r.Success && r.PolicyMemberId != null)
            .Select(r => r.PolicyMemberId!)
            .ToList();
        var members = memberIds.Select(id => CreatePolicyMember(id, false)).ToList();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(uploadId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);
        _mockMemberRepository.Setup(x => x.FindAllAsync(memberIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(members);
        _mockMemberRepository.Setup(x => x.UpdateAsync(It.IsAny<PolicyMember>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DbUpdateConcurrencyException("Member concurrency conflict"));

        // Act & Assert
        await _mockUploadRepository.Object.FindByIdAsync(uploadId, CancellationToken.None);
        List<PolicyMember> foundMembers = await _mockMemberRepository.Object.FindAllAsync(memberIds, CancellationToken.None);

        PolicyMember firstMember = foundMembers.First();
        firstMember.MarkAsRemoved();

        await Assert.ThrowsAsync<DbUpdateConcurrencyException>(
            () => _mockMemberRepository.Object.UpdateAsync(firstMember, CancellationToken.None));
    }

    #region Helper Methods

    private PolicyMemberUpload CreateUploadWithImportedMembers(PolicyMemberUploadId id, PolicyMemberUploadStatus status)
    {
        var upload = PolicyMemberUpload.Create(
            _fixture.Create<PolicyId>(),
            "uploads/test-file.csv",
            100);

        // Use reflection to set the ID and status for testing
        SetUploadIdAndStatus(upload, id, status);

        upload.ImportedResults.Clear();

        // Add successful imported results
        for (int i = 0; i < 3; i++)
        {
            var importedResult = PolicyMemberUploadImportedResult.CreateSuccess(
                upload.Id, i + 1, _fixture.Create<PolicyMemberId>());
            upload.ImportedResults.Add(importedResult);
        }

        return upload;
    }

    private PolicyMemberUpload CreateUploadWithoutImportedMembers(PolicyMemberUploadId id, PolicyMemberUploadStatus status)
    {
        var upload = PolicyMemberUpload.Create(
            _fixture.Create<PolicyId>(),
            "uploads/test-file.csv",
            100);

        // Use reflection to set the ID and status for testing
        SetUploadIdAndStatus(upload, id, status);

        upload.ImportedResults.Clear();
        return upload;
    }

    private PolicyMemberUpload CreateUploadWithMixedImportResults(PolicyMemberUploadId id, PolicyMemberUploadStatus status)
    {
        var upload = PolicyMemberUpload.Create(
            _fixture.Create<PolicyId>(),
            "uploads/test-file.csv",
            100);

        // Use reflection to set the ID and status for testing
        SetUploadIdAndStatus(upload, id, status);

        upload.ImportedResults.Clear();

        // Add successful imported results
        for (int i = 0; i < 2; i++)
        {
            var successResult = PolicyMemberUploadImportedResult.CreateSuccess(
                upload.Id, i + 1, _fixture.Create<PolicyMemberId>());
            upload.ImportedResults.Add(successResult);
        }

        // Add failed imported results
        for (int i = 0; i < 2; i++)
        {
            var failedResult = PolicyMemberUploadImportedResult.CreateFailure(
                upload.Id, i + 3, "VALIDATION_ERROR", "Validation failed");
            upload.ImportedResults.Add(failedResult);
        }

        return upload;
    }

    private PolicyMember CreatePolicyMember(PolicyMemberId id, bool isRemoved)
    {
        var member = PolicyMember.Create(
            _fixture.Create<PolicyId>(),
            $"MEMBER-{id.Value}",
            DateOnly.FromDateTime(DateTime.Today),
            DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            "TEST-PLAN");

        // Use reflection to set the ID to match the requested ID
        PropertyInfo? idProperty = typeof(PolicyMember).BaseType?.GetProperty("Id");
        if (idProperty != null && idProperty.CanWrite)
        {
            idProperty.SetValue(member, id);
        }
        else
        {
            FieldInfo? idField = typeof(PolicyMember).BaseType?.GetField("_id",
                BindingFlags.NonPublic | BindingFlags.Instance);
            idField?.SetValue(member, id);
        }

        // Set IsRemoved state if needed
        if (isRemoved)
        {
            member.MarkAsRemoved();
        }

        return member;
    }

    private void SetUploadIdAndStatus(PolicyMemberUpload upload, PolicyMemberUploadId id, PolicyMemberUploadStatus status)
    {
        // Use reflection to set the ID to match the requested ID
        PropertyInfo? idProperty = typeof(PolicyMemberUpload).BaseType?.GetProperty("Id");
        if (idProperty != null && idProperty.CanWrite)
        {
            idProperty.SetValue(upload, id);
        }
        else
        {
            FieldInfo? idField = typeof(PolicyMemberUpload).BaseType?.GetField("_id",
                BindingFlags.NonPublic | BindingFlags.Instance);
            idField?.SetValue(upload, id);
        }

        // Set status using reflection
        PropertyInfo? statusProperty = typeof(PolicyMemberUpload).GetProperty("Status");
        statusProperty?.SetValue(upload, status);
    }

    private void SetupRepositoryMocks(PolicyMemberUpload upload, List<PolicyMember> members, List<PolicyMemberId> memberIds)
    {
        _mockUploadRepository.Setup(x => x.FindByIdAsync(upload.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);
        _mockUploadRepository.Setup(x => x.UpdateAsync(upload, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMemberUpload u, CancellationToken _) => u);

        _mockMemberRepository.Setup(x => x.FindAllAsync(memberIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(members);
        _mockMemberRepository.Setup(x => x.UpdateAsync(It.IsAny<PolicyMember>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMember m, CancellationToken _) => m);
    }

    #endregion
}
