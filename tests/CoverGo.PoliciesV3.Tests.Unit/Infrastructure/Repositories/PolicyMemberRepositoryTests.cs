using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using System.Reflection;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using Microsoft.EntityFrameworkCore;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Repositories;

public class PolicyMemberRepositoryTests
{
    private readonly Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> _mockRepository;
    private readonly Fixture _fixture;

    public PolicyMemberRepositoryTests()
    {
        _mockRepository = new Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>>();
        _fixture = new Fixture();
        _fixture.Customize<PolicyMemberId>(c => c.FromFactory(() => PolicyMemberId.New));
        _fixture.Customize<PolicyId>(c => c.FromFactory(() => PolicyId.New));
    }

    #region FindAllAsync Tests for CancelUpload Handler

    [Fact]
    public async Task FindAllAsync_ForCancelUploadHandler_WithValidPolicyMemberIds_ShouldReturnMembers()
    {
        // Arrange
        var memberIds = _fixture.CreateMany<PolicyMemberId>(3).ToList();
        var expectedMembers = memberIds.Select(id => CreatePolicyMember(id, false)).ToList();

        _mockRepository.Setup(x => x.FindAllAsync(memberIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedMembers);

        // Act
        List<PolicyMember> result = await _mockRepository.Object.FindAllAsync(memberIds, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(3);
        result.Select(x => x.Id).Should().BeEquivalentTo(memberIds);
        result.Should().AllSatisfy(member => member.IsRemoved.Should().BeFalse());
    }

    [Fact]
    public async Task FindAllAsync_ForCancelUploadHandler_WithEmptyIdsList_ShouldReturnEmptyList()
    {
        // Arrange
        var emptyIds = new List<PolicyMemberId>();

        _mockRepository.Setup(x => x.FindAllAsync(emptyIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<PolicyMember>());

        // Act
        List<PolicyMember> result = await _mockRepository.Object.FindAllAsync(emptyIds, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task FindAllAsync_ForCancelUploadHandler_WithNonExistentIds_ShouldReturnEmptyList()
    {
        // Arrange
        var nonExistentIds = _fixture.CreateMany<PolicyMemberId>(3).ToList();

        _mockRepository.Setup(x => x.FindAllAsync(nonExistentIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<PolicyMember>());

        // Act
        List<PolicyMember> result = await _mockRepository.Object.FindAllAsync(nonExistentIds, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task FindAllAsync_ForCancelUploadHandler_WithMixedExistentAndNonExistentIds_ShouldReturnOnlyExistentMembers()
    {
        // Arrange
        var existentIds = _fixture.CreateMany<PolicyMemberId>(2).ToList();
        var nonExistentIds = _fixture.CreateMany<PolicyMemberId>(2).ToList();
        var allIds = existentIds.Concat(nonExistentIds).ToList();

        var existentMembers = existentIds.Select(id => CreatePolicyMember(id, false)).ToList();

        _mockRepository.Setup(x => x.FindAllAsync(allIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existentMembers);

        // Act
        List<PolicyMember> result = await _mockRepository.Object.FindAllAsync(allIds, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Select(x => x.Id).Should().BeEquivalentTo(existentIds);
    }

    [Fact]
    public async Task FindAllAsync_ForCancelUploadHandler_WithCancellationToken_ShouldPassTokenToRepository()
    {
        // Arrange
        var memberIds = _fixture.CreateMany<PolicyMemberId>(2).ToList();
        var cancellationToken = new CancellationToken(true);

        _mockRepository.Setup(x => x.FindAllAsync(memberIds, cancellationToken))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _mockRepository.Object.FindAllAsync(memberIds, cancellationToken));

        _mockRepository.Verify(x => x.FindAllAsync(memberIds, cancellationToken), Times.Once);
    }

    #endregion

    #region UpdateAsync Tests for CancelUpload Handler

    [Fact]
    public async Task UpdateAsync_ForCancelUploadHandler_WithMarkedAsRemovedMember_ShouldUpdateSuccessfully()
    {
        // Arrange
        PolicyMember member = CreatePolicyMember(PolicyMemberId.New, false);
        member.MarkAsRemoved(); // This is what the cancel handler does

        _mockRepository.Setup(x => x.UpdateAsync(member, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMember m, CancellationToken _) => m);

        // Act
        await _mockRepository.Object.UpdateAsync(member, CancellationToken.None);

        // Assert
        member.IsRemoved.Should().BeTrue();
        _mockRepository.Verify(x => x.UpdateAsync(member, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateAsync_ForCancelUploadHandler_WithMultipleMembers_ShouldUpdateEachMember()
    {
        // Arrange
        var members = _fixture.CreateMany<PolicyMemberId>(3)
            .Select(id => CreatePolicyMember(id, false))
            .ToList();

        // Mark all as removed (simulating the cancel handler behavior)
        foreach (PolicyMember member in members)
        {
            member.MarkAsRemoved();
        }

        _mockRepository.Setup(x => x.UpdateAsync(It.IsAny<PolicyMember>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMember m, CancellationToken _) => m);

        // Act
        foreach (PolicyMember member in members)
        {
            await _mockRepository.Object.UpdateAsync(member, CancellationToken.None);
        }

        // Assert
        members.Should().AllSatisfy(member => member.IsRemoved.Should().BeTrue());
        _mockRepository.Verify(x => x.UpdateAsync(It.IsAny<PolicyMember>(), It.IsAny<CancellationToken>()), Times.Exactly(3));
    }

    [Fact]
    public async Task UpdateAsync_ForCancelUploadHandler_WithConcurrencyConflict_ShouldThrowException()
    {
        // Arrange
        PolicyMember member = CreatePolicyMember(PolicyMemberId.New, false);
        member.MarkAsRemoved();

        _mockRepository.Setup(x => x.UpdateAsync(member, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DbUpdateConcurrencyException("Concurrency conflict"));

        // Act & Assert
        await Assert.ThrowsAsync<DbUpdateConcurrencyException>(
            () => _mockRepository.Object.UpdateAsync(member, CancellationToken.None));
    }

    [Fact]
    public async Task UpdateAsync_ForCancelUploadHandler_WithNullMember_ShouldThrowArgumentNullException()
    {
        // Arrange
        _mockRepository.Setup(x => x.UpdateAsync(null!, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new ArgumentNullException());

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _mockRepository.Object.UpdateAsync(null!, CancellationToken.None));
    }

    [Fact]
    public async Task UpdateAsync_ForCancelUploadHandler_WithCancellationToken_ShouldPassTokenToRepository()
    {
        // Arrange
        PolicyMember member = CreatePolicyMember(PolicyMemberId.New, false);
        member.MarkAsRemoved();
        var cancellationToken = new CancellationToken(true);

        _mockRepository.Setup(x => x.UpdateAsync(member, cancellationToken))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _mockRepository.Object.UpdateAsync(member, cancellationToken));

        _mockRepository.Verify(x => x.UpdateAsync(member, cancellationToken), Times.Once);
    }

    #endregion

    #region Batch Operations Tests for CancelUpload Handler

    [Fact]
    public async Task UpdateBatchAsync_ForCancelUploadHandler_WithMultipleMarkedAsRemovedMembers_ShouldUpdateAllSuccessfully()
    {
        // Arrange
        var members = _fixture.CreateMany<PolicyMemberId>(5)
            .Select(id => CreatePolicyMember(id, false))
            .ToList();

        // Mark all as removed (simulating bulk cancellation)
        foreach (PolicyMember member in members)
        {
            member.MarkAsRemoved();
        }

        _mockRepository.Setup(x => x.UpdateBatchAsync(members, It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _mockRepository.Object.UpdateBatchAsync(members, CancellationToken.None);

        // Assert
        members.Should().AllSatisfy(member => member.IsRemoved.Should().BeTrue());
        _mockRepository.Verify(x => x.UpdateBatchAsync(members, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateBatchAsync_ForCancelUploadHandler_WithEmptyList_ShouldCompleteSuccessfully()
    {
        // Arrange
        var emptyMembers = new List<PolicyMember>();

        _mockRepository.Setup(x => x.UpdateBatchAsync(emptyMembers, It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _mockRepository.Object.UpdateBatchAsync(emptyMembers, CancellationToken.None);

        // Assert
        _mockRepository.Verify(x => x.UpdateBatchAsync(emptyMembers, It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region Performance and Edge Case Tests

    [Fact]
    public async Task FindAllAsync_ForCancelUploadHandler_WithLargeNumberOfIds_ShouldHandleEfficiently()
    {
        // Arrange
        var largeNumberOfIds = _fixture.CreateMany<PolicyMemberId>(1000).ToList();
        var expectedMembers = largeNumberOfIds.Select(id => CreatePolicyMember(id, false)).ToList();

        _mockRepository.Setup(x => x.FindAllAsync(largeNumberOfIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedMembers);

        // Act
        List<PolicyMember> result = await _mockRepository.Object.FindAllAsync(largeNumberOfIds, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1000);
        result.Select(x => x.Id).Should().BeEquivalentTo(largeNumberOfIds);
    }

    [Fact]
    public async Task UpdateAsync_ForCancelUploadHandler_WithAlreadyRemovedMember_ShouldRemainRemoved()
    {
        // Arrange
        PolicyMember member = CreatePolicyMember(PolicyMemberId.New, true); // Already removed
        member.MarkAsRemoved(); // Call again (idempotent operation)

        _mockRepository.Setup(x => x.UpdateAsync(member, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMember m, CancellationToken _) => m);

        // Act
        await _mockRepository.Object.UpdateAsync(member, CancellationToken.None);

        // Assert
        member.IsRemoved.Should().BeTrue();
        _mockRepository.Verify(x => x.UpdateAsync(member, It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region Helper Methods

    private PolicyMember CreatePolicyMember(PolicyMemberId id, bool isRemoved)
    {
        var member = PolicyMember.Create(
            _fixture.Create<PolicyId>(),
            $"MEMBER-{id.Value}",
            DateOnly.FromDateTime(DateTime.Today),
            DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            "TEST-PLAN");

        // Use reflection to set the ID to match the requested ID
        PropertyInfo? idProperty = typeof(PolicyMember).BaseType?.GetProperty("Id");
        if (idProperty != null && idProperty.CanWrite)
        {
            idProperty.SetValue(member, id);
        }
        else
        {
            // Try to access the private field if property is not writable
            FieldInfo? idField = typeof(PolicyMember).BaseType?.GetField("_id",
                BindingFlags.NonPublic | BindingFlags.Instance);
            idField?.SetValue(member, id);
        }

        // Set IsRemoved state if needed
        if (isRemoved)
        {
            member.MarkAsRemoved();
        }

        return member;
    }

    #endregion
}
