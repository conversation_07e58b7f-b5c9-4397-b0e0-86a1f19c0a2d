using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using System.Reflection;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using Microsoft.EntityFrameworkCore;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Repositories;

public class PostgreSqlRepositoryTests
{
    private readonly Fixture _fixture;

    public PostgreSqlRepositoryTests()
    {
        _fixture = new Fixture();
        _fixture.Customize<PolicyMemberId>(c => c.FromFactory(() => PolicyMemberId.New));
        _fixture.Customize<PolicyMemberUploadId>(c => c.FromFactory(() => PolicyMemberUploadId.New));
        _fixture.Customize<PolicyId>(c => c.FromFactory(() => PolicyId.New));
        _fixture.Customize<DateOnly>(c => c.FromFactory(() => DateOnly.FromDateTime(DateTime.Today.AddDays(_fixture.Create<int>() % 365))));
    }

    #region FindByIdAsync Tests

    [Fact]
    public async Task FindByIdAsync_WithValidId_ShouldReturnEntity()
    {
        // Arrange
        PolicyMemberId? id = _fixture.Create<PolicyMemberId>();
        PolicyMember expectedEntity = CreatePolicyMemberWithId(id);

        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.FindByIdAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(() => expectedEntity);

        // Act
        PolicyMember result = await mockRepository.Object.FindByIdAsync(id, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(expectedEntity);
        result.Id.Should().Be(id);
    }

    [Fact]
    public async Task FindByIdAsync_WithNonExistentId_ShouldReturnNull()
    {
        // Arrange
        PolicyMemberId? id = _fixture.Create<PolicyMemberId>();

        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.FindByIdAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(() => null!);

        // Act
        PolicyMember result = await mockRepository.Object.FindByIdAsync(id, CancellationToken.None);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task FindByIdAsync_WithCancellationToken_ShouldPassTokenToRepository()
    {
        // Arrange
        PolicyMemberId? id = _fixture.Create<PolicyMemberId>();
        var cancellationToken = new CancellationToken(true);

        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.FindByIdAsync(id, cancellationToken))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => mockRepository.Object.FindByIdAsync(id, cancellationToken));

        mockRepository.Verify(x => x.FindByIdAsync(id, cancellationToken), Times.Once);
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_WithValidEntity_ShouldUpdateSuccessfully()
    {
        // Arrange
        PolicyMember entity = CreatePolicyMember();

        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.UpdateAsync(entity, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMember e, CancellationToken _) => e);

        // Act
        await mockRepository.Object.UpdateAsync(entity, CancellationToken.None);

        // Assert
        mockRepository.Verify(x => x.UpdateAsync(entity, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateAsync_WithNullEntity_ShouldThrowArgumentNullException()
    {
        // Arrange
        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.UpdateAsync(null!, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new ArgumentNullException());

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => mockRepository.Object.UpdateAsync(null!, CancellationToken.None));
    }

    [Fact]
    public async Task UpdateAsync_WithConcurrencyConflict_ShouldThrowDbUpdateConcurrencyException()
    {
        // Arrange
        PolicyMember entity = CreatePolicyMember();

        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.UpdateAsync(entity, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DbUpdateConcurrencyException());

        // Act & Assert
        await Assert.ThrowsAsync<DbUpdateConcurrencyException>(
            () => mockRepository.Object.UpdateAsync(entity, CancellationToken.None));
    }

    [Fact]
    public async Task UpdateAsync_WithCancellationToken_ShouldPassTokenToRepository()
    {
        // Arrange
        PolicyMember entity = CreatePolicyMember();
        var cancellationToken = new CancellationToken(true);

        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.UpdateAsync(entity, cancellationToken))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => mockRepository.Object.UpdateAsync(entity, cancellationToken));

        mockRepository.Verify(x => x.UpdateAsync(entity, cancellationToken), Times.Once);
    }

    #endregion

    #region FindAllAsync Tests

    [Fact]
    public async Task FindAllAsync_WithValidIds_ShouldReturnMatchingEntities()
    {
        // Arrange
        var ids = _fixture.CreateMany<PolicyMemberId>(3).ToList();
        var expectedEntities = ids.Select(CreatePolicyMemberWithId).ToList();

        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.FindAllAsync(ids, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedEntities);

        // Act
        List<PolicyMember> result = await mockRepository.Object.FindAllAsync(ids, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(3);
        result.Should().BeEquivalentTo(expectedEntities);
        result.Select(x => x.Id).Should().BeEquivalentTo(ids);
    }

    [Fact]
    public async Task FindAllAsync_WithEmptyIdsList_ShouldReturnEmptyList()
    {
        // Arrange
        var emptyIds = new List<PolicyMemberId>();

        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.FindAllAsync(emptyIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<PolicyMember>());

        // Act
        List<PolicyMember> result = await mockRepository.Object.FindAllAsync(emptyIds, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task FindAllAsync_WithNonExistentIds_ShouldReturnEmptyList()
    {
        // Arrange
        var nonExistentIds = _fixture.CreateMany<PolicyMemberId>(3).ToList();

        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.FindAllAsync(nonExistentIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<PolicyMember>());

        // Act
        List<PolicyMember> result = await mockRepository.Object.FindAllAsync(nonExistentIds, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task FindAllAsync_WithMixedExistentAndNonExistentIds_ShouldReturnOnlyExistentEntities()
    {
        // Arrange
        var existentIds = _fixture.CreateMany<PolicyMemberId>(2).ToList();
        var nonExistentIds = _fixture.CreateMany<PolicyMemberId>(2).ToList();
        var allIds = existentIds.Concat(nonExistentIds).ToList();

        var existentEntities = existentIds.Select(CreatePolicyMemberWithId).ToList();

        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.FindAllAsync(allIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existentEntities);

        // Act
        List<PolicyMember> result = await mockRepository.Object.FindAllAsync(allIds, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().BeEquivalentTo(existentEntities);
        result.Select(x => x.Id).Should().BeEquivalentTo(existentIds);
    }

    [Fact]
    public async Task FindAllAsync_WithCancellationToken_ShouldPassTokenToRepository()
    {
        // Arrange
        var ids = _fixture.CreateMany<PolicyMemberId>(2).ToList();
        var cancellationToken = new CancellationToken(true);

        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.FindAllAsync(ids, cancellationToken))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => mockRepository.Object.FindAllAsync(ids, cancellationToken));

        mockRepository.Verify(x => x.FindAllAsync(ids, cancellationToken), Times.Once);
    }

    #endregion

    #region Batch Operations Tests

    [Fact]
    public async Task UpdateBatchAsync_WithValidEntities_ShouldUpdateAllSuccessfully()
    {
        // Arrange
        var entities = new List<PolicyMember>
        {
            CreatePolicyMember(),
            CreatePolicyMember(),
            CreatePolicyMember()
        };

        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.UpdateBatchAsync(entities, It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await mockRepository.Object.UpdateBatchAsync(entities, CancellationToken.None);

        // Assert
        mockRepository.Verify(x => x.UpdateBatchAsync(entities, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateBatchAsync_WithEmptyList_ShouldCompleteSuccessfully()
    {
        // Arrange
        var emptyEntities = new List<PolicyMember>();

        Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> mockRepository = CreateMockRepository<PolicyMember, PolicyMemberId>();
        mockRepository.Setup(x => x.UpdateBatchAsync(emptyEntities, It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await mockRepository.Object.UpdateBatchAsync(emptyEntities, CancellationToken.None);

        // Assert
        mockRepository.Verify(x => x.UpdateBatchAsync(emptyEntities, It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region Helper Methods

    private Mock<IPaginatedRepository<TEntity, TId>> CreateMockRepository<TEntity, TId>()
        where TEntity : class, IAggregateRoot<TId>
        where TId : notnull
    {
        return new Mock<IPaginatedRepository<TEntity, TId>>();
    }

    private PolicyMember CreatePolicyMember()
    {
        return PolicyMember.Create(
            _fixture.Create<PolicyId>(),
            "TEST-MEMBER",
            DateOnly.FromDateTime(DateTime.Today),
            DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            "TEST-PLAN");
    }

    private PolicyMember CreatePolicyMemberWithId(PolicyMemberId id)
    {
        PolicyMember member = CreatePolicyMember();

        // Use reflection to set the ID to match the requested ID
        PropertyInfo? idProperty = typeof(PolicyMember).BaseType?.GetProperty("Id");
        if (idProperty != null && idProperty.CanWrite)
        {
            idProperty.SetValue(member, id);
        }
        else
        {
            // Try to access the private field if property is not writable
            FieldInfo? idField = typeof(PolicyMember).BaseType?.GetField("_id",
                BindingFlags.NonPublic | BindingFlags.Instance);
            idField?.SetValue(member, id);
        }

        return member;
    }

    #endregion
}
