using System.Text.Json;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Infrastructure.CustomFields;
using CoverGo.Products.Client;
using Microsoft.Extensions.Logging;
using DomainProductId = CoverGo.PoliciesV3.Domain.Policies.ProductId;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Repositories;

public class PolicyMemberFieldsSchemaRepositoryTests
{
    private readonly Mock<ILogger<PolicyMemberFieldsSchemaRepository>> _logger;
    private readonly Mock<IUsersService> _usersService;
    private readonly Mock<ICasesService> _casesService;
    private readonly Mock<IProductService> _productService;
    private readonly PolicyMemberFieldsSchemaRepository _repository;
    private readonly Fixture _fixture;

    public PolicyMemberFieldsSchemaRepositoryTests()
    {
        _logger = new Mock<ILogger<PolicyMemberFieldsSchemaRepository>>();
        _usersService = new Mock<IUsersService>();
        _casesService = new Mock<ICasesService>();
        _productService = new Mock<IProductService>();

        SetupCasesServiceMock();
        SetupUsersServiceMock();
        SetupProductServiceMock();

        _repository = new PolicyMemberFieldsSchemaRepository(
            _usersService.Object,
            _casesService.Object,
            _productService.Object,
            _logger.Object);

        _fixture = new Fixture();
        _fixture.Customize<DomainProductId>(c => c.FromFactory(() => new DomainProductId("PLAN-001", "HEALTH", "1.0")));
    }

    private void SetupCasesServiceMock()
    {
        string mockCasesDataJson = """
        {
            "properties": {
                "firstName": {
                    "type": "string",
                    "meta": {
                        "label": "First Name",
                        "required": true
                    }
                },
                "lastName": {
                    "type": "string",
                    "meta": {
                        "label": "Last Name",
                        "required": true
                    }
                }
            }
        }
        """;

        JsonElement mockJsonElement = JsonSerializer.Deserialize<JsonElement>(mockCasesDataJson);
        _casesService.Setup(x => x.GetMemberDataSchema(It.IsAny<CancellationToken>()))
                    .ReturnsAsync(mockJsonElement);
    }

    private void SetupUsersServiceMock()
    {
        JsonElement emptyJsonElement = JsonSerializer.Deserialize<JsonElement>("{}");
        _usersService.Setup(x => x.GetCompanyContractHolderMembersFieldsById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                    .ReturnsAsync(emptyJsonElement);
    }

    private void SetupProductServiceMock() =>
        _productService.Setup(x => x.GetProductMemberSchema(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync((string?)null);

    [Fact]
    public async Task GetCustomFieldsSchema_ShouldReturnBasicSchema()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        PolicyMemberFieldsSchema result = await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.MemberFields.Should().NotBeEmpty();
        result.MemberFields.Should().HaveCount(2); // firstName and lastName

        // Verify basic member fields
        result.MemberFields.Should().Contain(f => f.Name == "firstName" && f.Label == "First Name" && f.IsRequired);
        result.MemberFields.Should().Contain(f => f.Name == "lastName" && f.Label == "Last Name" && f.IsRequired);

        // Product and Census fields should be empty (current implementation returns empty collections, not null)
        result.ProductFields.Should().BeEmpty();
        result.CensusFields.Should().BeEmpty();

        // OneOfValidations should be empty (current implementation returns empty collections, not null)
        result.OneOfValidations.Should().BeEmpty();
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WithNullContractHolderId_ShouldReturnSchema()
    {
        // Arrange
        string? contractHolderId = null;
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        PolicyMemberFieldsSchema result = await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.MemberFields.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GetCustomFieldsSchema_ShouldLogInformationMessages()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        _logger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Getting custom fields schema for")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _logger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Schema created with")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task GetCustomFieldsSchema_ShouldReturnFieldsWithCorrectTypes()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        PolicyMemberFieldsSchema result = await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        PolicyMemberFieldDefinition firstNameField = result.MemberFields.First(f => f.Name == "firstName");
        firstNameField.Type.Should().BeOfType<StringFieldType>();
        firstNameField.IsUnique.Should().BeFalse();

        PolicyMemberFieldDefinition lastNameField = result.MemberFields.First(f => f.Name == "lastName");
        lastNameField.Type.Should().BeOfType<StringFieldType>();
        lastNameField.IsUnique.Should().BeFalse();
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        DomainProductId productId = _fixture.Create<DomainProductId>();
        using var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _repository.GetCustomFieldsSchema(contractHolderId, productId, cts.Token));
    }

    [Fact]
    public async Task GetCustomFieldsSchema_ShouldCallExternalServices()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert - Verify that external service calls are made
        _casesService.Verify(x => x.GetMemberDataSchema(It.IsAny<CancellationToken>()), Times.Once);
        _usersService.Verify(x => x.GetCompanyContractHolderMembersFieldsById(contractHolderId, It.IsAny<CancellationToken>()), Times.Once);
        _productService.Verify(x => x.GetProductMemberSchema(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Theory]
    [InlineData("CONTRACT-001")]
    [InlineData("CONTRACT-999")]
    [InlineData(null)]
    public async Task GetCustomFieldsSchema_WithDifferentContractHolderIds_ShouldReturnConsistentSchema(string? contractHolderId)
    {
        // Arrange
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        PolicyMemberFieldsSchema result = await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.MemberFields.Should().HaveCount(2);
        result.MemberFields.Should().Contain(f => f.Name == "firstName");
        result.MemberFields.Should().Contain(f => f.Name == "lastName");
    }

    [Fact]
    public async Task GetCustomFieldsSchema_ShouldReturnNewInstanceEachTime()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        PolicyMemberFieldsSchema result1 = await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);
        PolicyMemberFieldsSchema result2 = await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result1.Should().NotBeSameAs(result2);
        result1.Should().BeEquivalentTo(result2);
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateInstance()
    {
        // Arrange
        var usersService = new Mock<IUsersService>();
        var casesService = new Mock<ICasesService>();
        var productService = new Mock<IProductService>();
        var logger = new Mock<ILogger<PolicyMemberFieldsSchemaRepository>>();

        // Act
        var repository = new PolicyMemberFieldsSchemaRepository(
            usersService.Object,
            casesService.Object,
            productService.Object,
            logger.Object);

        // Assert
        repository.Should().NotBeNull();
    }

    [Fact]
    public async Task GetMembersFields_WithJsonFile_ShouldShowParsingFlow()
    {
        // Arrange - This test demonstrates how JSON from your file.json gets processed
        // We'll use the actual JSON structure that would come from CasesService.GetMemberDataSchema()

        string testFilePath = Path.Combine("Files", "file.json");
        await File.ReadAllTextAsync(testFilePath);

        // The GetMembersFields method expects a CasesDataSchema structure from the external service
        // Let's create a realistic mock that shows how the parsing works
        string mockCasesServiceResponse = """
        {
            "properties": {
                "gender": {
                    "type": "string",
                    "meta": {
                        "label": "Gender",
                        "required": true,
                        "options": [
                            {"name": "Female", "value": "female"},
                            {"name": "Male", "value": "male"}
                        ],
                        "validations": "required"
                    }
                },
                "memberType": {
                    "type": "string",
                    "meta": {
                        "label": "Member Type",
                        "required": true,
                        "options": [
                            {"name": "Primary", "value": "employee"},
                            {"name": "Dependent", "value": "dependent"}
                        ],
                        "validations": "required"
                    }
                },
                "email": {
                    "type": "string",
                    "meta": {
                        "label": "Email",
                        "required": false,
                        "validations": "email"
                    }
                },
                "fullName": {
                    "type": "string",
                    "meta": {
                        "label": "Name",
                        "required": true,
                        "validations": "required"
                    }
                },
                "dateOfBirth": {
                    "type": "string",
                    "meta": {
                        "label": "Date of Birth",
                        "fieldType": "date",
                        "required": true,
                        "validations": "required"
                    }
                },
                "hkid": {
                    "type": "string",
                    "meta": {
                        "label": "National ID",
                        "required": false
                    }
                },
                "address": {
                    "type": "object",
                    "meta": {
                        "label": "Home Address",
                        "required": false
                    },
                    "properties": {
                        "flatNumber": {
                            "type": "string",
                            "meta": {
                                "label": "Flat Number / Building",
                                "required": false
                            }
                        },
                        "streetNumberAndName": {
                            "type": "string",
                            "meta": {
                                "label": "Street Number & Street Name",
                                "required": false
                            }
                        },
                        "district": {
                            "type": "string",
                            "meta": {
                                "label": "District",
                                "required": false
                            }
                        },
                        "region": {
                            "type": "string",
                            "meta": {
                                "label": "Region",
                                "required": false
                            }
                        }
                    }
                }
            }
        }
        """;

        JsonElement mockJsonElement = JsonSerializer.Deserialize<JsonElement>(mockCasesServiceResponse);

        // Setup the mock to return our test data
        var casesService = new Mock<ICasesService>();
        casesService.Setup(x => x.GetMemberDataSchema(It.IsAny<CancellationToken>()))
                   .ReturnsAsync(mockJsonElement);

        var repository = new PolicyMemberFieldsSchemaRepository(
            new Mock<IUsersService>().Object,
            casesService.Object,
            new Mock<IProductService>().Object,
            new Mock<ILogger<PolicyMemberFieldsSchemaRepository>>().Object);

        // Act - This calls GetMembersFields internally
        PolicyMemberFieldsSchema result = await repository.GetCustomFieldsSchema(null, _fixture.Create<DomainProductId>(), CancellationToken.None);

        // Verify the parsing worked correctly
        result.Should().NotBeNull();
        result.MemberFields.Should().NotBeEmpty();
        result.MemberFields.Should().HaveCount(7); // gender, memberType, email, fullName, dateOfBirth, hkid, address

        // Test specific field types were created correctly
        PolicyMemberFieldDefinition? genderField = result.MemberFields.FirstOrDefault(f => f.Name == "gender");
        genderField.Should().NotBeNull();
        genderField!.Type.Should().BeOfType<StringFieldType>();

        PolicyMemberFieldDefinition? dateField = result.MemberFields.FirstOrDefault(f => f.Name == "dateOfBirth");
        dateField.Should().NotBeNull();
        dateField!.Type.Should().BeOfType<DateFieldType>();

        PolicyMemberFieldDefinition? addressField = result.MemberFields.FirstOrDefault(f => f.Name == "address");
        addressField.Should().NotBeNull();
        addressField!.Type.Should().BeOfType<ObjectFieldType>("address field should be parsed as ObjectFieldType, not AddressFieldType in this context");

        PolicyMemberFieldDefinition? hkidField = result.MemberFields.FirstOrDefault(f => f.Name == "hkid");
        hkidField.Should().NotBeNull();
        // Note: IsIdentityField is determined by the field name, not the JSON content
        // hkid is recognized as an identity field by the IdentificationIdField.IsIdentificationId method
    }

    [Fact]
    public async Task GetMembersFields_PerformanceOptimizations_ShouldMaintainFunctionality()
    {
        // Arrange - Test that performance optimizations don't break functionality
        string mockCasesServiceResponse = """
        {
            "properties": {
                "testField1": {
                    "type": "string",
                    "meta": {
                        "label": "Test Field 1",
                        "required": true,
                        "options": [
                            {"name": "Option 1", "value": "opt1"},
                            {"name": "Option 2", "value": "opt2"},
                            {"name": "Option 3", "value": "opt3"}
                        ],
                        "validations": "required"
                    }
                },
                "testField2": {
                    "type": "number",
                    "meta": {
                        "label": "Test Field 2",
                        "required": false,
                        "options": [
                            {"name": "Number 1", "value": 1},
                            {"name": "Number 2", "value": 2}
                        ]
                    }
                },
                "testField3": {
                    "type": "string",
                    "meta": {
                        "label": "Test Field 3",
                        "required": false
                    }
                }
            }
        }
        """;

        JsonElement mockJsonElement = JsonSerializer.Deserialize<JsonElement>(mockCasesServiceResponse);

        var casesService = new Mock<ICasesService>();
        casesService.Setup(x => x.GetMemberDataSchema(It.IsAny<CancellationToken>()))
                   .ReturnsAsync(mockJsonElement);

        var repository = new PolicyMemberFieldsSchemaRepository(
            new Mock<IUsersService>().Object,
            casesService.Object,
            new Mock<IProductService>().Object,
            new Mock<ILogger<PolicyMemberFieldsSchemaRepository>>().Object);

        // Act
        PolicyMemberFieldsSchema result = await repository.GetCustomFieldsSchema(null, _fixture.Create<DomainProductId>(), CancellationToken.None);

        // Assert - Verify optimizations maintain exact same functionality
        result.Should().NotBeNull();
        result.MemberFields.Should().HaveCount(3);

        // Test string field with options (performance optimization: CreateStringOptions)
        PolicyMemberFieldDefinition? stringField = result.MemberFields.FirstOrDefault(f => f.Name == "testField1");
        stringField.Should().NotBeNull();
        stringField!.Type.Should().BeOfType<StringFieldType>();
        var stringType = (StringFieldType)stringField.Type;
        stringType.Options.Should().NotBeNull();
        stringType.Options!.Should().HaveCount(3);
        stringType.Options.Should().Contain(o => o.Value == "opt1" && o.Label == "Option 1");
        stringType.Validations.Should().Be("required");

        // Test number field with options (performance optimization: CreateNumberOptions)
        PolicyMemberFieldDefinition? numberField = result.MemberFields.FirstOrDefault(f => f.Name == "testField2");
        numberField.Should().NotBeNull();
        numberField!.Type.Should().BeOfType<NumberFieldType>();
        var numberType = (NumberFieldType)numberField.Type;
        numberType.Options.Should().NotBeNull();
        numberType.Options!.Should().HaveCount(2);

        // Test field without options (performance optimization: null check avoids LINQ)
        PolicyMemberFieldDefinition? simpleField = result.MemberFields.FirstOrDefault(f => f.Name == "testField3");
        simpleField.Should().NotBeNull();
        simpleField!.Type.Should().BeOfType<StringFieldType>();
        var simpleStringType = (StringFieldType)simpleField.Type;
        simpleStringType.Options.Should().BeNull();
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WithIsBenefitFieldSupport_ShouldMapBenefitFieldsCorrectly()
    {
        // Test that the migrated methods correctly handle IsBenefitField property from both CasesService and ProductService
        string mockCasesServiceResponse = """
        {
            "properties": {
                "benefitAmount": {
                    "type": "number",
                    "meta": {
                        "label": "Benefit Amount",
                        "required": true,
                        "isBenefitField": true,
                        "validations": "required"
                    }
                },
                "regularField": {
                    "type": "string",
                    "meta": {
                        "label": "Regular Field",
                        "required": false,
                        "isBenefitField": false
                    }
                },
                "fieldWithoutBenefitFlag": {
                    "type": "string",
                    "meta": {
                        "label": "Field Without Benefit Flag",
                        "required": false
                    }
                }
            }
        }
        """;

        JsonElement mockJsonElement = JsonSerializer.Deserialize<JsonElement>(mockCasesServiceResponse);

        var casesService = new Mock<ICasesService>();
        casesService.Setup(x => x.GetMemberDataSchema(It.IsAny<CancellationToken>()))
                   .ReturnsAsync(mockJsonElement);

        var repository = new PolicyMemberFieldsSchemaRepository(
            new Mock<IUsersService>().Object,
            casesService.Object,
            new Mock<IProductService>().Object,
            new Mock<ILogger<PolicyMemberFieldsSchemaRepository>>().Object);

        // Act
        PolicyMemberFieldsSchema result = await repository.GetCustomFieldsSchema(null, _fixture.Create<DomainProductId>(), CancellationToken.None);

        // Assert - Verify IsBenefitField is correctly mapped
        result.Should().NotBeNull();
        result.MemberFields.Should().HaveCount(3);

        // Verify benefit field is correctly identified
        PolicyMemberFieldDefinition? benefitField = result.MemberFields.FirstOrDefault(f => f.Name == "benefitAmount");
        benefitField.Should().NotBeNull();
        benefitField!.IsBenefitField.Should().BeTrue();
        benefitField.Label.Should().Be("Benefit Amount");
        benefitField.IsRequired.Should().BeTrue();

        // Verify regular field with explicit false flag
        PolicyMemberFieldDefinition? regularField = result.MemberFields.FirstOrDefault(f => f.Name == "regularField");
        regularField.Should().NotBeNull();
        regularField!.IsBenefitField.Should().BeFalse();
        regularField.Label.Should().Be("Regular Field");
        regularField.IsRequired.Should().BeFalse();

        // Verify field without benefit flag defaults to false
        PolicyMemberFieldDefinition? fieldWithoutFlag = result.MemberFields.FirstOrDefault(f => f.Name == "fieldWithoutBenefitFlag");
        fieldWithoutFlag.Should().NotBeNull();
        fieldWithoutFlag!.IsBenefitField.Should().BeFalse();
        fieldWithoutFlag.Label.Should().Be("Field Without Benefit Flag");
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WithMalformedCasesServiceJSON_ShouldReturnEmptySchemaGracefully()
    {
        // Test that the repository handles malformed JSON from CasesService gracefully
        // This simulates the real-world issue where CasesService returns nested arrays instead of objects
        // The updated implementation handles malformed JSON gracefully and returns empty collections
        string malformedCasesServiceResponse = """
        {
            "properties": [[[[[]],[[[[]],[[]],[[]],[[]],[[[[[]],[[]],[[]]],[[[]],[[]],[[]]]]],[[]],[[]],[[]]]]]],[[[[]],[[[[]],[[]],[[]],[[]],[[[[[]],[[]],[[]]],[[[]],[[]],[[]]]]],[[]],[[]],[[]],[[]]]]]]]
        }
        """;

        JsonElement mockJsonElement = JsonSerializer.Deserialize<JsonElement>(malformedCasesServiceResponse);

        var casesService = new Mock<ICasesService>();
        casesService.Setup(x => x.GetMemberDataSchema(It.IsAny<CancellationToken>()))
                   .ReturnsAsync(mockJsonElement);

        var repository = new PolicyMemberFieldsSchemaRepository(
            new Mock<IUsersService>().Object,
            casesService.Object,
            new Mock<IProductService>().Object,
            new Mock<ILogger<PolicyMemberFieldsSchemaRepository>>().Object);

        // Act
        PolicyMemberFieldsSchema result = await repository.GetCustomFieldsSchema(null, _fixture.Create<DomainProductId>(), CancellationToken.None);

        // Assert - Should return empty schema instead of throwing exception
        result.Should().NotBeNull();
        result.MemberFields.Should().BeEmpty(); // No member fields due to malformed JSON

        // Should still have basic schema structure (product fields, census fields may still work)
        result.ProductFields.Should().NotBeNull();
        result.CensusFields.Should().NotBeNull();
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WithEmptyJSON_ShouldReturnEmptyMemberFields()
    {
        // Test that the repository handles empty JSON gracefully
        // Empty JSON object {} is valid JSON but contains no properties
        JsonElement mockJsonElement = JsonSerializer.Deserialize<JsonElement>("{}"); // Valid JSON but no properties

        var casesService = new Mock<ICasesService>();
        casesService.Setup(x => x.GetMemberDataSchema(It.IsAny<CancellationToken>()))
                   .ReturnsAsync(mockJsonElement);

        var repository = new PolicyMemberFieldsSchemaRepository(
            new Mock<IUsersService>().Object,
            casesService.Object,
            new Mock<IProductService>().Object,
            new Mock<ILogger<PolicyMemberFieldsSchemaRepository>>().Object);

        // Act
        PolicyMemberFieldsSchema result = await repository.GetCustomFieldsSchema(null, _fixture.Create<DomainProductId>(), CancellationToken.None);

        // Assert - Should return empty member fields for empty JSON
        result.Should().NotBeNull();
        result.MemberFields.Should().BeEmpty(); // No member fields due to empty JSON
    }
}
