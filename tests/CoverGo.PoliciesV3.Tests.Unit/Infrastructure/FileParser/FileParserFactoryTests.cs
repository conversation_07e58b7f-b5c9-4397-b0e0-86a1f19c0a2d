using System.Text;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Infrastructure.FileProcessing;
using Microsoft.Extensions.Logging;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.FileParser;

[Trait("Category", "Unit")]
[Trait("Component", "Infrastructure")]
[Trait("Feature", "FileParser")]
public class FileParserFactoryTests
{
    private readonly Mock<ILogger<FileParserFactory>> _loggerMock;
    private readonly FileParserFactory _factory;

    public FileParserFactoryTests()
    {
        _loggerMock = new Mock<ILogger<FileParserFactory>>();
        _factory = new FileParserFactory(_loggerMock.Object);
    }

    #region CreateParser Tests

    [Fact]
    public void CreateParser_WithCsvContent_ShouldReturnCsvFileParser()
    {
        // Arrange
        string csvContent = "Name,Age\nJohn,30";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        IFileParser parser = _factory.CreateParser(fileContent);

        // Assert
        parser.Should().BeOfType<CsvFileParser>();
    }

    [Fact]
    public void CreateParser_WithExcelContent_ShouldReturnXlsxFileParser()
    {
        // Arrange
        byte[] fileContent = CreateExcelFile();

        // Act
        IFileParser parser = _factory.CreateParser(fileContent);

        // Assert
        parser.Should().BeOfType<XlsxFileParser>();
    }

    [Fact]
    public void CreateParser_WithExcelMagicNumber_ShouldReturnXlsxFileParser()
    {
        // Arrange - Create content that starts with Excel magic number (ZIP signature: PK..)
        byte[] fileContent = [0x50, 0x4B, 0x03, 0x04, 0x14, 0x00, 0x06, 0x00];

        // Act
        IFileParser parser = _factory.CreateParser(fileContent);

        // Assert
        parser.Should().BeOfType<XlsxFileParser>();
    }

    [Fact]
    public void CreateParser_WithRandomBinaryContent_ShouldReturnCsvFileParser()
    {
        // Arrange - Random binary content that doesn't match Excel magic number
        byte[] fileContent = [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]; // PNG signature

        // Act
        IFileParser parser = _factory.CreateParser(fileContent);

        // Assert
        parser.Should().BeOfType<CsvFileParser>();
    }

    [Fact]
    public void CreateParser_WithEmptyContent_ShouldThrowBadFileContentException()
    {
        // Arrange
        byte[] emptyContent = [];

        // Act & Assert
        BadFileContentException exception = Assert.Throws<BadFileContentException>(() => _factory.CreateParser(emptyContent));
        exception.ErrorCode.Should().Be(ErrorCodes.EmptyFile);
        exception.Message.Should().Be("File content is empty");
    }

    [Fact]
    public void CreateParser_WithNullContent_ShouldThrowBadFileContentException()
    {
        // Act & Assert
        BadFileContentException exception = Assert.Throws<BadFileContentException>(() => _factory.CreateParser(null!));
        exception.ErrorCode.Should().Be(ErrorCodes.EmptyFile);
        exception.Message.Should().Be("File content is empty");
    }

    [Fact]
    public void CreateParser_WithShortContent_ShouldReturnCsvFileParser()
    {
        // Arrange - Content shorter than Excel magic number
        byte[] shortContent = [0x50, 0x4B]; // Only 2 bytes

        // Act
        IFileParser parser = _factory.CreateParser(shortContent);

        // Assert
        parser.Should().BeOfType<CsvFileParser>();
    }

    #endregion

    #region Logging Tests

    [Fact]
    public void CreateParser_WithEmptyContent_ShouldLogError()
    {
        // Arrange
        byte[] emptyContent = [];

        // Act & Assert
        Assert.Throws<BadFileContentException>(() => _factory.CreateParser(emptyContent));

        // Verify error was logged
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Attempted to create parser for empty file content")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public void CreateParser_WithValidContent_ShouldLogDebug()
    {
        // Arrange
        string csvContent = "Name,Age\nJohn,30";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        _factory.CreateParser(fileContent);

        // Assert - Verify debug log was called
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Detected file type")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    #endregion

    #region File Type Detection Tests

    [Theory]
    [InlineData(new byte[] { 0x50, 0x4B, 0x03, 0x04 })] // Standard Excel magic number
    [InlineData(new byte[] { 0x50, 0x4B, 0x03, 0x04, 0x14, 0x00 })] // Excel with additional bytes
    public void CreateParser_WithExcelMagicNumbers_ShouldReturnXlsxFileParser(byte[] magicBytes)
    {
        // Arrange
        byte[] fileContent = [.. magicBytes, 0x00, 0x00, 0x00, 0x00];

        // Act
        IFileParser parser = _factory.CreateParser(fileContent);

        // Assert
        parser.Should().BeOfType<XlsxFileParser>();
    }

    [Theory]
    [InlineData(new byte[] { 0x50, 0x4B, 0x03 })] // Incomplete magic number
    [InlineData(new byte[] { 0x50, 0x4B, 0x03, 0x05 })] // Wrong magic number
    [InlineData(new byte[] { 0x89, 0x50, 0x4E, 0x47 })] // PNG signature
    [InlineData(new byte[] { 0xFF, 0xD8, 0xFF, 0xE0 })] // JPEG signature
    public void CreateParser_WithNonExcelMagicNumbers_ShouldReturnCsvFileParser(byte[] magicBytes)
    {
        // Arrange
        byte[] fileContent = [.. magicBytes, 0x00, 0x00, 0x00, 0x00];

        // Act
        IFileParser parser = _factory.CreateParser(fileContent);

        // Assert
        parser.Should().BeOfType<CsvFileParser>();
    }

    #endregion

    #region Integration Tests

    [Fact]
    public void CreateParser_WithRealExcelFile_ShouldParseCorrectly()
    {
        // Arrange
        byte[] excelContent = CreateExcelFile();

        // Act
        IFileParser parser = _factory.CreateParser(excelContent);
        FileParseResult result = parser.ParseFile(excelContent);

        // Assert
        parser.Should().BeOfType<XlsxFileParser>();
        result.Headers.Should().ContainInOrder("Name", "Age");
        result.Contents.Should().HaveCount(1);
        result.Contents[0]["Name"].Should().Be("John Doe");
        result.Contents[0]["Age"].Should().Be("30");
    }

    [Fact]
    public void CreateParser_WithRealCsvFile_ShouldParseCorrectly()
    {
        // Arrange
        string csvContent = "Name,Age\nJohn Doe,30";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        IFileParser parser = _factory.CreateParser(fileContent);
        FileParseResult result = parser.ParseFile(fileContent);

        // Assert
        parser.Should().BeOfType<CsvFileParser>();
        result.Headers.Should().ContainInOrder("Name", "Age");
        result.Contents.Should().HaveCount(1);
        result.Contents[0]["Name"].Should().Be("John Doe");
        result.Contents[0]["Age"].Should().Be("30");
    }

    #endregion

    #region Helper Methods

    private static byte[] CreateExcelFile()
    {
        using var workbook = new XSSFWorkbook();
        ISheet sheet = workbook.CreateSheet("Sheet1");

        // Header row
        IRow headerRow = sheet.CreateRow(0);
        headerRow.CreateCell(0).SetCellValue("Name");
        headerRow.CreateCell(1).SetCellValue("Age");

        // Data row
        IRow dataRow = sheet.CreateRow(1);
        dataRow.CreateCell(0).SetCellValue("John Doe");
        dataRow.CreateCell(1).SetCellValue("30");

        using var stream = new MemoryStream();
        workbook.Write(stream);
        return stream.ToArray();
    }

    #endregion
}
