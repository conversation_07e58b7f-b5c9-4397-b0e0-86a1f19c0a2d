using System.Text;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Infrastructure.FileProcessing;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.FileParser;

[Trait("Category", "Unit")]
[Trait("Component", "Infrastructure")]
[Trait("Feature", "FileParser")]
public class CsvFileParserTests
{
    private readonly CsvFileParser _parser;

    public CsvFileParserTests()
    {
        _parser = new CsvFileParser();
    }

    #region ParseFile Tests

    [Fact]
    public void ParseFile_WithValidCsvContent_ShouldReturnCorrectResult()
    {
        // Arrange
        string csvContent = "Name,Age,Email\nJohn <PERSON>e,30,<EMAIL>\n<PERSON><PERSON>,25,<EMAIL>";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.Should().NotBeNull();
        result.Headers.Should().HaveCount(3);
        result.Headers.Should().ContainInOrder("Name", "Age", "Email");
        result.Contents.Should().HaveCount(2);
        result.Count.Should().Be(2);

        // Verify first row
        result.Contents[0].Should().ContainKeys("Name", "Age", "Email");
        result.Contents[0]["Name"].Should().Be("John Doe");
        result.Contents[0]["Age"].Should().Be("30");
        result.Contents[0]["Email"].Should().Be("<EMAIL>");

        // Verify second row
        result.Contents[1]["Name"].Should().Be("Jane Smith");
        result.Contents[1]["Age"].Should().Be("25");
        result.Contents[1]["Email"].Should().Be("<EMAIL>");
    }

    [Fact]
    public void ParseFile_WithEmptyValues_ShouldReturnNullForEmptyValues()
    {
        // Arrange
        string csvContent = "Name,Age,Email\nJohn Doe,,<EMAIL>\n,25,";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.Contents[0]["Age"].Should().BeNull();
        result.Contents[1]["Name"].Should().BeNull();
        result.Contents[1]["Email"].Should().BeNull();
    }

    [Fact]
    public void ParseFile_WithWhitespaceValues_ShouldReturnNullForWhitespaceValues()
    {
        // Arrange
        string csvContent = "Name,Age,Email\nJohn Doe,  ,<EMAIL>\n   ,25,\t";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.Contents[0]["Age"].Should().BeNull();
        result.Contents[1]["Name"].Should().BeNull();
        result.Contents[1]["Email"].Should().BeNull();
    }

    [Fact]
    public void ParseFile_WithEmptyFile_ShouldThrowBadFileContentException()
    {
        // Arrange
        byte[] emptyContent = [];

        // Act & Assert
        BadFileContentException exception = Assert.Throws<BadFileContentException>(() => _parser.ParseFile(emptyContent));
        exception.ErrorCode.Should().Be(ErrorCodes.EmptyFile);
        exception.Message.Should().Be("File content is empty");
    }

    [Fact]
    public void ParseFile_WithNullContent_ShouldThrowBadFileContentException()
    {
        // Act & Assert
        BadFileContentException exception = Assert.Throws<BadFileContentException>(() => _parser.ParseFile(null!));
        exception.ErrorCode.Should().Be(ErrorCodes.EmptyFile);
    }

    [Fact]
    public void ParseFile_WithHeaderOnly_ShouldReturnEmptyContents()
    {
        // Arrange
        string csvContent = "Name,Age,Email";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.Headers.Should().HaveCount(3);
        result.Contents.Should().BeEmpty();
        result.Count.Should().Be(0);
    }

    [Fact]
    public void ParseFile_WithMismatchedColumnCount_ShouldThrowBadFileContentException()
    {
        // Arrange
        string csvContent = "Name,Age,Email\nJohn Doe,30\nJane Smith,25,<EMAIL>,extra";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act & Assert
        BadFileContentException exception = Assert.Throws<BadFileContentException>(() => _parser.ParseFile(fileContent));
        exception.ErrorCode.Should().Be(ErrorCodes.InvalidRow);
        exception.Message.Should().Contain("Invalid content at row index 1");
    }

    #endregion

    #region HeadersSet Tests

    [Fact]
    public void ParseFile_ShouldInitializeHeadersSetWithCaseInsensitiveComparison()
    {
        // Arrange
        string csvContent = "Name,Age,Email\nJohn,30,<EMAIL>";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.HeadersSet.Should().HaveCount(3);
        result.HeadersSet.Contains("name").Should().BeTrue(); // Case insensitive
        result.HeadersSet.Contains("NAME").Should().BeTrue(); // Case insensitive
        result.HeadersSet.Contains("Age").Should().BeTrue();
        result.HeadersSet.Contains("NonExistent").Should().BeFalse();
    }

    #endregion
}
