using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Infrastructure.FileProcessing;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.FileParser;

[Trait("Category", "Unit")]
[Trait("Component", "Infrastructure")]
[Trait("Feature", "FileParser")]
public class XlsxFileParserTests
{
    private readonly XlsxFileParser _parser;

    public XlsxFileParserTests()
    {
        _parser = new XlsxFileParser();
    }

    #region ParseFile Tests

    [Fact]
    public void ParseFile_WithValidExcelContent_ShouldReturnCorrectResult()
    {
        // Arrange
        byte[] fileContent = CreateExcelFile(
        [
            ["Name", "Age", "Email"],
            ["<PERSON>", "30", "<EMAIL>"],
            ["<PERSON>", "25", "<EMAIL>"]
        ]);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.Should().NotBeNull();
        result.Headers.Should().HaveCount(3);
        result.Headers.Should().ContainInOrder("Name", "Age", "Email");
        result.Contents.Should().HaveCount(2);
        result.Count.Should().Be(2);

        // Verify first row
        result.Contents[0].Should().ContainKeys("Name", "Age", "Email");
        result.Contents[0]["Name"].Should().Be("John Doe");
        result.Contents[0]["Age"].Should().Be("30");
        result.Contents[0]["Email"].Should().Be("<EMAIL>");

        // Verify second row
        result.Contents[1]["Name"].Should().Be("Jane Smith");
        result.Contents[1]["Age"].Should().Be("25");
        result.Contents[1]["Email"].Should().Be("<EMAIL>");
    }

    [Fact]
    public void ParseFile_WithEmptyValues_ShouldReturnNullForEmptyValues()
    {
        // Arrange
        byte[] fileContent = CreateExcelFile(
        [
            ["Name", "Age", "Email"],
            ["John Doe", "", "<EMAIL>"],
            ["", "25", ""]
        ]);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.Contents[0]["Age"].Should().BeNull();
        result.Contents[1]["Name"].Should().BeNull();
        result.Contents[1]["Email"].Should().BeNull();
    }

    [Fact]
    public void ParseFile_WithEmptyFile_ShouldThrowBadFileContentException()
    {
        // Arrange
        byte[] emptyContent = [];

        // Act & Assert
        BadFileContentException exception = Assert.Throws<BadFileContentException>(() => _parser.ParseFile(emptyContent));
        exception.ErrorCode.Should().Be(ErrorCodes.EmptyFile);
        exception.Message.Should().Be("File content is empty");
    }

    [Fact]
    public void ParseFile_WithNullContent_ShouldThrowBadFileContentException()
    {
        // Act & Assert
        BadFileContentException exception = Assert.Throws<BadFileContentException>(() => _parser.ParseFile(null!));
        exception.ErrorCode.Should().Be(ErrorCodes.EmptyFile);
    }

    [Fact]
    public void ParseFile_WithHeaderOnly_ShouldReturnEmptyContents()
    {
        // Arrange
        byte[] fileContent = CreateExcelFile(
        [
            ["Name", "Age", "Email"]
        ]);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.Headers.Should().HaveCount(3);
        result.Contents.Should().BeEmpty();
        result.Count.Should().Be(0);
    }

    [Fact]
    public void ParseFile_WithEmptyRows_ShouldSkipEmptyRows()
    {
        // Arrange
        byte[] fileContent = CreateExcelFileWithEmptyRows();

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.Headers.Should().HaveCount(3);
        result.Contents.Should().HaveCount(2); // Should skip empty rows
        result.Contents[0]["Name"].Should().Be("John Doe");
        result.Contents[1]["Name"].Should().Be("Jane Smith");
    }

    #endregion

    #region HeadersSet Tests

    [Fact]
    public void ParseFile_ShouldInitializeHeadersSetWithCaseInsensitiveComparison()
    {
        // Arrange
        byte[] fileContent = CreateExcelFile(
        [
            ["Name", "Age", "Email"],
            ["John", "30", "<EMAIL>"]
        ]);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.HeadersSet.Should().HaveCount(3);
        result.HeadersSet.Contains("name").Should().BeTrue(); // Case insensitive
        result.HeadersSet.Contains("NAME").Should().BeTrue(); // Case insensitive
        result.HeadersSet.Contains("Age").Should().BeTrue();
        result.HeadersSet.Contains("NonExistent").Should().BeFalse();
    }

    #endregion

    #region Helper Methods

    private static byte[] CreateExcelFile(string[][] data)
    {
        using var workbook = new XSSFWorkbook();
        ISheet sheet = workbook.CreateSheet("Sheet1");

        for (int rowIndex = 0; rowIndex < data.Length; rowIndex++)
        {
            IRow row = sheet.CreateRow(rowIndex);
            for (int colIndex = 0; colIndex < data[rowIndex].Length; colIndex++)
            {
                ICell cell = row.CreateCell(colIndex);
                cell.SetCellValue(data[rowIndex][colIndex]);
            }
        }

        using var stream = new MemoryStream();
        workbook.Write(stream);
        return stream.ToArray();
    }

    private static byte[] CreateExcelFileWithEmptyRows()
    {
        using var workbook = new XSSFWorkbook();
        ISheet sheet = workbook.CreateSheet("Sheet1");

        // Header row
        IRow headerRow = sheet.CreateRow(0);
        headerRow.CreateCell(0).SetCellValue("Name");
        headerRow.CreateCell(1).SetCellValue("Age");
        headerRow.CreateCell(2).SetCellValue("Email");

        // Data row 1
        IRow dataRow1 = sheet.CreateRow(1);
        dataRow1.CreateCell(0).SetCellValue("John Doe");
        dataRow1.CreateCell(1).SetCellValue("30");
        dataRow1.CreateCell(2).SetCellValue("<EMAIL>");

        // Empty row 2 (will be skipped)
        sheet.CreateRow(2);

        // Data row 3
        IRow dataRow3 = sheet.CreateRow(3);
        dataRow3.CreateCell(0).SetCellValue("Jane Smith");
        dataRow3.CreateCell(1).SetCellValue("25");
        dataRow3.CreateCell(2).SetCellValue("<EMAIL>");

        using var stream = new MemoryStream();
        workbook.Write(stream);
        return stream.ToArray();
    }

    #endregion
}
