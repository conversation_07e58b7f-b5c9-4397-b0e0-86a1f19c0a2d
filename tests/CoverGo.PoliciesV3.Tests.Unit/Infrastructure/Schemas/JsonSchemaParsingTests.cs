using CoverGo.PoliciesV3.Infrastructure.Schemas;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Schemas;

public class JsonSchemaParsingTests
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true,
        Converters = { new JsonStringEnumConverter() }
    };

    #region ProductsDataSchema Tests

    [Fact]
    public void ProductsDataSchema_Deserialization_ShouldParseCorrectly()
    {
        // Arrange
        string json = """
        {
            "memberCustomSchema": {
                "description": "Member custom fields",
                "properties": {
                    "firstName": {
                        "type": "string",
                        "meta": {
                            "label": "First Name",
                            "required": true
                        }
                    },
                    "age": {
                        "type": "number",
                        "meta": {
                            "label": "Age",
                            "required": false
                        }
                    }
                }
            }
        }
        """;

        // Act
        ProductsDataSchema? result = JsonSerializer.Deserialize<ProductsDataSchema>(json, JsonOptions);

        // Assert
        result.Should().NotBeNull();
        result!.MemberCustomSchema.Should().NotBeNull();
        result.MemberCustomSchema!.Properties.Should().NotBeNull();
        result.MemberCustomSchema.Properties.Should().HaveCount(2);

        result.MemberCustomSchema.Properties.Should().ContainKey("firstName");
        result.MemberCustomSchema.Properties.Should().ContainKey("age");

        PolicyMemberCustomField firstNameField = result.MemberCustomSchema.Properties!["firstName"];
        firstNameField.Type.Should().Be(PolicyMemberCustomFieldType.String);
        firstNameField.IsRequired.Should().BeTrue();
        firstNameField.Meta!.Label.Should().Be("First Name");

        PolicyMemberCustomField ageField = result.MemberCustomSchema.Properties["age"];
        ageField.Type.Should().Be(PolicyMemberCustomFieldType.Number);
        ageField.IsRequired.Should().BeFalse();
        ageField.Meta!.Label.Should().Be("Age");
    }

    [Fact]
    public void ProductsDataSchema_WithEmptyProperties_ShouldParseCorrectly()
    {
        // Arrange
        string json = """
        {
            "memberCustomSchema": {
                "description": "Empty schema",
                "properties": {}
            }
        }
        """;

        // Act
        ProductsDataSchema? result = JsonSerializer.Deserialize<ProductsDataSchema>(json, JsonOptions);

        // Assert
        result.Should().NotBeNull();
        result!.MemberCustomSchema.Should().NotBeNull();
        result.MemberCustomSchema!.Properties.Should().NotBeNull();
        result.MemberCustomSchema.Properties.Should().BeEmpty();
    }

    [Fact]
    public void ProductsDataSchema_WithNullMemberCustomSchema_ShouldParseCorrectly()
    {
        // Arrange
        string json = """
        {
            "memberCustomSchema": null
        }
        """;

        // Act
        ProductsDataSchema? result = JsonSerializer.Deserialize<ProductsDataSchema>(json, JsonOptions);

        // Assert
        result.Should().NotBeNull();
        result!.MemberCustomSchema.Should().BeNull();
    }

    #endregion

    #region CasesDataSchema Tests

    [Fact]
    public void CasesDataSchema_Deserialization_ShouldParseCorrectly()
    {
        // Arrange
        string json = """
        {
            "properties": {
                "employeeId": {
                    "type": "string",
                    "meta": {
                        "label": "Employee ID",
                        "required": true,
                        "validations": "unique"
                    }
                },
                "isActive": {
                    "type": "boolean",
                    "meta": {
                        "label": "Is Active",
                        "required": false
                    }
                }
            }
        }
        """;

        // Act
        CasesDataSchema? result = JsonSerializer.Deserialize<CasesDataSchema>(json, JsonOptions);

        // Assert
        result.Should().NotBeNull();
        result!.Properties.Should().NotBeNull();
        result.Properties.Should().HaveCount(2);

        result.Properties.Should().ContainKey("employeeId");
        result.Properties.Should().ContainKey("isActive");

        PolicyMemberCustomField employeeIdField = result.Properties!["employeeId"];
        employeeIdField.Type.Should().Be(PolicyMemberCustomFieldType.String);
        employeeIdField.IsRequired.Should().BeTrue();
        employeeIdField.IsUnique.Should().BeTrue();
        employeeIdField.Meta!.Label.Should().Be("Employee ID");

        PolicyMemberCustomField isActiveField = result.Properties["isActive"];
        isActiveField.Type.Should().Be(PolicyMemberCustomFieldType.Boolean);
        isActiveField.IsRequired.Should().BeFalse();
        isActiveField.IsUnique.Should().BeFalse();
        isActiveField.Meta!.Label.Should().Be("Is Active");
    }

    [Fact]
    public void CasesDataSchema_WithObjectField_ShouldParseCorrectly()
    {
        // Arrange
        string json = """
        {
            "properties": {
                "address": {
                    "type": "object",
                    "meta": {
                        "label": "Address",
                        "required": true
                    },
                    "properties": {
                        "street": {
                            "type": "string",
                            "meta": {
                                "label": "Street",
                                "required": true
                            }
                        },
                        "city": {
                            "type": "string",
                            "meta": {
                                "label": "City",
                                "required": true
                            }
                        }
                    }
                }
            }
        }
        """;

        // Act
        CasesDataSchema? result = JsonSerializer.Deserialize<CasesDataSchema>(json, JsonOptions);

        // Assert
        result.Should().NotBeNull();
        result!.Properties.Should().NotBeNull();
        result.Properties.Should().HaveCount(1);

        PolicyMemberCustomField addressField = result.Properties!["address"];
        addressField.Type.Should().Be(PolicyMemberCustomFieldType.Object);
        addressField.IsRequired.Should().BeTrue();
        addressField.Properties.Should().NotBeNull();
        addressField.Properties.Should().HaveCount(2);
        addressField.Properties.Should().ContainKey("street");
        addressField.Properties.Should().ContainKey("city");
    }

    #endregion

    #region ContractHoldersCensusLevelSchema Tests

    [Fact]
    public void ContractHoldersCensusLevelSchema_Deserialization_ShouldParseCorrectly()
    {
        // Arrange
        string json = """
        {
            "censusLevel": [
                {
                    "id": "department",
                    "name": "Department"
                },
                {
                    "id": "location",
                    "name": "Location"
                }
            ]
        }
        """;

        // Act
        ContractHoldersCensusLevelSchema? result = JsonSerializer.Deserialize<ContractHoldersCensusLevelSchema>(json, JsonOptions);

        // Assert
        result.Should().NotBeNull();
        result!.CensusLevel.Should().NotBeNull();
        result.CensusLevel.Should().HaveCount(2);

        result.CensusLevel![0].Id.Should().Be("department");
        result.CensusLevel[0].Name.Should().Be("Department");

        result.CensusLevel[1].Id.Should().Be("location");
        result.CensusLevel[1].Name.Should().Be("Location");
    }

    [Fact]
    public void ContractHoldersCensusLevelSchema_WithEmptyArray_ShouldParseCorrectly()
    {
        // Arrange
        string json = """
        {
            "censusLevel": []
        }
        """;

        // Act
        ContractHoldersCensusLevelSchema? result = JsonSerializer.Deserialize<ContractHoldersCensusLevelSchema>(json, JsonOptions);

        // Assert
        result.Should().NotBeNull();
        result!.CensusLevel.Should().NotBeNull();
        result.CensusLevel.Should().BeEmpty();
    }

    [Fact]
    public void ContractHoldersCensusLevelSchema_WithNullCensusLevel_ShouldParseCorrectly()
    {
        // Arrange
        string json = """
        {
            "censusLevel": null
        }
        """;

        // Act
        ContractHoldersCensusLevelSchema? result = JsonSerializer.Deserialize<ContractHoldersCensusLevelSchema>(json, JsonOptions);

        // Assert
        result.Should().NotBeNull();
        result!.CensusLevel.Should().BeNull();
    }

    #endregion

    #region JToken Parsing Tests

    [Fact]
    public void JToken_ToObject_CasesDataSchema_ShouldParseCorrectly()
    {
        // Arrange
        string json = """
        {
            "properties": {
                "testField": {
                    "type": "string",
                    "meta": {
                        "label": "Test Field",
                        "required": true
                    }
                }
            }
        }
        """;

        using var document = JsonDocument.Parse(json);

        // Act
        CasesDataSchema? result = JsonSerializer.Deserialize<CasesDataSchema>(document.RootElement.GetRawText(), JsonOptions);

        // Assert
        result.Should().NotBeNull();
        result!.Properties.Should().NotBeNull();
        result.Properties.Should().HaveCount(1);
        result.Properties.Should().ContainKey("testField");
    }

    [Fact]
    public void JToken_ToObject_ContractHoldersCensusLevelSchema_ShouldParseCorrectly()
    {
        // Arrange
        string json = """
        {
            "censusLevel": [
                {
                    "id": "test",
                    "name": "Test"
                }
            ]
        }
        """;

        using var document = JsonDocument.Parse(json);

        // Act
        ContractHoldersCensusLevelSchema? result = JsonSerializer.Deserialize<ContractHoldersCensusLevelSchema>(document.RootElement.GetRawText(), JsonOptions);

        // Assert
        result.Should().NotBeNull();
        result!.CensusLevel.Should().NotBeNull();
        result.CensusLevel.Should().HaveCount(1);
        result.CensusLevel![0].Id.Should().Be("test");
        result.CensusLevel[0].Name.Should().Be("Test");
    }

    #endregion

    #region Complex Field Types Tests

    [Fact]
    public void PolicyMemberCustomField_WithFormulaType_ShouldParseCorrectly()
    {
        // Arrange
        string json = """
        {
            "type": "string",
            "meta": {
                "label": "Calculated Field",
                "fieldType": "dynamic",
                "required": false,
                "formula": [
                    {
                        "id": "formula1",
                        "name": "Test Formula",
                        "label": "Test Formula Label"
                    }
                ]
            }
        }
        """;

        // Act
        PolicyMemberCustomField? result = JsonSerializer.Deserialize<PolicyMemberCustomField>(json, JsonOptions);

        // Assert
        result.Should().NotBeNull();
        result!.Type.Should().Be(PolicyMemberCustomFieldType.String);
        result.Meta.Should().NotBeNull();
        result.Meta!.FieldType.Should().Be("dynamic");
        result.Meta.Formula.Should().NotBeNull();
        result.Meta.Formula.Should().HaveCount(1);
        result.Meta.Formula![0]!.Id.Should().Be("formula1");
        result.Meta.Formula[0]!.Name.Should().Be("Test Formula");
        result.Meta.Formula[0]!.Label.Should().Be("Test Formula Label");
    }

    [Fact]
    public void PolicyMemberCustomField_WithOptionsType_ShouldParseCorrectly()
    {
        // Arrange
        string json = """
        {
            "type": "string",
            "meta": {
                "label": "Select Field",
                "required": true,
                "options": [
                    {
                        "value": "option1",
                        "name": "Option 1"
                    },
                    {
                        "value": "option2",
                        "name": "Option 2"
                    }
                ]
            }
        }
        """;

        // Act
        PolicyMemberCustomField? result = JsonSerializer.Deserialize<PolicyMemberCustomField>(json, JsonOptions);

        // Assert
        result.Should().NotBeNull();
        result!.Type.Should().Be(PolicyMemberCustomFieldType.String);
        result.Meta.Should().NotBeNull();
        result.Meta!.Options.Should().NotBeNull();
        result.Meta.Options.Should().HaveCount(2);
        result.Meta.Options![0].Value.ToString().Should().Be("option1");
        result.Meta.Options[0].Name.Should().Be("Option 1");
        result.Meta.Options[1].Value.ToString().Should().Be("option2");
        result.Meta.Options[1].Name.Should().Be("Option 2");
    }

    #endregion
}
