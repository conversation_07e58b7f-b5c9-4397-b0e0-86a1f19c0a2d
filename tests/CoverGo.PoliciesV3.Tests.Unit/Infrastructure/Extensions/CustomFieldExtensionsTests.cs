using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Infrastructure.Extensions;
using CoverGo.PoliciesV3.Infrastructure.Schemas;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Extensions;

public class CustomFieldExtensionsTests
{
    #region String Field Type Tests

    [Fact]
    public void ToCustomFieldType_WithStringField_ShouldReturnStringFieldType()
    {
        // Arrange
        var field = new PolicyMemberCustomField
        {
            Type = PolicyMemberCustomFieldType.String,
            Meta = new PolicyMemberCustomFieldMeta
            {
                Label = "Test String Field"
            }
        };

        // Act
        CustomFieldTypeBase? result = field.ToCustomFieldType();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<StringFieldType>();

        var stringFieldType = result as StringFieldType;
        stringFieldType!.Options.Should().BeNull();
        stringFieldType.Validations.Should().BeNull();
    }

    [Fact]
    public void ToCustomFieldType_WithStringFieldWithOptions_ShouldReturnStringFieldTypeWithOptions()
    {
        // Arrange
        var field = new PolicyMemberCustomField
        {
            Type = PolicyMemberCustomFieldType.String,
            Meta = new PolicyMemberCustomFieldMeta
            {
                Label = "Test String Field",
                Options =
                [
                    new() { Value = "option1", Name = "Option 1" },
                    new() { Value = "option2", Name = "Option 2" }
                ]
            }
        };

        // Act
        CustomFieldTypeBase? result = field.ToCustomFieldType();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<StringFieldType>();

        var stringFieldType = result as StringFieldType;
        stringFieldType!.Options.Should().NotBeNull();
        stringFieldType.Options.Should().HaveCount(2);
        stringFieldType.Options![0].Value.Should().Be("option1");
        stringFieldType.Options[0].Label.Should().Be("Option 1");
        stringFieldType.Options[1].Value.Should().Be("option2");
        stringFieldType.Options[1].Label.Should().Be("Option 2");
    }

    [Fact]
    public void ToCustomFieldType_WithDateFieldType_ShouldReturnDateFieldType()
    {
        // Arrange
        var field = new PolicyMemberCustomField
        {
            Type = PolicyMemberCustomFieldType.String,
            Meta = new PolicyMemberCustomFieldMeta
            {
                Label = "Test Date Field",
                FieldType = "date"
            }
        };

        // Act
        CustomFieldTypeBase? result = field.ToCustomFieldType();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<DateFieldType>();
    }

    #endregion

    #region Number Field Type Tests

    [Fact]
    public void ToCustomFieldType_WithNumberField_ShouldReturnNumberFieldType()
    {
        // Arrange
        var field = new PolicyMemberCustomField
        {
            Type = PolicyMemberCustomFieldType.Number,
            Meta = new PolicyMemberCustomFieldMeta
            {
                Label = "Test Number Field"
            }
        };

        // Act
        CustomFieldTypeBase? result = field.ToCustomFieldType();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<NumberFieldType>();
    }

    #endregion

    #region Boolean Field Type Tests

    [Fact]
    public void ToCustomFieldType_WithBooleanField_ShouldReturnBooleanFieldType()
    {
        // Arrange
        var field = new PolicyMemberCustomField
        {
            Type = PolicyMemberCustomFieldType.Boolean,
            Meta = new PolicyMemberCustomFieldMeta
            {
                Label = "Test Boolean Field"
            }
        };

        // Act
        CustomFieldTypeBase? result = field.ToCustomFieldType();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<BooleanFieldType>();
    }

    #endregion

    #region Files Field Type Tests

    [Fact]
    public void ToCustomFieldType_WithFilesField_ShouldReturnFilesFieldType()
    {
        // Arrange
        var field = new PolicyMemberCustomField
        {
            Type = PolicyMemberCustomFieldType.Files,
            Meta = new PolicyMemberCustomFieldMeta
            {
                Label = "Test Files Field"
            }
        };

        // Act
        CustomFieldTypeBase? result = field.ToCustomFieldType();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<FilesFieldType>();
    }

    #endregion

    #region Formula Field Type Tests

    [Fact]
    public void ToCustomFieldType_WithFormulaField_ShouldReturnFormulaFieldType()
    {
        // Arrange
        var field = new PolicyMemberCustomField
        {
            Type = PolicyMemberCustomFieldType.String,
            Meta = new PolicyMemberCustomFieldMeta
            {
                Label = "Test Formula Field",
                FieldType = "dynamic",
                Formula =
                [
                    new PolicyMemberCustomFieldFormula
                    {
                        Id = "formula1",
                        Name = "Test Formula",
                        Label = "Test Formula Label"
                    }
                ]
            }
        };

        // Act
        CustomFieldTypeBase? result = field.ToCustomFieldType();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<FormulaFieldType>();
    }

    #endregion

    #region Address Field Type Tests

    [Fact]
    public void ToCustomFieldType_WithAddressField_ShouldReturnAddressFieldType()
    {
        // Arrange
        var field = new PolicyMemberCustomField
        {
            Type = PolicyMemberCustomFieldType.Address,
            Meta = new PolicyMemberCustomFieldMeta
            {
                Label = "Test Address Field"
            },
            Properties = new Dictionary<string, PolicyMemberCustomField>
            {
                ["street"] = new()
                {
                    Type = PolicyMemberCustomFieldType.String,
                    Meta = new PolicyMemberCustomFieldMeta { Label = "Street", Required = true }
                },
                ["city"] = new()
                {
                    Type = PolicyMemberCustomFieldType.String,
                    Meta = new PolicyMemberCustomFieldMeta { Label = "City", Required = true }
                }
            }
        };

        // Act
        CustomFieldTypeBase? result = field.ToCustomFieldType();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<AddressFieldType>();

        var addressFieldType = result as AddressFieldType;
        addressFieldType!.InnerFieldDefinitions.Should().HaveCount(2);
        addressFieldType.InnerFieldDefinitions.Should().Contain(f => f.Name == "street");
        addressFieldType.InnerFieldDefinitions.Should().Contain(f => f.Name == "city");
    }

    #endregion

    #region Object Field Type Tests

    [Fact]
    public void ToCustomFieldType_WithObjectField_ShouldReturnObjectFieldType()
    {
        // Arrange
        var field = new PolicyMemberCustomField
        {
            Type = PolicyMemberCustomFieldType.Object,
            Meta = new PolicyMemberCustomFieldMeta
            {
                Label = "Test Object Field"
            },
            Properties = new Dictionary<string, PolicyMemberCustomField>
            {
                ["property1"] = new()
                {
                    Type = PolicyMemberCustomFieldType.String,
                    Meta = new PolicyMemberCustomFieldMeta { Label = "Property 1", Required = true }
                },
                ["property2"] = new()
                {
                    Type = PolicyMemberCustomFieldType.Number,
                    Meta = new PolicyMemberCustomFieldMeta { Label = "Property 2", Required = false }
                }
            }
        };

        // Act
        CustomFieldTypeBase? result = field.ToCustomFieldType();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<ObjectFieldType>();

        var objectFieldType = result as ObjectFieldType;
        // Current implementation creates empty ObjectFieldType regardless of Properties
        objectFieldType!.InnerFieldDefinitions.Should().HaveCount(0);
    }

    #endregion

    #region Error Cases

    [Fact]
    public void ToCustomFieldType_WithAddressFieldWithoutProperties_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var field = new PolicyMemberCustomField
        {
            Type = PolicyMemberCustomFieldType.Address,
            Meta = new PolicyMemberCustomFieldMeta
            {
                Label = "Test Address Field"
            },
            Properties = null
        };

        // Act & Assert
        Func<CustomFieldTypeBase?> action = () => field.ToCustomFieldType();
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Address type should have defined properties");
    }

    [Fact]
    public void ToCustomFieldType_WithObjectFieldWithoutProperties_ShouldReturnEmptyObjectFieldType()
    {
        // Arrange
        var field = new PolicyMemberCustomField
        {
            Type = PolicyMemberCustomFieldType.Object,
            Meta = new PolicyMemberCustomFieldMeta
            {
                Label = "Test Object Field"
            },
            Properties = null
        };

        // Act
        CustomFieldTypeBase? result = field.ToCustomFieldType();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<ObjectFieldType>();

        var objectFieldType = result as ObjectFieldType;
        // Current implementation creates empty ObjectFieldType even when Properties is null
        objectFieldType!.InnerFieldDefinitions.Should().HaveCount(0);
    }

    #endregion
}
