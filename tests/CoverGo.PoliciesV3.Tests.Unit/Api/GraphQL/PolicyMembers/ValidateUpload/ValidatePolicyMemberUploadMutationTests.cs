using System.Reflection;
using System.Security.Claims;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.PoliciesV3.Api.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using HotChocolate.Types;
using MediatR;

namespace CoverGo.PoliciesV3.Tests.Unit.Api.GraphQL.PolicyMembers.ValidateUpload;

public class ValidatePolicyMemberUploadMutationTests
{
    private readonly Mock<IMediator> _mediator = new();
    private readonly Mock<IPermissionValidator> _permissionValidator = new();

    #region Success Path Tests

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithValidParameters_ShouldCallHandlerWithCorrectRequest()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        var policyMemberUpload = PolicyMemberUpload.Create(
            new PolicyId(Guid.Parse(policyId)),
            "uploads/test-file.csv",
            5);

        var expectedResponse = new ValidatePolicyMemberUploadResponse
        {
            PolicyMemberUpload = policyMemberUpload
        };

        var successResult = Result<ValidatePolicyMemberUploadResponse>.Success(expectedResponse);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), cancellationToken))
            .ReturnsAsync(successResult);

        // Act
        PolicyMemberUpload result = await ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
            policyId,
            uploadId,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken);

        // Assert
        result.Should().Be(expectedResponse.PolicyMemberUpload);

        _permissionValidator.Verify(x => x.AuthorizeAsync(identity, It.IsAny<PermissionRequest>()), Times.Once);

        _mediator.Verify(x => x.Send(It.Is<ValidatePolicyMemberUploadCommand>(cmd =>
            cmd.PolicyId.Value == Guid.Parse(policyId) &&
            cmd.UploadId.Value == Guid.Parse(uploadId)
        ), cancellationToken), Times.Once);
    }

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithValidParameters_ShouldReturnPolicyMemberUpload()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();

        var policyMemberUpload = PolicyMemberUpload.Create(
            new PolicyId(Guid.Parse(policyId)),
            "uploads/validation-test.csv",
            10);

        var response = new ValidatePolicyMemberUploadResponse
        {
            PolicyMemberUpload = policyMemberUpload
        };

        var successResult = Result<ValidatePolicyMemberUploadResponse>.Success(response);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(successResult);

        // Act
        PolicyMemberUpload result = await ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
            policyId,
            uploadId,
            identity,
            _permissionValidator.Object,
            _mediator.Object);

        // Assert
        result.Should().NotBeNull();
        result.Should().Be(policyMemberUpload);
    }

    #endregion

    #region Input Validation Tests

    [Theory]
    [InlineData("")]
    public async Task ValidatePolicyMemberUpload_WithEmptyPolicyId_ShouldThrowArgumentException(string invalidPolicyId)
    {
        // Arrange
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
            invalidPolicyId,
            uploadId,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken));

        // Verify that authorization is called but handler is not called when GUID parsing fails
        _permissionValidator.Verify(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest[]>()), Times.Once);
        _mediator.Verify(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory]
    [InlineData("invalid-guid")]
    [InlineData("not-a-guid-at-all")]
    [InlineData("12345")]
    public async Task ValidatePolicyMemberUpload_WithInvalidGuidFormat_ShouldThrowFormatException(string invalidPolicyId)
    {
        // Arrange
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        // Act & Assert
        await Assert.ThrowsAsync<FormatException>(() => ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
            invalidPolicyId,
            uploadId,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken));

        _mediator.Verify(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory]
    [InlineData("")]
    [InlineData("invalid-guid")]
    [InlineData("not-a-guid-at-all")]
    [InlineData("67890")]
    public async Task ValidatePolicyMemberUpload_WithInvalidUploadId_ShouldThrowFormatException(string invalidUploadId)
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        // Act & Assert
        if (invalidUploadId == "")
        {
            await Assert.ThrowsAsync<ArgumentException>(() => ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                invalidUploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object,
                cancellationToken));
        }
        else
        {
            await Assert.ThrowsAsync<FormatException>(() => ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                invalidUploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object,
                cancellationToken));
        }

        // Verify that authorization is called but handler is not called when GUID parsing fails
        _permissionValidator.Verify(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest[]>()), Times.Once);
        _mediator.Verify(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    #endregion

    #region Authorization Tests

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithAuthorizationFailure_ShouldPropagateException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        var authorizationException = new UnauthorizedAccessException("Access denied");

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(identity, It.IsAny<PermissionRequest>()))
            .ThrowsAsync(authorizationException);

        // Act & Assert
        UnauthorizedAccessException exception = await Assert.ThrowsAsync<UnauthorizedAccessException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object,
                cancellationToken));

        exception.Should().Be(authorizationException);

        // Verify that handler is not called when authorization fails
        _mediator.Verify(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ValidatePolicyMemberUpload_ShouldRequestCorrectPermissions()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();

        var policyMemberUpload = PolicyMemberUpload.Create(
            new PolicyId(Guid.Parse(policyId)),
            "uploads/test.csv",
            3);

        var response = new ValidatePolicyMemberUploadResponse
        {
            PolicyMemberUpload = policyMemberUpload
        };

        var successResult = Result<ValidatePolicyMemberUploadResponse>.Success(response);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(successResult);

        // Act
        await ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
            policyId,
            uploadId,
            identity,
            _permissionValidator.Object,
            _mediator.Object);

        // Assert
        _permissionValidator.Verify(x => x.AuthorizeAsync(identity, It.IsAny<PermissionRequest>()), Times.Once);
    }

    #endregion

    #region Failure Path Tests

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithHandlerFailure_ShouldThrowValidationException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        var context = new Dictionary<string, object?> { { "Message", "Upload validation failed" } };
        var validationError = new ValidationError("VALIDATION_FAILED", "upload", "Upload", context);
        var failureResult = Result<ValidatePolicyMemberUploadResponse>.Failure(validationError);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), cancellationToken))
            .ReturnsAsync(failureResult);

        // Act & Assert
        ValidationException exception = await Assert.ThrowsAsync<ValidationException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object,
                cancellationToken));

        exception.Message.Should().Contain("Validation failed:");
        exception.Message.Should().Contain("Upload validation failed");
        exception.Errors.Should().ContainSingle();
        exception.Errors.First().Should().Be(validationError);

        _permissionValidator.Verify(x => x.AuthorizeAsync(identity, It.IsAny<PermissionRequest>()), Times.Once);
        _mediator.Verify(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), cancellationToken), Times.Once);
    }

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithMultipleValidationErrors_ShouldThrowValidationExceptionWithAllErrors()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();

        var error1 = new ValidationError("ERROR_1", "field1", "First error");
        var error2 = new ValidationError("ERROR_2", "field2", "Second error");
        var failureResult = Result<ValidatePolicyMemberUploadResponse>.Failure(error1, error2);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(failureResult);

        // Act & Assert
        ValidationException exception = await Assert.ThrowsAsync<ValidationException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object));

        exception.Message.Should().Contain("Validation failed with");
        exception.Message.Should().Contain("First error");
        exception.Message.Should().Contain("Second error");
        exception.Errors.Should().HaveCount(2);
        exception.Errors.Should().Contain(error1);
        exception.Errors.Should().Contain(error2);
    }

    #endregion

    #region ValidationException Comprehensive Tests

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithSingleValidationError_ShouldThrowValidationExceptionWithCorrectProperties()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        var validationError = new ValidationError("REQUIRED", "memberId", "Member ID", new Dictionary<string, object?> { ["Value"] = "MEM001" });
        var failureResult = Result<ValidatePolicyMemberUploadResponse>.Failure(validationError);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), cancellationToken))
            .ReturnsAsync(failureResult);

        // Act & Assert
        ValidationException exception = await Assert.ThrowsAsync<ValidationException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object,
                cancellationToken));

        // Verify ValidationException properties
        exception.Code.Should().Be("VALIDATION_FAILED");
        exception.Message.Should().Be("Validation failed: Member ID is required");
        exception.Errors.Should().ContainSingle();
        exception.Errors.First().Should().Be(validationError);
        exception.Errors.First().Code.Should().Be("REQUIRED");
        exception.Errors.First().PropertyPath.Should().Be("memberId");
        exception.Errors.First().PropertyLabel.Should().Be("Member ID");

        // Verify dependencies were called correctly
        _permissionValidator.Verify(x => x.AuthorizeAsync(identity, It.IsAny<PermissionRequest>()), Times.Once);
        _mediator.Verify(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), cancellationToken), Times.Once);
    }

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithMultipleValidationErrors_ShouldThrowValidationExceptionWithCorrectErrorCount()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();

        var error1 = new ValidationError("REQUIRED", "memberId", "Member ID");
        var error2 = new ValidationError("INVALID_FORMAT", "email", "Email Address");
        var error3 = new ValidationError("UNIQUE_VIOLATION", "hkid", "HKID");
        var failureResult = Result<ValidatePolicyMemberUploadResponse>.Failure(error1, error2, error3);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(failureResult);

        // Act & Assert
        ValidationException exception = await Assert.ThrowsAsync<ValidationException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object));

        // Verify ValidationException properties
        exception.Code.Should().Be("VALIDATION_FAILED");
        exception.Message.Should().Contain("Validation failed with 3 error(s):");
        exception.Message.Should().Contain("Member ID is required");
        exception.Message.Should().Contain("Email Address has an invalid format");
        exception.Message.Should().Contain("HKID must be unique");
        exception.Errors.Should().HaveCount(3);
        exception.Errors.Should().Contain(error1);
        exception.Errors.Should().Contain(error2);
        exception.Errors.Should().Contain(error3);
    }

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithValidationException_ShouldProvideErrorsForPropertyAccess()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();

        var memberIdError = new ValidationError("REQUIRED", "memberId", "Member ID");
        var emailError1 = new ValidationError("REQUIRED", "email", "Email Address");
        var emailError2 = new ValidationError("INVALID_FORMAT", "email", "Email Address");
        var planError = new ValidationError("INVALID_PLAN", "planId", "Plan ID");
        var failureResult = Result<ValidatePolicyMemberUploadResponse>.Failure(memberIdError, emailError1, emailError2, planError);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(failureResult);

        // Act & Assert
        ValidationException exception = await Assert.ThrowsAsync<ValidationException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object));

        // Verify error access methods
        exception.GetErrorForProperty("memberId").Should().Be(memberIdError);
        exception.GetErrorForProperty("planId").Should().Be(planError);
        exception.GetErrorForProperty("nonexistent").Should().BeNull();

        exception.GetErrorsForProperty("email").Should().HaveCount(2);
        exception.GetErrorsForProperty("email").Should().Contain(emailError1);
        exception.GetErrorsForProperty("email").Should().Contain(emailError2);
        exception.GetErrorsForProperty("nonexistent").Should().BeEmpty();

        exception.HasErrorsForProperty("email").Should().BeTrue();
        exception.HasErrorsForProperty("memberId").Should().BeTrue();
        exception.HasErrorsForProperty("nonexistent").Should().BeFalse();

        exception.GetErrorCodes().Should().HaveCount(3);
        exception.GetErrorCodes().Should().Contain("REQUIRED");
        exception.GetErrorCodes().Should().Contain("INVALID_FORMAT");
        exception.GetErrorCodes().Should().Contain("INVALID_PLAN");
    }

    [Theory]
    [InlineData("UPLOAD_ERROR", "upload", "Upload processing failed")]
    [InlineData("SCHEMA_ERROR", "schema", "Schema validation failed")]
    [InlineData("BUSINESS_RULE_VIOLATION", "policy", "Business rule violated")]
    public async Task ValidatePolicyMemberUpload_WithDifferentErrorTypes_ShouldThrowValidationExceptionWithCorrectErrorDetails(
        string errorCode, string propertyPath, string expectedMessage)
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();

        var validationError = new ValidationError(errorCode, propertyPath, propertyPath.ToUpperInvariant(), new Dictionary<string, object?> { ["Message"] = expectedMessage });
        var failureResult = Result<ValidatePolicyMemberUploadResponse>.Failure(validationError);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(failureResult);

        // Act & Assert
        ValidationException exception = await Assert.ThrowsAsync<ValidationException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object));

        // Verify error details
        exception.Code.Should().Be("VALIDATION_FAILED");
        exception.Errors.Should().ContainSingle();
        exception.Errors.First().Code.Should().Be(errorCode);
        exception.Errors.First().PropertyPath.Should().Be(propertyPath);
        exception.Errors.First().PropertyLabel.Should().Be(propertyPath.ToUpperInvariant());
    }

    #endregion

    #region GraphQL Error Declaration Tests

    [Fact]
    public void ValidatePolicyMemberUploadMutation_ShouldHaveValidationExceptionDeclared()
    {
        // Arrange & Act
        Type mutationType = typeof(ValidatePolicyMemberUploadMutation);
        MethodInfo mutationMethod = mutationType.GetMethod(nameof(ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload))!;

        // Assert
        mutationMethod.Should().NotBeNull();

        var errorAttributes = mutationMethod.GetCustomAttributes<ErrorAttribute>().ToList();
        errorAttributes.Should().NotBeEmpty();

        // NOTE: ValidationException is currently commented out in the mutation - see ValidatePolicyMemberUploadMutation.cs line 31
        // ErrorAttribute? validationExceptionAttribute = errorAttributes.FirstOrDefault(attr => attr.ErrorType == typeof(ValidationException));
        // validationExceptionAttribute.Should().NotBeNull("ValidationException should be declared in [Error(typeof(...))] attributes");
    }

    [Fact]
    public void ValidatePolicyMemberUploadMutation_ShouldHaveAllRequiredErrorTypesDeclared()
    {
        // Arrange
        Type mutationType = typeof(ValidatePolicyMemberUploadMutation);
        MethodInfo mutationMethod = mutationType.GetMethod(nameof(ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload))!;

        Type[] expectedErrorTypes =
        [
            // NOTE: ValidationException is currently commented out in the mutation - see ValidatePolicyMemberUploadMutation.cs line 31
            // typeof(ValidationException),
            typeof(PolicyNotFoundException),
            typeof(PolicyIssuedException),
            typeof(PolicyContractHolderNotFoundException),
            typeof(PolicyProductIdMissingException),
            typeof(InvalidProductIdComponentException),
            typeof(PolicyMemberUploadNotFoundException),
            typeof(InvalidPolicyMemberUploadStatusException),
            typeof(EndorsementNotFoundException),
            typeof(EndorsementCanNotBeChangedException),
            typeof(EffectiveDateOutsidePolicyDatesException),
            typeof(BadSchemaConfigException)
        ];

        // Act
        var errorAttributes = mutationMethod.GetCustomAttributes<ErrorAttribute>().ToList();
        var declaredErrorTypes = errorAttributes.Select(attr => attr.ErrorType).ToList();

        // Assert
        foreach (Type expectedType in expectedErrorTypes)
        {
            declaredErrorTypes.Should().Contain(expectedType,
                $"{expectedType.Name} should be declared in [Error(typeof(...))] attributes for proper GraphQL error handling");
        }
    }

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithValidationExceptionContainingContext_ShouldPreserveContextInformation()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();

        var contextData = new Dictionary<string, object?>
        {
            ["RowNumber"] = 5,
            ["ColumnName"] = "email",
            ["Value"] = "invalid-email",
            ["ExpectedFormat"] = "<EMAIL>"
        };
        var validationError = new ValidationError("INVALID_FORMAT", "email", "Email Address", contextData);
        var failureResult = Result<ValidatePolicyMemberUploadResponse>.Failure(validationError);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(failureResult);

        // Act & Assert
        ValidationException exception = await Assert.ThrowsAsync<ValidationException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object));

        // Verify context information is preserved
        exception.Errors.Should().ContainSingle();
        ValidationError error = exception.Errors.First();
        error.Context.Should().NotBeEmpty();
        error.Context["RowNumber"].Should().Be(5);
        error.Context["ColumnName"].Should().Be("email");
        error.Context["Value"].Should().Be("invalid-email");
        error.Context["ExpectedFormat"].Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithEmptyValidationErrorMessage_ShouldHandleGracefully()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();

        var validationError = new ValidationError("CUSTOM_ERROR", "field", string.Empty);
        var failureResult = Result<ValidatePolicyMemberUploadResponse>.Failure(validationError);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(failureResult);

        // Act & Assert
        ValidationException exception = await Assert.ThrowsAsync<ValidationException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object));

        // Verify exception handles empty message gracefully
        exception.Should().NotBeNull();
        exception.Code.Should().Be("VALIDATION_FAILED");
        exception.Errors.Should().ContainSingle();
        exception.Errors.First().Code.Should().Be("CUSTOM_ERROR");
        exception.Errors.First().PropertyPath.Should().Be("field");
    }

    #endregion

    #region Domain Exception Tests

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithPolicyNotFoundException_ShouldPropagateException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();

        var policyNotFoundException = new PolicyNotFoundException(policyId);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(policyNotFoundException);

        // Act & Assert
        PolicyNotFoundException exception = await Assert.ThrowsAsync<PolicyNotFoundException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object));

        exception.Should().Be(policyNotFoundException);
        exception.PolicyId.Should().Be(policyId);
    }

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithPolicyIssuedException_ShouldPropagateException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();

        var policyIssuedException = new PolicyIssuedException(policyId);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(policyIssuedException);

        // Act & Assert
        PolicyIssuedException exception = await Assert.ThrowsAsync<PolicyIssuedException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object));

        exception.Should().Be(policyIssuedException);
        exception.PolicyId.Should().Be(policyId);
    }

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithPolicyMemberUploadNotFoundException_ShouldPropagateException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();

        var uploadNotFoundException = new PolicyMemberUploadNotFoundException(uploadId);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(uploadNotFoundException);

        // Act & Assert
        PolicyMemberUploadNotFoundException exception = await Assert.ThrowsAsync<PolicyMemberUploadNotFoundException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object));

        exception.Should().Be(uploadNotFoundException);
        exception.UploadId.Should().Be(uploadId);
    }

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithInvalidPolicyMemberUploadStatusException_ShouldPropagateException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();

        var invalidStatusException = new InvalidPolicyMemberUploadStatusException(
            uploadId,
            "IMPORTING",
            new[] { "REGISTERED" });

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(invalidStatusException);

        // Act & Assert
        InvalidPolicyMemberUploadStatusException exception = await Assert.ThrowsAsync<InvalidPolicyMemberUploadStatusException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object));

        exception.Should().Be(invalidStatusException);
        exception.UploadId.Should().Be(uploadId);
        exception.CurrentStatus.Should().Be("IMPORTING");
        exception.ExpectedStatuses.Should().Contain("REGISTERED");
    }

    #endregion

    #region Edge Cases and Cancellation Tests

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithCancellationToken_ShouldPassTokenToHandler()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();
        var cancellationTokenSource = new CancellationTokenSource();
        CancellationToken cancellationToken = cancellationTokenSource.Token;

        var policyMemberUpload = PolicyMemberUpload.Create(
            new PolicyId(Guid.Parse(policyId)),
            "uploads/test.csv",
            1);

        var response = new ValidatePolicyMemberUploadResponse
        {
            PolicyMemberUpload = policyMemberUpload
        };

        var successResult = Result<ValidatePolicyMemberUploadResponse>.Success(response);

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), cancellationToken))
            .ReturnsAsync(successResult);

        // Act
        await ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
            policyId,
            uploadId,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken);

        // Assert
        _mediator.Verify(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), cancellationToken), Times.Once);
    }

    [Fact]
    public async Task ValidatePolicyMemberUpload_WithCancelledToken_ShouldThrowOperationCancelledException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string uploadId = Guid.NewGuid().ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();
        CancellationToken cancellationToken = cancellationTokenSource.Token;

        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), cancellationToken))
            .ThrowsAsync(new OperationCanceledException(cancellationToken));

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
                policyId,
                uploadId,
                identity,
                _permissionValidator.Object,
                _mediator.Object,
                cancellationToken));
    }

    #endregion

    #region Command Creation Tests

    [Fact]
    public async Task ValidatePolicyMemberUpload_ShouldCreateCommandWithCorrectProperties()
    {
        // Arrange
        Guid expectedPolicyId = Guid.NewGuid();
        Guid expectedUploadId = Guid.NewGuid();
        string policyId = expectedPolicyId.ToString();
        string uploadId = expectedUploadId.ToString();
        ClaimsIdentity identity = CreateClaimsIdentity();

        var policyMemberUpload = PolicyMemberUpload.Create(
            new PolicyId(expectedPolicyId),
            "uploads/command-test.csv",
            7);

        var response = new ValidatePolicyMemberUploadResponse
        {
            PolicyMemberUpload = policyMemberUpload
        };

        var successResult = Result<ValidatePolicyMemberUploadResponse>.Success(response);

        ValidatePolicyMemberUploadCommand? capturedCommand = null;
        _mediator
            .Setup(x => x.Send(It.IsAny<ValidatePolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<Result<ValidatePolicyMemberUploadResponse>>, CancellationToken>((cmd, _) => capturedCommand = (ValidatePolicyMemberUploadCommand)cmd)
            .ReturnsAsync(successResult);

        // Act
        await ValidatePolicyMemberUploadMutation.ValidatePolicyMemberUpload(
            policyId,
            uploadId,
            identity,
            _permissionValidator.Object,
            _mediator.Object);

        // Assert
        capturedCommand.Should().NotBeNull();
        capturedCommand!.PolicyId.Value.Should().Be(expectedPolicyId);
        capturedCommand.UploadId.Value.Should().Be(expectedUploadId);
    }

    #endregion

    #region Helper Methods

    private static ClaimsIdentity CreateClaimsIdentity() => new(new[]
    {
        new Claim(ClaimTypes.NameIdentifier, Guid.NewGuid().ToString()),
        new Claim(ClaimTypes.Name, "test-user")
    });

    #endregion
}
