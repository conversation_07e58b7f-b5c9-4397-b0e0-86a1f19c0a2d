using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.PoliciesV3.Api.PolicyMemberUploads.RegisterUpload;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.RegisterPolicyMemberUpload;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using MediatR;
using System.Security.Claims;

namespace CoverGo.PoliciesV3.Tests.Unit.Api.GraphQL.PolicyMembers.RegisterUpload;

public class RegisterPolicyMemberUploadMutationTests
{
    private readonly Mock<IMediator> _mediator = new();
    private readonly Mock<IPermissionValidator> _permissionValidator = new();

    [Fact]
    public async Task RegisterPolicyMemberUpload_WithValidParameters_ShouldCallHandlerWithCorrectRequest()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string endorsementId = Guid.NewGuid().ToString();
        string path = "uploads/test-file.csv";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        var expectedResponse = new RegisterPolicyMemberUploadResponse
        {
            PolicyMemberUpload = PolicyMemberUpload.Create(policyId, path, 3, endorsementId)
        };

        _mediator
            .Setup(x => x.Send(It.IsAny<RegisterPolicyMemberUploadCommand>(), cancellationToken))
            .ReturnsAsync(expectedResponse);

        // Act
        var input = new RegisterPolicyMemberUploadInput
        {
            PolicyId = policyId,
            EndorsementId = endorsementId,
            Path = path
        };

        PolicyMemberUpload result = await RegisterPolicyMemberUploadMutation.RegisterPolicyMemberUpload(
            input,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken);

        // Assert
        result.Should().Be(expectedResponse.PolicyMemberUpload);

        _permissionValidator.Verify(x => x.AuthorizeAsync(identity, It.IsAny<PermissionRequest>()), Times.Once);
        _mediator.Verify(x => x.Send(It.Is<RegisterPolicyMemberUploadCommand>(cmd =>
            cmd.PolicyId.Value == Guid.Parse(policyId) &&
            cmd.EndorsementId!.Value == Guid.Parse(endorsementId) &&
            cmd.Path == path
        ), cancellationToken), Times.Once);
    }

    [Fact]
    public async Task RegisterPolicyMemberUpload_WithNullEndorsementId_ShouldCallHandlerWithNullEndorsementId()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string? endorsementId = null;
        string path = "uploads/test-file.csv";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        var expectedResponse = new RegisterPolicyMemberUploadResponse
        {
            PolicyMemberUpload = PolicyMemberUpload.Create(policyId, path, 3, null)
        };

        _mediator
            .Setup(x => x.Send(It.IsAny<RegisterPolicyMemberUploadCommand>(), cancellationToken))
            .ReturnsAsync(expectedResponse);

        // Act
        var input = new RegisterPolicyMemberUploadInput
        {
            PolicyId = policyId,
            EndorsementId = endorsementId,
            Path = path
        };

        PolicyMemberUpload result = await RegisterPolicyMemberUploadMutation.RegisterPolicyMemberUpload(
            input,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken);

        // Assert
        result.Should().Be(expectedResponse.PolicyMemberUpload);

        _permissionValidator.Verify(x => x.AuthorizeAsync(identity, It.IsAny<PermissionRequest>()), Times.Once);
        _mediator.Verify(x => x.Send(It.Is<RegisterPolicyMemberUploadCommand>(cmd =>
            cmd.PolicyId.Value == Guid.Parse(policyId) &&
            cmd.EndorsementId == null &&
            cmd.Path == path
        ), cancellationToken), Times.Once);
    }

    [Theory]
    [InlineData("")]
    public async Task RegisterPolicyMemberUpload_WithEmptyPolicyId_ShouldThrowArgumentException(string invalidPolicyId)
    {
        // Arrange
        string? endorsementId = null;
        string path = "uploads/test-file.csv";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        // Act & Assert
        var input = new RegisterPolicyMemberUploadInput
        {
            PolicyId = invalidPolicyId,
            EndorsementId = endorsementId,
            Path = path
        };

        await Assert.ThrowsAsync<ArgumentException>(() => RegisterPolicyMemberUploadMutation.RegisterPolicyMemberUpload(
            input,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken));

        _mediator.Verify(x => x.Send(It.IsAny<RegisterPolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory]
    [InlineData("invalid-guid")]
    [InlineData("not-a-guid-at-all")]
    public async Task RegisterPolicyMemberUpload_WithInvalidGuidFormat_ShouldThrowFormatException(string invalidPolicyId)
    {
        // Arrange
        string? endorsementId = null;
        string path = "uploads/test-file.csv";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        // Act & Assert
        var input = new RegisterPolicyMemberUploadInput
        {
            PolicyId = invalidPolicyId,
            EndorsementId = endorsementId,
            Path = path
        };

        await Assert.ThrowsAsync<FormatException>(() => RegisterPolicyMemberUploadMutation.RegisterPolicyMemberUpload(
            input,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken));

        _mediator.Verify(x => x.Send(It.IsAny<RegisterPolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task RegisterPolicyMemberUpload_WithNullPolicyId_ThrowsArgumentException()
    {
        // Arrange
        string? policyId = null;
        string? endorsementId = null;
        string path = "uploads/test-file.csv";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        // Act & Assert
        var input = new RegisterPolicyMemberUploadInput
        {
            PolicyId = policyId!,
            EndorsementId = endorsementId,
            Path = path
        };

        await Assert.ThrowsAsync<ArgumentException>(() => RegisterPolicyMemberUploadMutation.RegisterPolicyMemberUpload(
            input,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken));

        _mediator.Verify(x => x.Send(It.IsAny<RegisterPolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task RegisterPolicyMemberUpload_WithWhitespacePolicyId_ThrowsArgumentException()
    {
        // Arrange
        string policyId = "   ";
        string? endorsementId = null;
        string path = "uploads/test-file.csv";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        // Act & Assert
        var input = new RegisterPolicyMemberUploadInput
        {
            PolicyId = policyId,
            EndorsementId = endorsementId,
            Path = path
        };

        await Assert.ThrowsAsync<ArgumentException>(() => RegisterPolicyMemberUploadMutation.RegisterPolicyMemberUpload(
            input,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken));

        _mediator.Verify(x => x.Send(It.IsAny<RegisterPolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task RegisterPolicyMemberUpload_WithWhitespaceEndorsementId_ThrowsArgumentException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string endorsementId = "   ";
        string path = "uploads/test-file.csv";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        // Act & Assert
        var input = new RegisterPolicyMemberUploadInput
        {
            PolicyId = policyId,
            EndorsementId = endorsementId,
            Path = path
        };

        await Assert.ThrowsAsync<ArgumentException>(() => RegisterPolicyMemberUploadMutation.RegisterPolicyMemberUpload(
            input,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken));

        _mediator.Verify(x => x.Send(It.IsAny<RegisterPolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task RegisterPolicyMemberUpload_WithMalformedGuidEndorsementId_ThrowsFormatException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string endorsementId = "not-a-guid";
        string path = "uploads/test-file.csv";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        // Act & Assert
        var input = new RegisterPolicyMemberUploadInput
        {
            PolicyId = policyId,
            EndorsementId = endorsementId,
            Path = path
        };

        await Assert.ThrowsAsync<FormatException>(() => RegisterPolicyMemberUploadMutation.RegisterPolicyMemberUpload(
            input,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken));

        _mediator.Verify(x => x.Send(It.IsAny<RegisterPolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task RegisterPolicyMemberUpload_WithComplexMalformedGuidEndorsementId_ThrowsFormatException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string endorsementId = "12345678-1234-1234-1234-123456789012-invalid";
        string path = "uploads/test-file.csv";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        // Act & Assert
        var input = new RegisterPolicyMemberUploadInput
        {
            PolicyId = policyId,
            EndorsementId = endorsementId,
            Path = path
        };

        await Assert.ThrowsAsync<FormatException>(() => RegisterPolicyMemberUploadMutation.RegisterPolicyMemberUpload(
            input,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken));

        _mediator.Verify(x => x.Send(It.IsAny<RegisterPolicyMemberUploadCommand>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task RegisterPolicyMemberUpload_WithEmptyEndorsementId_ShouldTreatAsNull()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string endorsementId = "";
        string path = "uploads/test-file.csv";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        var expectedResponse = new RegisterPolicyMemberUploadResponse
        {
            PolicyMemberUpload = PolicyMemberUpload.Create(policyId, path, 3, null)
        };

        _mediator
            .Setup(x => x.Send(It.IsAny<RegisterPolicyMemberUploadCommand>(), cancellationToken))
            .ReturnsAsync(expectedResponse);

        // Act
        var input = new RegisterPolicyMemberUploadInput
        {
            PolicyId = policyId,
            EndorsementId = endorsementId,
            Path = path
        };

        PolicyMemberUpload result = await RegisterPolicyMemberUploadMutation.RegisterPolicyMemberUpload(
            input,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken);

        // Assert
        result.Should().Be(expectedResponse.PolicyMemberUpload);

        _permissionValidator.Verify(x => x.AuthorizeAsync(identity, It.IsAny<PermissionRequest>()), Times.Once);
        _mediator.Verify(x => x.Send(It.Is<RegisterPolicyMemberUploadCommand>(cmd =>
            cmd.PolicyId.Value == Guid.Parse(policyId) &&
            cmd.EndorsementId == null &&
            cmd.Path == path
        ), cancellationToken), Times.Once);
    }

    [Fact]
    public async Task RegisterPolicyMemberUpload_WhenHandlerThrowsException_ShouldPropagateException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string? endorsementId = null;
        string path = "uploads/test-file.csv";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        var expectedException = new InvalidOperationException("Test exception");

        _mediator
            .Setup(x => x.Send(It.IsAny<RegisterPolicyMemberUploadCommand>(), cancellationToken))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var input = new RegisterPolicyMemberUploadInput
        {
            PolicyId = policyId,
            EndorsementId = endorsementId,
            Path = path
        };

        var actualException = await Assert.ThrowsAsync<InvalidOperationException>(() => RegisterPolicyMemberUploadMutation.RegisterPolicyMemberUpload(
            input,
            identity,
            _permissionValidator.Object,
            _mediator.Object,
            cancellationToken));

        actualException.Should().Be(expectedException);
        _permissionValidator.Verify(x => x.AuthorizeAsync(identity, It.IsAny<PermissionRequest>()), Times.Once);
        _mediator.Verify(x => x.Send(It.IsAny<RegisterPolicyMemberUploadCommand>(), cancellationToken), Times.Once);
    }

    private static ClaimsIdentity CreateClaimsIdentity() => new([
        new Claim(ClaimTypes.NameIdentifier, "test-user-id"),
        new Claim(ClaimTypes.Name, "test-user"),
        new Claim("permissions", "writePolicies")
    ]);
}
