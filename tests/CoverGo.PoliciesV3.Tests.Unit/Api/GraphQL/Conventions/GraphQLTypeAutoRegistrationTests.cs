using CoverGo.PoliciesV3.Api.GraphQL.Common;
using CoverGo.PoliciesV3.Api.GraphQL.Conventions;
using CoverGo.PoliciesV3.Domain.Policies;
using HotChocolate.Execution.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.PoliciesV3.Tests.Unit.Api.GraphQL.Conventions;

public class GraphQLTypeAutoRegistrationTests
{
    [Fact]
    public void AddDomainScalarTypes_ShouldRegisterTypeConverters()
    {
        // Arrange
        var services = new ServiceCollection();
        IRequestExecutorBuilder builder = services.AddGraphQLServer();

        // Act
        builder.AddDomainScalarTypes();

        // Assert
        // The type converter should be registered and functional
        // We can test this by verifying that the FieldMappingHelper can handle various input types
        TestFieldMappingHelperWithDictionary();
        TestFieldMappingHelperWithJsonString();
        TestFieldMappingHelperWithNull();
        TestFieldMappingHelperWithEmptyDictionary();
    }

    private static void TestFieldMappingHelperWithDictionary()
    {
        // Arrange
        var input = new Dictionary<string, object?> { ["name"] = "John", ["age"] = 30 };

        // Act
        List<PolicyField> result = FieldMappingHelper.MapToPolicyFields(input);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(2);
    }

    private static void TestFieldMappingHelperWithJsonString()
    {
        // Arrange
        string input = "{\"name\":\"Jane\",\"age\":25}";

        // Act
        List<PolicyField> result = FieldMappingHelper.MapToPolicyFields(input);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(2);
    }

    private static void TestFieldMappingHelperWithNull()
    {
        // Arrange
        object? input = null;

        // Act
        List<PolicyField> result = FieldMappingHelper.MapToPolicyFields(input);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    private static void TestFieldMappingHelperWithEmptyDictionary()
    {
        // Arrange
        var input = new Dictionary<string, object?>();

        // Act
        List<PolicyField> result = FieldMappingHelper.MapToPolicyFields(input);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public void FieldMappingHelper_ShouldHandleDictionaryInput()
    {
        // Arrange
        var input = new Dictionary<string, object?>
        {
            ["firstName"] = "John",
            ["lastName"] = "Doe",
            ["age"] = 30,
            ["isActive"] = true,
            ["startDate"] = "2024-01-01"
        };

        // Act
        List<PolicyField> result = FieldMappingHelper.MapToPolicyFields(input);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(5);

        result.Should().Contain(field => field.Key == "firstName" && field.Value != null && field.Value.ToString() == "John");
        result.Should().Contain(field => field.Key == "lastName" && field.Value != null && field.Value.ToString() == "Doe");
        result.Should().Contain(field => field.Key == "age" && field.Value != null && field.Value.ToString() == "30");
        result.Should().Contain(field => field.Key == "isActive" && field.Value != null && field.Value.ToString() == "True");
        result.Should().Contain(field => field.Key == "startDate" && field.Value != null && field.Value.ToString() == "2024-01-01");
    }

    [Fact]
    public void FieldMappingHelper_ShouldHandleJsonStringInput()
    {
        // Arrange
        string input = "{\"firstName\":\"Jane\",\"lastName\":\"Smith\",\"age\":25}";

        // Act
        List<PolicyField> result = FieldMappingHelper.MapToPolicyFields(input);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(3);

        result.Should().Contain(field => field.Key == "firstName" && field.Value != null && field.Value.ToString() == "Jane");
        result.Should().Contain(field => field.Key == "lastName" && field.Value != null && field.Value.ToString() == "Smith");
        result.Should().Contain(field => field.Key == "age" && field.Value != null && field.Value.ToString() == "25");
    }

    [Fact]
    public void FieldMappingHelper_ShouldHandleNullInput()
    {
        // Arrange
        object? input = null;

        // Act
        List<PolicyField> result = FieldMappingHelper.MapToPolicyFields(input);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public void FieldMappingHelper_ShouldHandleComplexObjectInput()
    {
        // Arrange
        var input = new Dictionary<string, object?>
        {
            ["address"] = new Dictionary<string, object?>
            {
                ["street"] = "123 Main St",
                ["city"] = "New York",
                ["zipCode"] = "10001"
            },
            ["preferences"] = new[] { "email", "sms" }
        };

        // Act
        List<PolicyField> result = FieldMappingHelper.MapToPolicyFields(input);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(2);

        // Address should be serialized as JSON string
        result.Should().Contain(field => field.Key == "address" && field.Value is string);
        PolicyField addressField = result.First(f => f.Key == "address");
        addressField.Value?.ToString().Should().Contain("123 Main St");
        addressField.Value?.ToString().Should().Contain("New York");

        // Preferences should be a list
        result.Should().Contain(field => field.Key == "preferences" && field.Value is List<object>);
    }

    [Fact]
    public void TransformToDictionary_ShouldConvertPolicyFieldsBackToDictionary()
    {
        // Arrange
        var policyFields = new List<PolicyField>
        {
            new() { Key = "firstName", Value = "John" },
            new() { Key = "lastName", Value = "Doe" },
            new() { Key = "age", Value = 30 }
        };

        // Act
        Dictionary<string, object?> result = FieldMappingHelper.TransformToDictionary(policyFields);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(3);
        result["firstName"].Should().Be("John");
        result["lastName"].Should().Be("Doe");
        result["age"].Should().Be(30);
    }
}