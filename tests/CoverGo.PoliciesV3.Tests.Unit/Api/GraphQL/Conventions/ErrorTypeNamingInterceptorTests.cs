using System.Diagnostics;
using CoverGo.PoliciesV3.Api.GraphQL.Conventions;
using HotChocolate.Configuration;
using HotChocolate.Types.Descriptors.Definitions;
using Microsoft.Extensions.Options;

namespace CoverGo.PoliciesV3.Tests.Unit.Api.GraphQL.Conventions;

public class ErrorTypeNamingInterceptorTests
{
    private readonly ErrorTypeNamingInterceptor _interceptor;
    private readonly GraphQLNamingOptions _options;

    public ErrorTypeNamingInterceptorTests()
    {
        _options = new GraphQLNamingOptions
        {
            SchemaPrefix = "policies"
        };
        IOptions<GraphQLNamingOptions> optionsWrapper = Options.Create(_options);
        _interceptor = new ErrorTypeNamingInterceptor(optionsWrapper);
    }

    #region Constructor Tests

    [Fact]
    public void Constructor_WithNullOptions_ShouldUseDefaultOptions()
    {
        // Act
        var interceptor = new ErrorTypeNamingInterceptor();

        // Assert
        interceptor.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_WithValidOptions_ShouldInitializeCorrectly()
    {
        // Arrange
        IOptions<GraphQLNamingOptions> customOptions = Options.Create(new GraphQLNamingOptions { SchemaPrefix = "custom" });

        // Act
        var interceptor = new ErrorTypeNamingInterceptor(customOptions);

        // Assert
        interceptor.Should().NotBeNull();
    }

    #endregion

    #region OnBeforeCompleteName Tests

    [Fact]
    public void OnBeforeCompleteName_WithNullDefinition_ShouldThrowArgumentNullException()
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();

        // Act & Assert
        Action act = () => _interceptor.OnBeforeCompleteName(context, null!);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void OnBeforeCompleteName_WithNonObjectTypeDefinition_ShouldNotModifyName()
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        var inputDefinition = new InputObjectTypeDefinition { Name = "TestError" };

        // Act
        _interceptor.OnBeforeCompleteName(context, inputDefinition);

        // Assert
        inputDefinition.Name.Should().Be("TestError");
    }

    [Theory]
    [InlineData("ValidationError")]
    [InlineData("BusinessLogicError")]
    [InlineData("CustomError")]
    public void OnBeforeCompleteName_WithErrorTypeHavingMessageField_ShouldApplyPrefix(string originalName)
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        ObjectTypeDefinition objectDefinition = CreateErrorTypeDefinition(originalName, hasMessageField: true);

        // Act
        _interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert
        objectDefinition.Name.Should().Be($"policies_{originalName}");
    }

    [Theory]
    [InlineData("ValidationError")]
    [InlineData("BusinessLogicError")]
    [InlineData("CustomError")]
    public void OnBeforeCompleteName_WithErrorTypeHavingCodeField_ShouldApplyPrefix(string originalName)
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        ObjectTypeDefinition objectDefinition = CreateErrorTypeDefinition(originalName, hasCodeField: true);

        // Act
        _interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert
        objectDefinition.Name.Should().Be($"policies_{originalName}");
    }

    [Theory]
    [InlineData("ValidationError")]
    [InlineData("BusinessLogicError")]
    [InlineData("CustomError")]
    public void OnBeforeCompleteName_WithErrorTypeHavingBothFields_ShouldApplyPrefix(string originalName)
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        ObjectTypeDefinition objectDefinition = CreateErrorTypeDefinition(originalName, hasMessageField: true, hasCodeField: true);

        // Act
        _interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert
        objectDefinition.Name.Should().Be($"policies_{originalName}");
    }

    [Theory]
    [InlineData("policies_ValidationError")]
    [InlineData("policies_BusinessLogicError")]
    [InlineData("policies_CustomError")]
    public void OnBeforeCompleteName_WithAlreadyPrefixedErrorType_ShouldNotApplyPrefixAgain(string originalName)
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        ObjectTypeDefinition objectDefinition = CreateErrorTypeDefinition(originalName, hasMessageField: true);

        // Act
        _interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert
        objectDefinition.Name.Should().Be(originalName);
    }

    [Theory]
    [InlineData("ValidationError")]
    [InlineData("BusinessLogicError")]
    [InlineData("CustomError")]
    public void OnBeforeCompleteName_WithErrorTypeWithoutStandardFields_ShouldNotApplyPrefix(string originalName)
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        ObjectTypeDefinition objectDefinition = CreateErrorTypeDefinition(originalName, hasMessageField: false, hasCodeField: false);

        // Act
        _interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert
        objectDefinition.Name.Should().Be(originalName);
    }

    [Theory]
    [InlineData("ValidationResult")]
    [InlineData("BusinessLogicResult")]
    [InlineData("CustomType")]
    public void OnBeforeCompleteName_WithNonErrorType_ShouldNotApplyPrefix(string originalName)
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        ObjectTypeDefinition objectDefinition = CreateErrorTypeDefinition(originalName, hasMessageField: true);

        // Act
        _interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert
        objectDefinition.Name.Should().Be(originalName);
    }



    #endregion

    #region Custom Schema Prefix Tests

    [Fact]
    public void OnBeforeCompleteName_WithCustomSchemaPrefix_ShouldApplyCustomPrefix()
    {
        // Arrange
        IOptions<GraphQLNamingOptions> customOptions = Options.Create(new GraphQLNamingOptions { SchemaPrefix = "custom" });
        var interceptor = new ErrorTypeNamingInterceptor(customOptions);
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        ObjectTypeDefinition objectDefinition = CreateErrorTypeDefinition("ValidationError", hasMessageField: true);

        // Act
        interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert
        objectDefinition.Name.Should().Be("custom_ValidationError");
    }

    #endregion

    #region Case Sensitivity Tests

    [Theory]
    [InlineData("MESSAGE")]
    [InlineData("Message")]
    [InlineData("message")]
    [InlineData("MeSsAgE")]
    public void OnBeforeCompleteName_WithMessageFieldCaseInsensitive_ShouldApplyPrefix(string fieldName)
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        ObjectTypeDefinition objectDefinition = CreateErrorTypeDefinitionWithCustomFieldName("ValidationError", fieldName);

        // Act
        _interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert
        objectDefinition.Name.Should().Be("policies_ValidationError");
    }

    [Theory]
    [InlineData("CODE")]
    [InlineData("Code")]
    [InlineData("code")]
    [InlineData("CoDe")]
    public void OnBeforeCompleteName_WithCodeFieldCaseInsensitive_ShouldApplyPrefix(string fieldName)
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        ObjectTypeDefinition objectDefinition = CreateErrorTypeDefinitionWithCustomFieldName("ValidationError", fieldName);

        // Act
        _interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert
        objectDefinition.Name.Should().Be("policies_ValidationError");
    }

    #endregion

    #region Edge Cases Tests

    [Theory]
    [InlineData("Error")]
    [InlineData("SomeError")]
    [InlineData("AnotherError")]
    public void OnBeforeCompleteName_WithErrorSuffixOnly_ShouldApplyPrefixWhenHasStandardFields(string typeName)
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        ObjectTypeDefinition objectDefinition = CreateErrorTypeDefinition(typeName, hasMessageField: true);

        // Act
        _interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert
        objectDefinition.Name.Should().Be($"policies_{typeName}");
    }

    [Fact]
    public void OnBeforeCompleteName_WithMultipleFields_ShouldStillApplyPrefix()
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        var objectDefinition = new ObjectTypeDefinition("ValidationError");
        objectDefinition.Fields.Add(new ObjectFieldDefinition { Name = "message" });
        objectDefinition.Fields.Add(new ObjectFieldDefinition { Name = "code" });
        objectDefinition.Fields.Add(new ObjectFieldDefinition { Name = "details" });
        objectDefinition.Fields.Add(new ObjectFieldDefinition { Name = "timestamp" });

        // Act
        _interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert
        objectDefinition.Name.Should().Be("policies_ValidationError");
    }

    [Fact]
    public void OnBeforeCompleteName_WithOnlyOtherFields_ShouldNotApplyPrefix()
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        var objectDefinition = new ObjectTypeDefinition("ValidationError");
        objectDefinition.Fields.Add(new ObjectFieldDefinition { Name = "details" });
        objectDefinition.Fields.Add(new ObjectFieldDefinition { Name = "timestamp" });
        objectDefinition.Fields.Add(new ObjectFieldDefinition { Name = "severity" });

        // Act
        _interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert
        objectDefinition.Name.Should().Be("ValidationError");
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("\t")]
    [InlineData("\n")]
    public void OnBeforeCompleteName_WithWhitespaceSchemaPrefix_ShouldUseDefaultPrefix(string prefix)
    {
        // Arrange
        IOptions<GraphQLNamingOptions> customOptions = Options.Create(new GraphQLNamingOptions { SchemaPrefix = prefix });
        var interceptor = new ErrorTypeNamingInterceptor(customOptions);
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        ObjectTypeDefinition objectDefinition = CreateErrorTypeDefinition("ValidationError", hasMessageField: true);

        // Act
        interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert - Should use default "policies" prefix when schema prefix is whitespace
        objectDefinition.Name.Should().Be("policies_ValidationError");
    }

    #endregion

    #region Performance Tests

    [Fact]
    public void OnBeforeCompleteName_WithLargeNumberOfFields_ShouldPerformEfficiently()
    {
        // Arrange
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        var objectDefinition = new ObjectTypeDefinition("ValidationError");

        // Add many fields to test performance
        for (int i = 0; i < 1000; i++)
        {
            objectDefinition.Fields.Add(new ObjectFieldDefinition { Name = $"field{i}" });
        }
        objectDefinition.Fields.Add(new ObjectFieldDefinition { Name = "message" }); // Add at the end

        // Act
        var stopwatch = Stopwatch.StartNew();
        _interceptor.OnBeforeCompleteName(context, objectDefinition);
        stopwatch.Stop();

        // Assert
        objectDefinition.Name.Should().Be("policies_ValidationError");
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(100); // Should be very fast
    }

    #endregion

    #region Integration Tests

    [Fact]
    public void OnBeforeCompleteName_WithRealWorldErrorType_ShouldApplyPrefixCorrectly()
    {
        // Arrange - Simulate a real mutation error type
        ITypeCompletionContext context = Mock.Of<ITypeCompletionContext>();
        var objectDefinition = new ObjectTypeDefinition("CreatePolicyError");
        objectDefinition.Fields.Add(new ObjectFieldDefinition { Name = "message" });
        objectDefinition.Fields.Add(new ObjectFieldDefinition { Name = "code" });
        objectDefinition.Fields.Add(new ObjectFieldDefinition { Name = "path" });
        objectDefinition.Fields.Add(new ObjectFieldDefinition { Name = "extensions" });

        // Act
        _interceptor.OnBeforeCompleteName(context, objectDefinition);

        // Assert
        objectDefinition.Name.Should().Be("policies_CreatePolicyError");
    }

    #endregion

    #region Helper Methods

    private static ObjectTypeDefinition CreateErrorTypeDefinition(
        string typeName,
        bool hasMessageField = false,
        bool hasCodeField = false)
    {
        var definition = new ObjectTypeDefinition(typeName);

        if (hasMessageField)
        {
            definition.Fields.Add(new ObjectFieldDefinition { Name = "message" });
        }

        if (hasCodeField)
        {
            definition.Fields.Add(new ObjectFieldDefinition { Name = "code" });
        }

        return definition;
    }

    private static ObjectTypeDefinition CreateErrorTypeDefinitionWithCustomFieldName(
        string typeName,
        string fieldName)
    {
        var definition = new ObjectTypeDefinition(typeName);
        definition.Fields.Add(new ObjectFieldDefinition { Name = fieldName });
        return definition;
    }

    #endregion
}
