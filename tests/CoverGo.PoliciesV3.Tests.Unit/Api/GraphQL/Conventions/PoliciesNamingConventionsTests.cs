using CoverGo.PoliciesV3.Api.GraphQL.Conventions;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using HotChocolate.Types;
using Microsoft.Extensions.Options;
using System.Reflection;
using System.Reflection.Emit;

namespace CoverGo.PoliciesV3.Tests.Unit.Api.GraphQL.Conventions;

public class PoliciesNamingConventionsTests
{
    private readonly PoliciesNamingConventions _namingConventions;

    private static readonly Dictionary<string, Type> _mockTypes = [];

    public PoliciesNamingConventionsTests()
    {
        IOptions<GraphQLNamingOptions> options = Options.Create(new GraphQLNamingOptions
        {
            SchemaPrefix = "policies",
            ApplyToInputTypes = true // Enable for testing input types
        });
        _namingConventions = new PoliciesNamingConventions(options);
    }

    /// <summary>
    /// Creates a mock type with the specified name for testing purposes
    /// </summary>
    private static Type CreateMockType(string typeName)
    {
        if (_mockTypes.TryGetValue(typeName, out Type? existingType))
            return existingType;

        // Create a dynamic type with the specified name
        var assemblyName = new AssemblyName($"TestAssembly_{typeName}");
        var assemblyBuilder = AssemblyBuilder.DefineDynamicAssembly(assemblyName, AssemblyBuilderAccess.Run);
        var moduleBuilder = assemblyBuilder.DefineDynamicModule("TestModule");
        var typeBuilder = moduleBuilder.DefineType(typeName, TypeAttributes.Public | TypeAttributes.Class);

        var type = typeBuilder.CreateType();
        _mockTypes[typeName] = type;
        return type;
    }

    /// <summary>
    /// Helper method to test GetTypeName with TypeKind parameter
    /// </summary>
    private void AssertGetTypeName(string typeName, TypeKind kind, string expectedName)
    {
        // Arrange
        Type mockType = CreateMockType(typeName);

        // Act
        string result = _namingConventions.GetTypeName(mockType, kind);

        // Assert
        result.Should().Be(expectedName);
    }

    /// <summary>
    /// Helper method to test GetTypeName with default TypeKind.Object
    /// </summary>
    private void AssertGetTypeName(string typeName, string expectedName)
    {
        // Arrange
        Type mockType = CreateMockType(typeName);

        // Act
        string result = _namingConventions.GetTypeName(mockType, TypeKind.Object);

        // Assert
        result.Should().Be(expectedName);
    }

    [Theory]
    [InlineData("PolicyNotFoundError", TypeKind.Object, "policies_PolicyNotFoundError")]
    [InlineData("PolicyIssuedError", TypeKind.Object, "policies_PolicyIssuedError")]
    [InlineData("BadFileContentError", TypeKind.Object, "policies_BadFileContentError")]
    [InlineData("SomeCustomError", TypeKind.Object, "policies_SomeCustomError")]
    public void GetTypeName_WithErrorTypeNames_ShouldApplyPrefix(string typeName, TypeKind kind, string expectedName)
        => AssertGetTypeName(typeName, kind, expectedName);

    [Theory]
    [InlineData("policies_AlreadyPrefixedError", TypeKind.Object, "policies_AlreadyPrefixedError")]
    [InlineData("policies_SomeType", TypeKind.Object, "policies_SomeType")]
    public void GetTypeName_WithAlreadyPrefixedTypes_ShouldNotApplyPrefixAgain(string typeName, TypeKind kind, string expectedName)
        => AssertGetTypeName(typeName, kind, expectedName);

    [Theory]
    [InlineData("policies_CreatePolicyInput", TypeKind.InputObject, "policies_CreatePolicyInput")]
    [InlineData("policies_RegisterPolicyMemberUploadPayload", TypeKind.Object, "policies_RegisterPolicyMemberUploadPayload")]
    [InlineData("policies_SomeMutationError", TypeKind.Object, "policies_SomeMutationError")]
    public void GetTypeName_WithMutationConventionTypes_ShouldNotApplyPrefixAgain(string typeName, TypeKind kind, string expectedName)
        => AssertGetTypeName(typeName, kind, expectedName);

    [Theory]
    [InlineData("Query", TypeKind.Object, "Query")]
    [InlineData("Mutation", TypeKind.Object, "Mutation")]
    [InlineData("Subscription", TypeKind.Object, "Subscription")]
    public void GetTypeName_WithRootTypes_ShouldNotApplyPrefix(string typeName, TypeKind kind, string expectedName)
        => AssertGetTypeName(typeName, kind, expectedName);

    [Theory]
    [InlineData("String", TypeKind.Object, "String")]
    [InlineData("Int", TypeKind.Object, "Int")]
    [InlineData("Boolean", TypeKind.Object, "Boolean")]
    [InlineData("DateTime", TypeKind.Object, "DateTime")]
    public void GetTypeName_WithBuiltInTypes_ShouldNotApplyPrefix(string typeName, TypeKind kind, string expectedName)
        => AssertGetTypeName(typeName, kind, expectedName);

    [Theory]
    [InlineData("SomeUnionType", TypeKind.Union, "policies_SomeUnionType")]
    [InlineData("ErrorUnion", TypeKind.Union, "policies_ErrorUnion")]
    public void GetTypeName_WithUnionTypes_ShouldApplyPrefix(string typeName, TypeKind kind, string expectedName)
        => AssertGetTypeName(typeName, kind, expectedName);

    [Theory]
    [InlineData("CurrencyCodeInput", TypeKind.InputObject, "policies_CurrencyCodeInput")]
    [InlineData("ProductIdInput", TypeKind.InputObject, "policies_ProductIdInput")]
    [InlineData("SomeCustomInput", TypeKind.InputObject, "policies_SomeCustomInput")]
    public void GetTypeName_WithInputObjectTypes_ShouldApplyPrefix(string typeName, TypeKind kind, string expectedName)
        => AssertGetTypeName(typeName, kind, expectedName);

    [Theory]
    [InlineData("SomeType", TypeKind.Enum, "policies_SomeType")]
    [InlineData("SomeType", TypeKind.Scalar, "SomeType")]
    public void GetTypeName_WithNonObjectNonUnionNonInputTypes_ShouldApplyPrefixToEnumsOnly(string typeName, TypeKind kind, string expectedName)
        => AssertGetTypeName(typeName, kind, expectedName);

    [Fact]
    public void GetTypeName_WithDomainExceptionType_ShouldApplyPrefixUsingTypeOverload()
    {
        // Arrange
        Type exceptionType = typeof(PolicyNotFoundException);

        // Act
        string result = _namingConventions.GetTypeName(exceptionType);

        // Assert
        result.Should().Be("policies_PolicyNotFoundException");
    }

    [Theory]
    [InlineData("RegularType", "policies_RegularType")]
    [InlineData("SomeResponse", "policies_SomeResponse")]
    public void GetTypeName_WithRegularTypes_ShouldApplyPrefix(string typeName, string expectedName)
        => AssertGetTypeName(typeName, expectedName);

    [Theory]
    [InlineData("BadFileContentError", "policies_BadFileContentError")]
    [InlineData("PolicyNotFoundError", "policies_PolicyNotFoundError")]
    [InlineData("SomeCustomError", "policies_SomeCustomError")]
    public void GetTypeName_WithHotChocolateGeneratedErrorTypes_ShouldApplyPrefix(string typeName, string expectedName)
        => AssertGetTypeName(typeName, expectedName);
}
