﻿# =============================================================================
# Base runtime image
# =============================================================================
FROM mcr.microsoft.com/dotnet/aspnet:9.0-alpine AS base
RUN apk add --no-cache icu-libs
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=0
WORKDIR /app
EXPOSE 8080

# =============================================================================
# SDK base image with tools
# =============================================================================
FROM mcr.microsoft.com/dotnet/sdk:9.0-alpine AS sdk-base
RUN apk add findutils && apk cache clean
WORKDIR /app

# =============================================================================
# Restore dependencies
# =============================================================================
FROM sdk-base AS restore
ARG GH_ACCOUNT
ARG GH_TOKEN

# Copy MSBuild configuration files first for better caching
COPY nuget.config .
COPY Directory.Packages.props .
COPY Directory.Build.props .
COPY Directory.Build.targets .
COPY tests/Directory.Build.props tests/

# Copy project files for dependency restoration
COPY src/CoverGo.PoliciesV3.Api/CoverGo.PoliciesV3.Api.csproj src/CoverGo.PoliciesV3.Api/
COPY src/CoverGo.PoliciesV3.Application/CoverGo.PoliciesV3.Application.csproj src/CoverGo.PoliciesV3.Application/
COPY src/CoverGo.PoliciesV3.Domain/CoverGo.PoliciesV3.Domain.csproj src/CoverGo.PoliciesV3.Domain/
COPY src/CoverGo.PoliciesV3.Infrastructure/CoverGo.PoliciesV3.Infrastructure.csproj src/CoverGo.PoliciesV3.Infrastructure/
COPY tests/CoverGo.PoliciesV3.Tests.Unit/CoverGo.PoliciesV3.Tests.Unit.csproj tests/CoverGo.PoliciesV3.Tests.Unit/
COPY tests/CoverGo.PoliciesV3.Tests.Integration/CoverGo.PoliciesV3.Tests.Integration.csproj tests/CoverGo.PoliciesV3.Tests.Integration/


# Configure NuGet source and restore packages
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text
RUN find "./" -type f -name "*.csproj" | xargs -L 1 -d '\n' dotnet restore

# =============================================================================
# Build and publish application
# =============================================================================
FROM restore AS publish
ARG BUILDCONFIG=Release
ARG VERSION=1.0.0
ARG APP_VERSION=1.0.0

# Copy source code
COPY . .

# Build and publish the application
RUN dotnet build src/CoverGo.PoliciesV3.Api/CoverGo.PoliciesV3.Api.csproj -c "$BUILDCONFIG" --no-restore
RUN dotnet publish src/CoverGo.PoliciesV3.Api/CoverGo.PoliciesV3.Api.csproj -c "$BUILDCONFIG" -o ./publish /p:Version=$APP_VERSION --no-build

# =============================================================================
# Final runtime image
# =============================================================================
FROM base AS runtime
COPY --from=publish /app/publish ./
ENTRYPOINT ["dotnet", "CoverGo.PoliciesV3.Api.dll"]

# =============================================================================
# Test stage
# =============================================================================
FROM publish AS tests
ARG BUILDCONFIG=Release
ENV TESTBUILDCONFIG=$BUILDCONFIG
# Change to Env variable when build-publish will support it

# Build test projects
RUN dotnet build tests/CoverGo.PoliciesV3.Tests.Unit/CoverGo.PoliciesV3.Tests.Unit.csproj -c "$BUILDCONFIG" --no-restore
RUN dotnet build tests/CoverGo.PoliciesV3.Tests.Integration/CoverGo.PoliciesV3.Tests.Integration.csproj -c "$BUILDCONFIG" --no-restore

# Execute tests with original behavior
ENTRYPOINT /bin/sh -c "\
  dotnet test tests/CoverGo.PoliciesV3.Tests.Unit/CoverGo.PoliciesV3.Tests.Unit.csproj --collect:'XPlat Code Coverage' -c \"$TESTBUILDCONFIG\" --no-build --verbosity normal --settings coverlet.runsettings --logger:'junit;LogFileName=TestResults.Unit.{assembly}.{framework}.xml;verbosity=normal' --logger:'console;verbosity=normal' && \
  dotnet test tests/CoverGo.PoliciesV3.Tests.Integration/CoverGo.PoliciesV3.Tests.Integration.csproj --collect:'XPlat Code Coverage' -c \"$TESTBUILDCONFIG\" --no-build --verbosity normal --settings coverlet.runsettings --logger:'junit;LogFileName=TestResults.Integration.{assembly}.{framework}.xml;verbosity=normal' --logger:'console;verbosity=normal' \
  "
