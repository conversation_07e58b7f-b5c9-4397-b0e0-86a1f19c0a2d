# rule.md

All business logic MUST be moved from application and infrastructure layers to the appropriate domain layer
Use proper domain entities, value objects, and domain services
Infrastructure layer MUST only handle data persistence operations
Maintain 100% behavioral compatibility when refactoring existing code
Eliminate all magic strings
Domain Layer: Business logic, domain entities, value objects, domain services, business rules
Application Layer: Orchestration, use cases, application services
Infrastructure Layer: Data persistence, external service integration, repositories (data access only)
Implement fail-fast validation patterns using `ArgumentNullException.ThrowIfNull`
Enforce non-null state at creation time
Eliminate defensive null checks in favor of proper type definitions
Use strongly-typed properties with clear nullability contracts
Place validation in appropriate DDD domain layer classes
Create strongly-typed ID classes as value objects that wrap GUIDs
Include proper EF Core converters and registration in ApplicationDbContext
Configure GraphQL serialization as strings, not complex objects
Maintain DDD value object principles
Use builder pattern instead of factory methods with a lot of parameters
Implement fluent method chaining for complex object construction
Follow DDD compliance principles
Move logic to domain services, entity methods, or value objects as appropriate
Fix underlying issues rather than suppressing warnings
Make property definitions more strictly typed
What NOT to Do
FORBIDDEN: Magic strings in any form (exceptions, configurations, etc.)
FORBIDDEN: Business logic in infrastructure layer
FORBIDDEN: Defensive null checks when proper typing is available
FORBIDDEN: Factory methods with excessive parameters (16+)
FORBIDDEN: Dual-API patterns for object creation
FORBIDDEN: Breaking behavioral compatibility during refactoring
FORBIDDEN: Suppressing compiler warnings without fixing root causes

## Guidelines

Before Submitting Code:

- All business logic is in appropriate domain layer
- No magic strings present anywhere
- Proper DDD layer separation maintained
- Fail-fast validation implemented where needed
- Strongly-typed IDs used for all entities
- All compiler warnings resolved properly
- ErrorCodes and ErrorMessages classes used consistently
  Remember: These rules are designed to maintain clean DDD architecture, ensure type safety, eliminate technical debt, and maintain 100% behavioral compatibility. Every code change must follow these principles without exception.
