mode: ContinuousDeployment
branches:
  master:
    regex: ^master$|^main$
    mode: ContinuousDeployment
    tag: rc
    increment: Minor
    source-branches: []
    tracks-release-branches: false
    is-release-branch: true
    is-mainline: true
  release:
    regex: ^release[/-]
    mode: ContinuousDeployment
    tag: rc
    increment: Patch
    prevent-increment-of-merged-branch-version: false
    track-merge-target: false
    source-branches: [ 'master', 'main' ]
    tracks-release-branches: false
    is-release-branch: true
    is-mainline: true
  tags:
    regex: '^(?:(?:refs\/tags\/|tags\/)?[vV]?)(?<Version>\d+\.\d+\.\d+(?:-[A-Za-z0-9]+\.[0-9]+)?)$'
    tag: '{Version}'
    increment: None
    prevent-increment-of-merged-branch-version: true
    is-release-branch: true
    source-branches: []
  unknown:
    mode: ContinuousDeployment
    tag: 'rc'
    regex: (?<BranchName>.+)
    source-branches:
    - main
    - release
ignore:
  sha: []
