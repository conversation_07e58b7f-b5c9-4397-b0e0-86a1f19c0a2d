using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Products;
using Newtonsoft.Json.Linq;
using LegacyPolicy = CoverGo.Policies.Client.Policy;

namespace CoverGo.PoliciesV3.Application.Mapping.LegacyPolicyMapping;

/// <summary>
/// Maps legacy policy domain objects to simplified PolicyDto objects
/// Encapsulates complex extraction logic from PolicyExtensions
/// </summary>
public static class PolicyDtoMapper
{
    /// <summary>
    /// Maps a legacy policy to a PolicyDto
    /// </summary>
    /// <param name="legacyPolicy">The legacy policy to map</param>
    /// <returns>PolicyDto with essential policy information</returns>
    /// <exception cref="ArgumentNullException">Thrown when legacyPolicy is null</exception>
    /// <exception cref="InvalidOperationException">Thrown when required data is missing</exception>
    public static PolicyDto MapToDto(LegacyPolicy legacyPolicy)
    {
        // Validate input parameters
        ArgumentNullException.ThrowIfNull(legacyPolicy);

        if (string.IsNullOrEmpty(legacyPolicy.Id))
            throw new InvalidOperationException("Policy ID is required");

        // Extract policy dates using legacy extension logic
        try
        {
            DateOnly startDate = ExtractStartDate(legacyPolicy);
            DateOnly endDate = ExtractEndDate(legacyPolicy);

            // Extract contract holder ID (nullable)
            string? contractHolderId = legacyPolicy.ContractHolder?.Id;

            // Extract approved endorsement IDs
            IReadOnlyList<string> approvedEndorsementIds = ExtractApprovedEndorsementIds(legacyPolicy);

            // Extract all endorsements with status information
            IReadOnlyList<EndorsementDto> endorsements = ExtractEndorsements(legacyPolicy);

            // Determine upload eligibility
            bool canUploadMembers = DetermineCanUploadMembers(startDate, endDate);

            // Create ProductIdDto if ProductId is available
            ProductIdDto? productIdDto = null;
            if (legacyPolicy.ProductId is not null &&
                !string.IsNullOrEmpty(legacyPolicy.ProductId.Plan) &&
                !string.IsNullOrEmpty(legacyPolicy.ProductId.Type) &&
                !string.IsNullOrEmpty(legacyPolicy.ProductId.Version))
            {
                productIdDto = new ProductIdDto
                {
                    Type = legacyPolicy.ProductId.Type,
                    Plan = legacyPolicy.ProductId.Plan,
                    Version = legacyPolicy.ProductId.Version,
                    Plans = null // Plans will be loaded on-demand when needed
                };
            }

            // Create and return PolicyDto
            return new PolicyDto
            {
                Id = legacyPolicy.Id,
                ProductId = productIdDto,
                ContractHolderId = contractHolderId,
                StartDate = startDate,
                EndDate = endDate,
                ApprovedEndorsementIds = approvedEndorsementIds,
                Endorsements = endorsements,
                IsIssued = legacyPolicy.IsIssued,
                CanUploadMembers = canUploadMembers,
                IsV2 = legacyPolicy.IsV2 ?? false,
                IsRenewal = legacyPolicy.IsRenewal,
                Status = legacyPolicy.Status
            };
        }
        catch (Exception ex) when (!(ex is ArgumentNullException || ex is InvalidOperationException))
        {
            throw new InvalidOperationException("Failed to extract policy dates", ex);
        }
    }

    /// <summary>
    /// Extracts policy start date using the same logic as PolicyExtensions.GetStartDate()
    /// </summary>
    /// <param name="policy">The legacy policy</param>
    /// <returns>Policy start date</returns>
    /// <exception cref="InvalidOperationException">Thrown when start date cannot be determined</exception>
    private static DateOnly ExtractStartDate(LegacyPolicy policy)
    {
        // Try direct StartDate property first
        if (policy.StartDate.HasValue)
            return DateOnly.FromDateTime(policy.StartDate.Value);

        // Try extracting from a Fields object
        if (policy.Fields == null) throw new InvalidOperationException("Policy start date is not set");
        var fieldsJObject = policy.Fields as JObject;

        // Try expectedStartDate field
        if (DateOnly.TryParse(fieldsJObject?["expectedStartDate"]?.ToString(), out DateOnly expectedStartDate))
            return expectedStartDate;

        // Try nested policy.startDate field
        if (DateOnly.TryParse(fieldsJObject?["policy"]?["startDate"]?.ToString(), out DateOnly nestedStartDate))
            return nestedStartDate;

        // Throw if no valid start date found
        throw new InvalidOperationException("Policy start date is not set");
    }

    /// <summary>
    /// Extracts policy end date using the same logic as PolicyExtensions.GetEndDate()
    /// </summary>
    /// <param name="policy">The legacy policy</param>
    /// <returns>Policy end date</returns>
    /// <exception cref="InvalidOperationException">Thrown when end date cannot be determined</exception>
    private static DateOnly ExtractEndDate(LegacyPolicy policy)
    {
        // Try direct EndDate property first
        if (policy.EndDate.HasValue)
            return DateOnly.FromDateTime(policy.EndDate.Value);

        // Try extracting from a Fields object
        if (policy.Fields == null) throw new InvalidOperationException("Policy end date is not set");
        var fieldsJObject = policy.Fields as JObject;

        // Try endDate field
        if (DateOnly.TryParse(fieldsJObject?["endDate"]?.ToString(), out DateOnly endDate))
            return endDate;

        // Try nested policy.endDate field
        if (DateOnly.TryParse(fieldsJObject?["policy"]?["endDate"]?.ToString(), out DateOnly nestedEndDate))
            return nestedEndDate;

        // Throw if no valid end date found
        throw new InvalidOperationException("Policy end date is not set");
    }

    /// <summary>
    /// Extracts approved endorsement IDs from policy endorsements
    /// </summary>
    /// <param name="policy">The legacy policy</param>
    /// <returns>List of approved endorsement IDs</returns>
    private static List<string> ExtractApprovedEndorsementIds(LegacyPolicy policy) => policy.Endorsements is null || policy.Endorsements.Count == 0
            ? []
            : [.. from endorsement in policy.Endorsements where endorsement.Status == EndorsementStatus.Approved && !string.IsNullOrEmpty(endorsement.Id) select endorsement.Id];

    /// <summary>
    /// Extracts all endorsements with their status information for validation
    /// </summary>
    /// <param name="policy">The legacy policy</param>
    /// <returns>List of endorsement DTOs</returns>
    private static List<EndorsementDto> ExtractEndorsements(LegacyPolicy policy) => policy.Endorsements is null || policy.Endorsements.Count == 0
            ? []
            : [.. from endorsement in policy.Endorsements where !string.IsNullOrEmpty(endorsement.Id) && !string.IsNullOrEmpty(endorsement.Status) select new EndorsementDto { Id = endorsement.Id!, Status = endorsement.Status! }];

    /// <summary>
    /// Determines if the policy can accept member uploads based on business rules
    /// </summary>
    /// <param name="startDate">Policy start date</param>
    /// <param name="endDate">Policy end date</param>
    /// <returns>True if policy can accept uploads, false otherwise</returns>
    private static bool DetermineCanUploadMembers(DateOnly startDate, DateOnly endDate)
    {
        try
        {
            var today = DateOnly.FromDateTime(DateTime.Today);

            // Policy must be within valid date range
            return today >= startDate && today <= endDate;
        }
        catch
        {
            return false;
        }
    }
}
