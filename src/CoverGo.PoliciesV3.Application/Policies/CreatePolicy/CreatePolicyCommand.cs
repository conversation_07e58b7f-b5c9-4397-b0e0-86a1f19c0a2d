using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Application.Policies.CreatePolicy;

public class CreatePolicyCommand : ICommand<CreatePolicyResult>
{
    public DateOnly? StartDate { get; init; }
    public DateOnly? EndDate { get; init; }
    public required ProductId ProductId { get; init; }
    //public IReadOnlyList<PolicyField>? Fields { get; init; }
}

public record CreatePolicyResult(Guid PolicyId);
