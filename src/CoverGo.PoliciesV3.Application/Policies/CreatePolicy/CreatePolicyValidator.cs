using FluentValidation;

namespace CoverGo.PoliciesV3.Application.Policies.CreatePolicy;

public class CreatePolicyValidator : AbstractValidator<CreatePolicyCommand>
{
    public CreatePolicyValidator()
    {
        RuleFor(x => x.StartDate)
            .NotNull().WithMessage($"{nameof(CreatePolicyCommand.StartDate)} is required");

        RuleFor(x => x.EndDate)
            .NotNull().WithMessage($"{nameof(CreatePolicyCommand.EndDate)} is required")
            .GreaterThan(x => x.StartDate).WithMessage($"{nameof(CreatePolicyCommand.EndDate)} must be after {nameof(CreatePolicyCommand.StartDate)}");
    }
}
