using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Application.Policies.GetPagedPolicies;

public class GetPagedPoliciesHandler(
    IPaginatedRepository<Policy, PolicyId> policyRepository) : IQueryHandler<GetPagedPoliciesQuery, GetPagedPoliciesResponse>
{
    public async Task<GetPagedPoliciesResponse> Handle(GetPagedPoliciesQuery query, CancellationToken cancellationToken)
    {
        // Query the repository with pagination
        PageResult<Policy> pageResult = await policyRepository.GetPagedAsync(
            x => (string.IsNullOrWhiteSpace(query.OriginalPolicyNumber) || x.OriginalPolicyNumber == query.OriginalPolicyNumber)
                && (!query.StartDateFrom.HasValue || x.StartDate.HasValue && x.StartDate.Value >= query.StartDateFrom.Value)
                && (!query.StartDateTo.HasValue || x.StartDate.HasValue && x.StartDate.Value <= query.StartDateTo.Value)
                && (!query.EndDateFrom.HasValue || x.EndDate.HasValue && x.EndDate.Value >= query.EndDateFrom.Value)
                && (!query.EndDateTo.HasValue || x.EndDate.HasValue && x.EndDate.Value <= query.EndDateTo.Value),
            query.Skip,
            query.Take,
            query.SortBy,
            query.SortDirection,
            cancellationToken);

        return new GetPagedPoliciesResponse(
            pageResult.Items.Select(x => new PolicyDto
            {
                PolicyId = x.Id.Value,
                OriginalPolicyNumber = x.OriginalPolicyNumber!,
                StartDate = x.StartDate,
                EndDate = x.EndDate,
                IsIssued = x.IsIssued,
                IssueDate = x.IssueDate,
                Status = x.Status.Value
            }),
            pageResult.TotalCount,
            query.Skip,
            query.Take);
    }
}
