using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Application.PolicyMemberUploads;

/// <summary>
/// Specialized repository for PolicyMemberUpload with business-specific operations.
/// Extends the generic repository with domain-specific methods that express clear business intent.
/// </summary>
public interface IPolicyMemberUploadRepository : IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>
{
    /// <summary>
    /// Starts the validation process for an upload if it's not in a locked status.
    /// Updates status to VALIDATING and clears any existing validation errors.
    /// </summary>
    /// <param name="id">The upload ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the validation was started, false if upload is locked or doesn't exist</returns>
    Task<bool> StartValidationIfNotLockedAsync(
        PolicyMemberUploadId id,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Completes the validation process for an upload if it's not in a locked status.
    /// Updates the final validation status and member counts.
    /// </summary>
    /// <param name="id">The upload ID</param>
    /// <param name="finalStatus">The final validation status (VALIDATED or VALIDATING_ERROR)</param>
    /// <param name="validCount">Number of valid members</param>
    /// <param name="invalidCount">Number of invalid members</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the validation was completed, false if upload is locked or doesn't exist</returns>
    Task<bool> CompleteValidationIfNotLockedAsync(
        PolicyMemberUploadId id,
        PolicyMemberUploadStatus finalStatus,
        int validCount,
        int invalidCount,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Marks the validation as failed due to a system error if the upload is not in a locked status.
    /// Updates status to VALIDATING_ERROR and sets an error message.
    /// </summary>
    /// <param name="id">The upload ID</param>
    /// <param name="errorMessage">The error message describing the failure</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the failure was recorded, false if upload is locked or doesn't exist</returns>
    Task<bool> FailValidationIfNotLockedAsync(
        PolicyMemberUploadId id,
        string errorMessage,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates the validation progress for an upload if it's not in a locked status.
    /// Used during the validation process to update member counts and status.
    /// </summary>
    /// <param name="id">The upload ID</param>
    /// <param name="status">The current validation status</param>
    /// <param name="validCount">Current number of valid members processed</param>
    /// <param name="invalidCount">Current number of invalid members processed</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the progress was updated, false if upload is locked or doesn't exist</returns>
    Task<bool> UpdateValidationProgressIfNotLockedAsync(
        PolicyMemberUploadId id,
        PolicyMemberUploadStatus status,
        int validCount,
        int invalidCount,
        CancellationToken cancellationToken = default);
}
