using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Application.PolicyMemberUploads.RegisterPolicyMemberUpload;

public class RegisterPolicyMemberUploadCommand : ICommand<RegisterPolicyMemberUploadResponse>
{
    public required PolicyId PolicyId { get; init; }
    public EndorsementId? EndorsementId { get; init; }
    public required string Path { get; init; }
}
