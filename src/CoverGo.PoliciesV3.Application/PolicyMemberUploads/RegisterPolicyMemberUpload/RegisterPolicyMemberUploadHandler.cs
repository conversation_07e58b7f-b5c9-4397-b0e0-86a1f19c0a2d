using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.Validation;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Domain.Services;
using Microsoft.Extensions.Logging;
using static CoverGo.PoliciesV3.Domain.Common.Validation.Errors;

namespace CoverGo.PoliciesV3.Application.PolicyMemberUploads.RegisterPolicyMemberUpload;

public class RegisterPolicyMemberUploadHandler(
    IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId> policyMemberUploadRepository,
    ILegacyPolicyService legacyPolicyService,
    IPolicyMemberFieldsSchemaProvider policyMemberFieldsSchemaProvider,
    IFileSystemService fileSystemService,
    IFileParserFactory fileParserFactory,
    ILogger<RegisterPolicyMemberUploadHandler> logger) : ICommandHandler<RegisterPolicyMemberUploadCommand, RegisterPolicyMemberUploadResponse>
{
    public async Task<RegisterPolicyMemberUploadResponse> Handle(
        RegisterPolicyMemberUploadCommand command,
        CancellationToken cancellationToken)
    {
        logger.LogInformation("Starting registration for policy {PolicyId}, upload path: {Path}",
            command.PolicyId, command.Path);

        try
        {
            // 1. PARALLEL VALIDATION
            (PolicyDto policyDto, FileParseResult parseResult) = await ExecuteParallelValidationAsync(command, cancellationToken);

            // 2. VALIDATE SCHEMA (requires both policy and file - most expensive validation)
            await ValidateSchemaFieldsAsync(parseResult.HeadersSet, policyDto, command.EndorsementId?.Value, cancellationToken);

            // 3. CREATE AND PERSIST UPLOAD (final step - only after all validations pass)
            var upload = PolicyMemberUpload.Create(command.PolicyId, command.Path, parseResult.Count, command.EndorsementId);
            await policyMemberUploadRepository.InsertAsync(upload, cancellationToken);

            logger.LogInformation("Successfully registered upload for policy {PolicyId} with {Count} members",
                command.PolicyId, parseResult.Count);

            return new RegisterPolicyMemberUploadResponse
            {
                PolicyMemberUpload = upload
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to register upload for policy {PolicyId}, path: {Path}",
                command.PolicyId, command.Path);
            throw;
        }
    }

    #region Parallel Validation

    /// <summary>
    /// Executes policy validation and file parsing
    /// </summary>
    private async Task<(PolicyDto policyDto, FileParseResult parseResult)> ExecuteParallelValidationAsync(
        RegisterPolicyMemberUploadCommand command,
        CancellationToken cancellationToken)
    {
        Task<PolicyDto> policyTask = GetAndValidatePolicyAsync(command.PolicyId.Value.ToString(), command.EndorsementId?.Value, cancellationToken);
        Task<FileParseResult> fileTask = ParseAndValidateFileAsync(command.Path, command.PolicyId.Value.ToString(), cancellationToken);

        await Task.WhenAll(policyTask, fileTask);

        return (await policyTask, await fileTask);
    }

    #endregion

    #region Policy Validation

    /// <summary>
    /// Retrieves and validates the policy for member upload operations.
    /// </summary>
    private async Task<PolicyDto> GetAndValidatePolicyAsync(string policyId, Guid? endorsementId, CancellationToken cancellationToken)
    {
        PolicyDto? policyDto = await legacyPolicyService.GetPolicyById(policyId, cancellationToken);

        if (policyDto is null || string.IsNullOrEmpty(policyDto.Id))
            throw new PolicyNotFoundException(policyId);

        policyDto.ValidatePolicyStateForMemberUpload(endorsementId);

        return policyDto;
    }

    #endregion

    #region File Processing and Validation

    private async Task<FileParseResult> ParseAndValidateFileAsync(string path, string policyId, CancellationToken cancellationToken)
    {
        // Download file content once
        byte[] fileContent = await fileSystemService.GetFileByPath(path, cancellationToken)
            ?? throw new UploadFileNotFoundException(policyId, path);

        if (fileContent.Length == 0)
            throw new BadFileContentException([EmptyFile()]);

        // Validate file size based on file type
        ValidateFileSize(fileContent.Length, path);

        IFileParser parser = fileParserFactory.CreateParser(fileContent);
        FileParseResult parseResult = await parser.ParseFileAsync(fileContent, cancellationToken)
            ?? throw new BadFileContentException([UnexpectedError("Failed to parse file content")]);

        ValidateParseResult(parseResult);

        parseResult.InitializeHeadersSet();
        return parseResult;
    }

    /// <summary>
    /// Validates file size against configured limits to prevent performance issues.
    /// Supports different limits for different file types.
    /// </summary>
    /// <param name="fileSize">File size in bytes</param>
    /// <param name="path">File path to determine file type</param>
    /// <exception cref="BadFileContentException">Thrown when file size exceeds limits</exception>
    private static void ValidateFileSize(long fileSize, string path)
    {
        if (fileSize < ValidationConstants.FileSizeLimits.MinFileSizeBytes)
            throw new BadFileContentException([EmptyFile()]);

        long maxSize = GetMaxFileSizeForPath(path);
        if (fileSize > maxSize)
            throw new BadFileContentException([FileTooLarge(fileSize, maxSize)]);
    }

    private static long GetMaxFileSizeForPath(string path)
    {
        string extension = Path.GetExtension(path).ToLowerInvariant();

        return extension switch
        {
            ".csv" => ValidationConstants.FileSizeLimits.MaxCsvFileSizeBytes,
            ".xlsx" => ValidationConstants.FileSizeLimits.MaxXlsxFileSizeBytes,
            ".xls" => ValidationConstants.FileSizeLimits.MaxXlsxFileSizeBytes,
            _ => ValidationConstants.FileSizeLimits.MaxGeneralFileSizeBytes
        };
    }

    private static void ValidateParseResult(FileParseResult parseResult)
    {
        if (parseResult.Headers is null || parseResult.Headers.Count == 0)
            throw new BadFileContentException([NoColumns()]);

        if (parseResult.Count <= 0)
            throw new BadFileContentException([NoMembers()]);
    }

    #endregion

    #region Schema Validation

    /// <summary>
    /// Validates that the upload file headers are compatible with the policy schema.
    /// </summary>
    private async Task ValidateSchemaFieldsAsync(
        HashSet<string> uploadFileHeadersSet,
        PolicyDto policyDto,
        Guid? endorsementId,
        CancellationToken cancellationToken)
    {
        PolicyMemberFieldsSchema schema = await GetSchemaAsync(policyDto, endorsementId, cancellationToken);
        List<ValidationError> errors = CollectAllValidationErrors(schema, uploadFileHeadersSet);

        if (errors.Count > 0)
            throw new BadFileContentException(errors);
    }

    private static List<ValidationError> CollectAllValidationErrors(PolicyMemberFieldsSchema schema, HashSet<string> uploadFileHeadersSet)
    {
        var errors = new List<ValidationError>(capacity: 8);

        CollectOneOfValidationErrors(schema, uploadFileHeadersSet, errors);
        CollectMandatoryFieldErrors(schema, uploadFileHeadersSet, errors);
        CollectFormulaFieldErrors(schema, uploadFileHeadersSet, errors);

        return errors;
    }

    private async Task<PolicyMemberFieldsSchema> GetSchemaAsync(PolicyDto policyDto, Guid? endorsementId, CancellationToken cancellationToken)
    {
        policyDto.ValidateProductId();

        try
        {
            PolicyMemberFieldsSchema schema = await policyMemberFieldsSchemaProvider.GetMemberUploadSchema(
                policyDto.ContractHolderId,
                policyDto.ProductId!.ToDomainProductId(),
                endorsementId.HasValue ? (EndorsementId)endorsementId.Value : null,
                cancellationToken);

            return schema ?? throw new BadSchemaConfigException("Failed to retrieve valid schema for policy member upload validation");
        }
        catch (DomainException)
        {
            throw;
        }
        catch (Exception ex)
        {
            throw new BadSchemaConfigException("Failed to retrieve schema configuration for policy member upload", ex);
        }
    }

    private static void CollectOneOfValidationErrors(PolicyMemberFieldsSchema schema, HashSet<string> uploadFileHeadersSet, List<ValidationError> errors)
    {
        IReadOnlyList<CustomFieldOneOfValidation> oneOfValidations = schema.OneOfValidations;

        errors.AddRange(from oneOfValidation in oneOfValidations
                        select oneOfValidation.Validations.Select(validation => validation.Field.GetFullLabel())
                            .ToList()
            into fieldLabels
                        let hasAnyField = fieldLabels.Any(uploadFileHeadersSet.Contains)
                        where !hasAnyField
                        select OneOfRequired(fieldLabels));
    }

    private static void CollectMandatoryFieldErrors(PolicyMemberFieldsSchema schema, HashSet<string> uploadFileHeadersSet, List<ValidationError> errors)
    {
        var missingHeaders = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        foreach (PolicyMemberFieldDefinition field in schema.Fields)
        {
            if (!field.IsRequired) continue;

            switch (field.Type)
            {
                case ObjectFieldType objectFieldType:
                    ProcessObjectFieldSubfields(objectFieldType, uploadFileHeadersSet, missingHeaders);
                    break;

                case FormulaFieldType:
                    break;

                default:
                    string fullLabel = field.GetFullLabel();
                    if (!uploadFileHeadersSet.Contains(fullLabel)) missingHeaders.Add(fullLabel);
                    break;
            }
        }

        if (missingHeaders.Count > 0) errors.Add(MissingMandatoryColumns(missingHeaders));
    }

    private static void ProcessObjectFieldSubfields(ObjectFieldType objectFieldType, HashSet<string> uploadFileHeadersSet, HashSet<string> missingHeaders)
    {
        IEnumerable<string> missingSubfieldLabels = objectFieldType.InnerFieldDefinitions
            .Where(subfield => subfield.IsRequired)
            .Select(subfield => subfield.GetFullLabel())
            .Where(fullLabel => !uploadFileHeadersSet.Contains(fullLabel));

        foreach (string label in missingSubfieldLabels) missingHeaders.Add(label);
    }

    private static void CollectFormulaFieldErrors(PolicyMemberFieldsSchema schema, HashSet<string> uploadFileHeadersSet, List<ValidationError> errors)
    {
        var extraFormulaFields = schema.Fields
            .Where(field => field.Type is FormulaFieldType)
            .Select(field => field.GetFullLabel())
            .Where(uploadFileHeadersSet.Contains)
            .ToHashSet(StringComparer.OrdinalIgnoreCase);

        if (extraFormulaFields.Count > 0) errors.Add(ExtraColumns(extraFormulaFields));
    }

    #endregion
}