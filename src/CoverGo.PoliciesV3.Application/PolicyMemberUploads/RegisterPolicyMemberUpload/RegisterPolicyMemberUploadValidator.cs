using FluentValidation;

namespace CoverGo.PoliciesV3.Application.PolicyMemberUploads.RegisterPolicyMemberUpload;

public class RegisterPolicyMemberUploadValidator : AbstractValidator<RegisterPolicyMemberUploadCommand>
{
    public RegisterPolicyMemberUploadValidator()
    {
        RuleFor(x => x.PolicyId.Value)
            .NotEmpty()
            .WithMessage("PolicyId is required");

        RuleFor(x => x.Path)
            .NotEmpty()
            .WithMessage("Path is required")
            .MaximumLength(500)
            .WithMessage("Path cannot exceed 500 characters");
    }
}
