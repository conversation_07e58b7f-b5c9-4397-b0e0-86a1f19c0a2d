using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;

public class ValidatePolicyMemberUploadCommand : ICommand<Result<ValidatePolicyMemberUploadResponse>>
{
    public required PolicyId PolicyId { get; init; }
    public required PolicyMemberUploadId UploadId { get; init; }
}