using FluentValidation;

namespace CoverGo.PoliciesV3.Application.PolicyMemberUploads.CancelUpload;

public class CancelPolicyMemberUploadValidator : AbstractValidator<CancelPolicyMemberUploadCommand>
{
    public CancelPolicyMemberUploadValidator()
    {
        RuleFor(x => x.PolicyId)
            .NotEmpty()
            .WithMessage("PolicyId is required");

        RuleFor(x => x.UploadId)
            .NotEmpty()
            .WithMessage("UploadId is required");
    }
} 