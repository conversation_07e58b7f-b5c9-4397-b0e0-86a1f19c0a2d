using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Application.PolicyMemberUploads.CancelUpload;

public class CancelPolicyMemberUploadCommand : ICommand<Result<CancelPolicyMemberUploadResponse>>
{
    public required PolicyId PolicyId { get; init; }
    public required PolicyMemberUploadId UploadId { get; init; }
}