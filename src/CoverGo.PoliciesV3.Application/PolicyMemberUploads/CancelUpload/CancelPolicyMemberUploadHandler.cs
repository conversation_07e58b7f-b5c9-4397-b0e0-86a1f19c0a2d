using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Application.PolicyMemberUploads.CancelUpload;

public sealed class CancelPolicyMemberUploadHandler(
    IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId> uploadRepository,
    IPaginatedRepository<PolicyMember, PolicyMemberId> memberRepository,
    ILegacyPolicyService policyService,
    ILogger<CancelPolicyMemberUploadHandler> logger)
    : ICommandHandler<CancelPolicyMemberUploadCommand, Result<CancelPolicyMemberUploadResponse>>
{
    public async Task<Result<CancelPolicyMemberUploadResponse>> Handle(
        CancelPolicyMemberUploadCommand command,
        CancellationToken cancellationToken)
    {
        logger.LogInformation("Starting cancellation for upload {UploadId} in policy {PolicyId}",
            command.UploadId, command.PolicyId);

        try
        {
            // Step 1: Validate - policy is only needed here
            PolicyMemberUpload upload = await ValidateAsync(command, cancellationToken);

            // Step 2: Execute cancellation - only need upload
            await ExecuteCancellationAsync(upload, cancellationToken);

            logger.LogInformation("Successfully cancelled upload {UploadId}", command.UploadId);

            return Result<CancelPolicyMemberUploadResponse>.Success(
                new CancelPolicyMemberUploadResponse { PolicyMemberUpload = upload });
        }
        catch (ValidationException ex)
        {
            logger.LogWarning("Validation failed for upload {UploadId}: {Errors}",
                command.UploadId, string.Join(", ", ex.GetErrorCodes()));
            return Result<CancelPolicyMemberUploadResponse>.Failure(ex.Errors);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error cancelling upload {UploadId}", command.UploadId);
            ValidationError error = Errors.UnexpectedError(ex.Message);
            return Result<CancelPolicyMemberUploadResponse>.Failure(error);
        }
    }

    #region Validation

    private async Task<PolicyMemberUpload> ValidateAsync(
        CancelPolicyMemberUploadCommand command,
        CancellationToken cancellationToken)
    {
        // Execute validations in parallel
        Task<PolicyMemberUpload> uploadTask = ValidateUploadAsync(command.UploadId, cancellationToken);
        Task<PolicyDto> policyTask = ValidatePolicyAsync(command.PolicyId, cancellationToken);

        await Task.WhenAll(uploadTask, policyTask);

        PolicyMemberUpload upload = await uploadTask;
        PolicyDto policy = await policyTask;

        // Cross-validate policy and upload relationship
        ValidatePolicyUploadRelationship(policy, upload);

        // Return only upload since policy is not needed after validation
        return upload;
    }

    /// <summary>
    /// Validates upload exists and can be cancelled
    /// </summary>
    private async Task<PolicyMemberUpload> ValidateUploadAsync(
        PolicyMemberUploadId uploadId,
        CancellationToken cancellationToken)
    {
        try
        {
            PolicyMemberUpload upload = await uploadRepository.FindByIdAsync(uploadId, cancellationToken);
            upload.EnsureCanCancel(); // Domain validation
            return upload;
        }
        catch (PolicyMemberUploadNotFoundException)
        {
            logger.LogWarning("Upload {UploadId} not found", uploadId);
            ValidationError error = Errors.NotFound("upload.id", uploadId.Value.ToString(), "Upload");
            throw new ValidationException(error);
        }
        catch (InvalidPolicyMemberUploadStatusException ex)
        {
            logger.LogWarning("Upload {UploadId} has invalid status {Status}", uploadId, ex.CurrentStatus);
            ValidationError error = Errors.InvalidState("upload.status", $"Upload cannot be canceled due to status: {ex.CurrentStatus}", "Upload Status");
            throw new ValidationException(error);
        }
    }

    /// <summary>
    /// Validates policy exists and is accessible
    /// </summary>
    private async Task<PolicyDto> ValidatePolicyAsync(
        PolicyId policyId,
        CancellationToken cancellationToken)
    {
        try
        {
            PolicyDto? policy = await policyService.GetPolicyById(policyId.Value.ToString(), cancellationToken);

            if (policy?.Id != null) return policy;
            logger.LogWarning("Policy {PolicyId} not found", policyId);
            ValidationError error = Errors.NotFound("policy.id", policyId.Value.ToString(), "Policy");
            throw new ValidationException(error);
        }
        catch (ValidationException)
        {
            throw; // Re-throw validation exceptions
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error validating policy {PolicyId}", policyId);
            ValidationError error = Errors.UnexpectedError($"Policy validation failed: {ex.Message}");
            throw new ValidationException(error);
        }
    }

    /// <summary>
    /// Validates policy state allows upload operations
    /// </summary>
    private void ValidatePolicyUploadRelationship(PolicyDto policy, PolicyMemberUpload upload)
    {
        try
        {
            policy.ValidatePolicyStateForMemberUpload(upload.EndorsementId?.Value);
        }
        catch (PolicyIssuedException ex)
        {
            logger.LogWarning("Policy {PolicyId} is issued, cannot modify upload {UploadId}",
                policy.Id, upload.Id);
            ValidationError error = Errors.InvalidState("policy.status", ex.Message, "Policy Status");
            throw new ValidationException(error);
        }
    }

    #endregion

    #region Cancellation

    /// <summary>
    /// Executes the cancellation process with intelligent status handling
    /// </summary>
    private async Task ExecuteCancellationAsync(PolicyMemberUpload upload, CancellationToken cancellationToken)
    {
        try
        {
            // Step 1: Start cancellation (intelligent status transition)
            upload.StartCanceling();
            await uploadRepository.UpdateAsync(upload, cancellationToken);

            // Step 2: Cleanup imported members if needed (using domain method)
            if (upload.RequiresMemberCleanup())
            {
                await CleanupImportedMembersAsync(upload, cancellationToken);
            }

            // Step 3: Complete cancellation if still in CANCELING state
            if (upload.Status == PolicyMemberUploadStatus.CANCELING)
            {
                upload.CompleteCancellation();
                await uploadRepository.UpdateAsync(upload, cancellationToken);
            }

            logger.LogDebug("Cancellation completed for upload {UploadId}", upload.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during cancellation for upload {UploadId}", upload.Id);
            throw new InvalidOperationException($"Cancellation failed: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Cleans up imported members
    /// </summary>
    private async Task CleanupImportedMembersAsync(PolicyMemberUpload upload, CancellationToken cancellationToken)
    {
        logger.LogInformation("Cleaning up {Count} imported members for upload {UploadId}",
            upload.GetImportedMembersCount(), upload.Id);

        try
        {
            // Get imported member IDs using domain method
            List<PolicyMemberId> memberIds = upload.GetImportedMemberIds();

            if (memberIds.Count == 0)
            {
                logger.LogDebug("No imported members to cleanup for upload {UploadId}", upload.Id);
                return;
            }

            // Bulk mark members as removed
            List<PolicyMember> members = await memberRepository.FindAllAsync(memberIds, cancellationToken);
            foreach (PolicyMember member in members)
            {
                member.MarkAsRemoved();
            }

            // Batch update all members in a single database operation
            await memberRepository.UpdateBatchAsync(members, cancellationToken);

            // Clear imported results (cascade delete handles DB cleanup)
            upload.ImportedResults.Clear();
            await uploadRepository.UpdateAsync(upload, cancellationToken);

            logger.LogInformation("Successfully cleaned up {Count} imported members for upload {UploadId} using batch update",
                members.Count, upload.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to cleanup imported members for upload {UploadId}", upload.Id);
            throw new InvalidOperationException($"Member cleanup failed: {ex.Message}", ex);
        }
    }

    #endregion
}