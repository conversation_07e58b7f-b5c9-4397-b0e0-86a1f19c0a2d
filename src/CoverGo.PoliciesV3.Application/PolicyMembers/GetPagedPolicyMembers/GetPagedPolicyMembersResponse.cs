using CoverGo.BuildingBlocks.DataAccess.PostgreSql;

namespace CoverGo.PoliciesV3.Application.PolicyMembers.GetPagedPolicyMembers;

public class GetPagedPolicyMembersResponse(IEnumerable<PolicyMemberDto> items, int totalCount, int pageNumber, int pageSize)
    : PageResult<PolicyMemberDto>(items, totalCount, pageNumber, pageSize)
{
}

public class PolicyMemberDto
{
    public required Guid PolicyMemberId { get; set; }
    public required Guid PolicyId { get; set; }
    public required string MemberId { get; set; }
    public DateOnly? StartDate { get; set; }
    public DateOnly? EndDate { get; set; }
}
