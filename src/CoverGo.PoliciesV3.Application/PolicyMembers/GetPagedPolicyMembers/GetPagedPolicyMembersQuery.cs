using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;

namespace CoverGo.PoliciesV3.Application.PolicyMembers.GetPagedPolicyMembers;

public class GetPagedPolicyMembersQuery : IQuery<GetPagedPolicyMembersResponse>
{
    public required Guid PolicyId { get; set; }
    public string? MemberId { get; set; }
    public DateOnly? StartDateFrom { get; set; }
    public DateOnly? StartDateTo { get; set; }
    public DateOnly? EndDateFrom { get; set; }
    public DateOnly? EndDateTo { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public required string SortBy { get; set; } = "Id";
    public required string SortDirection { get; set; } = "ASC";
    public int Skip => (Page - 1) * PageSize;
    public int Take => PageSize;
}
