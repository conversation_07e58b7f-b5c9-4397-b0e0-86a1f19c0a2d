using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Application.PolicyMembers.GetPagedPolicyMembers;

public class GetPagedPolicyMembersHandler(
    IPaginatedRepository<PolicyMember, PolicyMemberId> policyMemberRepository) : IQueryHandler<GetPagedPolicyMembersQuery, GetPagedPolicyMembersResponse>
{
    public async Task<GetPagedPolicyMembersResponse> Handle(GetPagedPolicyMembersQuery query, CancellationToken cancellationToken)
    {
        PageResult<PolicyMember> pageResult = await policyMemberRepository.GetPagedAsync(
            x => x.PolicyId == (PolicyId)query.PolicyId
                && (string.IsNullOrWhiteSpace(query.MemberId) || x.MemberId.Contains(query.MemberId))
                && (!query.StartDateFrom.HasValue || x.States.Any(s => s.StartDate >= query.StartDateFrom))
                && (!query.StartDateTo.HasValue || x.States.Any(s => s.StartDate <= query.StartDateTo))
                && (!query.EndDateFrom.HasValue || x.States.Any(s => s.EndDate >= query.EndDateFrom))
                && (!query.EndDateTo.HasValue || x.States.Any(s => s.EndDate <= query.EndDateTo)),
            query.Skip,
            query.Take,
            query.SortBy,
            query.SortDirection,
            cancellationToken);

        return new GetPagedPolicyMembersResponse(
            pageResult.Items.Select(x => new PolicyMemberDto
            {
                PolicyMemberId = x.Id.Value,
                PolicyId = x.PolicyId.Value,
                MemberId = x.MemberId,
                StartDate = x.CurrentState?.StartDate ?? x.States.OrderBy(s => s.StartDate).FirstOrDefault()?.StartDate,
                EndDate = x.CurrentState?.EndDate ?? x.States.OrderBy(s => s.StartDate).FirstOrDefault()?.EndDate,
            }),
            pageResult.TotalCount,
            query.Page,
            query.PageSize);
    }
}
