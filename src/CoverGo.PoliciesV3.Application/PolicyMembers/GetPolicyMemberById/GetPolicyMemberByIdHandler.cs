using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Application.PolicyMembers.GetPolicyMemberById;

public class GetPolicyMemberByIdHandler(
    IPaginatedRepository<PolicyMember, PolicyMemberId> policyMemberRepository) : IQueryHandler<GetPolicyMemberByIdQuery, GetPolicyMemberByIdResponse>
{
    public async Task<GetPolicyMemberByIdResponse> Handle(GetPolicyMemberByIdQuery query, CancellationToken cancellationToken)
    {
        PolicyMember? policyMember = await policyMemberRepository.FindByIdAsync(query.PolicyMemberId, cancellationToken);

        if (policyMember == null)
        {
            return new GetPolicyMemberByIdResponse { CurrentState = null };
        }

        return new GetPolicyMemberByIdResponse
        {
            CurrentState = policyMember.CurrentState
        };
    }
}