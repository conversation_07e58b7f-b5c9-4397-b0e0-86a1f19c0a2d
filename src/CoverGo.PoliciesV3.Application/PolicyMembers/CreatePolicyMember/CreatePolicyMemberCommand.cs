using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Application.PolicyMembers.CreatePolicyMember;

public class CreatePolicyMemberCommand : ICommand<CreatePolicyMemberResponse>
{
    public required string MemberId { get; init; }
    public required Guid PolicyId { get; init; }
    public Guid? DependentOfId { get; init; }
    public Guid? IndividualId { get; init; }
    public DateOnly? StartDate { get; init; }
    public DateOnly? EndDate { get; init; }
    public required string PlanId { get; init; }
    public ICollection<PolicyField> Fields { get; init; } = new HashSet<PolicyField>();
}
