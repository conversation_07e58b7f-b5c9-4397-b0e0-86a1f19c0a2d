using FluentValidation;

namespace CoverGo.PoliciesV3.Application.PolicyMembers.CreatePolicyMember;

public class CreatePolicyMemberValidator : AbstractValidator<CreatePolicyMemberCommand>
{
    public CreatePolicyMemberValidator()
    {
        RuleFor(x => x.PolicyId)
            .NotEmpty().WithMessage($"{nameof(CreatePolicyMemberCommand.PolicyId)} is required");

        RuleFor(x => x.EndDate)
            .GreaterThan(x => x.StartDate).WithMessage($"{nameof(CreatePolicyMemberCommand.EndDate)} must be after {nameof(CreatePolicyMemberCommand.StartDate)}")
            .When(x => x.StartDate.HasValue && x.EndDate.HasValue);

        RuleFor(x => x.PlanId)
            .NotEmpty().WithMessage($"{nameof(CreatePolicyMemberCommand.PlanId)} is required");
    }
}
