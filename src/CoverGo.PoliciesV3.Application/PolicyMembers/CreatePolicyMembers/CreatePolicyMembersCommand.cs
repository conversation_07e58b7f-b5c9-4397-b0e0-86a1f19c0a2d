using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;

namespace CoverGo.PoliciesV3.Application.PolicyMembers.CreatePolicyMembers;

public class CreatePolicyMembersCommand : ICommand<CreatePolicyMembersResponse>
{
    public required string PolicyId { get; init; }
    public required List<PolicyMemberToCreate> PolicyMembers { get; init; }
    public bool SkipMemberValidation { get; init; } = false;
}

public class PolicyMemberToCreate
{
    public string? MemberId { get; init; }
    public required string PlanId { get; init; }
    public string? Class { get; init; }
    public object? Fields { get; init; }
    public string? DependentOf { get; init; }
}