using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Application.PolicyMembers.CreatePolicyMember;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using System.Runtime.CompilerServices;

namespace CoverGo.PoliciesV3.Application.PolicyMembers.CreatePolicyMembers;

public class CreatePolicyMembersHandler(
    IPaginatedRepository<PolicyMember, PolicyMemberId> policyMemberRepository,
    ILegacyPolicyService legacyPolicyService,
    IndividualMemberValidationSpecification individualMemberValidationSpec,
    IUsersService usersService,
    IPolicyMemberQueryService policyMemberQueryService,
    ILogger<CreatePolicyMembersHandler> logger,
    PolicyMemberValidationDataService validationDataService)
    : ICommandHandler<CreatePolicyMembersCommand, CreatePolicyMembersResponse>
{
    public async Task<CreatePolicyMembersResponse> Handle(CreatePolicyMembersCommand command, CancellationToken cancellationToken)
    {
        logger.LogInformation("Starting CreatePolicyMembers for policy {PolicyId} with {MemberCount} members",
            command.PolicyId, command.PolicyMembers.Count);

        // Ensure policy exists
        PolicyDto policy = await FindPolicyByIdAsync(command.PolicyId, cancellationToken);

        // Validate policy state early
        validationDataService.ValidatePolicyState(policy, null);

        // Process members efficiently using batch processing
        var (policyMembers, validationErrors) = await ProcessMembersEfficientlyAsync(policy, command, cancellationToken);

        if (validationErrors.Count > 0)
        {
            logger.LogWarning("CreatePolicyMembers completed for policy {PolicyId} with {ErrorCount} errors. No members were inserted.",
                command.PolicyId, validationErrors.Count);

            // Throw a single ValidationException with all errors - HotChocolate will expose all errors in the payload
            throw new ValidationException(validationErrors);
        }

        logger.LogInformation("CreatePolicyMembers completed for policy {PolicyId}. Success: {SuccessCount}",
            command.PolicyId, policyMembers.Count);

        return new CreatePolicyMembersResponse
        {
            PolicyMembers = policyMembers
        };
    }

    /// <summary>
    /// Processes members efficiently using batch processing and parallel execution
    /// </summary>
    private async Task<(List<PolicyMemberState> PolicyMembers, List<ValidationError> Errors)> ProcessMembersEfficientlyAsync(
        PolicyDto policy,
        CreatePolicyMembersCommand command,
        CancellationToken cancellationToken)
    {
        var allErrors = new List<ValidationError>();

        // 1. GATHER ALL VALIDATION DATA ONCE (shared across all members)
        (ResolvedValidationData resolvedData, PolicyMemberFieldsSchema schema, MembersUploadFields memberData, List<CreatePolicyMemberCommand> createCommands) =
            await GatherValidationDataForBatchAsync(policy, command, cancellationToken);

        // 2. PROCESS MEMBERS IN BATCHES FOR VALIDATION
        if (!command.SkipMemberValidation)
            try
            {
                await ValidateMembersInBatchesAsync(policy, memberData, schema, resolvedData, cancellationToken);
            }
            catch (Exception ex)
            {
                // Check if this exception should be thrown directly
                if (ShouldThrowDirectly(ex))
                    throw; // Re-throw domain exceptions that have their own GraphQL error types
                allErrors.Add(ConvertExceptionToValidationError(ex, command.PolicyId));
            }

        // 3. CREATE AND SAVE MEMBERS IN PARALLEL BATCHES (only if no validation errors)
        if (allErrors.Count == 0)
        {
            var batches = ProcessMembersInBatches(memberData, ValidationConstants.BatchProcessing.DefaultDatabaseInsertBatchSize, cancellationToken);

            // Process batches in parallel with limited concurrency
            var batchTasks = new List<Task<(List<PolicyMemberState> PolicyMembers, List<ValidationError> Errors)>>();
            var semaphore = new SemaphoreSlim(ValidationConstants.BatchProcessing.MaxConcurrentBatches, ValidationConstants.BatchProcessing.MaxConcurrentBatches);

            await foreach ((IReadOnlyList<MemberUploadFields> batch, int startIndex) in batches.WithCancellation(cancellationToken))
            {
                var batchTask = ProcessBatchWithConcurrencyControlAsync(policy, batch, startIndex, createCommands, semaphore, cancellationToken);
                batchTasks.Add(batchTask);
            }

            // Wait for all batches to complete and collect results
            var batchResults = await Task.WhenAll(batchTasks);
            var policyMembers = batchResults.SelectMany(result => result.PolicyMembers).ToList();
            var batchErrors = batchResults.SelectMany(result => result.Errors).ToList();

            // Only return successful members if there are no errors
            if (batchErrors.Count == 0)
                return (policyMembers, allErrors);
            else
            {
                allErrors.AddRange(batchErrors);
                return (new List<PolicyMemberState>(), allErrors);
            }
        }

        return (new List<PolicyMemberState>(), allErrors);
    }

    /// <summary>
    /// Processes a batch with concurrency control to prevent resource exhaustion
    /// </summary>
    private async Task<(List<PolicyMemberState> PolicyMembers, List<ValidationError> Errors)> ProcessBatchWithConcurrencyControlAsync(
        PolicyDto policy,
        IReadOnlyList<MemberUploadFields> batch,
        int startIndex,
        List<CreatePolicyMemberCommand> createCommands,
        SemaphoreSlim semaphore,
        CancellationToken cancellationToken)
    {
        await semaphore.WaitAsync(cancellationToken);
        try
        {
            return await ProcessMemberBatchAsync(policy, batch, startIndex, createCommands, cancellationToken);
        }
        finally
        {
            semaphore.Release();
        }
    }

    /// <summary>
    /// Gathers validation data once for the entire batch instead of per member
    /// </summary>
    private async Task<(ResolvedValidationData resolvedData, PolicyMemberFieldsSchema schema, MembersUploadFields memberData, List<CreatePolicyMemberCommand> createCommands)> GatherValidationDataForBatchAsync(
        PolicyDto policy,
        CreatePolicyMembersCommand command,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogDebug("Gathering validation data for batch of {MemberCount} members", command.PolicyMembers.Count);

            // 1. GATHER FEATURE FLAGS AND PRODUCT DATA (shared service - done once)
            (bool[] featureFlags, IReadOnlyList<string>? availablePlans, string? packageType, List<string>? contractHolderPolicies) =
                await validationDataService.GatherFeatureFlagsAndProductDataAsync(policy, cancellationToken);

            // 2. GET SCHEMA (shared service - done once)
            PolicyMemberFieldsSchema schema = await validationDataService.GetDataSchemaForUploadAsync(policy, null, cancellationToken);

            // 3. CREATE ALL COMMANDS ONCE (optimized - no duplicate conversion)
            var createCommands = new List<CreatePolicyMemberCommand>();
            foreach (var memberToCreate in command.PolicyMembers)
                createCommands.Add(ConvertToCreateCommand(command.PolicyId, memberToCreate));

            // 4. PROCESS ALL MEMBER DATA AT ONCE (using pre-created commands)
            MembersUploadFields memberData = CreateBatchMemberDataFromCommands(createCommands);

            // 5. GATHER MEMBER-SPECIFIC VALIDATION DATA (batch operation)
            MemberDataResults memberDataResults = await validationDataService.GatherMemberSpecificDataAsync(
                memberData, policy, null, usersService, policyMemberQueryService, cancellationToken);

            // 6. CREATE RESOLVED VALIDATION DATA (shared service)
            List<EndorsementId> contractHolderScopeEndorsements = await validationDataService.GetContractHolderScopeEndorsementsAsync(
                contractHolderPolicies ?? [], policy.IsV2, cancellationToken);

            ResolvedValidationData resolvedData = validationDataService.CreateResolvedValidationData(
                policy,
                featureFlags,
                availablePlans,
                packageType,
                contractHolderPolicies,
                contractHolderScopeEndorsements,
                memberDataResults,
                [], // validEndorsementIds
                memberDataResults.DependentMembersCache);

            logger.LogDebug("Gathered validation data for batch: {FeatureFlags} feature flags, {Plans} available plans, SME: {IsSme}, Members: {MemberCount}",
                featureFlags.Length, availablePlans?.Count ?? 0, resolvedData.IsProductSme, memberData.Count);

            return (resolvedData, schema, memberData, createCommands);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error gathering validation data for policy {PolicyId}", policy.Id);
            throw;
        }
    }

    /// <summary>
    /// Validates all members in batches using the efficient batch validation approach
    /// </summary>
    private async Task ValidateMembersInBatchesAsync(
        PolicyDto policy,
        MembersUploadFields memberData,
        PolicyMemberFieldsSchema schema,
        ResolvedValidationData resolvedData,
        CancellationToken cancellationToken)
    {
        logger.LogDebug("Starting batch validation for {MemberCount} members", memberData.Count);

        var batches = ProcessMembersInBatches(memberData, ValidationConstants.BatchProcessing.DefaultMemberBatchSize, cancellationToken);

        await foreach ((IReadOnlyList<MemberUploadFields> batch, int _) in batches.WithCancellation(cancellationToken))
        {
            // Create validation context for this batch
            var membersUploadFields = MembersUploadFields.CreateNonEmpty(batch);
            var validationContext = IndividualMemberValidationContext.Create(policy, membersUploadFields, schema, resolvedData);
            var validationResult = await individualMemberValidationSpec.ValidateBatchAsync(validationContext, cancellationToken);

            // Check if validation failed and throw exception with errors
            if (validationResult.InvalidCount > 0)
            {
                var allErrors = validationResult.RowErrors.Values.SelectMany(errors => errors).ToList();
                throw new ValidationException(allErrors);
            }
        }

        logger.LogDebug("Completed batch validation for {MemberCount} members", memberData.Count);
    }

    /// <summary>
    /// Processes a batch of members for creation and saving using efficient batch database operations
    /// </summary>
    private async Task<(List<PolicyMemberState> PolicyMembers, List<ValidationError> Errors)> ProcessMemberBatchAsync(
        PolicyDto policy,
        IReadOnlyList<MemberUploadFields> batch,
        int startIndex,
        List<CreatePolicyMemberCommand> createCommands,
        CancellationToken cancellationToken)
    {
        var successfulMembers = new List<PolicyMemberState>();
        var errors = new List<ValidationError>();

        try
        {
            // 1. CREATE ALL POLICY MEMBERS IN PARALLEL (CPU-bound operations)
            var memberCreationTasks = new List<Task<(PolicyMember? Member, int OriginalIndex, ValidationError? Error)>>();

            for (int batchIndex = 0; batchIndex < batch.Count; batchIndex++)
            {
                var originalIndex = startIndex + batchIndex;
                var createCommand = createCommands[originalIndex]; // Use pre-created command

                var creationTask = Task.Run(() =>
                {
                    try
                    {
                        var policyMember = PolicyMember.Create(
                            createCommand.PolicyId,
                            createCommand.MemberId,
                            createCommand.StartDate,
                            createCommand.EndDate,
                            createCommand.PlanId,
                            createCommand.DependentOfId.HasValue ? (PolicyMemberId)createCommand.DependentOfId.Value : null,
                            createCommand.IndividualId,
                            createCommand.Fields
                        );

                        return (Member: (PolicyMember?)policyMember, OriginalIndex: originalIndex, Error: (ValidationError?)null);
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning(ex, "Error creating policy member at index {Index} for policy {PolicyId}", originalIndex, policy.Id);
                        // Check if this exception should be thrown directly
                        if (ShouldThrowDirectly(ex))
                            throw; // Re-throw domain exceptions that have their own GraphQL error types
                        // Convert exception to ValidationError immediately
                        return (Member: (PolicyMember?)null, OriginalIndex: originalIndex, Error: ConvertExceptionToValidationError(ex, policy.Id));
                    }
                }, cancellationToken);

                memberCreationTasks.Add(creationTask);
            }

            // Wait for all member creation tasks to complete
            var memberCreationResults = await Task.WhenAll(memberCreationTasks);

            // 2. SEPARATE SUCCESSFUL AND FAILED CREATIONS
            var policyMembersToInsert = new List<PolicyMember>();
            var memberIndexMapping = new List<(PolicyMember Member, int OriginalIndex)>();

            foreach (var result in memberCreationResults)
                if (result.Member != null)
                {
                    policyMembersToInsert.Add(result.Member);
                    memberIndexMapping.Add((result.Member, result.OriginalIndex));
                }
                else if (result.Error != null)
                    // Collect all validation errors
                    errors.Add(result.Error);

            // 3. ONLY INSERT IF THERE ARE NO ERRORS (all-or-nothing approach)
            if (errors.Count == 0 && policyMembersToInsert.Count > 0)
            {
                await InsertPolicyMembersBatchAsync(policyMembersToInsert, cancellationToken);

                // 4. COLLECT SUCCESSFUL RESULTS
                foreach (var (policyMember, originalIndex) in memberIndexMapping)
                    if (policyMember.CurrentState != null)
                        successfulMembers.Add(policyMember.CurrentState);

                logger.LogDebug("Processed batch {StartIndex}-{EndIndex}: {SuccessCount} successful",
                    startIndex, startIndex + batch.Count - 1, successfulMembers.Count);
            }
            else if (errors.Count > 0)
                logger.LogWarning("Batch {StartIndex}-{EndIndex} has {ErrorCount} errors. No members will be inserted.",
                    startIndex, startIndex + batch.Count - 1, errors.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing batch {StartIndex}-{EndIndex} for policy {PolicyId}",
                startIndex, startIndex + batch.Count - 1, policy.Id);

            // Check if this exception should be thrown directly
            if (ShouldThrowDirectly(ex))
                throw; // Re-throw domain exceptions that have their own GraphQL error types

            // Convert exception to ValidationError immediately
            errors.Add(ConvertExceptionToValidationError(ex, policy.Id));
        }

        return (successfulMembers, errors);
    }

    /// <summary>
    /// Efficiently inserts a batch of policy members using the repository's batch insertion capability
    /// </summary>
    private async Task InsertPolicyMembersBatchAsync(List<PolicyMember> policyMembers, CancellationToken cancellationToken)
    {
        if (policyMembers.Count == 0)
            return;

        try
        {
            logger.LogDebug("Inserting batch of {Count} policy members", policyMembers.Count);

            // Use the existing InsertBatchAsync method from the repository
            await policyMemberRepository.InsertBatchAsync(policyMembers, cancellationToken);

            logger.LogDebug("Successfully inserted batch of {Count} policy members", policyMembers.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to insert batch of {Count} policy members", policyMembers.Count);
            throw; // Re-throw to be handled by the calling method
        }
    }

    /// <summary>
    /// Creates batch member data from all policy members in the command
    /// </summary>
    private MembersUploadFields CreateBatchMemberDataFromCommands(List<CreatePolicyMemberCommand> createCommands)
    {
        var memberDataList = new List<MemberUploadFields>();

        foreach (var createCommand in createCommands)
        {
            var memberData = CreateSingleMemberData(createCommand);
            memberDataList.Add(memberData);
        }

        return MembersUploadFields.CreateNonEmpty(memberDataList);
    }

    /// <summary>
    /// Processes member data in batches for efficient memory management and async processing
    /// </summary>
    private static async IAsyncEnumerable<(IReadOnlyList<MemberUploadFields> Batch, int StartIndex)> ProcessMembersInBatches(
        MembersUploadFields memberData,
        int batchSize = ValidationConstants.BatchProcessing.DefaultMemberBatchSize,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        for (int i = 0; i < memberData.Count; i += batchSize)
        {
            cancellationToken.ThrowIfCancellationRequested();

            int endIndex = Math.Min(i + batchSize, memberData.Count);
            var batch = new List<MemberUploadFields>(endIndex - i);

            for (int j = i; j < endIndex; j++)
                batch.Add(memberData[j]);

            yield return (batch, i);

            // Allow other tasks to run between batches for cooperative multitasking
            await Task.Yield();
        }
    }

    private CreatePolicyMemberCommand ConvertToCreateCommand(string policyId, PolicyMemberToCreate memberToCreate)
    {
        // Convert fields from object to ICollection<PolicyField>
        ICollection<PolicyField> fields = [];
        if (memberToCreate.Fields != null)
            fields = MapFieldsToPolicyFields(memberToCreate.Fields);

        // Use policy dates as default start and end dates for the member
        var startDate = DateOnly.FromDateTime(DateTime.UtcNow);
        var endDate = DateOnly.FromDateTime(DateTime.UtcNow.AddYears(1));

        return new CreatePolicyMemberCommand
        {
            MemberId = memberToCreate.MemberId ?? Guid.NewGuid().ToString(), // Generate member ID if not provided
            PolicyId = Guid.Parse(policyId),
            PlanId = memberToCreate.PlanId,
            Fields = fields,
            DependentOfId = !string.IsNullOrEmpty(memberToCreate.DependentOf) ? Guid.Parse(memberToCreate.DependentOf) : null,
            IndividualId = null, // Not provided in the input
            StartDate = startDate,
            EndDate = endDate
        };
    }

    private ICollection<PolicyField> MapFieldsToPolicyFields(object? fieldsInput)
    {
        if (fieldsInput == null)
            return [];

        if (fieldsInput is Dictionary<string, object?> dict)
            return dict.Select(kvp => new PolicyField
            {
                Key = kvp.Key,
                Value = kvp.Value?.ToString()
            }).ToList();

        // Fallback for other types
        return new List<PolicyField>
        {
            new() { Key = "value", Value = fieldsInput.ToString() }
        };
    }

    /// <summary>
    /// Helper to transform CreatePolicyMemberCommand into MemberUploadFields for a single member
    /// </summary>
    private MemberUploadFields CreateSingleMemberData(CreatePolicyMemberCommand command)
    {
        var memberDict = new Dictionary<string, string?>
        {
            ["memberId"] = command.MemberId,
            ["planId"] = command.PlanId,
            ["startDate"] = command.StartDate?.ToString(),
            ["endDate"] = command.EndDate?.ToString(),
            ["dependentOfId"] = command.DependentOfId?.ToString(),
            ["individualId"] = command.IndividualId?.ToString(),
        };
        // Flatten command.Fields (ICollection<PolicyField>)
        if (command.Fields != null)
            foreach (PolicyField field in command.Fields)
                memberDict[field.Key] = field.Value?.ToString();
        return new MemberUploadFields(memberDict);
    }

    private async Task<PolicyDto> FindPolicyByIdAsync(string policyId, CancellationToken cancellationToken)
    {
        try
        {
            PolicyDto? policy = await legacyPolicyService.GetPolicyById(policyId, cancellationToken);
            if (policy == null)
                throw new PolicyNotFoundException(policyId);
            return policy;
        }
        catch (PolicyNotFoundException)
        {
            // Re-throw PolicyNotFoundException directly - it's handled by mutation conventions
            throw;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error finding policy {PolicyId}", policyId);
            throw new PolicyNotFoundException(policyId, ex);
        }
    }

    /// <summary>
    /// Determines if an exception should be thrown directly or converted to ValidationError
    /// </summary>
    private static bool ShouldThrowDirectly(Exception exception)
    {
        // Domain exceptions that have their own GraphQL error types should be thrown directly
        return exception switch
        {
            PolicyNotFoundException => true,
            PolicyIssuedException => true,
            PolicyContractHolderNotFoundException => true,
            PolicyProductIdMissingException => true,
            InvalidProductIdComponentException => true,
            UploadValidationLockedException => true,
            EndorsementNotFoundException => true,
            EndorsementCanNotBeChangedException => true,
            EffectiveDateOutsidePolicyDatesException => true,
            BadSchemaConfigException => true,
            MemberNotFoundException => true,
            PolicyMemberExistsException => true,
            MemberNotInContractHolderException => true,
            PolicyMemberNotFoundException => true,
            _ => false
        };
    }

    /// <summary>
    /// Converts an exception to a ValidationError for consistent error handling
    /// </summary>
    private static ValidationError ConvertExceptionToValidationError(Exception exception, string policyId)
    {
        // Handle validation exceptions that already contain ValidationError objects
        if (exception is ValidationException validationException && validationException.Errors.Count > 0)
            // Return the first validation error from the validation exception
            return validationException.Errors.First();

        // Handle other exceptions by creating a generic validation error
        return new ValidationError(
            ErrorCodes.ValidationFailed,
            "policyMembers",
            "Policy Members",
            new Dictionary<string, object?> { ["PolicyId"] = policyId, ["OriginalMessage"] = exception.Message }
        );
    }
}