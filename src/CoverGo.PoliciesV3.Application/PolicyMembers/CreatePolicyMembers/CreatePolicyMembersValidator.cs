using FluentValidation;

namespace CoverGo.PoliciesV3.Application.PolicyMembers.CreatePolicyMembers;

public class CreatePolicyMembersValidator : AbstractValidator<CreatePolicyMembersCommand>
{
    public CreatePolicyMembersValidator()
    {
        RuleFor(x => x.PolicyId)
            .NotEmpty()
            .WithMessage("PolicyId is required");

        RuleFor(x => x.PolicyMembers)
            .NotEmpty()
            .WithMessage("At least one policy member must be provided");

        RuleForEach(x => x.PolicyMembers)
            .SetValidator(new PolicyMemberToCreateValidator());
    }
}

public class PolicyMemberToCreateValidator : AbstractValidator<PolicyMemberToCreate>
{
    public PolicyMemberToCreateValidator()
    {
        RuleFor(x => x.PlanId)
            .NotEmpty()
            .WithMessage("PlanId is required for each policy member");

        RuleFor(x => x.MemberId)
            .MaximumLength(100)
            .When(x => !string.IsNullOrEmpty(x.MemberId))
            .WithMessage("MemberId cannot exceed 100 characters");

        RuleFor(x => x.Class)
            .MaximumLength(50)
            .When(x => !string.IsNullOrEmpty(x.Class))
            .WithMessage("Class cannot exceed 50 characters");

        RuleFor(x => x.DependentOf)
            .MaximumLength(100)
            .When(x => !string.IsNullOrEmpty(x.DependentOf))
            .WithMessage("DependentOf cannot exceed 100 characters");
    }
} 