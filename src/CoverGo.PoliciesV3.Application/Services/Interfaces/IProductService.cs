using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Application.Services.Interfaces;

public interface IProductService
{
    Task<string?> GetProductMemberSchema(Products.Client.ProductId productId, CancellationToken cancellationToken);

    /// <summary>
    /// Gets the product package type for the specified product.
    /// Used to determine if a product is SME type for feature flag validation.
    /// </summary>
    /// <param name="productId">The product ID to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The product package type (e.g., "sme", "individual", etc.) or null if not found</returns>
    Task<string?> GetProductPackageType(Products.Client.ProductId productId, CancellationToken cancellationToken);

    /// <summary>
    /// Gets available plan IDs for the specified product.
    /// Used by validators to check plan availability.
    /// </summary>
    /// <param name="productId">The product ID to get plans for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of available plan IDs, or null if unable to retrieve</returns>
    Task<IReadOnlyList<string>?> GetAvailablePlanIds(
        ProductId productId,
        CancellationToken cancellationToken = default);
}