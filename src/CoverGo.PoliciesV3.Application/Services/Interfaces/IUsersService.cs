using System.Text.Json;
using CoverGo.PoliciesV3.Application.Common.DTOs;
using CoverGo.Users.Client;

namespace CoverGo.PoliciesV3.Application.Services.Interfaces;

public interface IUsersService
{
    Task<JsonElement> GetCompanyContractHolderMembersFieldsById(string id, CancellationToken cancellationToken);
    Task<HashSet<string>> GetMemberIdsWithoutExistingIndividual(HashSet<string> memberIds, CancellationToken cancellationToken);
    Task<List<DuplicatedIndividual>?> GetDuplicatedIndividualsByEmails(Dictionary<string, string> emailToMemberIdDictionary, CancellationToken cancellationToken);
    Task<List<Individual>> QueryIndividuals(QueryArgumentsOfIndividualWhere where, CancellationToken cancellationToken);
    Task<List<Individual>> QueryIndividualsByEmails(List<string> emails, CancellationToken cancellationToken);
}