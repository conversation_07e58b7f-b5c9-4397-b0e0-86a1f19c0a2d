namespace CoverGo.PoliciesV3.Application.Services.Interfaces;

/// <summary>
/// Service for interacting with the file system to retrieve file content and metadata
/// </summary>
public interface IFileSystemService
{
    /// <summary>
    /// Gets the complete file content as a byte array
    /// </summary>
    /// <param name="path">The file path</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File content as byte array, or null if file not found</returns>
    Task<byte[]?> GetFileByPath(string path, CancellationToken cancellationToken);
}