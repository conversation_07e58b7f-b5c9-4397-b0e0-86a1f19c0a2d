using CoverGo.PoliciesV3.Domain.Policies;
using LegacyPolicy = CoverGo.Policies.Client.Policy;

namespace CoverGo.PoliciesV3.Application.Services.Interfaces;

public interface ILegacyPolicyService
{
    /// <summary>
    /// Gets policy IDs by contract holder ID from the legacy policy system.
    /// Used for member ID validation to check contract holder constraints.
    /// </summary>
    /// <param name="contractHolderId">The contract holder ID to search for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of policy IDs associated with the contract holder, or empty list if none found</returns>
    Task<List<string>> GetIdsByContractHolderId(string? contractHolderId, CancellationToken cancellationToken);

    /// <summary>
    /// Gets policy data as DTO with essential information only.
    /// Replaces direct usage of LegacyPolicy domain object for better performance.
    /// </summary>
    /// <param name="policyId">The policy ID to retrieve</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>PolicyDto with essential policy information, or null if not found</returns>
    Task<PolicyDto?> GetPolicyById(string policyId, CancellationToken cancellationToken);

    /// <summary>
    /// Gets multiple raw legacy policy data from external API
    /// </summary>
    /// <param name="policyIds">List of policy IDs to retrieve</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of legacy policies that were successfully retrieved (excludes not found policies)</returns>
    Task<List<LegacyPolicy>> GetLegacyPoliciesByIds(List<string> policyIds, CancellationToken cancellationToken);

    /// <summary>
    /// Gets multiple policy data as DTOs with essential information only.
    /// Replaces direct usage of LegacyPolicy domain objects for better performance.
    /// </summary>
    /// <param name="policyIds">List of policy IDs to retrieve</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of PolicyDto objects that were successfully retrieved and mapped (excludes not found policies)</returns>
    Task<List<PolicyDto>> GetPolicyDtosByIds(List<string> policyIds, CancellationToken cancellationToken);

}