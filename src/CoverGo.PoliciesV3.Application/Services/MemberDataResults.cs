using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Application.Services;

/// <summary>
/// Container for member-specific validation data results.
/// </summary>
public record MemberDataResults(
    Dictionary<string, bool> IndividualExistenceMap,
    Dictionary<string, PolicyMember?> ExistingPolicyMembers,
    Dictionary<string, IReadOnlyList<PolicyMember>> MemberValidationStates,
    Dictionary<string, PolicyMember?> DependentMembersCache)
{
    /// <summary>
    /// Creates an empty result set for cases with no member data.
    /// </summary>
    public static MemberDataResults Empty() => new(
        [],
        [],
        [],
        []);
}
