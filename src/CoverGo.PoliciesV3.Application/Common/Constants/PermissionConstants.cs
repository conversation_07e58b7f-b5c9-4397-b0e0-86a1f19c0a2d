namespace CoverGo.PoliciesV3.Application.Common.Constants;

/// <summary>
/// Constants for permission names to avoid magic strings.
/// Centralizes all permission-related string literals used in authorization.
/// </summary>
public static class PermissionConstants
{
    /// <summary>
    /// Policy-related permissions
    /// </summary>
    public static class Policies
    {
        /// <summary>
        /// Permission to update policies
        /// </summary>
        public const string Update = "updatePolicies";

        /// <summary>
        /// Permission to write/create policies
        /// </summary>
        public const string Write = "writePolicies";
    }
}
