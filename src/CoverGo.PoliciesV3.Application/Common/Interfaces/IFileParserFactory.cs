namespace CoverGo.PoliciesV3.Application.Common.Interfaces;

/// <summary>
/// Factory interface for creating appropriate file parsers based on file content
/// </summary>
public interface IFileParserFactory
{
    /// <summary>
    /// Creates an appropriate file parser based on the file content
    /// </summary>
    /// <param name="fileContent">The file content as byte array</param>
    /// <returns>An instance of IFileParser suitable for the file type</returns>
    IFileParser CreateParser(byte[] fileContent);
}
