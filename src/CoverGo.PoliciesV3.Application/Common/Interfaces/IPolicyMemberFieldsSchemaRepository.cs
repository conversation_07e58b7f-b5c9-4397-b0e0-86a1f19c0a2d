using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Application.Common.Interfaces;

/// <summary>
/// Repository interface for retrieving raw policy member field schemas from data sources
/// This is focused on data access only - business logic is handled by the provider
/// </summary>
public interface IPolicyMemberFieldsSchemaRepository
{
    /// <summary>
    /// Gets the raw custom fields schema from data sources (combines Product/Census/Member schemas)
    /// </summary>
    /// <param name="contractHolderId">Optional contract holder ID</param>
    /// <param name="productId">Product ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The raw policy member fields schema</returns>
    Task<PolicyMemberFieldsSchema> GetCustomFieldsSchema(
        string? contractHolderId,
        ProductId productId,
        CancellationToken cancellationToken = default);
}
