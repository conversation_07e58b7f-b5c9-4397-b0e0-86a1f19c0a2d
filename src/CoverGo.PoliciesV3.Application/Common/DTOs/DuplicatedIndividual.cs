namespace CoverGo.PoliciesV3.Application.Common.DTOs;

/// <summary>
/// Represents a duplicated individual identified by email address
/// </summary>
public class DuplicatedIndividual
{
    /// <summary>
    /// Gets the email address of the duplicated individual
    /// </summary>
    public string? Email { get; init; }

    /// <summary>
    /// Gets the entity ID that this individual is duplicated by
    /// </summary>
    public string? DuplicatedByEntityId { get; init; }
}
