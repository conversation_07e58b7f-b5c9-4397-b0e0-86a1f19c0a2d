namespace CoverGo.PoliciesV3.Application.Common.DTOs;

/// <summary>
/// Represents a key-value pair for additional fields
/// </summary>
public class FieldValuePair
{
    /// <summary>
    /// Gets or sets the field name/key
    /// </summary>
    public required string Key { get; set; }

    /// <summary>
    /// Gets or sets the field value
    /// </summary>
    public object? Value { get; set; }

    /// <summary>
    /// Converts a list of FieldValuePair objects to a dictionary
    /// </summary>
    /// <param name="fieldValuePairs">The list of field value pairs to convert</param>
    /// <returns>A dictionary representation of the field value pairs, preserving null values</returns>
    public static Dictionary<string, object?> ToDictionary(List<FieldValuePair> fieldValuePairs)
    {
        var dictionary = new Dictionary<string, object?>();
        foreach (FieldValuePair pair in fieldValuePairs)
            dictionary[pair.Key] = pair.Value;
        return dictionary;
    }
}
