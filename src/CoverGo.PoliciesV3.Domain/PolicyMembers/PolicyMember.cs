using System.ComponentModel.DataAnnotations;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Events;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers;

public class PolicyMember : AggregateRootBase<PolicyMemberId>
{
    #region Constructors

    private PolicyMember(PolicyMemberId id) : base(id)
    {
    }

    public PolicyMember() : this(PolicyMemberId.Empty)
    {
    }

    #endregion

    #region Fields / Properties

    // Entity Identifiers
    public required string MemberId { get; init; }
    public required PolicyId PolicyId { get; init; }
    public PolicyMemberId? DependentOfId { get; private set; }
    public Guid? IndividualId { get; private set; }

    // State Fields
    public bool IsRemoved { get; private set; }
    public bool IsPrinted { get; private set; }
    public bool IsRenewed { get; private set; }
    public string? CertificateNumber { get; private set; }

    // Collections
    public virtual ICollection<PolicyMemberState> States { get; init; } = [];

    // Concurrency Control - maps to PostgreSQL xmin system column
    [Timestamp]
    public uint RowVersion { get; init; }

    // TODO: Determine if these quote-related fields are needed:
    // - QuoteMemberId: Links to original quote member
    // - QuoteId: Links to original quote
    // - ExternalValidationResult: Stores external validation state

    #endregion

    #region Navigation Properties

    public Policy? Policy { get; set; }
    public virtual PolicyMember? DependentOf { get; init; }

    #endregion

    #region Computed Properties

    /// <summary>
    /// Gets the current active state
    /// </summary>
    public PolicyMemberState? CurrentState => GetActiveStateOn(DateOnly.FromDateTime(DateTime.UtcNow));

    #endregion

    #region Factory Methods

    public static PolicyMember Create(
        PolicyId policyId,
        string memberId,
        DateOnly? startDate,
        DateOnly? endDate,
        string planId,
        PolicyMemberId? dependentOfId = null,
        Guid? individualId = null,
        ICollection<PolicyField>? fields = null)
    {
        var policyMember = new PolicyMember(PolicyMemberId.New)
        {
            PolicyId = policyId,
            MemberId = memberId,
            IndividualId = individualId,
            DependentOfId = dependentOfId
        };

        var @event = new PolicyMemberCreatedEvent(policyId)
        {
            PolicyMemberId = policyMember.Id,
            MemberId = memberId,
            StartDate = startDate,
            EndDate = endDate,
            PlanId = planId,
            DependentOfId = dependentOfId,
            IndividualId = individualId,
            Fields = fields ?? []
        };

        policyMember.AddDomainEvent(@event);

        // Create initial state if we have valid dates and plan
        if (startDate.HasValue && endDate.HasValue && !string.IsNullOrEmpty(planId))
        {
            // Validate date consistency using shared validation method
            PolicyMemberState.ValidateDateRange(startDate.Value, endDate.Value);

            var initialState = PolicyMemberState.Create(
                policyMember.Id,
                startDate.Value,
                endDate.Value,
                planId,
                null,
                null,
                fields);

            policyMember.States.Add(initialState);
        }

        return policyMember;
    }

    #endregion

    #region State Management

    /// <summary>
    /// Adds a new state to this policy member with validation for date consistency and overlaps
    /// </summary>
    /// <param name="startDate">Start date of the new state</param>
    /// <param name="endDate">End date of the new state</param>
    /// <param name="planId">Plan ID for the new state</param>
    /// <param name="class">Optional class for the new state</param>
    /// <param name="endorsementId">Optional endorsement ID for the new state</param>
    /// <param name="customFields">Optional custom fields for the new state</param>
    /// <exception cref="ArgumentException">Thrown when date validation fails or overlaps are detected</exception>
    public void AddState(
        DateOnly startDate,
        DateOnly endDate,
        string planId,
        string? @class = null,
        EndorsementId? endorsementId = null,
        ICollection<PolicyField>? customFields = null)
    {
        // Create the new state (this will validate date consistency)
        var newState = PolicyMemberState.Create(
            Id,
            startDate,
            endDate,
            planId,
            @class,
            endorsementId,
            customFields);

        // Check for overlaps with existing states
        PolicyMemberState? overlappingState = States.FirstOrDefault(existingState => existingState.OverlapsWith(newState));
        if (overlappingState != null)
        {
            throw new ArgumentException(
                $"New state period ({startDate} to {endDate}) overlaps with existing state period ({overlappingState.StartDate} to {overlappingState.EndDate})",
                nameof(startDate));
        }

        States.Add(newState);
    }

    /// <summary>
    /// Gets the active state on a specific date
    /// </summary>
    public PolicyMemberState? GetActiveStateOn(DateOnly date)
        => States.FirstOrDefault(s => s.IsActiveOn(date));

    /// <summary>
    /// Checks if this member has any states that are approved for underwriting
    /// </summary>
    public bool HasApprovedStates() =>
        States.Any(s => s.IsApprovedForUnderwriting());


    /// <summary>
    /// Checks if this policy member has a matching field value in any of its states
    /// </summary>
    public bool HasMatchingFieldValue(string fieldName, string fieldValue) =>
        States.Where(state => state.Fields.Count > 0)
              .SelectMany(state => state.Fields)
              .Where(field => string.Equals(field.Key, fieldName, StringComparison.OrdinalIgnoreCase))
              .Any(field => field.MatchesValue(fieldValue));

    /// <summary>
    /// Checks if this policy member has valid endorsements based on the provided endorsement IDs
    /// Business rule: Member is valid if it has states with null endorsement (base policy) or matching endorsement IDs
    /// </summary>
    public bool HasValidEndorsements(List<EndorsementId> validEndorsementIds, bool includeNullEndorsements = true) =>
        States.Any(s =>
            s.EndorsementId == null && includeNullEndorsements ||
            s.EndorsementId != null && validEndorsementIds.Contains(s.EndorsementId));

    /// <summary>
    /// Checks if this policy member should be excluded from validation based on current member context
    /// Business rule: Exclude if this is the current policy member or current member ID being validated
    /// </summary>
    public bool IsExcludedFromValidation(string? currentMemberId, PolicyMemberId? currentPolicyMemberId) => currentPolicyMemberId != null && Id == currentPolicyMemberId || !string.IsNullOrEmpty(currentMemberId) && MemberId == currentMemberId;

    /// <summary>
    /// Gets the latest policy member for each member ID from a collection
    /// Business rule: Groups by MemberId and returns the most recently modified member from each group
    /// </summary>
    public static IEnumerable<PolicyMember> GetLatestByMemberId(IEnumerable<PolicyMember> members) =>
        members.GroupBy(pm => pm.MemberId)
               .Select(g => g.OrderByDescending(pm => pm.EntityAuditInfo.LastModifiedAt ?? pm.EntityAuditInfo.CreatedAt)
                            .First());

    /// <summary>
    /// Marks this policy member as removed (soft delete)
    /// </summary>
    public void MarkAsRemoved() => IsRemoved = true;

    #endregion

}