using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;

public record MemberIdValidationContext : MemberValidationContext
{
    #region Properties

    /// <summary>
    /// List of contract holder policy IDs for business rule validation.
    /// Used to validate member ID constraints across contract holder policies.
    /// </summary>
    public required IReadOnlyList<string> ContractHolderPolicyIds { get; init; }

    /// <summary>
    /// Individual existence check.
    /// True if individual with member ID exists, false otherwise.
    /// </summary>
    public bool IndividualExists { get; init; }

    /// <summary>
    /// Feature flag for AllowMembersFromOtherContractHolders.
    /// True if members from other contract holders are allowed, false otherwise.
    /// </summary>
    public bool AllowMembersFromOtherContractHolders { get; init; }

    /// <summary>
    /// Existing policy member data.
    /// Null if no existing policy member found with the member ID.
    /// </summary>
    public PolicyMember? ExistingPolicyMember { get; init; }

    /// <summary>
    /// Member validation states.
    /// List of policy members across all policies for contract holder validation.
    /// </summary>
    public IReadOnlyList<PolicyMember> MemberValidationStates { get; init; } = [];

    #endregion

    #region Factory Method

    /// <summary>
    /// Creates a new MemberIdValidationContext with all necessary data for member ID validation.
    /// </summary>
    /// <param name="memberFields">The member fields to validate (contains member ID)</param>
    /// <param name="policy">The policy DTO containing policy information</param>
    /// <param name="schema">The schema containing field definitions and validation rules</param>
    /// <param name="contractHolderPolicyIds">List of contract holder policy IDs for business rule validation</param>
    /// <param name="individualExists">Individual existence check</param>
    /// <param name="allowMembersFromOtherContractHolders">Feature flag value</param>
    /// <param name="existingPolicyMember">Existing policy member data (optional)</param>
    /// <param name="memberValidationStates">Member validation states (optional)</param>
    /// <param name="endorsementId">The endorsement ID for the validation context (optional)</param>
    /// <returns>A new MemberIdValidationContext instance</returns>
    /// <exception cref="ArgumentNullException">Thrown when any required parameter is null</exception>
    public static MemberIdValidationContext Create(
        MemberUploadFields memberFields,
        PolicyDto policy,
        PolicyMemberFieldsSchema schema,
        IReadOnlyList<string> contractHolderPolicyIds,
        bool individualExists,
        bool allowMembersFromOtherContractHolders,
        PolicyMember? existingPolicyMember = null,
        IReadOnlyList<PolicyMember>? memberValidationStates = null,
        Guid? endorsementId = null)
    {
        // Validate base context requirements
        Validate(memberFields, policy, schema);

        // Validate member ID-specific requirements
        ArgumentNullException.ThrowIfNull(contractHolderPolicyIds, nameof(contractHolderPolicyIds));

        return new MemberIdValidationContext
        {
            MemberFields = memberFields,
            Policy = policy,
            Schema = schema,
            EndorsementId = endorsementId,
            ContractHolderPolicyIds = contractHolderPolicyIds,
            IndividualExists = individualExists,
            AllowMembersFromOtherContractHolders = allowMembersFromOtherContractHolders,
            ExistingPolicyMember = existingPolicyMember,
            MemberValidationStates = memberValidationStates ?? []
        };
    }

    #endregion

}