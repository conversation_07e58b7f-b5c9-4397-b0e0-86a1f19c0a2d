using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;

public sealed record DependentValidationContext : MemberValidationContext
{
    #region Dependent-Specific Properties

    /// <summary>
    /// The primary member fields from upload (optional).
    /// Used when validating dependent relationships within the same upload batch.
    /// </summary>
    public MemberUploadFields? PrimaryMemberFields { get; init; }

    /// <summary>
    /// The policy ID for validation.
    /// Required for dependent member relationship validation.
    /// </summary>
    public required string PolicyId { get; init; }

    /// <summary>
    /// Cache of dependent members for efficient lookup during validation.
    /// Key: member ID, Value: PolicyMember or null if not found.
    /// </summary>
    public required IReadOnlyDictionary<string, PolicyMember?> MembersCache { get; init; }

    /// <summary>
    /// List of valid endorsement IDs for the validation context.
    /// Used for endorsement-specific dependent validation scenarios.
    /// </summary>
    public required IReadOnlyList<string?> ValidEndorsementIds { get; init; }

    /// <summary>
    /// Cached EndorsementIdCollection for efficient reuse across multiple validation calls.
    /// Lazy-initialized from ValidEndorsementIds to avoid repeated creation overhead.
    /// </summary>
    private EndorsementIdCollection? _cachedEndorsementIdCollection;

    public EndorsementIdCollection CachedEndorsementIdCollection =>
        _cachedEndorsementIdCollection ??= EndorsementIdCollection.FromStringIds([.. ValidEndorsementIds]);

    /// <summary>
    /// <summary>
    /// Cached PolicyId for efficient reuse and safe conversion.
    /// Lazy-initialized from PolicyId string to avoid repeated parsing and ensure type safety.
    /// </summary>
    private PolicyId? _cachedPolicyId;
    public PolicyId CachedPolicyId =>
        _cachedPolicyId ??= PolicyId;

    #endregion

    #region Factory Method

    /// <summary>
    /// Creates a new DependentValidationContext with comprehensive validation.
    /// Validates all required parameters and ensures data integrity.
    /// </summary>
    /// <param name="dependentMemberFields">The dependent member fields to validate</param>
    /// <param name="policyId">The policy ID for validation</param>
    /// <param name="policy">The policy DTO containing policy information</param>
    /// <param name="schema">The schema containing field definitions</param>
    /// <param name="membersCache">Cache of dependent members for efficient lookup</param>
    /// <param name="validEndorsementIds">List of valid endorsement IDs</param>
    /// <param name="primaryMemberFields">The primary member fields from upload (optional)</param>
    /// <param name="endorsementId">The endorsement ID (optional)</param>
    /// <returns>A new DependentValidationContext instance</returns>
    /// <exception cref="ArgumentException">Thrown when policy ID is null or empty</exception>
    /// <exception cref="ArgumentNullException">Thrown when any required parameter is null</exception>
    public static DependentValidationContext Create(
        MemberUploadFields dependentMemberFields,
        string policyId,
        PolicyDto policy,
        PolicyMemberFieldsSchema schema,
        IReadOnlyDictionary<string, PolicyMember?> membersCache,
        IReadOnlyList<string?> validEndorsementIds,
        MemberUploadFields? primaryMemberFields = null,
        Guid? endorsementId = null)
    {
        // Validate base context requirements
        Validate(dependentMemberFields, policy, schema);

        // Validate dependent-specific requirements
        if (string.IsNullOrWhiteSpace(policyId))
            throw new ArgumentException("Policy ID cannot be null or empty", nameof(policyId));

        ArgumentNullException.ThrowIfNull(membersCache, nameof(membersCache));
        ArgumentNullException.ThrowIfNull(validEndorsementIds, nameof(validEndorsementIds));

        return new DependentValidationContext
        {
            MemberFields = dependentMemberFields,
            Policy = policy,
            Schema = schema,
            EndorsementId = endorsementId,
            PrimaryMemberFields = primaryMemberFields,
            PolicyId = policyId,
            MembersCache = membersCache,
            ValidEndorsementIds = validEndorsementIds
        };
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Gets the dependent member's 'dependentOf' field value.
    /// Returns null if the field is not present or empty.
    /// </summary>
    /// <returns>The dependentOf field value or null</returns>
    public string? GetDependentOf() => GetFieldValue("dependentOf");

    /// <summary>
    /// Determines if the member is a dependent based on the presence of 'dependentOf' field.
    /// </summary>
    /// <returns>True if the member is a dependent, false otherwise</returns>
    public bool IsDependent() => MemberFields.IsDependent();

    /// <summary>
    /// Checks if a primary member exists in the upload batch.
    /// Used when validating dependent relationships within the same upload.
    /// </summary>
    /// <returns>True if primary member fields are provided, false otherwise</returns>
    public bool HasPrimaryMemberInUpload() => PrimaryMemberFields != null;

    /// <summary>
    /// Gets a cached policy member by member ID.
    /// Returns null if the member is not found in the cache.
    /// </summary>
    /// <param name="memberId">The member ID to look up</param>
    /// <returns>The cached PolicyMember or null if not found</returns>
    public PolicyMember? GetCachedMember(string memberId) =>
        MembersCache.TryGetValue(memberId, out PolicyMember? member) ? member : null;

    /// <summary>
    /// Checks if a member exists in the cache.
    /// </summary>
    /// <param name="memberId">The member ID to check</param>
    /// <returns>True if the member exists in cache, false otherwise</returns>
    public bool IsMemberCached(string memberId) => MembersCache.ContainsKey(memberId);

    #endregion
}