using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Products;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;

/// <summary>
/// Business Rules:
/// - Plan ID must exist in the product's list of available plans
/// - Empty or null plan ID values are not validated (considered valid)
/// - Policy must have a valid product ID for plan validation to occur
/// - Available plans must be pre-resolved at application layer
/// - Gracefully handles missing available plans data
/// </summary>
public class MemberMustHaveValidPlanIdSpecification(ILogger<MemberMustHaveValidPlanIdSpecification> logger)
    : ISpecification<PlanValidationContext>
{
    public string BusinessRuleName => "Member Must Have Valid Plan ID";
    public string Description => "Validates that member plan IDs exist in the product's available plans to ensure plan availability and prevent invalid plan assignments";

    public virtual async Task<Result> IsSatisfiedBy(PlanValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            await Task.CompletedTask;

            string? planId = context.GetPlanId();
            if (string.IsNullOrWhiteSpace(planId))
            {
                logger.LogDebug("Plan ID is null or empty for policy: {PolicyId}, skipping validation", context.Policy.Id);
                return Result.Success();
            }

            if (!context.HasValidProductId())
            {
                logger.LogWarning("Policy {PolicyId} has no ProductId, cannot validate plan: {PlanId}", context.Policy.Id, planId);
                return Result.Success(); // Skip validation if no product ID (matches original behavior)
            }

            if (!context.CanValidatePlans())
            {
                logger.LogWarning("No available plans data provided for policy {PolicyId}, skipping plan validation for plan: {PlanId}",
                    context.Policy.Id, planId);
                return Result.Success(); // Skip validation if no available plans data
            }

            if (!context.IsPlanAvailable(planId))
            {
                ProductId domainProductId = context.Policy.ProductId!.ToDomainProductId();
                List<string> availablePlansList = context.GetAvailablePlansList();

                logger.LogWarning("Plan ID '{PlanId}' validation failed for policy {PolicyId}. Available plans: [{AvailablePlans}]",
                    planId, context.Policy.Id, string.Join(", ", availablePlansList));

                ValidationError error = Errors.InvalidPlanId("planId", planId, availablePlansList, "Plan ID", domainProductId.ToString());
                return Result.Failure([error]);
            }

            logger.LogDebug("Plan ID '{PlanId}' validation passed for policy {PolicyId}", planId, context.Policy.Id);
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error validating plan ID for policy {PolicyId}", context.Policy.Id);

            string? planId = context.GetPlanId();
            string productId = GetSafeProductIdString(context.Policy.ProductId);
            ValidationError error = Errors.InvalidPlanId("planId", planId ?? "unknown", [], "Plan ID", productId);
            return Result.Failure([error]);
        }
    }

    /// <summary>
    /// Safely converts ProductIdDto to string, returning "unknown" if conversion fails
    /// </summary>
    private static string GetSafeProductIdString(ProductIdDto? productId)
    {
        if (productId == null)
            return "unknown";

        try
        {
            // Check if all required fields are present and valid
            return string.IsNullOrWhiteSpace(productId.Type) ||
                string.IsNullOrWhiteSpace(productId.Plan) ||
                string.IsNullOrWhiteSpace(productId.Version)
                ? "unknown"
                : productId.ToString();
        }
        catch
        {
            return "unknown";
        }
    }
}