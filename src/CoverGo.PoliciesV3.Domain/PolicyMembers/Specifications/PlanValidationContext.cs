using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;

public sealed record PlanValidationContext : MemberValidationContext
{
    #region Additional Properties

    /// <summary>
    /// Pre-resolved available plans for the product to avoid application service dependencies in domain layer.
    /// Null indicates that plan validation should be skipped (plans not available or not applicable).
    /// </summary>
    public IReadOnlySet<string>? AvailablePlans { get; init; }

    #endregion

    #region Factory Methods

    /// <summary>
    /// Creates a new PlanValidationContext with comprehensive validation.
    /// </summary>
    /// <param name="memberFields">The member fields to validate</param>
    /// <param name="policy">The policy containing the member</param>
    /// <param name="schema">The schema containing field definitions and validation rules</param>
    /// <param name="availablePlans">Pre-resolved available plans for the product (null to skip validation)</param>
    /// <param name="endorsementId">The endorsement ID for the validation context (optional)</param>
    /// <returns>A new PlanValidationContext instance</returns>
    /// <exception cref="ArgumentNullException">Thrown when required parameters are null</exception>
    public static PlanValidationContext Create(
        MemberUploadFields memberFields,
        PolicyDto policy,
        PolicyMemberFieldsSchema schema,
        IReadOnlySet<string>? availablePlans = null,
        Guid? endorsementId = null)
    {
        // Validate required parameters using base class method
        Validate(memberFields, policy, schema);

        return new PlanValidationContext
        {
            MemberFields = memberFields,
            Policy = policy,
            Schema = schema,
            AvailablePlans = availablePlans,
            EndorsementId = endorsementId
        };
    }

    #endregion

    #region Specialized Helper Methods

    /// <summary>
    /// Determines if the policy has a valid product ID for plan validation.
    /// </summary>
    /// <returns>True if the policy has a valid product ID, false otherwise</returns>
    public bool HasValidProductId() => Policy.ProductId != null;

    /// <summary>
    /// Determines if plan validation can be performed.
    /// Plan validation requires both a valid product ID and available plans data.
    /// </summary>
    /// <returns>True if plan validation can be performed, false otherwise</returns>
    public bool CanValidatePlans() => HasValidProductId() && AvailablePlans != null;

    /// <summary>
    /// Determines if a specific plan ID is available for the product.
    /// </summary>
    /// <param name="planId">The plan ID to check</param>
    /// <returns>True if the plan is available, false otherwise</returns>
    public bool IsPlanAvailable(string planId) =>
        AvailablePlans != null && !string.IsNullOrWhiteSpace(planId) && AvailablePlans.Contains(planId);

    /// <summary>
    /// Gets the list of available plans for the product.
    /// </summary>
    /// <returns>List of available plan IDs, empty list if no plans are available</returns>
    public List<string> GetAvailablePlansList() => AvailablePlans?.ToList() ?? [];

    /// <summary>
    /// Determines if the member's plan ID is valid and available.
    /// </summary>
    /// <returns>True if the member's plan ID is valid and available, false otherwise</returns>
    public bool IsMemberPlanValid()
    {
        string? planId = GetPlanId();
        return !string.IsNullOrWhiteSpace(planId) && IsPlanAvailable(planId);
    }

    /// <summary>
    /// Gets the count of available plans.
    /// </summary>
    /// <returns>The number of available plans, 0 if no plans are available</returns>
    public int GetAvailablePlansCount() => AvailablePlans?.Count ?? 0;

    /// <summary>
    /// Determines if any plans are available for the product.
    /// </summary>
    /// <returns>True if at least one plan is available, false otherwise</returns>
    public bool HasAvailablePlans() => GetAvailablePlansCount() > 0;

    #endregion
}
