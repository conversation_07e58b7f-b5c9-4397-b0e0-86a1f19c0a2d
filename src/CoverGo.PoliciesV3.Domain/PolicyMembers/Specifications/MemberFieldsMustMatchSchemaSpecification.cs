using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;

/// <summary>
/// Business Rules:
/// - Member fields must conform to schema field definitions and types
/// - Required fields must be present and valid
/// - Field types must match schema expectations
/// - OneOf validation rules must be satisfied for field combinations
/// - Dependent member field requirements are adjusted based on member type
/// </summary>
public class MemberFieldsMustMatchSchemaSpecification(ILogger<MemberFieldsMustMatchSchemaSpecification> logger)
    : ISpecification<FieldValidationContext>
{
    public string BusinessRuleName => "Member Fields Must Match Schema";
    public string Description => "Validates that member fields conform to schema definitions including field types, required fields, and validation rules";

    public virtual async Task<Result> IsSatisfiedBy(FieldValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            await Task.CompletedTask;

            var fieldsErrors = new List<ValidationError>();

            logger.LogDebug("MemberFieldsMustMatchSchemaSpecification: context.MemberFields.Value.Count: {Count}", context.MemberFields.Value.Count);
            foreach (KeyValuePair<string, string?> field in context.MemberFields.Value)
            {
                logger.LogDebug("MemberFieldsMustMatchSchemaSpecification: Field {Key} = {Value}", field.Key, field.Value);
            }

            var fieldsDict = context.MemberFields.Value.ToDictionary(it => it.Key, it => (object?)it.Value);

            logger.LogDebug("About to call ValidateFields with memberId: {MemberId}", context.MemberId);
            try
            {
                List<ValidationError> schemaErrors = context.Schema.ValidateFields(fieldsDict, context.MemberId, null);
                logger.LogDebug("ValidateFields returned {ErrorCount} errors", schemaErrors.Count);
                fieldsErrors.AddRange(schemaErrors);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Exception in ValidateFields: {Message}", ex.Message);
                throw;
            }

            if (fieldsErrors.Count > 0)
            {
                logger.LogWarning("Field validation failed for member in policy: {PolicyId}. Found {ErrorCount} validation errors",
                    context.Policy.Id, fieldsErrors.Count);
                return Result.Failure(fieldsErrors);
            }

            logger.LogDebug("Field validation passed for member in policy: {PolicyId}", context.Policy.Id);
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during field validation for member in policy: {PolicyId}", context.Policy.Id);
            return Result.Failure([Errors.InvalidFormat("fields", "Fields")]);
        }
    }
}
