using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Events;

/// <summary>
/// Domain event raised when a state is removed from a policy member
/// </summary>
public class PolicyMemberStateRemovedEvent(PolicyId aggregateId) : DomainEvent<PolicyId>(aggregateId)
{
    public PolicyMemberId PolicyMemberId { get; init; } = PolicyMemberId.Empty;
    public PolicyMemberState State { get; init; } = null!;

    public PolicyMemberStateRemovedEvent(PolicyId aggregateId, PolicyMemberId policyMemberId, PolicyMemberState state)
        : this(aggregateId)
    {
        PolicyMemberId = policyMemberId;
        State = state;
    }
}
