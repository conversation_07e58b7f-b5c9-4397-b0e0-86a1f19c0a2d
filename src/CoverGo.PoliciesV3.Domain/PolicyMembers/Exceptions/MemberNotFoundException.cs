using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Exceptions;

/// <summary>
/// Exception thrown when an individual member is not found by member ID
/// Used during member ID validation to ensure the individual exists in the system
/// </summary>
public class MemberNotFoundException : DomainException
{
    public MemberNotFoundException(string memberId)
        : base(ErrorMessages.MemberNotFound(memberId))
    {
        MemberId = memberId;
    }

    public MemberNotFoundException(string memberId, Exception innerException)
        : base(ErrorMessages.MemberNotFound(memberId), innerException)
    {
        MemberId = memberId;
    }

    public override string Code => ErrorCodes.MemberNotFound;

    public string MemberId { get; }
}
