using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Exceptions;

/// <summary>
/// Exception thrown when a policy member is not found by member ID
/// Used during dependent member validation to indicate missing primary members
/// </summary>
public class PolicyMemberNotFoundException : DomainException
{
    public PolicyMemberNotFoundException(string memberId)
        : base(ErrorMessages.PolicyMemberNotFound(memberId))
    {
        MemberId = memberId;
    }

    public PolicyMemberNotFoundException(string message, string memberId)
        : base(message)
    {
        MemberId = memberId;
    }

    public PolicyMemberNotFoundException(string memberId, Exception innerException)
        : base(ErrorMessages.PolicyMemberNotFound(memberId), innerException)
    {
        MemberId = memberId;
    }

    public override string Code => ErrorCodes.PolicyMemberNotFound;

    public string MemberId { get; }
}
