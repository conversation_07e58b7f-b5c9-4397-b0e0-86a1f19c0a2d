using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Exceptions;

/// <summary>
/// Exception thrown when a member is found in policies that don't belong to the current contract holder
/// Used during member ID validation when the feature flag "AllowMembersFromOtherContractHolders" is disabled
/// </summary>
public class MemberNotInContractHolderException : DomainException
{
    public MemberNotInContractHolderException()
        : base("Member is not in the current contract holder")
    {
    }

    public MemberNotInContractHolderException(string memberId, string policyId)
        : base(ErrorMessages.MemberNotInContractHolder(memberId, policyId))
    {
        MemberId = memberId;
        PolicyId = policyId;
    }

    public MemberNotInContractHolderException(Exception innerException)
        : base("Member is not in the current contract holder", innerException)
    {
    }

    public MemberNotInContractHolderException(string memberId, string policyId, Exception innerException)
        : base(ErrorMessages.MemberNotInContractHolder(memberId, policyId), innerException)
    {
        MemberId = memberId;
        PolicyId = policyId;
    }

    public override string Code => ErrorCodes.MemberNotInContractHolder;

    public string? MemberId { get; }
    public string? PolicyId { get; }
}
