using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Exceptions;

/// <summary>
/// Exception thrown when policy member states have overlapping date ranges
/// Business rule: Policy member states cannot have overlapping date ranges
/// </summary>
public class PolicyMemberStateOverlapException : DomainException
{
    public PolicyMemberStateOverlapException(DateOnly firstStateEndDate, DateOnly secondStateStartDate)
        : base(ErrorMessages.PolicyMemberStateOverlap(firstStateEndDate, secondStateStartDate))
    {
        FirstStateEndDate = firstStateEndDate;
        SecondStateStartDate = secondStateStartDate;
    }

    public PolicyMemberStateOverlapException(DateOnly firstStateEndDate, DateOnly secondStateStartDate, Exception innerException)
        : base(ErrorMessages.PolicyMemberStateOverlap(firstStateEndDate, secondStateStartDate), innerException)
    {
        FirstStateEndDate = firstStateEndDate;
        SecondStateStartDate = secondStateStartDate;
    }

    public override string Code => ErrorCodes.PolicyMemberStateOverlap;

    public DateOnly FirstStateEndDate { get; }
    public DateOnly SecondStateStartDate { get; }
}
