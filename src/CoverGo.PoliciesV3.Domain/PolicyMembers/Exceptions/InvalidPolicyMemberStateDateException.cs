using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Exceptions;

/// <summary>
/// Exception thrown when a policy member state has invalid date ranges
/// Business rule: State end date must be after or equal to start date
/// </summary>
public class InvalidPolicyMemberStateDateException : DomainException
{
    public InvalidPolicyMemberStateDateException(DateOnly startDate, DateOnly endDate)
        : base(ErrorMessages.InvalidPolicyMemberStateDate(startDate, endDate))
    {
        StartDate = startDate;
        EndDate = endDate;
    }

    public InvalidPolicyMemberStateDateException(DateOnly startDate, DateOnly endDate, Exception innerException)
        : base(ErrorMessages.InvalidPolicyMemberStateDate(startDate, endDate), innerException)
    {
        StartDate = startDate;
        EndDate = endDate;
    }

    public override string Code => ErrorCodes.InvalidPolicyMemberStateDate;

    public DateOnly StartDate { get; }
    public DateOnly EndDate { get; }
}
