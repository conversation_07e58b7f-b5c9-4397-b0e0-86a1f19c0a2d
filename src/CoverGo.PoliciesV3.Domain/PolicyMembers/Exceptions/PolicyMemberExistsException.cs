using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Exceptions;

/// <summary>
/// Exception thrown when a member ID is already taken by another policy member
/// Used during member ID validation to prevent duplicate member IDs within a policy
/// </summary>
public class PolicyMemberExistsException : DomainException
{
    public PolicyMemberExistsException(Guid policyMemberId, string memberId)
        : base(ErrorMessages.PolicyMemberExists(memberId, policyMemberId))
    {
        PolicyMemberId = policyMemberId;
        MemberId = memberId;
    }

    public PolicyMemberExistsException(Guid policyMemberId, string memberId, Exception innerException)
        : base(ErrorMessages.PolicyMemberExists(memberId, policyMemberId), innerException)
    {
        PolicyMemberId = policyMemberId;
        MemberId = memberId;
    }

    public override string Code => ErrorCodes.PolicyMemberExists;

    public Guid PolicyMemberId { get; }
    public string MemberId { get; }
}
