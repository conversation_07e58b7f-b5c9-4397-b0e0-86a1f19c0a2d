using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;

/// <summary>
/// Represents the validation result of a policy member
/// </summary>
public record PolicyMemberValidationResult(string Value) : ValueObject<string>(Value)
{
    public static PolicyMemberValidationResult Valid => new("VALID");
    public static PolicyMemberValidationResult Invalid => new("INVALID");
}
