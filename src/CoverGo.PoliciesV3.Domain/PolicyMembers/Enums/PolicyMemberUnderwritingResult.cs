using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;

/// <summary>
/// Represents the underwriting result of a policy member
/// </summary>
public record PolicyMemberUnderwritingResult(string Value) : ValueObject<string>(Value)
{
    public static PolicyMemberUnderwritingResult Pending => new("PENDING");
    public static PolicyMemberUnderwritingResult Approved => new("APPROVED");
    public static PolicyMemberUnderwritingResult Rejected => new("REJECTED");
    public static PolicyMemberUnderwritingResult ManualPending => new("MANUAL_PENDING");
    public static PolicyMemberUnderwritingResult ManualApproved => new("MANUAL_APPROVED");
    public static PolicyMemberUnderwritingResult ManualRejected => new("MANUAL_REJECTED");
}