using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;

/// <summary>
/// Represents the underwriting status for members
/// </summary>
public record MemberUnderwritingStatus(string Value) : ValueObject<string>(Value)
{
    public static MemberUnderwritingStatus Pending => new("PENDING");
    public static MemberUnderwritingStatus InProgress => new("IN_PROGRESS");
    public static MemberUnderwritingStatus Completed => new("COMPLETED");
    public static MemberUnderwritingStatus RequiresReview => new("REQUIRES_REVIEW");
    public static MemberUnderwritingStatus Approved => new("APPROVED");
    public static MemberUnderwritingStatus Rejected => new("REJECTED");
}
