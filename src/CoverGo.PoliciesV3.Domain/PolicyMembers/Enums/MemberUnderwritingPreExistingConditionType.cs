using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;

/// <summary>
/// Represents the pre-existing condition type for member underwriting
/// </summary>
public record MemberUnderwritingPreExistingConditionType(string Value) : ValueObject<string>(Value)
{
    public static MemberUnderwritingPreExistingConditionType Disclosed => new("DISCLOSED");
    public static MemberUnderwritingPreExistingConditionType NotDisclosed => new("NOT_DISCLOSED");
    public static MemberUnderwritingPreExistingConditionType Waived => new("WAIVED");
    public static MemberUnderwritingPreExistingConditionType Excluded => new("EXCLUDED");
}
