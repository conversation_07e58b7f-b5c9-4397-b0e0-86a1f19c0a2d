using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;

/// <summary>
/// Represents the exclusion type for member underwriting
/// </summary>
public record MemberUnderwritingExclusionType(string Value) : ValueObject<string>(Value)
{
    public static MemberUnderwritingExclusionType Medical => new("MEDICAL");
    public static MemberUnderwritingExclusionType Activity => new("ACTIVITY");
    public static MemberUnderwritingExclusionType Occupation => new("OCCUPATION");
    public static MemberUnderwritingExclusionType Geographic => new("GEOGRAPHIC");
    public static MemberUnderwritingExclusionType Other => new("OTHER");
}
