using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers;

public record PolicyMemberId(Guid Value) : ValueObject<Guid>(Value)
{
    public static PolicyMemberId Empty => new(Guid.Empty);
    public static PolicyMemberId New => new(Guid.CreateVersion7());

    /// <summary>
    /// Implicit conversion from Guid to PolicyMemberId
    /// </summary>
    public static implicit operator PolicyMemberId(Guid value) => new(value);

    /// <summary>
    /// Implicit conversion from string to PolicyMemberId
    /// </summary>
    public static implicit operator PolicyMemberId(string value) =>
        string.IsNullOrWhiteSpace(value) ? throw new ArgumentException("PolicyMemberId cannot be null or empty", nameof(value))
        : new(Guid.Parse(value));
    /// <summary>
    /// Implicit conversion from PolicyMemberId to Guid
    /// </summary>
    public static implicit operator Guid(PolicyMemberId policyMemberId) => policyMemberId.Value;
}

