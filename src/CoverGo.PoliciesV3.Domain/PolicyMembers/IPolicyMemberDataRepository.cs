using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers;

/// <summary>
/// Domain repository interface for basic policy member data access operations
/// Contains only data retrieval methods without business logic - follows DDD repository pattern
/// </summary>
public interface IPolicyMemberDataRepository
{
    /// <summary>
    /// Gets all policy members excluding a specific policy
    /// </summary>
    Task<List<PolicyMember>> GetByExcludingPolicyAsync(
        PolicyId excludePolicyId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all policy members for a specific policy
    /// </summary>
    Task<List<PolicyMember>> GetByPolicyIdAsync(
        PolicyId policyId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all policy members for multiple policies
    /// </summary>
    Task<List<PolicyMember>> GetByPolicyIdsAsync(
        List<PolicyId> policyIds,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all policy members for a specific member ID
    /// </summary>
    Task<List<PolicyMember>> GetByMemberIdAsync(
        string memberId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all policy members for multiple member IDs and a specific policy
    /// </summary>
    Task<List<PolicyMember>> GetByMemberIdsAndPolicyAsync(
        List<string> memberIds,
        PolicyId policyId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all policy members for multiple member IDs with batch optimization
    /// </summary>
    Task<List<PolicyMember>> GetByMemberIdsAsync(
        IReadOnlyList<string> memberIds,
        CancellationToken cancellationToken = default);
}
