namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

/// <summary>
/// Represents a non-empty collection of member upload fields with helper methods for dependent relationships.
/// </summary>
public sealed record MembersUploadFields
{
    private readonly IReadOnlyList<MemberUploadFields> _value;

    /// <summary>
    /// Private constructor for internal use, including empty collections
    /// </summary>
    private MembersUploadFields(IReadOnlyList<MemberUploadFields> value, bool allowEmpty)
    {
        if (!allowEmpty && value.Count == 0)
            throw new ArgumentException("Member upload fields collection cannot be empty", nameof(value));

        _value = [.. value];
    }

    /// <summary>
    /// Creates a new instance with validation to ensure non-empty collection
    /// </summary>
    /// <param name="value">The collection of member upload fields</param>
    /// <exception cref="ArgumentException">When value is empty</exception>
    public MembersUploadFields(IReadOnlyList<MemberUploadFields> value) : this(value, false)
    {
    }

    /// <summary>
    /// Creates an empty instance for cases where no member data exists.
    /// This is intentionally separate to make empty collections explicit.
    /// </summary>
    public static MembersUploadFields Empty() => new([], true);

    /// <summary>
    /// Creates an instance and validates it's not empty
    /// </summary>
    public static MembersUploadFields CreateNonEmpty(IReadOnlyList<MemberUploadFields> value) => value.Count == 0
            ? throw new ArgumentException("Cannot create non-empty MembersUploadFields with empty collection", nameof(value))
            : new MembersUploadFields(value);

    /// <summary>
    /// Gets the primary member (employee) for a dependent at the specified row index
    /// </summary>
    public MemberUploadFields? GetPrimaryMember(int dependentRowIndex)
    {
        // Search backwards from the dependent's position to find the first non-dependent member
        for (int index = dependentRowIndex - 2; index >= 0; index--)
        {
            if (!_value[index].IsDependent())
            {
                return _value[index];
            }
        }
        return null;
    }

    /// <summary>
    /// Gets the total count of members - guaranteed to be >= 0
    /// </summary>
    public int Count => _value.Count;

    /// <summary>
    /// Checks if the collection is empty
    /// </summary>
    public bool IsEmpty => _value.Count == 0;

    /// <summary>
    /// Checks if the collection has members
    /// </summary>
    public bool HasMembers => _value.Count > 0;

    /// <summary>
    /// Gets a member by index (0-based)
    /// </summary>
    public MemberUploadFields this[int index] => _value[index];

    /// <summary>
    /// Provides access to the underlying collection for LINQ operations
    /// </summary>
    public IReadOnlyList<MemberUploadFields> AsReadOnlyList() => [.. _value];
}
