using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

/// <summary>
/// Strongly-typed identifier for PolicyMemberUploadValidationError entities
/// </summary>
public record PolicyMemberUploadValidationErrorId(Guid Value) : ValueObject<Guid>(Value)
{
    public static PolicyMemberUploadValidationErrorId Empty => new(Guid.Empty);
    public static PolicyMemberUploadValidationErrorId New => new(Guid.CreateVersion7());

    /// <summary>
    /// Implicit conversion from Guid to PolicyMemberUploadValidationErrorId
    /// </summary>
    public static implicit operator PolicyMemberUploadValidationErrorId(Guid value) => new(value);

    /// <summary>
    /// Implicit conversion from string to PolicyMemberUploadValidationErrorId
    /// </summary>
    public static implicit operator PolicyMemberUploadValidationErrorId(string value) =>
        Guid.TryParse(value, out Guid result)
            ? new(result)
            : throw new ArgumentException($"Invalid GUID format: {value}", nameof(value));

    /// <summary>
    /// Implicit conversion from PolicyMemberUploadValidationErrorId to Guid
    /// </summary>
    public static implicit operator Guid(PolicyMemberUploadValidationErrorId policyMemberUploadValidationErrorId) => policyMemberUploadValidationErrorId.Value;
}