using CoverGo.BuildingBlocks.Domain.Core.Entities;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

/// <summary>
/// Represents a validation error for a specific row in an uploaded member file
/// </summary>
public class PolicyMemberUploadValidationError : AggregateRootBase<PolicyMemberUploadValidationErrorId>
{
    #region Constructors

    private PolicyMemberUploadValidationError(PolicyMemberUploadValidationErrorId id) : base(id)
    {
    }

    public PolicyMemberUploadValidationError() : this(PolicyMemberUploadValidationErrorId.New)
    {
    }

    #endregion

    #region Fields / Properties

    /// <summary>
    /// Foreign key to the PolicyMemberUpload
    /// </summary>
    public PolicyMemberUploadId PolicyMemberUploadId { get; private set; } = PolicyMemberUploadId.Empty;

    /// <summary>
    /// The row index in the uploaded file where the error occurred
    /// </summary>
    public int RowIndex { get; private set; }

    /// <summary>
    /// Error code for categorization
    /// </summary>
    public string Code { get; private set; } = string.Empty;

    /// <summary>
    /// Human-readable error message
    /// </summary>
    public string Message { get; private set; } = string.Empty;

    #endregion

    #region Navigation Properties

    /// <summary>
    /// Navigation property to the parent PolicyMemberUpload
    /// </summary>
    public virtual PolicyMemberUpload PolicyMemberUpload { get; set; } = null!;

    #endregion

    #region Factory Methods

    /// <summary>
    /// Creates a new validation error
    /// </summary>
    /// <param name="policyMemberUploadId">The upload this error belongs to</param>
    /// <param name="rowIndex">The row index where the error occurred</param>
    /// <param name="code">Error code</param>
    /// <param name="message">Error message</param>
    /// <returns>A new PolicyMemberUploadValidationError instance</returns>
    public static PolicyMemberUploadValidationError Create(
        PolicyMemberUploadId policyMemberUploadId,
        int rowIndex,
        string code,
        string message)
    {
        var error = new PolicyMemberUploadValidationError
        {
            PolicyMemberUploadId = policyMemberUploadId,
            RowIndex = rowIndex,
            Code = code,
            Message = message
        };

        return error;
    }

    #endregion
}