using CoverGo.PoliciesV3.Domain.Common.Extensions;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

/// <summary>
/// Represents the field data for a single member in a policy member upload
/// Wraps the raw dictionary data with helper methods for common operations
/// </summary>
public record MemberUploadFields(IReadOnlyDictionary<string, string?> Value)
{

    /// <summary>
    /// Determines if this member is a dependent by checking the "memberType" field.
    /// </summary>
    /// <returns>True if memberType is "dependent", false otherwise</returns>
    public bool IsDependent()
    {
        string? memberType = Value.TryGetValueOrDefault("memberType");
        return string.Equals("dependent", memberType, StringComparison.OrdinalIgnoreCase);
    }
}
