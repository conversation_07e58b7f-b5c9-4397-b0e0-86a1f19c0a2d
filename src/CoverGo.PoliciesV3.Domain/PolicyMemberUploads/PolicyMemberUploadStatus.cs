using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

/// <summary>
/// Represents the status of a policy member upload process
/// </summary>
public record PolicyMemberUploadStatus(string Value) : ValueObject<string>(Value)
{
    public static PolicyMemberUploadStatus REGISTERED => new("REGISTERED");
    public static PolicyMemberUploadStatus VALIDATING => new("VALIDATING");
    public static PolicyMemberUploadStatus VALIDATING_ERROR => new("VALIDATING_ERROR");
    public static PolicyMemberUploadStatus VALIDATED => new("VALIDATED");
    public static PolicyMemberUploadStatus IMPORTING => new("IMPORTING");
    public static PolicyMemberUploadStatus IMPORTING_ERROR => new("IMPORTING_ERROR");
    public static PolicyMemberUploadStatus IMPORTED => new("IMPORTED");
    public static PolicyMemberUploadStatus REVERTING => new("REVERTING");
    public static PolicyMemberUploadStatus REVERTED => new("REVERTED");
    public static PolicyMemberUploadStatus CANCELING => new("CANCELING");
    public static PolicyMemberUploadStatus CANCELED => new("CANCELED");
    public static PolicyMemberUploadStatus FAILED => new("FAILED");
}
