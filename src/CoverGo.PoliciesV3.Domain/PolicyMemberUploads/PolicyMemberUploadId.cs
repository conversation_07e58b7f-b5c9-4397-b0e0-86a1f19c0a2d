using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

/// <summary>
/// Strongly-typed identifier for PolicyMemberUpload entities
/// </summary>
public record PolicyMemberUploadId(Guid Value) : ValueObject<Guid>(Value)
{
    public static PolicyMemberUploadId Empty => new(Guid.Empty);
    public static PolicyMemberUploadId New => new(Guid.CreateVersion7());

    /// <summary>
    /// Implicit conversion from Guid to PolicyMemberUploadId
    /// </summary>
    public static implicit operator PolicyMemberUploadId(Guid value) => new(value);

    /// <summary>
    /// Implicit conversion from string to PolicyMemberUploadId
    /// </summary>
    public static implicit operator PolicyMemberUploadId(string value) =>
        string.IsNullOrWhiteSpace(value) ? throw new ArgumentException("PolicyMemberUploadId cannot be null or empty", nameof(value))
        : new(Guid.Parse(value));

    /// <summary>
    /// Implicit conversion from PolicyMemberUploadId to Guid
    /// </summary>
    public static implicit operator Guid(PolicyMemberUploadId policyMemberUploadId) => policyMemberUploadId.Value;
}
