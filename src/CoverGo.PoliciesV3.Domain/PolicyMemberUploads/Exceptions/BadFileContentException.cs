using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using HotChocolate;
using ErrorCodes = CoverGo.PoliciesV3.Domain.Common.Validation.ErrorCodes;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

/// <summary>
/// Exception thrown when file content is invalid or cannot be parsed.
/// Uses the unified ValidationError system for rich error information.
/// </summary>
public class BadFileContentException : DomainException
{
    /// <summary>
    /// Initializes a new instance with a single error code and message.
    /// </summary>
    /// <param name="errorCode">The error code</param>
    /// <param name="message">The error message</param>
    public BadFileContentException(string errorCode, string message) : base(message)
    {
        ErrorCode = errorCode;
        Errors = [new ValidationError(errorCode, "file", "Upload File")];
    }

    /// <summary>
    /// Initializes a new instance with a single error code, message, and inner exception.
    /// </summary>
    /// <param name="errorCode">The error code</param>
    /// <param name="message">The error message</param>
    /// <param name="innerException">The inner exception</param>
    public BadFileContentException(string errorCode, string message, Exception innerException) : base(message, innerException)
    {
        ErrorCode = errorCode;
        Errors = [new ValidationError(errorCode, "file", "Upload File")];
    }

    /// <summary>
    /// Initializes a new instance with multiple validation errors.
    /// This is the primary constructor for rich validation error information.
    /// </summary>
    /// <param name="errors">The validation errors with full context</param>
    public BadFileContentException(IEnumerable<ValidationError> errors)
        : base(string.Join("; ", errors.Select(e => e.Message)))
    {
        var errorList = errors.ToList();
        if (errorList.Count == 0)
            throw new ArgumentException("At least one validation error is required", nameof(errors));

        ErrorCode = errorList.FirstOrDefault()?.Code ?? ErrorCodes.InvalidRow;
        Errors = errorList;
    }

    /// <summary>
    /// Initializes a new instance with multiple validation errors and an inner exception.
    /// </summary>
    /// <param name="errors">The validation errors with full context</param>
    /// <param name="innerException">The inner exception</param>
    public BadFileContentException(IEnumerable<ValidationError> errors, Exception innerException)
        : base(string.Join("; ", errors.Select(e => e.Message)), innerException)
    {
        var errorList = errors.ToList();
        if (errorList.Count == 0)
            throw new ArgumentException("At least one validation error is required", nameof(errors));

        ErrorCode = errorList.FirstOrDefault()?.Code ?? ErrorCodes.InvalidRow;
        Errors = errorList;
    }

    /// <summary>
    /// Gets the domain exception code.
    /// </summary>
    public override string Code => ErrorCode;

    /// <summary>
    /// Gets the primary error code for this file content error.
    /// </summary>
    public string ErrorCode { get; }

    /// <summary>
    /// Gets the collection of validation errors with full context information.
    /// This includes PropertyPath, PropertyLabel, Context, and rich error details.
    /// </summary>
    [GraphQLIgnore]
    public IReadOnlyList<ValidationError> Errors { get; }
}


