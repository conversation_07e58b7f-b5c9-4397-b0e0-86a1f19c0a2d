using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

/// <summary>
/// Exception thrown when attempting to import a policy member upload that has no valid members
/// Business rule: Import operations require at least one valid member to proceed
/// </summary>
public class NoValidMembersForImportException : DomainException
{
    public NoValidMembersForImportException(string uploadId)
        : base(ErrorMessages.NoValidMembersForImport(uploadId))
    {
        UploadId = uploadId;
    }

    public NoValidMembersForImportException(string uploadId, Exception innerException)
        : base(ErrorMessages.NoValidMembersForImport(uploadId), innerException)
    {
        UploadId = uploadId;
    }

    public override string Code => ErrorCodes.NoValidMembersForImport;

    public string UploadId { get; }
}
