using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

/// <summary>
/// Exception thrown when a policy member upload cannot be found
/// </summary>
public class PolicyMemberUploadNotFoundException : DomainException
{
    public PolicyMemberUploadNotFoundException(string uploadId)
        : base(ErrorMessages.PolicyMemberUploadNotFound(uploadId))
    {
        UploadId = uploadId;
    }

    public PolicyMemberUploadNotFoundException(string uploadId, Exception innerException)
        : base(ErrorMessages.PolicyMemberUploadNotFound(uploadId), innerException)
    {
        UploadId = uploadId;
    }

    public override string Code => ErrorCodes.PolicyMemberUploadNotFound;

    public string UploadId { get; }
}