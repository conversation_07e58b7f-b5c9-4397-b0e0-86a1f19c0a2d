using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

/// <summary>
/// Exception thrown when a policy member upload has an invalid status for the requested operation
/// </summary>
public class InvalidPolicyMemberUploadStatusException(string uploadId, string currentStatus, IEnumerable<string> expectedStatuses) : DomainException(ErrorMessages.InvalidPolicyMemberUploadStatus(uploadId, currentStatus, expectedStatuses))
{
    public override string Code => ErrorCodes.InvalidPolicyMemberUploadStatus;

    public string UploadId { get; } = uploadId;
    public string CurrentStatus { get; } = currentStatus;
    public IReadOnlyList<string> ExpectedStatuses { get; } = [.. expectedStatuses];
}