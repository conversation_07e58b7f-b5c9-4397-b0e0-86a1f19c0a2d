using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

/// <summary>
/// Exception thrown when upload validation cannot be started due to concurrency or locking issues
/// </summary>
public class UploadValidationLockedException : DomainException
{
    public UploadValidationLockedException(string uploadId)
        : base(ErrorMessages.UploadValidationLocked(uploadId))
    {
        UploadId = uploadId;
    }

    public UploadValidationLockedException(string uploadId, Exception innerException)
        : base(ErrorMessages.UploadValidationLocked(uploadId), innerException)
    {
        UploadId = uploadId;
    }

    public override string Code => ErrorCodes.UploadValidationLocked;

    public string UploadId { get; }
}
