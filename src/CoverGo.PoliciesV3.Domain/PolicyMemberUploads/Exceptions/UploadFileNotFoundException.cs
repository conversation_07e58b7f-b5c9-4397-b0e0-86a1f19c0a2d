using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

/// <summary>
/// Exception thrown when an uploaded file cannot be found
/// </summary>
public class UploadFileNotFoundException(string policyId, string path) : DomainException(ErrorMessages.UploadFileNotFound(policyId, path))
{
    public override string Code => ErrorCodes.UploadFileNotFound;

    public string PolicyId { get; } = policyId;
    public string Path { get; } = path;
}
