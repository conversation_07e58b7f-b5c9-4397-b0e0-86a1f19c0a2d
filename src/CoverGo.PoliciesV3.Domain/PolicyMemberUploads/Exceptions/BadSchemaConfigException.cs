using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

/// <summary>
/// Exception thrown when there is an issue with schema configuration for policy member uploads
/// </summary>
public class BadSchemaConfigException : DomainException
{
    /// <summary>
    /// Gets the error code for bad schema configuration
    /// </summary>
    public override string Code => ErrorCodes.BadSchemaConfig;

    /// <summary>
    /// Initializes a new instance of BadSchemaConfigException with a custom message
    /// </summary>
    /// <param name="message">The error message</param>
    public BadSchemaConfigException(string message) : base(message)
    {
    }

    /// <summary>
    /// Initializes a new instance of BadSchemaConfigException with a custom message and inner exception
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="innerException">The inner exception</param>
    public BadSchemaConfigException(string message, Exception innerException) : base(message, innerException)
    {
    }
}
