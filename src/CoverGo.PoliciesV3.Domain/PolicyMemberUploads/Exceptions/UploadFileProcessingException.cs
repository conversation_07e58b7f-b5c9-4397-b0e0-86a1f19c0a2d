using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

/// <summary>
/// Exception thrown when an error occurs during upload file processing
/// </summary>
public class UploadFileProcessingException : DomainException
{
    public UploadFileProcessingException(string policyId, string filePath, string message)
        : base(ErrorMessages.UploadFileProcessingError(policyId, filePath, message))
    {
        PolicyId = policyId;
        FilePath = filePath;
    }

    public UploadFileProcessingException(string policyId, string filePath, string message, Exception innerException)
        : base(ErrorMessages.UploadFileProcessingError(policyId, filePath, message), innerException)
    {
        PolicyId = policyId;
        FilePath = filePath;
    }

    public override string Code => ErrorCodes.UploadFileProcessingError;

    public string PolicyId { get; }
    public string FilePath { get; }
}
