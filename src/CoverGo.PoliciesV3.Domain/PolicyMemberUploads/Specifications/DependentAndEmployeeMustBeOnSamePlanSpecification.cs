using System.Collections.Concurrent;
using CoverGo.PoliciesV3.Domain.Common.Extensions;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;

/// <summary>
/// Business Rules:
/// - Each dependent must have the same plan ID as their primary member (employee)
/// - Only applies to dependent members (memberType = "dependent")
/// - Primary member plan ID is cached for efficiency during upload validation
/// - Validation is performed across the entire upload file
/// - Feature flag controlled: "UseTheSamePlanForEmployeeAndDependents"
/// </summary>
public class DependentAndEmployeeMustBeOnSamePlanSpecification(
    ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification> logger)
    : ISpecification<DependentPlanValidationContext>
{
    public string BusinessRuleName => "Dependent And Employee Must Be On Same Plan";
    public string Description => "Validates that dependent members have the same plan ID as their primary member (employee) within the upload file when feature flags enable this validation";

    public async Task<Result> IsSatisfiedBy(DependentPlanValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            // Use Task.CompletedTask for in-memory operations to maintain async pattern consistency
            await Task.CompletedTask;

            if (!context.ShouldExecuteValidation())
            {
                logger.LogDebug("Dependent plan validation skipped due to feature flag configuration");
                return Result.Success();
            }

            ConcurrentDictionary<string, string?> primaryMemberPlanCache = BuildPrimaryMemberPlanCache(context.MembersFields);
            Dictionary<int, List<ValidationError>> memberErrors = ValidateDependentPlanMatching(context.MembersFields, primaryMemberPlanCache);

            if (memberErrors.Count > 0)
            {
                var allErrors = memberErrors.Values.Where(errors => errors != null).SelectMany(errors => errors).ToList();
                logger.LogWarning("Dependent plan validation failed. Found {ErrorCount} plan mismatch errors across {MemberCount} dependents",
                    allErrors.Count, memberErrors.Count);
                return Result.Failure(allErrors);
            }

            logger.LogDebug("Dependent plan validation passed - all dependents have matching plans with their employees");
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during dependent plan validation");
            throw;
        }
    }

    /// <summary>
    /// Validates the context and returns errors indexed by row number for upload-wide validation scenarios.
    /// </summary>
    /// <param name="context">The dependent plan validation context</param>
    /// <returns>Dictionary mapping row indices to validation errors</returns>
    public virtual Dictionary<int, List<ValidationError>> ValidateWithRowIndexedErrors(DependentPlanValidationContext context)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            if (!context.ShouldExecuteValidation())
            {
                logger.LogDebug("Dependent plan validation skipped due to feature flag configuration");
                return [];
            }

            ConcurrentDictionary<string, string?> primaryMemberPlanCache = BuildPrimaryMemberPlanCache(context.MembersFields);
            return ValidateDependentPlanMatching(context.MembersFields, primaryMemberPlanCache);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during dependent plan validation");
            throw;
        }
    }

    private static ConcurrentDictionary<string, string?> BuildPrimaryMemberPlanCache(MembersUploadFields membersFields)
    {
        var primaryMemberPlanCache = new ConcurrentDictionary<string, string?>();

        for (int i = 0; i < membersFields.Count; i++)
        {
            MemberUploadFields member = membersFields[i];
            if (!member.IsDependent())
                continue;

            int rowIndex = i + 1;
            MemberUploadFields? primaryMember = membersFields.GetPrimaryMember(rowIndex);
            if (primaryMember == null)
                continue;

            string primaryMemberKey = $"row_{rowIndex}_primary";

            if (!primaryMemberPlanCache.ContainsKey(primaryMemberKey))
            {
                string? primaryMemberPlanId = primaryMember.Value.TryGetValueOrDefault(PolicyMemberUploadWellKnowFields.PlanIdField);
                primaryMemberPlanCache[primaryMemberKey] = primaryMemberPlanId;
            }
        }

        return primaryMemberPlanCache;
    }

    private static Dictionary<int, List<ValidationError>> ValidateDependentPlanMatching(
        MembersUploadFields membersFields,
        ConcurrentDictionary<string, string?> primaryMemberPlanCache)
    {
        var validationErrors = new Dictionary<int, List<ValidationError>>();

        for (int i = 0; i < membersFields.Count; i++)
        {
            MemberUploadFields member = membersFields[i];
            if (!member.IsDependent())
                continue;

            int rowIndex = i + 1;
            MemberUploadFields? primaryMember = membersFields.GetPrimaryMember(rowIndex);
            if (primaryMember == null)
                continue;

            string primaryMemberKey = $"row_{rowIndex}_primary";

            if (!primaryMemberPlanCache.TryGetValue(primaryMemberKey, out string? primaryMemberPlanId))
                continue;

            string? dependentMemberPlanId = member.Value.TryGetValueOrDefault(PolicyMemberUploadWellKnowFields.PlanIdField);

            if (dependentMemberPlanId == primaryMemberPlanId)
                continue;

            ValidationError error = Errors.DependentPlanMismatch(PolicyMemberUploadWellKnowFields.PlanIdField, "Plan ID");

            if (!validationErrors.TryGetValue(i, out List<ValidationError>? errorList))
            {
                errorList = [];
                validationErrors[i] = errorList;
            }

            errorList.Add(error);
        }

        return validationErrors;
    }
}
