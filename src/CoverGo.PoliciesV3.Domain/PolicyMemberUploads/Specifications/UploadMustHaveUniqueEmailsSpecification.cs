using CoverGo.PoliciesV3.Domain.Common.Extensions;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;

/// <summary>
/// Business Rules:
/// - Email addresses must be unique within the upload file for new members (without memberIds)
/// - Empty or null email values are not validated for uniqueness
/// - Only new members (without memberIds) are validated for email uniqueness
/// - Existing members (with memberIds) are excluded from upload uniqueness validation
/// </summary>
public class UploadMustHaveUniqueEmailsSpecification(ILogger<UploadMustHaveUniqueEmailsSpecification> logger)
    : ISpecification<UploadUniquenessValidationContext>
{
    public string BusinessRuleName => "Upload Must Have Unique Emails";
    public string Description => "Validates that email addresses are unique within the upload file to prevent duplicate email entries for new members";

    public async Task<Result> IsSatisfiedBy(UploadUniquenessValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            await Task.CompletedTask;

            PolicyMemberFieldDefinition? emailFieldDefinition = context.Schema.GetField("email");
            if (emailFieldDefinition == null)
            {
                logger.LogDebug("Email field not found in schema - skipping email uniqueness validation");
                return Result.Success();
            }

            Dictionary<string, List<int>> emailIndex = BuildEmailIndex(context.MembersFields);
            Dictionary<int, List<ValidationError>> memberErrors = ValidateEmailUniqueness(emailIndex, emailFieldDefinition);

            if (memberErrors.Count > 0)
            {
                var allErrors = memberErrors.Values.Where(errors => errors != null).SelectMany(errors => errors).ToList();
                logger.LogWarning("Upload email uniqueness validation failed. Found {ErrorCount} duplicate email errors across {MemberCount} members",
                    allErrors.Count, memberErrors.Count);
                return Result.Failure(allErrors);
            }

            logger.LogDebug("Upload email uniqueness validation passed - no duplicate emails found");
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during upload email uniqueness validation");
            throw;
        }
    }

    private static Dictionary<string, List<int>> BuildEmailIndex(MembersUploadFields membersFields)
    {
        var emailIndex = new Dictionary<string, List<int>>(StringComparer.OrdinalIgnoreCase);

        for (int i = 0; i < membersFields.Count; i++)
        {
            MemberUploadFields memberFields = membersFields[i];

            // Only validate new members (without memberIds) for upload uniqueness
            string? memberId = memberFields.Value.TryGetValueOrDefault(PolicyMemberUploadWellKnowFields.MemberIdField);
            if (!string.IsNullOrWhiteSpace(memberId))
                continue;

            string? emailValue = memberFields.Value.TryGetValueOrDefault("email");
            if (string.IsNullOrWhiteSpace(emailValue))
                continue;

            if (!emailIndex.TryGetValue(emailValue, out List<int>? indexList))
            {
                indexList = [];
                emailIndex[emailValue] = indexList;
            }
            indexList.Add(i);
        }

        return emailIndex;
    }

    private static Dictionary<int, List<ValidationError>> ValidateEmailUniqueness(
        Dictionary<string, List<int>> emailIndex,
        PolicyMemberFieldDefinition emailFieldDefinition)
    {
        var validationErrors = new Dictionary<int, List<ValidationError>>();

        foreach (KeyValuePair<string, List<int>> kvp in emailIndex)
        {
            if (kvp.Value.Count <= 1)
                continue; // Skip non-duplicates

            List<int> duplicateIndexes = kvp.Value;

            foreach (int memberIndex in duplicateIndexes)
            {
                ValidationError error = Errors.UniqueViolation("email", emailFieldDefinition.GetFullLabel(), "upload");
                // FIXED: Use 0-based indexing to match service method behavior exactly

                if (!validationErrors.TryGetValue(memberIndex, out List<ValidationError>? errors))
                {
                    errors = [];
                    validationErrors[memberIndex] = errors;
                }
                errors.Add(error);
            }
        }

        return validationErrors;
    }

    /// <summary>
    /// Validates email uniqueness and returns row-indexed errors for integration with validation orchestration.
    /// </summary>
    public virtual Dictionary<int, List<ValidationError>> ValidateWithRowIndexedErrors(UploadUniquenessValidationContext context)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            PolicyMemberFieldDefinition? emailFieldDefinition = context.Schema.GetField("email");

            if (emailFieldDefinition == null)
            {
                logger.LogDebug("No email field found in schema - skipping upload email uniqueness validation");
                return [];
            }

            Dictionary<string, List<int>> emailIndex = BuildEmailIndex(context.MembersFields);
            return ValidateEmailUniqueness(emailIndex, emailFieldDefinition);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during upload email uniqueness validation with row-indexed errors");
            throw;
        }
    }
}
