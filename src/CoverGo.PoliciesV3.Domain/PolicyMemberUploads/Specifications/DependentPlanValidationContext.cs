using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.CustomFields;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;

public sealed record DependentPlanValidationContext : UploadValidationContext
{
    private DependentPlanValidationContext(
        MembersUploadFields membersFields,
        PolicyMemberFieldsSchema schema,
        bool shouldApplyDependentPlanValidation,
        bool shouldApplyOnlyForSmeProducts,
        bool isProductSmeType) : base(membersFields, schema)
    {
        ShouldApplyDependentPlanValidation = shouldApplyDependentPlanValidation;
        ShouldApplyOnlyForSmeProducts = shouldApplyOnlyForSmeProducts;
        IsProductSmeType = isProductSmeType;
    }

    /// <summary>
    /// Indicates whether dependent plan validation should be applied based on feature flags.
    /// </summary>
    public bool ShouldApplyDependentPlanValidation { get; }

    /// <summary>
    /// Indicates whether the validation should only apply to SME (Small and Medium Enterprise) products.
    /// </summary>
    public bool ShouldApplyOnlyForSmeProducts { get; }

    /// <summary>
    /// Indicates whether the current product is of SME type.
    /// </summary>
    public bool IsProductSmeType { get; }

    /// <summary>
    /// Creates a new instance of DependentPlanValidationContext with comprehensive validation.
    /// </summary>
    /// <param name="membersFields">The collection of member upload fields to validate</param>
    /// <param name="schema">The schema defining field definitions and validation rules</param>
    /// <param name="shouldApplyDependentPlanValidation">Whether dependent plan validation should be applied</param>
    /// <param name="shouldApplyOnlyForSmeProducts">Whether validation should only apply to SME products</param>
    /// <param name="isProductSmeType">Whether the current product is of SME type</param>
    /// <returns>A new DependentPlanValidationContext instance</returns>
    /// <exception cref="ArgumentNullException">Thrown when any required parameter is null</exception>
    public static DependentPlanValidationContext Create(
        MembersUploadFields membersFields,
        PolicyMemberFieldsSchema schema,
        bool shouldApplyDependentPlanValidation = false,
        bool shouldApplyOnlyForSmeProducts = false,
        bool isProductSmeType = false) =>
        new(
            membersFields,
            schema,
            shouldApplyDependentPlanValidation,
            shouldApplyOnlyForSmeProducts,
            isProductSmeType);

    /// <summary>
    /// Determines if dependent plan validation should be executed based on feature flags and business rules.
    /// </summary>
    /// <returns>True if validation should be executed, false otherwise</returns>
    public bool ShouldExecuteValidation() =>
        ShouldApplyDependentPlanValidation && (!ShouldApplyOnlyForSmeProducts || IsProductSmeType);
}