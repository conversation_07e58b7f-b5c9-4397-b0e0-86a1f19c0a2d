using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Products.Exceptions;

namespace CoverGo.PoliciesV3.Domain.Products;

/// <summary>
/// Product identifier components for service calls and validation
/// </summary>
public sealed record ProductIdDto
{
    /// <summary>
    /// Product plan identifier
    /// </summary>
    public required string Plan { get; init; }

    /// <summary>
    /// Product type identifier
    /// </summary>
    public required string Type { get; init; }

    /// <summary>
    /// Product version identifier
    /// </summary>
    public required string Version { get; init; }

    /// <summary>
    /// Available plan IDs for this product
    /// </summary>
    public IReadOnlySet<string>? Plans { get; init; }

    /// <summary>
    /// Creates a ProductIdDto with plans populated
    /// </summary>
    /// <param name="plan">Product plan identifier</param>
    /// <param name="type">Product type identifier</param>
    /// <param name="version">Product version identifier</param>
    /// <param name="plans">Available plan IDs for this product</param>
    /// <returns>ProductIdDto with plans populated</returns>
    public static ProductIdDto WithPlans(string plan, string type, string version, IReadOnlySet<string>? plans) =>
        new()
        {
            Plan = plan,
            Type = type,
            Version = version,
            Plans = plans
        };

    /// <summary>
    /// Converts this ProductIdDto to a domain ProductId object
    /// </summary>
    /// <returns>Domain ProductId object</returns>
    /// <exception cref="InvalidOperationException">Thrown when required properties are missing</exception>
    public ProductId ToDomainProductId()
    {
        if (string.IsNullOrWhiteSpace(Plan))
            throw new InvalidOperationException("ProductId Plan is required");

        if (string.IsNullOrWhiteSpace(Type))
            throw new InvalidOperationException("ProductId Type is required");

        if (string.IsNullOrWhiteSpace(Version))
            throw new InvalidOperationException("ProductId Version is required");

        return new ProductId(Plan, Type, Version);
    }

    /// <summary>
    /// Ensures that a member can have the specified plan ID
    /// </summary>
    /// <param name="planId">The plan ID to validate</param>
    /// <exception cref="InvalidPlanIdException">Thrown when the plan ID is not valid for this product</exception>
    public void EnsureMemberCanHavePlan(string planId)
    {
        if (MemberCanHavePlan(planId)) return;
        ProductId domainProductId = ToDomainProductId();
        List<string> availablePlans = Plans?.ToList() ?? [];
        throw new InvalidPlanIdException(domainProductId, planId, availablePlans);
    }

    /// <summary>
    /// Checks if a member can have the specified plan ID
    /// </summary>
    /// <param name="planId">The plan ID to check</param>
    /// <returns>True if the plan ID is valid for this product, false otherwise</returns>
    private bool MemberCanHavePlan(string planId) => !string.IsNullOrWhiteSpace(planId) && Plans is not null && Plans.Contains(planId);

    /// <summary>
    /// Returns the product ID in the format "type/plan/version"
    /// </summary>
    public override string ToString() => $"{Type}/{Plan}/{Version}";
}