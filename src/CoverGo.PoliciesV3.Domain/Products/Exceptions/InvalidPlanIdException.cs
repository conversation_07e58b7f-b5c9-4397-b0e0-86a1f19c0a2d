using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Domain.Products.Exceptions;

/// <summary>
/// Exception thrown when a plan ID is not valid for a specific product
/// </summary>
public class InvalidPlanIdException : DomainException
{
    public InvalidPlanIdException(ProductId productId, string planId, IReadOnlyList<string> availablePlans)
        : base(ErrorMessages.InvalidPlanId(planId, productId.ToString()))
    {
        ProductId = productId;
        PlanId = planId;
        AvailablePlans = availablePlans;
    }

    public InvalidPlanIdException(ProductId productId, string planId, IReadOnlyList<string> availablePlans, Exception innerException)
        : base(ErrorMessages.InvalidPlanId(planId, productId.ToString()), innerException)
    {
        ProductId = productId;
        PlanId = planId;
        AvailablePlans = availablePlans;
    }

    public override string Code => ErrorCodes.InvalidPlanId;

    public ProductId ProductId { get; }
    public string PlanId { get; }
    public IReadOnlyList<string> AvailablePlans { get; }
}
