using System.ComponentModel.DataAnnotations;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies.Events;
using PolicyMemberEntity = CoverGo.PoliciesV3.Domain.PolicyMembers.PolicyMember;
using PolicyMemberId = CoverGo.PoliciesV3.Domain.PolicyMembers.PolicyMemberId;

namespace CoverGo.PoliciesV3.Domain.Policies;

public class Policy(PolicyId id) : AggregateRootBase<PolicyId>(id)
{

    #region Constructors

    public Policy() : this(PolicyId.Empty)
    {
    }

    #endregion

    #region Fields / Properties

    // Required Properties
    public string? OriginalPolicyNumber { get; set; }

    // Policy Information
    public DateOnly? StartDate { get; set; }
    public DateOnly? EndDate { get; set; }
    public PolicyStatus Status { get; set; } = PolicyStatus.Draft;

    // Issue Information
    public bool IsIssued { get; set; }
    public DateTime? IssueDate { get; set; }

    // Premium & Renewal
    public bool IsPremiumOverridden { get; set; }
    public bool IsRenewal { get; set; }

    // Cancellation
    public string? CancellationReason { get; set; }

    // Relationships
    /// <summary>
    /// Optional foreign key to a ContractHolder
    /// </summary>
    public Guid? ContractHolderId { get; set; }

    /// <summary>
    /// Product information stored as JSON (Plan, Type, Version)
    /// </summary>
    public ProductId? ProductId { get; set; }

    // Collections
    public virtual ICollection<PolicyField> Fields { get; set; } = [];

    // Concurrency Control - maps to PostgreSQL xmin system column
    [Timestamp]
    public uint RowVersion { get; set; }

    #endregion

    #region Navigation Properties

    /// <summary>
    /// Navigation property to PolicyMembers
    /// </summary>
    public virtual ICollection<PolicyMemberEntity> PolicyMembers { get; set; } = [];

    /// <summary>
    /// Navigation property to Endorsements
    /// </summary>
    public virtual ICollection<Endorsement> Endorsements { get; set; } = [];

    #endregion

    #region Factory Methods

    public static Policy Create(
        string policyNumber,
        DateOnly startDate,
        DateOnly endDate,
        ProductId? productId = null,
        Guid? contractHolderId = null,
        ICollection<PolicyField>? fields = null)
    {
        var policy = new Policy(PolicyId.New)
        {
            OriginalPolicyNumber = policyNumber,
            Fields = fields ?? []
        };

        var @event = new PolicyCreatedEvent(policy.Id)
        {
            PolicyNumber = policyNumber,
            StartDate = startDate,
            EndDate = endDate,
            ProductId = productId,
            ContractHolderId = contractHolderId,
            Fields = fields ?? []
        };

        policy.AddDomainEvent(@event);

        // Apply event data that can change after creation
        policy.StartDate = @event.StartDate;
        policy.EndDate = @event.EndDate;
        policy.ProductId = @event.ProductId;
        policy.ContractHolderId = @event.ContractHolderId;

        return policy;
    }

    #endregion

    #region Member Management

    public PolicyMemberEntity AddPolicyMember(
        string memberId,
        DateOnly? startDate,
        DateOnly? endDate,
        string planId,
        PolicyMemberId? dependentOfPolicyMemberId = null,
        Guid? individualId = null,
        ICollection<PolicyField>? fields = null)
    {
        var policyMember = PolicyMemberEntity.Create(
            Id,
            memberId,
            startDate,
            endDate,
            planId,
            dependentOfPolicyMemberId,
            individualId,
            fields);

        PolicyMembers.Add(policyMember);
        return policyMember;
    }

    #endregion
}