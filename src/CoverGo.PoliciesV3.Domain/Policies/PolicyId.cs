using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.Policies;

public record PolicyId(Guid Value) : ValueObject<Guid>(Value)
{
    public static PolicyId Empty => new(Guid.Empty);
    public static PolicyId New => new(Guid.CreateVersion7());

    /// <summary>
    /// Implicit conversion from Guid to PolicyId
    /// </summary>
    public static implicit operator PolicyId(Guid value) => new(value);

    /// <summary>
    /// Implicit conversion from string to PolicyId
    /// </summary>
    public static implicit operator PolicyId(string value) =>
        string.IsNullOrWhiteSpace(value)
            ? throw new ArgumentException("PolicyId cannot be null or empty", nameof(value))
            : new(Guid.Parse(value));
    /// <summary>
    /// Implicit conversion from PolicyId to Guid
    /// </summary>
    public static implicit operator Guid(PolicyId policyId) => policyId.Value;
}

