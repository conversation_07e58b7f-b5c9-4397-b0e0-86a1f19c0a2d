using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Policies.Exceptions;

/// <summary>
/// Exception thrown when a policy's contract holder cannot be found
/// </summary>
public class PolicyContractHolderNotFoundException : DomainException
{
    public PolicyContractHolderNotFoundException(string policyId)
        : base(ErrorMessages.PolicyContractHolderNotFound(policyId))
    {
        PolicyId = policyId;
    }

    public PolicyContractHolderNotFoundException(string policyId, Exception innerException)
        : base(ErrorMessages.PolicyContractHolderNotFound(policyId), innerException)
    {
        PolicyId = policyId;
    }

    public override string Code => ErrorCodes.PolicyContractHolderNotFound;

    public string PolicyId { get; }
}
