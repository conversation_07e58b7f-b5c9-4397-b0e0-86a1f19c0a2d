using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Policies.Exceptions;

/// <summary>
/// Exception thrown when a policy cannot be found
/// </summary>
public class PolicyNotFoundException : DomainException
{
    public PolicyNotFoundException(string policyId) : base(ErrorMessages.PolicyNotFound(policyId))
    {
        PolicyId = policyId;
    }

    public PolicyNotFoundException(string policyId, Exception innerException)
        : base(ErrorMessages.PolicyNotFound(policyId), innerException)
    {
        PolicyId = policyId;
    }

    public override string Code => ErrorCodes.PolicyNotFound;

    public string PolicyId { get; }
}
