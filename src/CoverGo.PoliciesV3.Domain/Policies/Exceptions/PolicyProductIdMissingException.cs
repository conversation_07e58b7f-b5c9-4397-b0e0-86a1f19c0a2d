using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Policies.Exceptions;

/// <summary>
/// Exception thrown when a policy is missing its ProductId
/// Used during policy validation to indicate that ProductId is required but not present
/// </summary>
public class PolicyProductIdMissingException : DomainException
{
    public PolicyProductIdMissingException(string policyId)
        : base(ErrorMessages.PolicyProductIdMissing(policyId))
    {
        PolicyId = policyId;
    }

    public PolicyProductIdMissingException(string policyId, Exception innerException)
        : base(ErrorMessages.PolicyProductIdMissing(policyId), innerException)
    {
        PolicyId = policyId;
    }

    public override string Code => ErrorCodes.PolicyProductIdMissing;

    public string PolicyId { get; }
}
