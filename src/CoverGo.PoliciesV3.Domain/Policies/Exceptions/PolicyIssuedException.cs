using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Policies.Exceptions;

/// <summary>
/// Exception thrown when trying to perform operations on an issued policy that are not allowed
/// </summary>
public class PolicyIssuedException : DomainException
{
    public PolicyIssuedException(string policyId) : base(ErrorMessages.PolicyIssued(policyId))
    {
        PolicyId = policyId;
    }

    public PolicyIssuedException(string policyId, Exception innerException)
        : base(ErrorMessages.PolicyIssued(policyId), innerException)
    {
        PolicyId = policyId;
    }

    public override string Code => ErrorCodes.PolicyIssued;

    public string PolicyId { get; }
}
