using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Policies.Exceptions;

/// <summary>
/// Exception thrown when attempting to cancel a policy that is already cancelled
/// Business rule: A policy can only be cancelled once
/// </summary>
public class PolicyAlreadyCancelledException : DomainException
{
    public PolicyAlreadyCancelledException(string policyId)
        : base(ErrorMessages.PolicyAlreadyCancelled(policyId))
    {
        PolicyId = policyId;
    }

    public PolicyAlreadyCancelledException(string policyId, Exception innerException)
        : base(ErrorMessages.PolicyAlreadyCancelled(policyId), innerException)
    {
        PolicyId = policyId;
    }

    public override string Code => ErrorCodes.PolicyAlreadyCancelled;

    public string PolicyId { get; }
}
