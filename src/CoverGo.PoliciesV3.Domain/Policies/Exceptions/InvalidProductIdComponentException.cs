using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Policies.Exceptions;

/// <summary>
/// Exception thrown when a policy's ProductId has invalid or missing components
/// Business rule: ProductId must have valid Type, Plan, and Version components
/// </summary>
public class InvalidProductIdComponentException : DomainException
{
    public InvalidProductIdComponentException(string policyId, string componentName)
        : base(ErrorMessages.InvalidProductIdComponent(policyId, componentName))
    {
        PolicyId = policyId;
        ComponentName = componentName;
    }

    public InvalidProductIdComponentException(string policyId, string componentName, Exception innerException)
        : base(ErrorMessages.InvalidProductIdComponent(policyId, componentName), innerException)
    {
        PolicyId = policyId;
        ComponentName = componentName;
    }

    public override string Code => ErrorCodes.InvalidProductIdComponent;

    public string PolicyId { get; }
    public string ComponentName { get; }
}
