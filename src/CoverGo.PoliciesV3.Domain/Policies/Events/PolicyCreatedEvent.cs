using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.Policies.Events;

public class PolicyCreatedEvent(PolicyId aggregateId) : DomainEvent<PolicyId>(aggregateId)
{
    public required string PolicyNumber { get; init; }
    public DateOnly? StartDate { get; init; }
    public DateOnly? EndDate { get; init; }
    public ProductId? ProductId { get; init; }
    public Guid? ContractHolderId { get; init; }
    public ICollection<PolicyField> Fields { get; init; } = new HashSet<PolicyField>();
}