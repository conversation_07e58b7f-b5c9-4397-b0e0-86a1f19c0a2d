using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.Policies;

public record PolicyStatus(string Value) : ValueObject<string>(Value)
{
    public static PolicyStatus Draft => new("DRAFT");
    public static PolicyStatus Issued => new("ISSUED");
    public static PolicyStatus InForce => new("IN_FORCE");
    public static PolicyStatus Expired => new("EXPIRED");
    public static PolicyStatus Suspended => new("SUSPENDED");
    public static PolicyStatus Lapsed => new("LAPSED");
    public static PolicyStatus Renewed => new("RENEWED");
    public static PolicyStatus Cancelled => new("CANCELLED");
}

/// <summary>
/// Policy status constants for string-based comparisons and mappings
/// </summary>
public static class PolicyStatusValue
{
    public const string DRAFT = "DRAFT";
    public const string ISSUED = "ISSUED";
    public const string IN_FORCE = "IN_FORCE";
    public const string EXPIRED = "EXPIRED";
    public const string SUSPENDED = "SUSPENDED";
    public const string LAPSED = "LAPSED";
    public const string RENEWED = "RENEWED";
    public const string CANCELLED = "CANCELLED";
}