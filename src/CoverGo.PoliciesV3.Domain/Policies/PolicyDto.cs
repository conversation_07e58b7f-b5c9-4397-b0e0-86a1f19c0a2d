using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.Products;

namespace CoverGo.PoliciesV3.Domain.Policies;

public sealed record PolicyDto
{
    /// <summary>
    /// Unique policy identifier
    /// </summary>
    public required string Id { get; init; }

    /// <summary>
    /// Product identifier for plan validation and schema retrieval
    /// </summary>
    public ProductIdDto? ProductId { get; init; }

    /// <summary>
    /// Contract holder identifier for member validation and policy queries
    /// </summary>
    public string? ContractHolderId { get; init; }

    /// <summary>
    /// Policy start date for effective date validation
    /// </summary>
    public required DateOnly StartDate { get; init; }

    /// <summary>
    /// Policy end date for effective date validation
    /// </summary>
    public required DateOnly EndDate { get; init; }

    /// <summary>
    /// List of approved endorsement IDs for member state filtering
    /// </summary>
    public IReadOnlyList<string> ApprovedEndorsementIds { get; init; } = [];

    /// <summary>
    /// List of all endorsements with their status information for validation
    /// </summary>
    public IReadOnlyList<EndorsementDto> Endorsements { get; init; } = [];

    /// <summary>
    /// Indicates if the policy is issued (affects member upload validation)
    /// </summary>
    public bool IsIssued { get; init; }

    /// <summary>
    /// Indicates if the policy can accept member uploads
    /// </summary>
    public bool CanUploadMembers { get; init; } = true;

    /// <summary>
    /// Indicates if the policy is a V2 policy
    /// </summary>
    public bool IsV2 { get; init; }

    /// <summary>
    /// Indicates if the policy is a renewal policy
    /// </summary>
    public bool IsRenewal { get; init; }

    /// <summary>
    /// Policy status (e.g., DRAFT, ISSUED, IN_FORCE, etc.)
    /// </summary>
    public string? Status { get; init; }



    /// <summary>
    /// Validates policy can have members changed (non-endorsement scenarios)
    /// Equivalent to Policy.EnsureCanChangeMembers()
    /// </summary>
    /// <exception cref="PolicyIssuedException">Thrown when policy is issued</exception>
    private void ValidateCanChangeMembers()
    {
        if (IsIssued) throw new PolicyIssuedException(Id);
    }

    /// <summary>
    /// Validates policy can have members changed via endorsement
    /// </summary>
    /// <param name="endorsementId">The endorsement ID to validate</param>
    /// <param name="effectiveDate">Optional effective date for the movement</param>
    /// <exception cref="PolicyContractHolderNotFoundException">Thrown when contract holder is required but not found</exception>
    /// <exception cref="EndorsementNotFoundException">Thrown when endorsement is not found</exception>
    /// <exception cref="EndorsementCanNotBeChangedException">Thrown when endorsement cannot be changed</exception>
    /// <exception cref="EffectiveDateOutsidePolicyDatesException">Thrown when effective date is outside policy dates</exception>
    private void ValidateCanChangeMembersViaMovement(Guid endorsementId, DateOnly? effectiveDate = null)
    {
        // Check contract holder
        if (string.IsNullOrEmpty(ContractHolderId)) throw new PolicyContractHolderNotFoundException(Id);

        // Find endorsement
        EndorsementDto endorsement = Endorsements.FirstOrDefault(e =>
            Guid.TryParse(e.Id, out Guid id) && id == endorsementId) ?? throw new EndorsementNotFoundException(Id, endorsementId.ToString());

        // Check if endorsement can be changed
        if (!endorsement.CanBeChanged) throw new EndorsementCanNotBeChangedException(Id, endorsementId.ToString(), endorsement.Status);

        // Validate effective date is within policy dates
        if (effectiveDate != null && !EffectiveDateIsInPolicyDates(effectiveDate.Value)) throw new EffectiveDateOutsidePolicyDatesException(Id);
    }

    /// <summary>
    /// Validates that the policy has a valid ProductId for member upload operations
    /// This method focuses specifically on product data validation requirements
    /// </summary>
    /// <exception cref="PolicyProductIdMissingException">Thrown when ProductId is missing</exception>
    /// <exception cref="InvalidProductIdComponentException">Thrown when ProductId components are invalid</exception>
    public void ValidateProductIdForMemberUpload() => ValidateProductId();

    /// <summary>
    /// Validates that the policy state allows member uploads, handling both direct and endorsement-based scenarios
    /// This unified method consolidates validation logic and eliminates conditional logic in the application layer
    /// </summary>
    /// <param name="endorsementId">Optional endorsement ID for endorsement-based uploads</param>
    /// <param name="effectiveDate">Optional effective date for validation (only used with endorsement uploads)</param>
    /// <exception cref="PolicyIssuedException">Thrown when policy is issued (direct uploads only)</exception>
    /// <exception cref="PolicyContractHolderNotFoundException">Thrown when contract holder is required but not found (endorsement uploads only)</exception>
    /// <exception cref="EndorsementNotFoundException">Thrown when endorsement is not found (endorsement uploads only)</exception>
    /// <exception cref="EndorsementCanNotBeChangedException">Thrown when endorsement cannot be changed (endorsement uploads only)</exception>
    /// <exception cref="EffectiveDateOutsidePolicyDatesException">Thrown when effective date is outside policy dates (endorsement uploads only)</exception>
    public void ValidatePolicyStateForMemberUpload(Guid? endorsementId = null, DateOnly? effectiveDate = null)
    {
        if (endorsementId is null)
            // Direct member upload validation - check if policy is issued
            ValidateCanChangeMembers();
        else
            // Endorsement-based member uploads validation - check contract holder, endorsement, and effective date
            ValidateCanChangeMembersViaMovement(endorsementId.Value, effectiveDate);
    }

    /// <summary>
    /// Validates that the policy has a valid ProductId for member upload operations
    /// </summary>
    /// <exception cref="PolicyProductIdMissingException">Thrown when ProductId is missing</exception>
    /// <exception cref="InvalidProductIdComponentException">Thrown when ProductId components are invalid</exception>
    public void ValidateProductId()
    {
        if (ProductId == null)
            throw new PolicyProductIdMissingException(Id);

        if (string.IsNullOrWhiteSpace(ProductId.Type))
            throw new InvalidProductIdComponentException(Id, "Type");
        if (string.IsNullOrWhiteSpace(ProductId.Plan))
            throw new InvalidProductIdComponentException(Id, "Plan");
        if (string.IsNullOrWhiteSpace(ProductId.Version))
            throw new InvalidProductIdComponentException(Id, "Version");
    }



    /// <summary>
    /// Checks if the policy can accept member uploads
    /// Non-throwing version of the unified validation method
    /// </summary>
    /// <param name="endorsementId">Optional endorsement ID for upload</param>
    /// <param name="effectiveDate">Optional effective date for validation</param>
    /// <returns>True if policy can accept member uploads, false otherwise</returns>
    public bool CanAcceptMemberUpload(Guid? endorsementId = null, DateOnly? effectiveDate = null)
    {
        try
        {
            ValidateProductIdForMemberUpload();
            ValidatePolicyStateForMemberUpload(endorsementId, effectiveDate);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Gets valid endorsement IDs for filtering policy members.
    /// Includes approved endorsements, optional preview endorsement, and base policy (null).
    /// This method centralizes the business logic for determining which endorsements are valid
    /// for member state filtering operations.
    /// </summary>
    /// <param name="previewEndorsementId">Optional preview endorsement ID to include</param>
    /// <returns>List of valid endorsement IDs (including null for base policy)</returns>
    public List<string?> GetValidEndorsementIds(string? previewEndorsementId = null)
    {
        var endorsementIds = new List<string?>();

        // Add approved endorsement IDs
        endorsementIds.AddRange(ApprovedEndorsementIds);

        // Add preview endorsement if provided
        if (!string.IsNullOrEmpty(previewEndorsementId)) endorsementIds.Add(previewEndorsementId);

        // Always add null (for base policy members without endorsements)
        endorsementIds.Add(null);

        return endorsementIds;
    }

    /// <summary>
    /// Gets all approved endorsement IDs from multiple policies
    /// </summary>
    /// <param name="policies">List of policies to get endorsement IDs from</param>
    /// <returns>List of approved endorsement IDs (including null for base policy)</returns>
    public static List<string?> GetApprovedEndorsementIdsForPolicies(IReadOnlyList<PolicyDto> policies)
    {
        // Start with null for base policy
        var approvedEndorsementIds = new List<string?> { null };

        // Add all approved endorsement IDs from all policies
        approvedEndorsementIds.AddRange(
            policies
                .SelectMany(p => p.Endorsements)
                .Where(e => e.IsApproved)
                .Select(e => e.Id)
        );

        // Remove duplicates and return
        return approvedEndorsementIds.Distinct().ToList();
    }

    /// <summary>
    /// Filters policies for contract holder scope validation based on business rules.
    /// For V2 policies: excludes DRAFT status and renewal policies.
    /// For non-V2 policies: includes all policies.
    /// </summary>
    /// <param name="policies">List of policies to filter</param>
    /// <param name="isPolicyV2">Whether the requesting policy is V2</param>
    /// <returns>Filtered list of policies valid for contract holder scope validation</returns>
    public static List<PolicyDto> FilterForContractHolderScopeValidation(
        IReadOnlyList<PolicyDto> policies,
        bool isPolicyV2)
    {
        if (!isPolicyV2)
            // For non-V2 policies, include all policies
            return [.. policies];

        // For V2 policies, apply business rule filtering
        return [.. policies.Where(p =>
            p.IsV2 &&
            p.Status != "DRAFT" &&
            !p.IsRenewal)];
    }

    /// <summary>
    /// Checks if this policy can have members changed via endorsement movement
    /// </summary>
    /// <param name="endorsementId">The endorsement ID to check</param>
    /// <param name="effectiveDate">Optional effective date for validation</param>
    /// <returns>True if members can be changed via movement, false otherwise</returns>
    public bool CanChangeMembersViaMovement(Guid endorsementId, DateOnly? effectiveDate = null) => CanChangeMembersViaMovementInternal(endorsementId.ToString(), effectiveDate);

    /// <summary>
    /// Internal implementation for checking if members can be changed via endorsement movement
    /// </summary>
    /// <param name="endorsementId">The endorsement ID to check</param>
    /// <param name="effectiveDate">Optional effective date for validation</param>
    /// <returns>True if members can be changed via movement, false otherwise</returns>
    private bool CanChangeMembersViaMovementInternal(string endorsementId, DateOnly? effectiveDate = null)
    {
        try
        {
            // Check contract holder
            if (!HasContractHolder())
                return false;

            // Find endorsement
            if (!Guid.TryParse(endorsementId, out Guid endorsementGuid))
                return false;

            EndorsementDto? endorsement = Endorsements.FirstOrDefault(e =>
                Guid.TryParse(e.Id, out Guid id) && id == endorsementGuid);

            if (endorsement == null)
                return false;

            // Check if endorsement can be changed
            if (!endorsement.CanBeChanged)
                return false;

            // Check effective date if provided
            return !effectiveDate.HasValue || EffectiveDateIsInPolicyDates(effectiveDate.Value);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Checks if this policy has a contract holder
    /// </summary>
    /// <returns>True if policy has a contract holder, false otherwise</returns>
    private bool HasContractHolder()
        => !string.IsNullOrEmpty(ContractHolderId);

    /// <summary>
    /// Checks if the effective date falls within the policy's start and end dates
    /// </summary>
    /// <param name="effectiveDate">The effective date to check</param>
    /// <returns>True if effective date is within policy dates, false otherwise</returns>
    private bool EffectiveDateIsInPolicyDates(DateOnly effectiveDate)
        => StartDate <= effectiveDate && effectiveDate <= EndDate;
}