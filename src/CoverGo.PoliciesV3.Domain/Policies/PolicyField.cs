using System.Text.Json;

namespace CoverGo.PoliciesV3.Domain.Policies;

/// <summary>
/// Represents a dynamic field with key-value pair for policies and policy members
/// </summary>
public record PolicyField
{
    public required string Key { get; init; }
    public object? Value { get; init; }

    /// <summary>
    /// Checks if this field's value matches the expected value using business logic
    /// </summary>
    public bool MatchesValue(string expectedValue)
    {
        switch (Value)
        {
            case null:
                return false;
            // Fast path for string values (the most common case)
            case string stringValue:
                return string.Equals(stringValue, expectedValue, StringComparison.OrdinalIgnoreCase);
        }

        // Handle other types
        string fieldValueString = Value.ToString() ?? "";
        if (string.IsNullOrEmpty(fieldValueString))
            return false;

        // Direct comparison first
        if (string.Equals(fieldValueString, expectedValue, StringComparison.OrdinalIgnoreCase))
            return true;

        // Try JSON parsing only if direct comparison fails
        try
        {
            JsonElement fieldsElement = JsonSerializer.Deserialize<JsonElement>(fieldValueString);

            return fieldsElement.ValueKind switch
            {
                JsonValueKind.String => string.Equals(fieldsElement.GetString(), expectedValue,
                    StringComparison.OrdinalIgnoreCase),

                JsonValueKind.Object => MatchesComplexObject(fieldsElement, expectedValue),

                JsonValueKind.Number or JsonValueKind.True or JsonValueKind.False =>
                    string.Equals(fieldsElement.ToString(), expectedValue, StringComparison.OrdinalIgnoreCase),
                _ => false
            };
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Matches complex objects by ensuring ALL properties match
    /// </summary>
    private static bool MatchesComplexObject(JsonElement existingObject, string expectedValue)
    {
        try
        {
            JsonElement expectedObject = JsonSerializer.Deserialize<JsonElement>(expectedValue);

            if (expectedObject.ValueKind != JsonValueKind.Object)
            {
                // If expected is not an object, fall back to string comparison
                return false;
            }

            // Get all properties from the expected object
            var expectedProperties = expectedObject.EnumerateObject().ToList();
            var existingProperties = existingObject.EnumerateObject().ToList();

            // ALL expected properties must exist and match in the existing object
            foreach (JsonProperty expectedProp in expectedProperties)
            {
                JsonProperty existingProp = existingProperties.FirstOrDefault(p =>
                    string.Equals(p.Name, expectedProp.Name, StringComparison.OrdinalIgnoreCase));

                if (string.IsNullOrEmpty(existingProp.Name))
                {
                    // Property doesn't exist in existing object
                    return false;
                }

                // Compare property values
                if (!CompareJsonValues(existingProp.Value, expectedProp.Value))
                {
                    return false;
                }
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Compares two JSON values with proper type handling
    /// </summary>
    private static bool CompareJsonValues(JsonElement existing, JsonElement expected) => existing.ValueKind == expected.ValueKind && existing.ValueKind switch
    {
        JsonValueKind.String => string.Equals(existing.GetString(), expected.GetString(),
            StringComparison.OrdinalIgnoreCase),
        JsonValueKind.Number => existing.GetDecimal() == expected.GetDecimal(),
        JsonValueKind.True or JsonValueKind.False => existing.GetBoolean() == expected.GetBoolean(),
        JsonValueKind.Null => true,
        _ => string.Equals(existing.ToString(), expected.ToString(), StringComparison.OrdinalIgnoreCase)
    };
}