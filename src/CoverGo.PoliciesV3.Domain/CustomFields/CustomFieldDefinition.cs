using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

namespace CoverGo.PoliciesV3.Domain.CustomFields;

/// <summary>
/// Base class for custom field definitions
/// </summary>
public abstract record CustomFieldDefinition
{
    /// <summary>
    /// Key of how it's used by FE, BE and stored in DB.
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// UI friendly label.
    /// </summary>
    public required string Label { get; init; }

    /// <summary>
    /// Field type definition.
    /// </summary>
    public required IFieldType Type { get; init; }

    /// <summary>
    /// Whether the field is required or not.
    /// </summary>
    public required bool IsRequired { get; init; }

    /// <summary>
    /// Whether the field must be unique.
    /// </summary>
    public bool IsUnique { get; init; }

    /// <summary>
    /// Parent field definition for nested fields.
    /// </summary>
    public CustomFieldDefinition? Parent { get; init; }

    /// <summary>
    /// Whether this field is a benefit field.
    /// </summary>
    public bool? IsBenefitField { get; init; }

    /// <summary>
    /// Returns label with parent fields labels prepended.
    /// If label is null, empty, or whitespace, returns the field name instead.
    /// </summary>
    public string GetFullLabel()
    {
        string effectiveLabel = string.IsNullOrWhiteSpace(Label) ? Name : Label;
        return Parent is null ? effectiveLabel : $"{Parent.GetFullLabel()} - {effectiveLabel}";
    }

    public virtual bool Equals(CustomFieldDefinition? other) =>
        other != null && other.Name == Name && Parent == other.Parent;

    public override int GetHashCode() =>
        HashCode.Combine(Name.GetHashCode(), Parent?.GetHashCode() ?? 0);
}
