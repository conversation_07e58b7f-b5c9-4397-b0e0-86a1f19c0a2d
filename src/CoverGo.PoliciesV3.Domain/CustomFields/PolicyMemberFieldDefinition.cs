using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Validators;

namespace CoverGo.PoliciesV3.Domain.CustomFields;

/// <summary>
/// Represents a field definition for policy member custom fields
/// </summary>
public sealed record PolicyMemberFieldDefinition : CustomFieldDefinition
{
    /// <summary>
    /// Whether the field is required for dependents.
    /// </summary>
    public bool IsRequiredForDependent { get; init; }

    /// <summary>
    /// Whether this field is an identity field
    /// </summary>
    public bool IsIdentityField => IdentificationIdField.IsIdentificationId(Name);

    /// <summary>
    /// Conditional logic for this field
    /// </summary>
    public CustomFieldCondition? Condition { get; init; }

    /// <summary>
    /// Tries to parse and validate the field value with optional validations using the new validation system
    /// </summary>
    public Result<object?> TryParseField(object? fieldValue, List<ICustomFieldValidation>? validations = null, IDictionary<string, object?>? otherFields = null)
    {
        if (validations is null) return Type.TryParseField(fieldValue, this, otherFields);

        // Collect all validation errors first
        List<ValidationError> validationErrors = [.. validations
            .Select(validation => validation.ValidateField(fieldValue))
            .OfType<ValidationError>()];

        // Return failure if any validation errors exist
        return validationErrors.Count != 0 ? Result<object?>.Failure(validationErrors) : Type.TryParseField(fieldValue, this, otherFields);
    }
}
