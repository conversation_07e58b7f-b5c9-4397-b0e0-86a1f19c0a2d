using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Values;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Field type for numeric values with validation using the new validation system.
/// Supports options/choices and numeric parsing.
/// </summary>
public sealed record NumberFieldType : CustomFieldTypeBase, IOptionsFieldType<NumberOption>
{
    private HashSet<object>? _optionValuesCache;

    public List<NumberOption>? Options { get; init; }

    private HashSet<object> OptionValuesCache => _optionValuesCache ??=
        Options?.Select(x => NormalizeNumberValue(x.Value)).Where(v => v != null).Cast<object>().ToHashSet() ?? [];

    private static object? NormalizeNumberValue(object value) =>
        // Normalize all numeric values to the same types that string parsing would produce
        // This ensures consistent comparison between option values and parsed string values
        value switch
        {
            NumberValue nv => NormalizeNumberValue(nv.Value), // Handle NumberValue objects
            int i => (long)i,           // Convert int to long (same as string parsing)
            uint ui => (long)ui,        // Convert uint to long
            long l => l,                // Keep long as-is
            ulong ul => ul,             // Keep ulong as-is
            float f => (decimal)f,      // Convert float to decimal (same as string parsing)
            double d => (decimal)d,     // Convert double to decimal
            decimal dec => dec,         // Keep decimal as-is
            string s => NumberValue.CreateNullable(s)?.Value, // Parse string using same logic
            _ => NumberValue.CreateNullable(value)?.Value     // Fallback to NumberValue parsing
        };

    /// <summary>
    /// Validates the field value and returns a result with validation errors if any.
    /// </summary>
    public override Result<object?> ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        if (fieldValue == null)
            return Result<object?>.Success(null);

        NumberValue? numberValue = NumberValue.CreateNullable(fieldValue);
        if (numberValue is null)
            return Result<object?>.Failure(field.CreateInvalidNumberError());

        if (Options != null && !OptionValuesCache.Contains(NormalizeNumberValue(numberValue.Value.Value)!))
        {
            IEnumerable<string> availableOptions = Options.Select(o => o.Value.ToString()!);
            return Result<object?>.Failure(field.CreateInvalidOptionError(availableOptions));
        }

        return Result<object?>.Success(fieldValue);
    }

    /// <summary>
    /// Parses the field value to a NumberValue.
    /// </summary>
    protected override Result<object?> TryParseFieldSpecific(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        if (fieldValue == null)
            return Result<object?>.Success(null);

        NumberValue? numberValue = NumberValue.CreateNullable(fieldValue);
        return numberValue == null
            ? Result<object?>.Failure(field.CreateInvalidNumberError())
            : Result<object?>.Success(numberValue.Value.Value);
    }
}

/// <summary>
/// Represents a numeric option for number field types
/// </summary>
public record NumberOption
{
    public required object Value { get; init; }
    public string? Label { get; init; }
    public string? Name { get; init; }
    public string? Key { get; init; }
}
