using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Constants;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Extensions;
using CoverGo.PoliciesV3.Domain.Common.Extensions;
using CoverGo.PoliciesV3.Domain.Common.Providers;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

public sealed record DateFieldType : CustomFieldTypeBase
{
    /// <summary>
    /// Validates the field value and returns a result with validation errors if any.
    /// </summary>
    public override Result<object?> ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        switch (fieldValue)
        {
            case null or DateTime or DateOnly:
                break;
            case string strValue:
                {
                    if (!strValue.TryParseDateTime())
                        return Result<object?>.Failure(Errors.InvalidFormat(field.Name, field.GetFullLabel()));
                    break;
                }
            default:
                return Result<object?>.Failure(field.CreateInvalidFormatError());
        }

        return Result<object?>.Success(fieldValue);
    }

    /// <summary>
    /// Parses the field value to a DateOnly and validates age rules.
    /// </summary>
    protected override Result<object?> TryParseFieldSpecific(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        if (fieldValue == null)
            return Result<object?>.Success(null);

        DateOnly? parsedValue = fieldValue switch
        {
            string strValue => DateOnly.FromDateTime(strValue.ParseDateTime()),
            DateTime dateTime => DateOnly.FromDateTime(dateTime),
            DateOnly dateOnly => dateOnly,
            _ => null
        };

        if (!parsedValue.HasValue)
            return Result<object?>.Failure(field.CreateInvalidFormatError());

        // Validate age rules
        Result<object?> ageValidationResult = ValidateAgeRules(parsedValue.Value, field, otherFields);
        return ageValidationResult.IsFailure ? ageValidationResult : Result<object?>.Success(parsedValue);
    }

    /// <summary>
    /// Validates age-related rules for the date value.
    /// </summary>
    private Result<object?> ValidateAgeRules(DateOnly dateValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields)
    {
        foreach (ValidationInfo validationInfo in ValidationInfo)
        {
            ValidationError? error = validationInfo.Name switch
            {
                FieldValidationConstants.ValidationRules.MinEmployeeAge => ValidateMinEmployeeAge(dateValue, validationInfo, field, otherFields),
                FieldValidationConstants.ValidationRules.MaxEmployeeAge => ValidateMaxEmployeeAge(dateValue, validationInfo, field, otherFields),
                FieldValidationConstants.ValidationRules.MinChildDays => ValidateMinChildDays(dateValue, validationInfo, field, otherFields),
                FieldValidationConstants.ValidationRules.MinSpouseAge => ValidateMinSpouseAge(dateValue, validationInfo, field, otherFields),
                _ => null
            };

            if (error != null)
                return Result<object?>.Failure(error);
        }

        return Result<object?>.Success(dateValue);
    }

    /// <summary>
    /// Validates minimum employee age requirement.
    /// </summary>
    private static ValidationError? ValidateMinEmployeeAge(DateOnly dateValue, ValidationInfo validationInfo, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields)
    {
        object? memberTypeValue = otherFields?.TryGetValueOrDefault("memberType");
        if (memberTypeValue?.ToString() != FieldValidationConstants.MemberTypes.Employee)
            return null;

        if (!int.TryParse(validationInfo.Arguments.FirstOrDefault(), out int minAge))
            return null;

        int age = CalculateAge(dateValue);
        return age < minAge
            ? Errors.EmployeeMinAge(field.Name, minAge, age, field.GetFullLabel())
            : null;
    }

    /// <summary>
    /// Validates maximum employee age requirement.
    /// </summary>
    private static ValidationError? ValidateMaxEmployeeAge(DateOnly dateValue, ValidationInfo validationInfo, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields)
    {
        object? memberTypeValue = otherFields?.TryGetValueOrDefault("memberType");
        if (memberTypeValue?.ToString() != FieldValidationConstants.MemberTypes.Employee)
            return null;

        if (!int.TryParse(validationInfo.Arguments.FirstOrDefault(), out int maxAge))
            return null;

        int age = CalculateAge(dateValue);
        return age > maxAge
            ? Errors.EmployeeMaxAge(field.Name, maxAge, age, field.GetFullLabel())
            : null;
    }

    /// <summary>
    /// Validates minimum child days requirement.
    /// </summary>
    private static ValidationError? ValidateMinChildDays(DateOnly dateValue, ValidationInfo validationInfo, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields)
    {
        object? relationshipValue = otherFields?.TryGetValueOrDefault("relationshipToEmployee");
        if (relationshipValue?.ToString() != FieldValidationConstants.MemberTypes.Child)
            return null;

        if (!int.TryParse(validationInfo.Arguments.FirstOrDefault(), out int minDays))
            return null;

        int daysSinceBirth = (int)(DateTimeProvider.UtcNow - dateValue.ToDateTime(new TimeOnly())).TotalDays;
        return daysSinceBirth < minDays
            ? Errors.ChildMinDays(field.Name, minDays, daysSinceBirth, field.GetFullLabel())
            : null;
    }

    /// <summary>
    /// Validates minimum spouse age requirement.
    /// </summary>
    private static ValidationError? ValidateMinSpouseAge(DateOnly dateValue, ValidationInfo validationInfo, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields)
    {
        object? relationshipValue = otherFields?.TryGetValueOrDefault("relationshipToEmployee");
        if (relationshipValue?.ToString() != FieldValidationConstants.MemberTypes.Spouse)
            return null;

        if (!int.TryParse(validationInfo.Arguments.FirstOrDefault(), out int minAge))
            return null;

        int age = CalculateAge(dateValue);
        return age < minAge
            ? Errors.SpouseMinAge(field.Name, minAge, age, field.GetFullLabel())
            : null;
    }

    private static int CalculateAge(DateOnly birthDate) => (int)((DateTimeProvider.UtcNow - birthDate.ToDateTime(new TimeOnly())).TotalDays / FieldValidationConstants.AgeCalculation.AverageDaysPerYear);
}
