using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Interfaces;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Field type for formula/calculated fields with validation using the new validation system.
/// Formula fields should not accept direct input values.
/// </summary>
public record FormulaFieldType : CustomFieldTypeBase, IFormulaFieldType
{
    public IFormulaFunc? Func { get; init; }

    /// <summary>
    /// Validates that formula fields do not accept direct input values.
    /// </summary>
    public override Result<object?> ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null) =>
        fieldValue != null
            ? Result<object?>.Failure(field.CreateNotAllowedError())
            : Result<object?>.Success(null);
}
