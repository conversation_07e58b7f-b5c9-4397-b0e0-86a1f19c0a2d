using System.Collections.Concurrent;
using System.Text.RegularExpressions;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Constants;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Interfaces;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

public record StringOption
{
    /// <summary>
    /// Gets or sets the actual value of the option.
    /// </summary>
    public required string Value { get; init; }

    /// <summary>
    /// Gets or sets the human-readable label for the option.
    /// </summary>
    public string? Label { get; init; }

    /// <summary>
    /// Gets or sets the display name for the option (used in UI).
    /// </summary>
    public string? Name { get; init; }

    /// <summary>
    /// Gets or sets the unique key identifier for the option.
    /// </summary>
    public string? Key { get; init; }
}

/// <summary>
/// Field type for string values with validation using the new validation system.
/// Supports options/choices and various string validation rules.
/// </summary>
public sealed record StringFieldType : CustomFieldTypeBase, IOptionsFieldType<StringOption>
{
    private HashSet<string>? _optionValuesCache;

    // Cache compiled regex patterns at the type level to avoid recompilation
    private static readonly ConcurrentDictionary<string, Regex> RegexCache = new();

    // Pre-compiled number regex since it's used frequently
    private static readonly Regex NumberRegex = new(
        FieldValidationConstants.ValidationPatterns.NumberRegex,
        RegexOptions.Compiled,
        TimeSpan.FromMilliseconds(ValidationConstants.Timeouts.RegexTimeoutMilliseconds));

    public List<StringOption>? Options { get; init; }

    private HashSet<string> OptionValuesCache => _optionValuesCache ??=
        Options?.Select(x => x.Value).ToHashSet(StringComparer.OrdinalIgnoreCase) ?? new HashSet<string>(StringComparer.OrdinalIgnoreCase);

    /// <summary>
    /// Tries to parse the field value to a string. Converts non-string values to strings.
    /// </summary>
    public override Result<object?> TryParseField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        if (fieldValue == null)
            return Result<object?>.Success(null);

        // Convert non-string values to strings
        string stringValue = fieldValue.ToString() ?? string.Empty;

        return ValidateField(stringValue, field, otherFields);
    }

    /// <summary>
    /// Validates the field value and returns a result with validation errors if any.
    /// </summary>
    public override Result<object?> ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        if (fieldValue == null)
            return Result<object?>.Success(null);

        if (fieldValue is not string strValue)
            return Result<object?>.Failure(field.CreateInvalidTypeError());

        // Check options if they exist
        if (Options != null && !string.IsNullOrEmpty(strValue) && !OptionValuesCache.Contains(strValue))
        {
            IEnumerable<string> availableOptions = Options.Select(o => o.Value);
            return Result<object?>.Failure(field.CreateInvalidOptionError(availableOptions));
        }

        // Check string validation rules
        if (!string.IsNullOrEmpty(Validations))
        {
            Result<object?> validationResult = ValidateStringRules(strValue, field);
            if (validationResult.IsFailure)
                return validationResult;
        }

        return Result<object?>.Success(fieldValue);
    }

    /// <summary>
    /// Validates string-specific rules like number format and regex patterns.
    /// </summary>
    private Result<object?> ValidateStringRules(string strValue, PolicyMemberFieldDefinition field)
    {
        // Use cached validation info from base class instead of splitting each time
        foreach (ValidationInfo validationInfo in ValidationInfo)
        {
            string validationName = validationInfo.Name;

            if (validationName.Equals(FieldValidationConstants.ValidationRules.Number, StringComparison.OrdinalIgnoreCase))
            {
                if (!NumberRegex.IsMatch(strValue))
                {
                    return Result<object?>.Failure(field.CreateInvalidNumberError());
                }
            }
            else if (validationName.Equals("regex", StringComparison.OrdinalIgnoreCase) ||
                     validationName.StartsWith('/') && validationName.EndsWith('/'))
            {
                // Skip regex validation for whitespace-only values
                if (string.IsNullOrWhiteSpace(strValue))
                    continue;

                string pattern;
                string originalValidation;

                if (validationName.Equals("regex", StringComparison.OrdinalIgnoreCase))
                {
                    // Handle "regex:pattern" format - pattern is in arguments
                    pattern = validationInfo.Arguments.FirstOrDefault() ?? string.Empty;
                    originalValidation = $"regex:{pattern}";
                }
                else
                {
                    // Handle "/pattern/" format - pattern is in the validation name itself
                    pattern = validationName.Trim('/');
                    originalValidation = validationName;
                }

                if (string.IsNullOrEmpty(pattern))
                    continue;

                // Use cached regex to avoid recompilation
                Regex regex = GetOrCreateRegex(pattern);

                if (!regex.IsMatch(strValue))
                {
                    return Result<object?>.Failure(field.CreateInvalidStringError(originalValidation));
                }
            }
        }

        return Result<object?>.Success(strValue);
    }

    /// <summary>
    /// Gets or creates a compiled regex pattern from cache
    /// </summary>
    private static Regex GetOrCreateRegex(string pattern) => RegexCache.GetOrAdd(pattern, p =>
                                                                      new Regex(p, RegexOptions.Compiled,
                                                                          TimeSpan.FromMilliseconds(ValidationConstants.Timeouts.RegexTimeoutMilliseconds)));

}