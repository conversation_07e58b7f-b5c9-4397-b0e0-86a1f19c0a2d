using System.Globalization;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Values;

/// <summary>
/// Represents a numeric value that can be integer or decimal
/// </summary>
public readonly struct NumberValue
{
    private NumberValue(object value) => Value = value;


    public static NumberValue Create(object value) =>
        // Handle NumberValue input first to prevent double-wrapping
        value is NumberValue nv
            ? nv
            : value switch
            {
                int i => new NumberValue((object)i),
                uint ui => new NumberValue((object)ui),
                long l => new NumberValue((object)l),
                ulong ul => new NumberValue((object)ul),
                float f => new NumberValue((object)f),
                double d => new NumberValue((object)d),
                decimal dec => new NumberValue((object)dec),
                string s =>
                    long.TryParse(s, NumberStyles.Integer, CultureInfo.InvariantCulture, out long longValue) ? new NumberValue((object)longValue) :
                    decimal.TryParse(s, NumberStyles.Number, CultureInfo.InvariantCulture, out decimal decimalValue) ? new NumberValue((object)decimalValue) :
                    double.TryParse(s, NumberStyles.Float, CultureInfo.InvariantCulture, out double doubleValue) ? new NumberValue((object)doubleValue) :
                    ulong.TryParse(s, NumberStyles.Integer, CultureInfo.InvariantCulture, out ulong ulongValue) ? new NumberValue((object)ulongValue) :
                    throw new ArgumentException($"NumberFieldType - string '{s}' is not a supported number format"),
                _ => throw new ArgumentException($"NumberFieldType - '{value.GetType()}' is not supported"),
            };

    public static NumberValue? CreateNullable(object value) =>
        // Handle NumberValue input first to prevent double-wrapping
        value is NumberValue nv
            ? nv
            : (NumberValue?)(value switch
            {
                int i => new NumberValue((object)i),
                uint ui => new NumberValue((object)ui),
                long l => new NumberValue((object)l),
                ulong ul => new NumberValue((object)ul),
                float f => new NumberValue((object)f),
                double d => new NumberValue((object)d),
                decimal dec => new NumberValue((object)dec),
                string s =>
                    long.TryParse(s, NumberStyles.Integer, CultureInfo.InvariantCulture, out long longValue) ? new NumberValue((object)longValue) :
                    decimal.TryParse(s, NumberStyles.Number, CultureInfo.InvariantCulture, out decimal decimalValue) ? new NumberValue((object)decimalValue) :
                    double.TryParse(s, NumberStyles.Float, CultureInfo.InvariantCulture, out double doubleValue) ? new NumberValue((object)doubleValue) :
                    ulong.TryParse(s, NumberStyles.Integer, CultureInfo.InvariantCulture, out ulong ulongValue) ? new NumberValue((object)ulongValue) :
                    null,
                _ => null,
            });

    public object Value { get; }

    public override string ToString() => Value.ToString() ?? string.Empty;
}
