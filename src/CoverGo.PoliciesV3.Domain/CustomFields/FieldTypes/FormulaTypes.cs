using CoverGo.PoliciesV3.Domain.Common.Extensions;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Value or Reference for formula calculations
/// </summary>
public interface IFormulaParameter
{
}

/// <summary>
/// Formula function interface
/// </summary>
public interface IFormulaFunc
{
    object? Calculate(IReadOnlyDictionary<string, object?>? customFields);
}

/// <summary>
/// Formula value parameter
/// </summary>
public class FormulaValue : IFormulaParameter
{
    public required object? Value { get; init; }
}

/// <summary>
/// Formula data reference parameter
/// </summary>
public class FormulaData : IFormulaParameter
{
    public required string Path { get; init; }

    public object? Get(IReadOnlyDictionary<string, object?>? fields) =>
        fields?.TryGetValueOrDefault(Path);
}

/// <summary>
/// Join formula implementation
/// </summary>
public class JoinFormula : IFormulaFunc
{
    public required List<IFormulaParameter> Inputs { get; init; }
    public required IFormulaParameter Separator { get; init; }

    public object? Calculate(IReadOnlyDictionary<string, object?>? customFields)
    {
        string? separator = (Separator switch
        {
            FormulaData f => f.Get(customFields),
            FormulaValue f => f.Value,
            _ => null
        })?.ToString();

        IEnumerable<string?> inputs = Inputs
            .Select(it => it switch
            {
                FormulaData f => f.Get(customFields),
                FormulaValue f => f.Value,
                _ => null
            })
            .Select(it => it?.ToString())
            .Where(it => !string.IsNullOrEmpty(it));

        return string.Join(separator, inputs);
    }
}