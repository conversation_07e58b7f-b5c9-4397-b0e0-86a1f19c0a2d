namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Constants;

public static class FieldValidationConstants
{
    public static class ValidationRules
    {
        public const string Number = "number";
        public const string MinEmployeeAge = "minEmployeeAge";
        public const string MaxEmployeeAge = "maxEmployeeAge";
        public const string MinChildDays = "minChildDays";
        public const string MinSpouseAge = "minSpouseAge";
    }

    public static class MemberTypes
    {
        public const string Employee = "employee";
        public const string Child = "child";
        public const string Spouse = "spouse";
    }

    public static class ValidationPatterns
    {
        public const string NumberRegex = @"^-?\d+(\.\d+)?$";
    }

    public static class AgeCalculation
    {
        public const double AverageDaysPerYear = 365.242199;
    }

    public static class Delimiters
    {
        public const char ValidationSeparator = '|';
        public const char ArgumentSeparator = ':';
        public const char ArgumentValueSeparator = ',';
    }
}
