using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Interfaces;

/// <summary>
/// Interface for field types that support validation using the new validation system
/// </summary>
public interface IValidatableFieldType : IFieldType
{
    /// <summary>
    /// Validates the field value and returns a result with validation errors if any
    /// </summary>
    Result<object?> ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null);
}

/// <summary>
/// Interface for field types that contain nested fields
/// </summary>
public interface INestedFieldType : IFieldType
{
    /// <summary>
    /// Gets the nested field definitions
    /// </summary>
    IReadOnlyList<PolicyMemberFieldDefinition> InnerFieldDefinitions { get; }
}

/// <summary>
/// Interface for field types that support options/choices
/// </summary>
public interface IOptionsFieldType<T> : IFieldType
{
    /// <summary>
    /// Gets the available options for this field
    /// </summary>
    List<T>? Options { get; }
}

/// <summary>
/// Interface for field types that support formula calculations
/// </summary>
public interface IFormulaFieldType : IFieldType
{
    /// <summary>
    /// Gets the formula function for calculations
    /// </summary>
    IFormulaFunc? Func { get; }
}
