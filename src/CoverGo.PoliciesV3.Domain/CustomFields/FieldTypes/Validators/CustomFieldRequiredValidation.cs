using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Validators;

/// <summary>
/// Validates required field constraints using the new validation system.
/// </summary>
public sealed class CustomFieldRequiredValidation : ICustomFieldValidation
{
    public required PolicyMemberFieldDefinition Field { get; init; }

    public bool IsRequired { get; init; } = true;

    /// <summary>
    /// Validates the field value and returns a validation error if the field is required but missing.
    /// </summary>
    /// <param name="fieldValue">The field value to validate</param>
    /// <returns>A validation error if the field is required but missing, null otherwise</returns>
    public ValidationError? ValidateField(object? fieldValue) => !IsRequired
            ? null
            : fieldValue switch
            {
                null => Field.CreateRequiredError(),
                string strValue when string.IsNullOrEmpty(strValue) => Field.CreateRequiredError(),
                _ => null,
            };
}
