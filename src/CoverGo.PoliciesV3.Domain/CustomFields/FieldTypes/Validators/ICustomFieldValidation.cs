using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Validators;

/// <summary>
/// Interface for custom field validation using the new validation system.
/// </summary>
public interface ICustomFieldValidation
{
    /// <summary>
    /// Validates the field value and returns a validation error if validation fails.
    /// </summary>
    /// <param name="fieldValue">The field value to validate</param>
    /// <returns>A validation error if validation fails, null if validation passes</returns>
    public ValidationError? ValidateField(object? fieldValue);
}
