using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Extensions;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Extensions;
using System.Text.Json;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Field type for object/nested field structures with validation using the new validation system.
/// Supports JSON parsing and nested field validation.
/// </summary>
public record ObjectFieldType(
    IReadOnlyList<PolicyMemberFieldDefinition> InnerFieldDefinitions,
    bool CheckExtraFields = false) : CustomFieldTypeBase, INestedFieldType
{
    /// <summary>
    /// Parses and validates object field values.
    /// </summary>
    public override Result<object?> TryParseField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        object? parsedFieldValue = fieldValue;

        if (fieldValue is Dictionary<string, object> dictionaryValue)
        {
            fieldValue = JsonSerializer.Serialize(dictionaryValue);
        }
        else if (fieldValue is string stringValue && stringValue.TryParseAsJsonElement(out JsonElement jsonElement))
        {
            parsedFieldValue = jsonElement.ToObjectDictionary();
        }

        Result<object?> validationResult = ValidateField(fieldValue, field, otherFields);
        return validationResult.IsSuccess ? Result<object?>.Success(parsedFieldValue) : validationResult;
    }

    /// <summary>
    /// Validates the object field value and its nested fields.
    /// </summary>
    public override Result<object?> ValidateField(object? objectFieldValue, PolicyMemberFieldDefinition fieldDefinition, IDictionary<string, object?>? otherFields = null)
    {
        if (objectFieldValue == null)
            return Result<object?>.Success(null);

        IDictionary<string, object?>? innerFields = objectFieldValue switch
        {
            string json when json.TryParseAsJsonElement(out JsonElement jsonElement) => jsonElement.ToObjectDictionary(),
            IDictionary<string, object?> value => value,
            _ => null
        };

        if (innerFields == null)
            return Result<object?>.Failure(fieldDefinition.CreateInvalidFormatError());

        List<ValidationError> errors = [];

        IReadOnlyList<PolicyMemberFieldDefinition> filteredDefinitions = [.. InnerFieldDefinitions.Where(f =>
            f.Condition is null || f.Condition.Evaluate(innerFields) == true)];

        foreach (PolicyMemberFieldDefinition field in filteredDefinitions)
        {
            object? fieldValue = innerFields.TryGetValueOrDefault(field.Name);

            // Check required validation first
            if (field.IsRequired && (fieldValue == null || fieldValue is string str && string.IsNullOrWhiteSpace(str)))
            {
                errors.Add(field.CreateRequiredError());
                continue;
            }

            // Skip formula fields for now (they need special handling)
            if (field.Type is FormulaFieldType)
            {
                innerFields[field.Name] = fieldValue;
                continue;
            }

            // Validate the field using the new system
            Result<object?> fieldResult = field.Type.TryParseField(fieldValue, field, otherFields);
            if (fieldResult.IsFailure)
            {
                errors.AddRange(fieldResult.Errors);
                continue;
            }

            innerFields[field.Name] = fieldResult.Value;
        }

        if (!CheckExtraFields)
            return errors.Count > 0 ? Result<object?>.Failure(errors) : Result<object?>.Success(objectFieldValue);
        IEnumerable<ValidationError> extraFieldErrors = ValidateThatThereAreNoExtraFields(filteredDefinitions, innerFields);
        errors.AddRange(extraFieldErrors);

        return errors.Count > 0 ? Result<object?>.Failure(errors) : Result<object?>.Success(objectFieldValue);
    }

    /// <summary>
    /// Validates that there are no extra fields present when CheckExtraFields is enabled.
    /// </summary>
    private static IEnumerable<ValidationError> ValidateThatThereAreNoExtraFields(IReadOnlyList<PolicyMemberFieldDefinition>? fields, IDictionary<string, object?>? fieldValues)
    {
        if (fields == null)
            yield break;

        IEnumerable<string>? extraFields = fieldValues?.Keys.Except(fields.Select(it => it.Name));
        if (extraFields == null) yield break;
        IEnumerable<string> enumerable = extraFields as string[] ?? extraFields.ToArray();
        if (!enumerable.Any()) yield break;
        foreach (string field in enumerable)
        {
            yield return Errors.NotAllowed(field, field);
        }
    }
}


