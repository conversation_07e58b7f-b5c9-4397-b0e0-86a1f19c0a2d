namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Represents an address field type with nested field definitions
/// Address fields should validate that no extra fields are present
/// </summary>
public sealed record AddressFieldType : ObjectFieldType
{
    public AddressFieldType(List<PolicyMemberFieldDefinition> fieldDefinitions)
        : base(fieldDefinitions, true)
    {
    }
}
