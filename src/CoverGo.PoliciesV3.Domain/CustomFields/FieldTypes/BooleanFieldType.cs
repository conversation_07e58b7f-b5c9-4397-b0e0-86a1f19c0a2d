using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Field type for boolean values with validation using the new validation system.
/// Supports parsing from boolean values and string representations ("true", "false").
/// </summary>
public sealed record BooleanFieldType : CustomFieldTypeBase
{
    /// <summary>
    /// Validates the field value and returns a result with validation errors if any.
    /// </summary>
    public override Result<object?> ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null) => fieldValue switch
    {
        null or bool => Result<object?>.Success(fieldValue),
        string fieldValueStr when bool.TryParse(fieldValueStr, out _) => Result<object?>.Success(fieldValue),
        _ => Result<object?>.Failure(field.CreateInvalidTypeError())
    };

    /// <summary>
    /// Parses string values to boolean when possible.
    /// </summary>
    protected override Result<object?> TryParseFieldSpecific(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        if (fieldValue == null)
            return Result<object?>.Success(null);

        if (fieldValue is bool)
            return Result<object?>.Success(fieldValue);

        if (fieldValue is string fieldValueStr && bool.TryParse(fieldValueStr, out bool parsedBoolValue))
            return Result<object?>.Success(parsedBoolValue);

        return Result<object?>.Success(fieldValue);
    }
}
