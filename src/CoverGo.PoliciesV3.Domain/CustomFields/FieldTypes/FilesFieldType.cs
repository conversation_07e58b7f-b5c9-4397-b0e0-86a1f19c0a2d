using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Extensions;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Field type for file collections with validation using the new validation system.
/// Validates that the field value is a list of file objects.
/// </summary>
public sealed record FilesFieldType : CustomFieldTypeBase
{
    /// <summary>
    /// Validates the field value and returns a result with validation errors if any.
    /// </summary>
    public override Result<object?> ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        if (fieldValue is null)
            return Result<object?>.Success(null);

        List<Dictionary<string, object>>? files = fieldValue.ToObjectOrDefault<List<Dictionary<string, object>>>();
        if (files == null)
            return Result<object?>.Failure(field.CreateInvalidTypeError());

        var validationErrors = files
            .Where(dictionary => dictionary is null)
            .Select(_ => field.CreateInvalidFormatError())
            .ToList();

        return validationErrors.Count > 0
            ? Result<object?>.Failure(validationErrors)
            : Result<object?>.Success(fieldValue);
    }
}
