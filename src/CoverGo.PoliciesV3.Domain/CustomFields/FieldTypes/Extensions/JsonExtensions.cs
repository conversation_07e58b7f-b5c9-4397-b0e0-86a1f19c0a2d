using System.Diagnostics.CodeAnalysis;
using System.Text.Json;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Extensions;

/// <summary>
/// Extension methods for JSON parsing and manipulation using System.Text.Json
/// </summary>
public static class JsonExtensions
{
    /// <summary>
    /// Tries to parse a string as a JsonElement
    /// </summary>
    public static bool TryParseAsJsonElement(this string json, out JsonElement jsonElement)
    {
        try
        {
            jsonElement = JsonDocument.Parse(json).RootElement;
            return true;
        }
        catch (Exception)
        {
            jsonElement = default;
            return false;
        }
    }

    /// <summary>
    /// Converts JsonElement to Dictionary
    /// </summary>
    public static Dictionary<string, object?>? ToObjectDictionary(this JsonElement jsonElement)
    {
        if (jsonElement.ValueKind != JsonValueKind.Object)
            return null;

        var result = new Dictionary<string, object?>();
        foreach (JsonProperty property in jsonElement.EnumerateObject())
        {
            result[property.Name] = ConvertJsonElementToObject(property.Value);
        }
        return result;
    }

    /// <summary>
    /// Converts JsonElement to Dictionary (nullable overload)
    /// </summary>
    [return: NotNullIfNotNull(nameof(jsonElement))]
    public static Dictionary<string, object?>? ToObjectDictionary(this JsonElement? jsonElement) => !jsonElement.HasValue ? null : jsonElement.Value.ToObjectDictionary()!;

    /// <summary>
    /// Converts JsonElement array to object array
    /// </summary>
    public static object?[]? ToObjectArray(this JsonElement jsonElement) => jsonElement.ValueKind != JsonValueKind.Array
            ? null
            : [.. jsonElement.EnumerateArray().Select(ConvertJsonElementToObject)];

    /// <summary>
    /// Converts JsonElement array to object array (nullable overload)
    /// </summary>
    [return: NotNullIfNotNull(nameof(jsonElement))]
    public static object?[]? ToObjectArray(this JsonElement? jsonElement) => !jsonElement.HasValue ? null : jsonElement.Value.ToObjectArray()!;

    /// <summary>
    /// Converts a JsonElement to a .NET object
    /// </summary>
    private static object? ConvertJsonElementToObject(JsonElement element) => element.ValueKind switch
    {
        JsonValueKind.Object => element.ToObjectDictionary(),
        JsonValueKind.Array => element.ToObjectArray(),
        JsonValueKind.String => element.GetString(),
        JsonValueKind.Number => element.TryGetInt64(out long longValue) ? longValue : element.GetDouble(),
        JsonValueKind.True => true,
        JsonValueKind.False => false,
        JsonValueKind.Null => null,
        _ => element.ToString()
    };
}
