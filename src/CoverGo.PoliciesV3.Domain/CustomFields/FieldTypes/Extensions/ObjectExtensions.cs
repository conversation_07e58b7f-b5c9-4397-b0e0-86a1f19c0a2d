using System.Text.Json;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Extensions;

/// <summary>
/// Extension methods for object conversion and manipulation using System.Text.Json
/// </summary>
public static class ObjectExtensions
{
    /// <summary>
    /// Converts an object to the specified type or returns default value
    /// </summary>
    public static T? ToObjectOrDefault<T>(this object? obj)
    {
        if (obj == null)
            return default;

        try
        {
            string json = JsonSerializer.Serialize(obj);
            return JsonSerializer.Deserialize<T>(json);
        }
        catch (JsonException)
        {
            // Handle JSON serialization/deserialization errors
            return default;
        }
        catch (ArgumentException)
        {
            // Handle type conversion errors
            return default;
        }
        catch (InvalidOperationException)
        {
            // Handle invalid operation errors during conversion
            return default;
        }
    }

    /// <summary>
    /// Converts an object to the specified type or returns the specified default value
    /// </summary>
    public static T ToObjectOrDefault<T>(this object? obj, T defaultValue)
    {
        if (obj == null)
            return defaultValue;

        try
        {
            string json = JsonSerializer.Serialize(obj);
            return JsonSerializer.Deserialize<T>(json) ?? defaultValue;
        }
        catch (JsonException)
        {
            // Handle JSON serialization/deserialization errors
            return defaultValue;
        }
        catch (ArgumentException)
        {
            // Handle type conversion errors
            return defaultValue;
        }
        catch (InvalidOperationException)
        {
            // Handle invalid operation errors during conversion
            return defaultValue;
        }
    }
}
