using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Constants;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Base interface for field types
/// </summary>
public interface IFieldType
{
    /// <summary>
    /// Tries to parse and validate the field value using the new validation system
    /// </summary>
    Result<object?> TryParseField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null);
}

/// <summary>
/// Base class for custom field types with validation support using the new validation system
/// </summary>
public abstract record CustomFieldTypeBase : IFieldType
{
    private IEnumerable<ValidationInfo>? _cachedValidationInfo;

    /// <summary>
    /// Validation rules string (e.g., "required|unique")
    /// </summary>
    public string? Validations { get; set; }

    /// <summary>
    /// Parsed validation information (cached for performance)
    /// </summary>
    public IEnumerable<ValidationInfo> ValidationInfo => _cachedValidationInfo ??= ParseValidationInfo();

    /// <summary>
    /// Validates the field value and returns a result with validation errors if any
    /// </summary>
    public abstract Result<object?> ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null);

    /// <summary>
    /// Template method for parsing fields - handles common validation flow using the new validation system
    /// </summary>
    public virtual Result<object?> TryParseField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        // Step 1: Common validation
        Result<object?> validationResult = ValidateField(fieldValue, field, otherFields);
        if (validationResult.IsFailure)
        {
            return validationResult;
        }

        // Step 2: Type-specific parsing (override in derived classes)
        return TryParseFieldSpecific(fieldValue, field, otherFields);
    }

    /// <summary>
    /// Override this method in derived classes for type-specific parsing logic
    /// </summary>
    protected virtual Result<object?> TryParseFieldSpecific(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null) =>
        Result<object?>.Success(fieldValue); // Default: no additional parsing needed

    private IEnumerable<ValidationInfo> ParseValidationInfo() => string.IsNullOrEmpty(Validations)
            ? []
            : Validations.Split(FieldValidationConstants.Delimiters.ValidationSeparator)
            .Select(validation =>
            {
                // Special handling for regex patterns which may contain colons in the pattern itself
                if (validation.StartsWith("regex:", StringComparison.OrdinalIgnoreCase))
                {
                    return new ValidationInfo
                    {
                        Name = "regex",
                        Arguments = [validation[6..]] // Everything after "regex:"
                    };
                }

                string[] parts = validation.Split(FieldValidationConstants.Delimiters.ArgumentSeparator);
                return new ValidationInfo
                {
                    Name = parts[0].Trim(),
                    Arguments = parts.Length > 1
                        ? parts[1].Split(FieldValidationConstants.Delimiters.ArgumentValueSeparator).Select(arg => arg.Trim())
                        : []
                };
            });
}