using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Validators;
using CoverGo.PoliciesV3.Domain.Common.Extensions;

namespace CoverGo.PoliciesV3.Domain.CustomFields.Validation;

/// <summary>
/// Represents a one-of validation rule where at least one field from a group must be present
/// </summary>
public sealed class CustomFieldOneOfValidation
{
    private readonly IReadOnlyList<CustomFieldRequiredValidation> _validations = null!;

    public required IReadOnlyList<CustomFieldRequiredValidation> Validations
    {
        get => _validations;
        init
        {
            if (value.Count == 0)
            {
                throw new InvalidOperationException("OneOf validations can't have 0 validations inside");
            }
            _validations = value;
        }
    }

    /// <summary>
    /// Validates fields and returns a ValidationError if validation fails, null if successful
    /// </summary>
    public ValidationError? EnsureValidFields(IDictionary<string, object?>? fields)
    {
        List<ValidationError>? validationErrors = ValidateFields(fields);
        if (validationErrors == null || validationErrors.Count == 0)
            return null;

        // Create a one-of validation error with all the field errors as context
        return Errors.OneOfRequired(
            GetGroupPropertyPath(),
            $"One of: {string.Join(", ", Validations.Select(v => v.Field.GetFullLabel()))}",
            Validations.Select(v => v.Field.Name),
            validationErrors);
    }

    /// <summary>
    /// Validates fields and returns a FieldOneOfException with all validation errors if none pass
    /// </summary>
    private List<ValidationError>? ValidateFields(IDictionary<string, object?>? fields)
    {
        var errors = new List<ValidationError>();

        foreach (CustomFieldRequiredValidation validation in Validations)
        {
            object? fieldValue = fields?.TryGetValueOrDefault(validation.Field.Name);
            ValidationError? validationError = validation.ValidateField(fieldValue);

            if (validationError is null)
            {
                // At least one field is valid, so one-of validation passes
                return null;
            }

            errors.Add(validationError);
        }

        // All fields failed validation, return the errors
        return errors;
    }

    /// <summary>
    /// Gets a representative property path for the group of fields
    /// </summary>
    private string GetGroupPropertyPath() =>
        Validations.Count > 0
            ? $"OneOf({string.Join(",", Validations.Select(v => v.Field.Name))})"
            : "OneOf(empty)";
}
