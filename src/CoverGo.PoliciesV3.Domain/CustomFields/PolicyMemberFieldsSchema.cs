using System.Collections.Immutable;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Domain.CustomFields;

public sealed class PolicyMemberFieldsSchema
{
    #region Private Fields & State

    private readonly ImmutableDictionary<string, PolicyMemberFieldDefinition> _systemMemberFields;
    private readonly ImmutableDictionary<string, PolicyMemberFieldDefinition> _customMemberFields;
    private readonly ImmutableDictionary<string, PolicyMemberFieldDefinition> _productFields;
    private readonly ImmutableDictionary<string, PolicyMemberFieldDefinition> _censusFields;
    private readonly IReadOnlyList<PolicyMemberFieldDefinition> _nonCustomizedFields;

    #endregion

    #region Constructor

    public PolicyMemberFieldsSchema(
        IEnumerable<PolicyMemberFieldDefinition> memberFields,
        IEnumerable<PolicyMemberFieldDefinition>? productFields = null,
        IEnumerable<PolicyMemberFieldDefinition>? censusFields = null,
        IReadOnlyList<CustomFieldOneOfValidation>? oneOfValidations = null)
    {
        ArgumentNullException.ThrowIfNull(memberFields);

        var memberFieldsList = memberFields.ToList();

        _systemMemberFields = memberFieldsList
            .Where(field => PolicyMemberUploadWellKnowFields.All.Contains(field.Name))
            .ToImmutableDictionary(f => f.Name);

        _customMemberFields = memberFieldsList
            .Where(field => !PolicyMemberUploadWellKnowFields.All.Contains(field.Name))
            .ToImmutableDictionary(f => f.Name);

        _productFields = productFields?.ToImmutableDictionary(f => f.Name) ??
                        ImmutableDictionary<string, PolicyMemberFieldDefinition>.Empty;
        _censusFields = censusFields?.ToImmutableDictionary(f => f.Name) ??
                       ImmutableDictionary<string, PolicyMemberFieldDefinition>.Empty;

        OneOfValidations = oneOfValidations?.ToImmutableList() ?? [];

        Fields = ComputeAllFields();
        _nonCustomizedFields = ComputeNonCustomizedFields();
        MemberFields = ComputeMemberFields();
    }

    #endregion

    #region Public Properties - Field Collections

    /// <summary>
    /// System member fields (well-known fields)
    /// </summary>
    public IReadOnlyList<PolicyMemberFieldDefinition> MemberSystemFields => [.. _systemMemberFields.Values];

    /// <summary>
    /// Custom member fields (non-well-known fields)
    /// </summary>
    public IReadOnlyList<PolicyMemberFieldDefinition> MemberCustomFields => [.. _customMemberFields.Values];

    /// <summary>
    /// All member fields (custom UNION system) - Cached
    /// </summary>
    public IReadOnlyList<PolicyMemberFieldDefinition> MemberFields { get; }

    /// <summary>
    /// Product-specific fields
    /// </summary>
    public IReadOnlyList<PolicyMemberFieldDefinition> ProductFields => [.. _productFields.Values];

    /// <summary>
    /// Census-specific fields
    /// </summary>
    public IReadOnlyList<PolicyMemberFieldDefinition> CensusFields => [.. _censusFields.Values];

    /// <summary>
    /// Gets all fields with priority: product > system > custom > census
    /// </summary>
    public IReadOnlyList<PolicyMemberFieldDefinition> Fields { get; }

    /// <summary>
    /// One of the validation rules
    /// </summary>
    public IReadOnlyList<CustomFieldOneOfValidation> OneOfValidations { get; }

    #endregion

    #region Public Methods - Field Operations

    /// <summary>
    /// Gets a field by name with priority: product > system > custom > census
    /// Simplified without caching for better maintainability
    /// </summary>
    public PolicyMemberFieldDefinition? GetField(string fieldName)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(fieldName);

        if (_productFields.TryGetValue(fieldName, out PolicyMemberFieldDefinition? field) ||
            _systemMemberFields.TryGetValue(fieldName, out field) ||
            _customMemberFields.TryGetValue(fieldName, out field) ||
            _censusFields.TryGetValue(fieldName, out field)) return field;

        return null;
    }

    #endregion

    #region Public Methods - Validation

    public List<ValidationError> ValidateFields(
        IDictionary<string, object?>? fieldValues,
        string? memberId = null,
        IDictionary<string, object?>? otherFields = null)
    {
        var errors = new List<ValidationError>();
        var properties = new List<PolicyMemberFieldDefinition>();

        if (string.IsNullOrEmpty(memberId))
        {
            properties.AddRange(MemberCustomFields.Where(ConfigurePredicateFavorProductFields));
            errors.AddRange(ValidateOneOfValidations(OneOfValidations, fieldValues));
        }

        properties.AddRange(GetNonCustomizedFields());

        bool isDependent = IsDependentMember(otherFields);
        properties = [.. properties.Select(it =>
            isDependent && it.IsRequiredForDependent
                ? (it with { IsRequired = true })
                : it)];

        var innerType = new ObjectFieldType(properties, CheckExtraFields: false);
        Result<object?> validationResult = innerType.ValidateField(
            fieldValues,
            new PolicyMemberFieldDefinition
            {
                IsRequired = false,
                IsRequiredForDependent = false,
                IsUnique = false,
                Label = "",
                Name = "",
                Type = innerType,
            },
            otherFields);

        if (validationResult.IsFailure) errors.AddRange(validationResult.Errors);

        return errors;

        bool ConfigurePredicateFavorProductFields(PolicyMemberFieldDefinition memberField) => ProductFields.All(productField => productField.Name != memberField.Name);
    }

    #endregion

    #region Private Methods - Field Collection & Computation

    /// <summary>
    /// Gets field sources in legacy priority order: product > system > custom > census
    /// </summary>
    private List<ImmutableDictionary<string, PolicyMemberFieldDefinition>?> GetFieldSourcesInPriority() =>
        [_productFields, _systemMemberFields, _customMemberFields, _censusFields];

    /// <summary>
    /// Gets non-customized field sources in priority order: product > system > census
    /// </summary>
    private List<ImmutableDictionary<string, PolicyMemberFieldDefinition>?> GetNonCustomizedFieldSourcesInPriority() =>
        [_productFields, _systemMemberFields, _censusFields];

    private ImmutableList<PolicyMemberFieldDefinition> ComputeAllFields() =>
        GetFields(GetFieldSourcesInPriority());

    private ImmutableList<PolicyMemberFieldDefinition> ComputeNonCustomizedFields() =>
        GetFields(GetNonCustomizedFieldSourcesInPriority());

    private ImmutableList<PolicyMemberFieldDefinition> ComputeMemberFields() =>
        [.. _customMemberFields.Values.Union(_systemMemberFields.Values)];

    private IReadOnlyList<PolicyMemberFieldDefinition> GetNonCustomizedFields() =>
        _nonCustomizedFields;

    private static ImmutableList<PolicyMemberFieldDefinition> GetFields(
        List<ImmutableDictionary<string, PolicyMemberFieldDefinition>?> sources) =>
        [.. sources
            .Aggregate(
                Enumerable.Empty<KeyValuePair<string, PolicyMemberFieldDefinition>>(),
                (result, nextItem) =>
                    nextItem != null
                        ? result.UnionBy(nextItem, kvp => kvp.Key)
                        : result)
            .Select(kvp => kvp.Value)];

    #endregion

    #region Private Methods - Validation Helpers

    /// <summary>
    /// Validates OneOf rules for field combinations
    /// </summary>
    private static List<ValidationError> ValidateOneOfValidations(
        IReadOnlyList<CustomFieldOneOfValidation> oneOfValidations,
        IDictionary<string, object?>? fieldValues) =>
        [.. oneOfValidations.Select(validation => validation.EnsureValidFields(fieldValues)).OfType<ValidationError>()];

    /// <summary>
    /// Determines if a member is a dependent based on field values
    /// </summary>
    private static bool IsDependentMember(IDictionary<string, object?>? otherFields) =>
        otherFields?.Any(x =>
            x is { Key: "relationshipToEmployee", Value: not null } ||
            x.Key == "memberType" && x.Value?.ToString() == "dependent") ?? false;

    #endregion
}
