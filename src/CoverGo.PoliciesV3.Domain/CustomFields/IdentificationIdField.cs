namespace CoverGo.PoliciesV3.Domain.CustomFields;

public static class IdentificationIdField
{
    private static readonly HashSet<string> IdentityFieldNames = new(StringComparer.OrdinalIgnoreCase)
    {
        "staffNo",
        "passportNo",
        "hkid",
    };

    public static bool IsIdentificationId(string fieldName) => !string.IsNullOrWhiteSpace(fieldName) && IdentityFieldNames.Contains(fieldName);
}
