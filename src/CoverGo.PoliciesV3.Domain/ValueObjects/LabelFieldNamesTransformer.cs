using System.Text.Json;
using CoverGo.PoliciesV3.Domain.Common.Extensions;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Values;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

public static class LabelFieldNamesTransformer
{
    /// <summary>
    /// Transforms raw member data from file labels to internal field names using the provided schema
    /// </summary>
    /// <param name="membersData">Raw member data with file column headers as keys</param>
    /// <param name="schema">Schema containing field definitions with labels and names</param>
    /// <returns>Transformed member data with internal field names</returns>
    public static MembersUploadFields TransformLabelsToFieldNames(
        IEnumerable<IReadOnlyDictionary<string, string?>> membersData,
        PolicyMemberFieldsSchema schema)
    {
        var result = new List<MemberUploadFields>();
        foreach (IReadOnlyDictionary<string, string?> memberData in membersData)
        {
            var memberFields = new Dictionary<string, string?>();

            TransformLabelsToFieldValues(schema, memberData, memberFields);
            TransformObjectFieldNames(schema, memberData, memberFields);

            result.Add(new(memberFields));
        }
        return new(result);
    }

    /// <summary>
    /// Transforms object field names for complex fields (e.g., Address fields) by parsing headers
    /// like "ObjectLabel - InnerLabel" and serializing the collected inner fields into a JSON string.
    /// </summary>
    private static void TransformObjectFieldNames(
        PolicyMemberFieldsSchema schema,
        IReadOnlyDictionary<string, string?> csvHeaderValues,
        Dictionary<string, string?> memberFields)
    {
        const string HeaderSeparator = " - ";
        const int ExpectedHeaderParts = 2;

        if (!schema.Fields.Any()) return;

        // Create a map from the label of an ObjectFieldType to its internal system name.
        // e.g., {"Mailing Address": "mailingAddress"}
        var objectFieldLabelToNameMap = schema.Fields
            .Where(field => field.Type is ObjectFieldType) // Filter for fields that are ObjectFieldType
            .DistinctBy(field => field.Label) // Ensure uniqueness by label
            .ToDictionary(field => field.Label, field => field.Name);

        // If there are no object fields defined in the schema, there's nothing to transform.
        if (objectFieldLabelToNameMap.Count == 0) return;

        // Create a map from the label of an inner field (within any ObjectFieldType) to its internal system name.
        // e.g., {"Street Line 1": "street1", "Postal Code": "postalCode"}
        var innerFieldLabelToNameMap = schema.Fields
            .Select(field => field.Type)
            .OfType<ObjectFieldType>() // Consider only ObjectFieldTypes
            .SelectMany(objectType => objectType.InnerFieldDefinitions) // Flatten all inner field definitions
            .DistinctBy(innerField => innerField.Label) // Ensure uniqueness by label
            .ToDictionary(innerField => innerField.Label, innerField => innerField.Name);

        // This dictionary will collect the data for each object field before serialization.
        // Key: Internal name of the main object field (e.g., "mailingAddress")
        // Value: A dictionary of inner field internal names to their values (e.g., {"street1": "123 Main St", "postalCode": "90210"})
        var parsedObjectDataCollector = objectFieldLabelToNameMap
            .ToDictionary(kvp => kvp.Value, _ => new Dictionary<string, string?>());

        foreach ((string rawCsvHeader, string? csvValue) in csvHeaderValues)
        {
            // Expect headers for object inner fields to follow a pattern like "Mailing Address - Street Line 1"
            var headerParts = rawCsvHeader.Split([HeaderSeparator], StringSplitOptions.None)
                                          .Select(part => part.Trim())
                                          .ToList();

            if (headerParts.Count != ExpectedHeaderParts) continue; // Skip if the pattern isn't matched

            string objectFieldLabel = headerParts[0];    // e.g., "Mailing Address"
            string innerFieldLabel = headerParts[1];     // e.g., "Street Line 1"

            // Try to find the internal names for both the main object field and the inner field
            if (objectFieldLabelToNameMap.TryGetValue(objectFieldLabel, out string? objectInternalName) &&
                innerFieldLabelToNameMap.TryGetValue(innerFieldLabel, out string? innerInternalName))
            {
                // If both names are found, store the value in our collector
                // Note: This assumes that the combination of objectInternalName and innerInternalName will be unique
                // for each entry from csvHeaderValues after parsing. If duplicates are possible and need merging,
                // this logic would need adjustment (e.g., using TryAdd or checking ContainsKey).
                parsedObjectDataCollector[objectInternalName][innerInternalName] = csvValue;
            }
        }

        // Serialize the collected data for each object field and add it to the final memberFields.
        foreach ((string objectInternalName, Dictionary<string, string?> innerFieldsData) in parsedObjectDataCollector)
        {
            if (innerFieldsData.Count == 0) continue; // Skip if no inner fields were actually collected for this object

            // If all collected values for an object's inner fields are null or empty,
            // treat the entire object field as null. Otherwise, serialize to JSON.
            string? serializedJsonValue = innerFieldsData.All(kvp => string.IsNullOrEmpty(kvp.Value))
                ? null
                : JsonSerializer.Serialize(innerFieldsData);

            memberFields.Add(objectInternalName, serializedJsonValue);
        }
    }

    /// <summary>
    /// Transforms simple field labels to field values with option mapping
    /// </summary>
    private static void TransformLabelsToFieldValues(PolicyMemberFieldsSchema schema, IReadOnlyDictionary<string, string?> memberData, Dictionary<string, string?> memberFields)
    {
        foreach (PolicyMemberFieldDefinition field in schema.Fields)
        {
            string? value = memberData.TryGetValueOrDefault(field.Label);
            if (string.IsNullOrEmpty(value))
                continue;

            // Determine field value based on field type
            string? fieldValue = value;

            if (field.Type is IOptionsFieldType<StringOption> stringOptionsType && stringOptionsType.Options != null)
            {
                // For string options, try to find a matching option by value
                fieldValue = stringOptionsType.Options!.FirstOrDefault(x =>
                    string.Equals(x.Value, value, StringComparison.OrdinalIgnoreCase))?.Value ?? value;
            }
            else if (field.Type is IOptionsFieldType<NumberOption> numberOptionsType && numberOptionsType.Options != null)
            {
                // For number options, try to find a matching option by numeric value
                fieldValue = numberOptionsType.Options!.FirstOrDefault(x =>
                {
                    var numberValue = (NumberValue)x.Value;
                    return string.Equals(numberValue.Value.ToString(), value, StringComparison.OrdinalIgnoreCase);
                })?.Value.ToString() ?? value;
            }

            memberFields[field.Name] = fieldValue;
        }
    }
}
