using CoverGo.PoliciesV3.Domain.Common.Enums;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents a member loading with all its properties
/// </summary>
public record MemberLoading
{
    public required string Id { get; init; }
    public string? Name { get; init; }
    public string? Remark { get; init; }
    public decimal Value { get; init; }
    public LoadingCondition? Condition { get; init; }
    public required LoadingMethodType Method { get; init; }
    public required LoadingSourceType Source { get; init; }
    public required LoadingStatusType Status { get; init; }
    public required DateOnly EffectiveDate { get; init; }
}
