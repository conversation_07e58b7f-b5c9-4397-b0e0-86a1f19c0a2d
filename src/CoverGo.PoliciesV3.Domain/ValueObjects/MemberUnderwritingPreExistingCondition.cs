using CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents a pre-existing condition for member underwriting
/// </summary>
public record MemberUnderwritingPreExistingCondition
{
    public required string Id { get; init; }
    public required MemberUnderwritingPreExistingConditionType Type { get; init; }
    public required string Description { get; init; }
    public string? MedicalCode { get; init; }
    public DateOnly? DiagnosisDate { get; init; }
    public string? TreatmentDetails { get; init; }
}
