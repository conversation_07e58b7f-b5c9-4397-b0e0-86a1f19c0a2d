using CoverGo.PoliciesV3.Domain.Common.Enums;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents a loading element for member benefits
/// </summary>
public record MemberBenefitsLoadingElement
{
    public required string Id { get; init; }
    public string? Name { get; init; }
    public string? Remark { get; init; }
    public decimal Value { get; init; }
    public LoadingCondition? Condition { get; init; }
    public required LoadingMethodType Method { get; init; }
    public required LoadingSourceType Source { get; init; }
}
