using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

public class ValidationOrchestrationResult
{
    public bool IsSuccessful { get; init; }
    public int ValidMembersCount { get; init; }
    public int InvalidMembersCount { get; init; }
    public Dictionary<int, List<ValidationError>> MemberErrors { get; init; } = [];
    public bool HasValidationErrors => MemberErrors.Any(kvp => kvp.Value?.Any() == true);

    public static ValidationOrchestrationResult Success(int validCount, int invalidCount, Dictionary<int, List<ValidationError>> memberErrors) =>
        new()
        {
            IsSuccessful = true,
            ValidMembersCount = validCount,
            InvalidMembersCount = invalidCount,
            MemberErrors = memberErrors
        };
}