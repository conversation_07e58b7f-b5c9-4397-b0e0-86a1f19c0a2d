using CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents underwriting information for member benefits
/// </summary>
public record MemberBenefitsUnderwriting
{
    public required string Id { get; init; }
    public required string BenefitId { get; init; }
    public string? BenefitName { get; init; }
    public required MemberBenefitUnderwritingStatus Status { get; init; }
    public ICollection<MemberUnderwritingExclusion> Exclusions { get; init; } = [];
    public string? Comments { get; init; }
    public DateOnly? ReviewDate { get; init; }
}
