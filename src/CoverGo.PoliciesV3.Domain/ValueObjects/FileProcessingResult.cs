namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Result of file processing operation containing member data and metadata.
/// Encapsulates all information needed for subsequent validation processing.
/// </summary>
public record FileProcessingResult
{
    /// <summary>
    /// Indicates if file processing was successful
    /// </summary>
    public bool IsSuccess { get; init; }

    /// <summary>
    /// Parsed member data from the file (null if processing failed)
    /// </summary>
    public IReadOnlyList<IReadOnlyDictionary<string, string?>>? MemberData { get; init; }

    /// <summary>
    /// File size in bytes for logging and metrics
    /// </summary>
    public long? FileSizeBytes { get; init; }

    /// <summary>
    /// Number of members found in the file
    /// </summary>
    public int MemberCount { get; init; }

    /// <summary>
    /// Error information if processing failed
    /// </summary>
    public string? ErrorMessage { get; init; }

    /// <summary>
    /// Exception that caused the failure (if any)
    /// </summary>
    public Exception? Exception { get; init; }

    /// <summary>
    /// Creates a successful file processing result
    /// </summary>
    public static FileProcessingResult Success(
        IReadOnlyList<IReadOnlyDictionary<string, string?>> memberData,
        long fileSizeBytes) =>
        new()
        {
            IsSuccess = true,
            MemberData = memberData,
            FileSizeBytes = fileSizeBytes,
            MemberCount = memberData.Count
        };

    /// <summary>
    /// Creates a failed file processing result
    /// </summary>
    public static FileProcessingResult Failure(string errorMessage, Exception? exception = null) =>
        new()
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            Exception = exception
        };
}
