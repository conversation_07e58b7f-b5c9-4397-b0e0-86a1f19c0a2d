using CoverGo.PoliciesV3.Domain.Common.Enums;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents a premium pricing component for a member
/// </summary>
public class MemberPremiumPricingComponent
{
    public int Order { get; init; }
    public required PolicyPremiumPricingComponentType Type { get; init; }
    public required string Name { get; init; }
    public decimal Amount { get; init; }
    public decimal Rate { get; init; }
    public required CurrencyCode Currency { get; init; }
    public ICollection<MemberPremiumPricingComponent> Children { get; init; } = [];
}
