using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Strongly-typed identifier for PolicyMemberState entities
/// </summary>
public record PolicyMemberStateId(Guid Value) : ValueObject<Guid>(Value)
{
    public static PolicyMemberStateId Empty => new(Guid.Empty);
    public static PolicyMemberStateId New => new(Guid.CreateVersion7());

    /// <summary>
    /// Implicit conversion from Guid to PolicyMemberStateId
    /// </summary>
    public static implicit operator PolicyMemberStateId(Guid value) => new(value);

    /// <summary>
    /// Implicit conversion from string to PolicyMemberStateId
    /// </summary>
    public static implicit operator PolicyMemberStateId(string value) =>
        string.IsNullOrWhiteSpace(value) ? throw new ArgumentException("PolicyMemberStateId cannot be null or empty", nameof(value))
        : new(Guid.Parse(value));

    /// <summary>
    /// Implicit conversion from PolicyMemberStateId to Guid
    /// </summary>
    public static implicit operator Guid(PolicyMemberStateId policyMemberStateId) => policyMemberStateId.Value;
}
