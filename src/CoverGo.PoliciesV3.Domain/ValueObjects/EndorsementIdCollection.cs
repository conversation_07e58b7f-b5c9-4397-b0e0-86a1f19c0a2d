using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Endorsements;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Value object representing a collection of endorsement IDs with conversion utilities
/// Encapsulates the business logic for converting between string IDs and strongly-typed EndorsementId value objects
/// </summary>
public record EndorsementIdCollection(List<EndorsementId> Value) : ValueObject<List<EndorsementId>>(Value)
{
    /// <summary>
    /// Creates an EndorsementIdCollection from a list of string IDs
    /// Business rule: Filters out invalid GUIDs and null values, converts valid ones to EndorsementId
    /// </summary>
    public static EndorsementIdCollection FromStringIds(List<string?> stringIds) =>
        new([.. stringIds
            .Where(id => id != null)
            .Select(id => Guid.TryParse(id, out Guid guid) ? (Guid?)guid : null)
            .Where(guid => guid.HasValue)
            .Select(guid => (EndorsementId)guid!.Value)]);

    /// <summary>
    /// Creates an EndorsementIdCollection from a list of EndorsementId objects
    /// Business rule: Direct conversion from strongly-typed EndorsementId list
    /// </summary>
    public static EndorsementIdCollection FromEndorsementIds(IReadOnlyList<EndorsementId> endorsementIds) =>
        new([.. endorsementIds]);

    /// <summary>
    /// Converts the collection back to string IDs
    /// </summary>
    public List<string?> ToStringIds() =>
        [.. Value.Select(id => id.Value.ToString()).Cast<string?>()];

    /// <summary>
    /// Checks if the original string ID list contained a null endorsement
    /// Business rule: Null endorsements represent base policy states (non-endorsement states)
    /// </summary>
    public static bool ContainsNullEndorsement(List<string?> originalStringIds) =>
        originalStringIds.Contains(null);

    /// <summary>
    /// Creates a collection that includes both the endorsement IDs and null for base policy states
    /// Business rule: Most queries need to include both endorsement states and base policy states
    /// </summary>
    public static EndorsementIdCollection FromStringIdsIncludingNull(List<string?> stringIds)
    {
        EndorsementIdCollection endorsementIds = FromStringIds(stringIds);
        return endorsementIds;
    }

    /// <summary>
    /// Checks if this collection contains a specific endorsement ID
    /// </summary>
    public bool Contains(EndorsementId endorsementId) =>
        Value.Contains(endorsementId);

    /// <summary>
    /// Gets the count of endorsement IDs in the collection
    /// </summary>
    public int Count => Value.Count;

    /// <summary>
    /// Checks if the collection is empty
    /// </summary>
    public bool IsEmpty => Value.Count == 0;

    /// <summary>
    /// Implicit conversion from List&lt;EndorsementId&gt; to EndorsementIdCollection
    /// </summary>
    public static implicit operator EndorsementIdCollection(List<EndorsementId> endorsementIds) =>
        new(endorsementIds);

    /// <summary>
    /// Implicit conversion from EndorsementIdCollection to List&lt;EndorsementId&gt;
    /// </summary>
    public static implicit operator List<EndorsementId>(EndorsementIdCollection collection) =>
        collection.Value;
}