using CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents an underwriting exclusion for a member
/// </summary>
public record MemberUnderwritingExclusion
{
    public required string Id { get; init; }
    public required MemberUnderwritingExclusionType Type { get; init; }
    public required string Description { get; init; }
    public string? Reason { get; init; }
    public DateOnly? EffectiveDate { get; init; }
    public DateOnly? ExpiryDate { get; init; }
}
