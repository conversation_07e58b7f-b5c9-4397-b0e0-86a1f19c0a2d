namespace CoverGo.PoliciesV3.Domain.Common.Providers;

/// <summary>
/// Provides current date and time values that can be overridden for testing purposes.
/// Uses UTC time by default to avoid time zone issues and ensure consistency.
/// </summary>
public static class DateTimeProvider
{
    private static Func<DateTime> _utcNowProvider = () => DateTime.UtcNow;

    /// <summary>
    /// Gets the current UTC DateTime. Can be overridden for testing.
    /// </summary>
    public static DateTime UtcNow => _utcNowProvider();

    /// <summary>
    /// Gets the current date as DateOnly in UTC. Can be overridden for testing.
    /// </summary>
    public static DateOnly Today => DateOnly.FromDateTime(UtcNow);

    /// <summary>
    /// Sets a custom UTC DateTime provider for testing purposes.
    /// </summary>
    /// <param name="utcNowProvider">Function that returns the current UTC DateTime</param>
    public static void SetUtcNowProvider(Func<DateTime> utcNowProvider) => _utcNowProvider = utcNowProvider ?? throw new ArgumentNullException(nameof(utcNowProvider));

    /// <summary>
    /// Sets a fixed UTC DateTime for testing purposes.
    /// </summary>
    /// <param name="fixedUtcDateTime">The fixed UTC DateTime to use</param>
    public static void SetFixedUtcDateTime(DateTime fixedUtcDateTime)
    {
        if (fixedUtcDateTime.Kind != DateTimeKind.Utc)
            throw new ArgumentException("DateTime must be UTC", nameof(fixedUtcDateTime));
        
        _utcNowProvider = () => fixedUtcDateTime;
    }

    /// <summary>
    /// Resets the provider to use the system's current UTC time.
    /// </summary>
    public static void Reset() => _utcNowProvider = () => DateTime.UtcNow;
}
