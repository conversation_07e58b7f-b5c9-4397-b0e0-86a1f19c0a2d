using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications;

/// <summary>
/// Base class for composite specifications that orchestrate multiple atomic specifications.
/// Provides common functionality for error aggregation and result composition.
/// This supports the thick composite specification pattern where business logic
/// is pushed down from application handlers into domain specifications.
/// Each concrete class will manage its own logger dependency.
/// </summary>
/// <typeparam name="T">The type of context this specification evaluates</typeparam>
public abstract class CompositeSpecificationBase<T> :
    ISpecification<T>,
    IRowIndexedSpecification<T>,
    IBatchValidationSpecification<T>
{
    /// <summary>
    /// Gets a human-readable name for this composite business rule specification.
    /// </summary>
    public abstract string BusinessRuleName { get; }

    /// <summary>
    /// Gets a description of what this composite specification validates.
    /// </summary>
    public abstract string Description { get; }

    /// <summary>
    /// Standard IAsyncSpecification implementation that delegates to batch validation.
    /// </summary>
    public virtual async Task<Result> IsSatisfiedBy(T entity, CancellationToken cancellationToken = default)
    {
        BatchValidationResult batchResult = await ValidateBatchAsync(entity, cancellationToken);

        return batchResult.IsValid
            ? Result.Success()
            : Result.Failure(batchResult.RowErrors.Values.SelectMany(errors => errors));
    }

    /// <summary>
    /// Row-indexed validation implementation that delegates to batch validation.
    /// </summary>
    public virtual async Task<Dictionary<int, List<ValidationError>>> ValidateWithRowIndexedErrorsAsync(
        T context,
        CancellationToken cancellationToken = default)
    {
        BatchValidationResult batchResult = await ValidateBatchAsync(context, cancellationToken);
        return batchResult.RowErrors;
    }

    /// <summary>
    /// Abstract method that concrete composite specifications must implement.
    /// This is where the main validation orchestration logic resides.
    /// </summary>
    public abstract Task<BatchValidationResult> ValidateBatchAsync(
        T context,
        CancellationToken cancellationToken = default);

    #region Helper Methods for Composite Specifications

    /// <summary>
    /// Merges multiple row-indexed error dictionaries into a single dictionary.
    /// Useful for combining results from multiple atomic specifications.
    /// </summary>
    protected static Dictionary<int, List<ValidationError>> MergeRowIndexedErrors(
        params Dictionary<int, List<ValidationError>>[] errorDictionaries)
    {
        var merged = new Dictionary<int, List<ValidationError>>();

        foreach (Dictionary<int, List<ValidationError>> errorDict in errorDictionaries)
        {
            foreach (KeyValuePair<int, List<ValidationError>> kvp in errorDict)
            {
                if (merged.TryGetValue(kvp.Key, out List<ValidationError>? existingErrors))
                {
                    existingErrors.AddRange(kvp.Value);
                }
                else
                {
                    merged[kvp.Key] = [.. kvp.Value];
                }
            }
        }

        return merged;
    }

    /// <summary>
    /// Calculates validation statistics from row-indexed errors.
    /// </summary>
    protected static (int validCount, int invalidCount) CalculateValidationStats(
        int totalCount,
        Dictionary<int, List<ValidationError>> errors)
    {
        int invalidCount = errors.Count;
        int validCount = totalCount - invalidCount;
        return (validCount, invalidCount);
    }

    /// <summary>
    /// Converts a standard Result to a BatchValidationResult for compatibility.
    /// </summary>
    protected static BatchValidationResult ConvertResultToBatchResult(
        Result result,
        int totalCount = 0)
    {
        if (result.IsSuccess)
        {
            return BatchValidationResult.Success(totalCount);
        }

        // Convert Result errors to row-indexed format (assuming single row for simple Result)
        var rowErrors = new Dictionary<int, List<ValidationError>>
        {
            [0] = [.. result.Errors]
        };
        return BatchValidationResult.WithErrors(0, 1, rowErrors);
    }

    #endregion

}
