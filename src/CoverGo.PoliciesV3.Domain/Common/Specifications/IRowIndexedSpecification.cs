using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications;

/// <summary>
/// Specification that can return validation errors indexed by row number.
/// Essential for upload validation where we need to track which specific rows have errors.
/// This extends the base IAsyncSpecification to provide row-level error tracking capabilities.
/// </summary>
/// <typeparam name="T">The type of context this specification evaluates</typeparam>
public interface IRowIndexedSpecification<T> : ISpecification<T>
{
    /// <summary>
    /// Validates and returns errors indexed by row number (0-based).
    /// This method provides detailed error tracking for batch operations where
    /// we need to know exactly which rows failed validation and why.
    /// </summary>
    /// <param name="context">The validation context containing the data to validate</param>
    /// <param name="cancellationToken">Cancellation token for the async operation</param>
    /// <returns>
    /// A dictionary where:
    /// - Key: Row index (0-based) that has validation errors
    /// - Value: List of validation errors for that specific row
    /// Empty dictionary indicates no validation errors found.
    /// </returns>
    Task<Dictionary<int, List<ValidationError>>> ValidateWithRowIndexedErrorsAsync(
        T context,
        CancellationToken cancellationToken = default);
}
