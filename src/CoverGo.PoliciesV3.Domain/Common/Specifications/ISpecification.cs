using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications;

/// <summary>
/// Represents an asynchronous business rule specification that can be evaluated against an entity.
/// Use this for specifications that require database access, external service calls, or other async operations.
/// This follows the Specification Pattern for Domain-Driven Design.
/// </summary>
/// <typeparam name="T">The type of entity this specification evaluates</typeparam>
public interface ISpecification<in T>
{
    /// <summary>
    /// Asynchronously evaluates the specification against the given entity.
    /// </summary>
    /// <param name="entity">The entity to evaluate</param>
    /// <param name="cancellationToken">Cancellation token for the async operation</param>
    /// <returns>A task that represents the async operation, containing a result indicating whether the specification is satisfied</returns>
    Task<Result> IsSatisfiedBy(T entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a human-readable name for this business rule specification.
    /// This should be clear enough for business stakeholders to understand.
    /// </summary>
    string BusinessRuleName { get; }

    /// <summary>
    /// Gets a description of what this specification validates.
    /// This provides additional context about the business rule.
    /// </summary>
    string Description { get; }
}
