using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications;

/// <summary>
/// Represents the result of a specification evaluation with additional context for business rules.
/// This extends the existing Result pattern with specification-specific functionality.
/// </summary>
public readonly struct SpecificationResult : IEquatable<SpecificationResult>
{
    private readonly Result _result;

    /// <summary>
    /// Initializes a new instance of SpecificationResult.
    /// </summary>
    /// <param name="result">The underlying result</param>
    /// <param name="businessRuleName">The name of the business rule that was evaluated</param>
    /// <param name="description">Description of the business rule</param>
    private SpecificationResult(Result result, string? businessRuleName = null, string? description = null)
    {
        _result = result;
        BusinessRuleName = businessRuleName;
        Description = description;
    }

    /// <summary>
    /// Gets a value indicating whether the specification was satisfied.
    /// </summary>
    public bool IsSatisfied => _result.IsSuccess;

    /// <summary>
    /// Gets a value indicating whether the specification was not satisfied.
    /// </summary>
    public bool IsNotSatisfied => _result.IsFailure;

    /// <summary>
    /// Gets the validation errors if the specification was not satisfied.
    /// </summary>
    public IReadOnlyList<ValidationError> Errors => _result.Errors;

    /// <summary>
    /// Gets the name of the business rule that was evaluated.
    /// </summary>
    public string? BusinessRuleName { get; }

    /// <summary>
    /// Gets the description of the business rule.
    /// </summary>
    public string? Description { get; }

    /// <summary>
    /// Gets the underlying Result for compatibility with existing code.
    /// </summary>
    public Result AsResult() => _result;

    #region Factory Methods

    /// <summary>
    /// Creates a satisfied specification result.
    /// </summary>
    public static SpecificationResult Satisfied(string? businessRuleName = null, string? description = null) =>
        new(Result.Success(), businessRuleName, description);

    /// <summary>
    /// Creates a not satisfied specification result with a single validation error.
    /// </summary>
    public static SpecificationResult NotSatisfied(ValidationError error, string? businessRuleName = null, string? description = null) =>
        new(Result.Failure(error), businessRuleName, description);

    /// <summary>
    /// Creates a not satisfied specification result with multiple validation errors.
    /// </summary>
    public static SpecificationResult NotSatisfied(IEnumerable<ValidationError> errors, string? businessRuleName = null, string? description = null) =>
        new(Result.Failure(errors), businessRuleName, description);

    /// <summary>
    /// Creates a specification result from an existing Result.
    /// </summary>
    public static SpecificationResult FromResult(Result result, string? businessRuleName = null, string? description = null) =>
        new(result, businessRuleName, description);

    #endregion

    #region Utility Methods

    /// <summary>
    /// Executes an action if the specification is satisfied.
    /// </summary>
    public SpecificationResult OnSatisfied(Action action)
    {
        if (IsSatisfied)
            action();
        return this;
    }

    /// <summary>
    /// Executes an action if the specification is not satisfied.
    /// </summary>
    public SpecificationResult OnNotSatisfied(Action<IReadOnlyList<ValidationError>> action)
    {
        if (IsNotSatisfied)
            action(Errors);
        return this;
    }

    /// <summary>
    /// Combines this specification result with another using logical AND.
    /// Both must be satisfied for the combined result to be satisfied.
    /// </summary>
    public SpecificationResult And(SpecificationResult other)
    {
        if (IsSatisfied && other.IsSatisfied)
            return Satisfied($"{BusinessRuleName} AND {other.BusinessRuleName}");

        var errors = new List<ValidationError>();
        if (IsNotSatisfied) errors.AddRange(Errors);
        if (other.IsNotSatisfied) errors.AddRange(other.Errors);

        return NotSatisfied(errors, $"{BusinessRuleName} AND {other.BusinessRuleName}");
    }

    /// <summary>
    /// Combines this specification result with another using logical OR.
    /// Either can be satisfied for the combined result to be satisfied.
    /// </summary>
    public SpecificationResult Or(SpecificationResult other)
    {
        if (IsSatisfied || other.IsSatisfied)
            return Satisfied($"{BusinessRuleName} OR {other.BusinessRuleName}");

        var errors = new List<ValidationError>();
        errors.AddRange(Errors);
        errors.AddRange(other.Errors);

        return NotSatisfied(errors, $"{BusinessRuleName} OR {other.BusinessRuleName}");
    }

    #endregion

    #region Implicit Conversions

    /// <summary>
    /// Implicitly converts a Result to a SpecificationResult.
    /// </summary>
    public static implicit operator SpecificationResult(Result result) => FromResult(result);

    /// <summary>
    /// Implicitly converts a SpecificationResult to a Result.
    /// </summary>
    public static implicit operator Result(SpecificationResult specificationResult) => specificationResult._result;

    #endregion

    /// <summary>
    /// Returns a string representation of this specification result.
    /// </summary>
    public override string ToString()
    {
        string status = IsSatisfied ? "Satisfied" : "Not Satisfied";
        string ruleName = !string.IsNullOrEmpty(BusinessRuleName) ? $" [{BusinessRuleName}]" : "";
        string errorInfo = IsNotSatisfied ? $" - {Errors.Count} error(s)" : "";
        return $"{status}{ruleName}{errorInfo}";
    }

    /// <summary>
    /// Determines whether the specified object is equal to the current specification result.
    /// </summary>
    public override bool Equals(object? obj) => obj is SpecificationResult other && Equals(other);

    /// <summary>
    /// Determines whether the specified specification result is equal to the current specification result.
    /// </summary>
    public bool Equals(SpecificationResult other) =>
        _result.Equals(other._result) && BusinessRuleName == other.BusinessRuleName;

    /// <summary>
    /// Returns a hash code for the current specification result.
    /// </summary>
    public override int GetHashCode() => HashCode.Combine(_result, BusinessRuleName);

    /// <summary>
    /// Determines whether two specification results are equal.
    /// </summary>
    public static bool operator ==(SpecificationResult left, SpecificationResult right) => left.Equals(right);

    /// <summary>
    /// Determines whether two specification results are not equal.
    /// </summary>
    public static bool operator !=(SpecificationResult left, SpecificationResult right) => !left.Equals(right);
}
