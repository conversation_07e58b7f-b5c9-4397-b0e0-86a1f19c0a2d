using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;

public sealed record IndividualMemberValidationContext : UploadValidationContext
{
    private readonly Dictionary<string, object> _additionalContext;

    private IndividualMemberValidationContext(
        EndorsementId? endorsementId,
        PolicyDto policy,
        MembersUploadFields membersFields,
        PolicyMemberFieldsSchema schema,
        ResolvedValidationData resolvedData,
        Dictionary<string, object> additionalContext) : base(membersFields, schema)
    {
        EndorsementId = endorsementId;
        Policy = policy;
        ResolvedData = resolvedData;
        _additionalContext = additionalContext;
    }

    public EndorsementId? EndorsementId { get; }
    public PolicyDto Policy { get; }
    public ResolvedValidationData ResolvedData { get; }
    public IReadOnlyDictionary<string, object> AdditionalContext => _additionalContext.AsReadOnly();

    public static IndividualMemberValidationContext Create(
        PolicyDto policy,
        MembersUploadFields membersFields,
        PolicyMemberFieldsSchema schema,
        ResolvedValidationData resolvedData,
        EndorsementId? endorsementId = null,
        Dictionary<string, object>? additionalContext = null)
    {
        ArgumentNullException.ThrowIfNull(policy, nameof(policy));
        ArgumentNullException.ThrowIfNull(resolvedData, nameof(resolvedData));

        return new IndividualMemberValidationContext(
            endorsementId, policy, membersFields, schema, resolvedData, additionalContext ?? []);
    }
}
