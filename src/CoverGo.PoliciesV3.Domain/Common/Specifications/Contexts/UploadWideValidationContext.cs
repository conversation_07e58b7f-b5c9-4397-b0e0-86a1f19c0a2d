using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;

public sealed record UploadWideValidationContext : UploadValidationContext
{
    private UploadWideValidationContext(
        PolicyMemberUpload upload,
        PolicyDto policy,
        MembersUploadFields membersFields,
        PolicyMemberFieldsSchema schema,
        ResolvedValidationData resolvedData) : base(membersFields, schema)
    {
        Upload = upload;
        Policy = policy;
        ResolvedData = resolvedData;
    }

    public PolicyMemberUpload Upload { get; }
    public PolicyDto Policy { get; }
    public ResolvedValidationData ResolvedData { get; }

    public static UploadWideValidationContext Create(
        PolicyMemberUpload upload,
        PolicyDto policy,
        MembersUploadFields membersFields,
        PolicyMemberFieldsSchema schema,
        ResolvedValidationData resolvedData)
    {
        ArgumentNullException.ThrowIfNull(upload, nameof(upload));
        ArgumentNullException.ThrowIfNull(policy, nameof(policy));
        ArgumentNullException.ThrowIfNull(resolvedData, nameof(resolvedData));

        return new UploadWideValidationContext(upload, policy, membersFields, schema, resolvedData);
    }
}
