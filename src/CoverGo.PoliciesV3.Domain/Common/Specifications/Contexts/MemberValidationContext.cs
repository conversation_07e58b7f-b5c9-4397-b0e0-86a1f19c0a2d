using CoverGo.PoliciesV3.Domain.Common.Extensions;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;

public abstract record MemberValidationContext
{
    #region Core Properties

    /// <summary>
    /// The member fields to validate containing all field data for the member.
    /// </summary>
    public required MemberUploadFields MemberFields { get; init; }

    /// <summary>
    /// The policy containing the member, providing policy-level context for validation.
    /// </summary>
    public required PolicyDto Policy { get; init; }

    /// <summary>
    /// The schema containing field definitions and validation rules for the policy.
    /// </summary>
    public required PolicyMemberFieldsSchema Schema { get; init; }

    /// <summary>
    /// The endorsement ID for the validation context (optional).
    /// Used for endorsement-specific validation scenarios.
    /// </summary>
    public Guid? EndorsementId { get; init; }

    #endregion

    #region Common Helper Methods

    /// <summary>
    /// Gets the member ID from the member fields.
    /// </summary>
    /// <returns>The member ID if present, null otherwise</returns>
    public string? GetMemberId() => GetFieldValue(PolicyMemberUploadWellKnowFields.MemberIdField);

    /// <summary>
    /// Gets the plan ID from the member fields.
    /// </summary>
    /// <returns>The plan ID if present, null otherwise</returns>
    public string? GetPlanId() => GetFieldValue(PolicyMemberUploadWellKnowFields.PlanIdField);

    /// <summary>
    /// Gets the email address from the member fields.
    /// </summary>
    /// <returns>The email address if present, null otherwise</returns>
    public string? GetEmail() => GetFieldValue(PolicyMemberUploadWellKnowFields.EmailField);

    /// <summary>
    /// Gets the value of a specific field from the member fields.
    /// </summary>
    /// <param name="fieldName">The name of the field to retrieve</param>
    /// <returns>The field value if present, null otherwise</returns>
    public string? GetFieldValue(string fieldName) => MemberFields.Value.TryGetValueOrDefault(fieldName);

    #endregion

    #region Protected Validation Methods

    /// <summary>
    /// Validates that required context properties are not null.
    /// This method should be called by derived context factory methods to ensure data integrity.
    /// </summary>
    /// <param name="memberFields">The member fields to validate</param>
    /// <param name="policy">The policy to validate</param>
    /// <param name="schema">The schema to validate</param>
    /// <exception cref="ArgumentNullException">Thrown when any required parameter is null</exception>
    protected static void Validate(MemberUploadFields memberFields, PolicyDto policy, PolicyMemberFieldsSchema schema)
    {
        ArgumentNullException.ThrowIfNull(memberFields, nameof(memberFields));
        ArgumentNullException.ThrowIfNull(policy, nameof(policy));
        ArgumentNullException.ThrowIfNull(schema, nameof(schema));
    }

    #endregion
}
