using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;

public sealed record CompleteValidationContext
{
    private CompleteValidationContext(
        PolicyMemberUpload upload,
        PolicyDto policy,
        ResolvedValidationData resolvedData,
        FileProcessingResult? fileResult = null,
        PolicyMemberFieldsSchema? schema = null,
        MembersUploadFields? transformedMemberData = null)
    {
        Upload = upload;
        Policy = policy;
        ResolvedData = resolvedData;
        FileResult = fileResult;
        Schema = schema;
        TransformedMemberData = transformedMemberData;
    }

    public PolicyMemberUpload Upload { get; }
    public PolicyDto Policy { get; }
    public ResolvedValidationData ResolvedData { get; }
    public FileProcessingResult? FileResult { get; }
    public PolicyMemberFieldsSchema? Schema { get; }
    public MembersUploadFields? TransformedMemberData { get; }

    /// <summary>
    /// Creates a complete validation context with all pre-processed data.
    /// </summary>
    public static CompleteValidationContext CreateWithProcessedData(
        PolicyMemberUpload upload,
        PolicyDto policy,
        ResolvedValidationData resolvedData,
        FileProcessingResult fileResult,
        PolicyMemberFieldsSchema schema,
        MembersUploadFields transformedMemberData)
    {
        ArgumentNullException.ThrowIfNull(upload);
        ArgumentNullException.ThrowIfNull(policy);
        ArgumentNullException.ThrowIfNull(resolvedData);
        ArgumentNullException.ThrowIfNull(fileResult);
        ArgumentNullException.ThrowIfNull(schema);
        ArgumentNullException.ThrowIfNull(transformedMemberData);

        ValidateBusinessRequirements(upload, policy);

        return new CompleteValidationContext(upload, policy, resolvedData, fileResult, schema, transformedMemberData);
    }

    /// <summary>
    /// Validates basic business requirements at creation time
    /// </summary>
    private static void ValidateBusinessRequirements(PolicyMemberUpload upload, PolicyDto policy)
    {
        if (string.IsNullOrEmpty(upload.Path))
            throw new ValidationException(Errors.Required("upload.Path", "Upload Path"));

        if (string.IsNullOrEmpty(policy.ContractHolderId))
            throw new ValidationException(Errors.Required("policy.ContractHolderId", "Contract Holder ID"));
    }

    /// <summary>
    /// Checks if the context has all pre-processed data available - guaranteed accurate
    /// </summary>
    public bool HasPreProcessedData => FileResult != null && Schema != null && TransformedMemberData != null;

    /// <summary>
    /// Gets the processed data with validation. Throws if not available.
    /// </summary>
    public (FileProcessingResult FileResult, PolicyMemberFieldsSchema Schema, MembersUploadFields TransformedData) GetProcessedDataOrThrow() => !HasPreProcessedData
            ? throw new ValidationException(Errors.InvalidState("context.PreProcessedData", "Processed data is not available", "Pre-processed Data"))
            : (FileResult!, Schema!, TransformedMemberData!);

    /// <summary>
    /// Validates that file processing was successful and data is available for validation
    /// </summary>
    public void ValidateForProcessing()
    {
        if (!HasPreProcessedData)
            throw new ValidationException(Errors.InvalidState("context.PreProcessedData", "Processed data is not available", "Pre-processed Data"));
    }
}
