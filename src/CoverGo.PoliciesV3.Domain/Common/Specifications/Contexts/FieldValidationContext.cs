using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;

public sealed record FieldValidationContext : MemberValidationContext
{
    #region Additional Properties

    /// <summary>
    /// The member ID for existing member validation (null for new members).
    /// Used to distinguish between new member creation and existing member updates.
    /// </summary>
    public string? MemberId { get; init; }

    #endregion

    #region Factory Methods

    /// <summary>
    /// Creates a new FieldValidationContext with comprehensive validation.
    /// </summary>
    /// <param name="memberFields">The member fields to validate</param>
    /// <param name="policy">The policy containing the member</param>
    /// <param name="schema">The schema containing field definitions and validation rules</param>
    /// <param name="memberId">The member ID for existing member validation (null for new members)</param>
    /// <param name="endorsementId">The endorsement ID for the validation context (optional)</param>
    /// <returns>A new FieldValidationContext instance</returns>
    /// <exception cref="ArgumentNullException">Thrown when required parameters are null</exception>
    public static FieldValidationContext Create(
        MemberUploadFields memberFields,
        PolicyDto policy,
        PolicyMemberFieldsSchema schema,
        string? memberId = null,
        Guid? endorsementId = null)
    {
        // Validate required parameters using base class method
        Validate(memberFields, policy, schema);

        return new FieldValidationContext
        {
            MemberFields = memberFields,
            Policy = policy,
            Schema = schema,
            MemberId = memberId,
            EndorsementId = endorsementId
        };
    }

    #endregion

    #region Additional Helper Methods

    /// <summary>
    /// Determines if this is a new member creation scenario.
    /// </summary>
    /// <returns>True if this is a new member (MemberId is null), false for existing member updates</returns>
    public bool IsNewMember() => string.IsNullOrWhiteSpace(MemberId);

    /// <summary>
    /// Determines if this is an existing member update scenario.
    /// </summary>
    /// <returns>True if this is an existing member update (MemberId is not null), false for new member creation</returns>
    public bool IsExistingMemberUpdate() => !IsNewMember();

    /// <summary>
    /// Gets the effective member ID for validation.
    /// Returns the explicit MemberId if set, otherwise attempts to get it from member fields.
    /// </summary>
    /// <returns>The effective member ID for validation purposes</returns>
    public string? GetEffectiveMemberId() => MemberId ?? GetMemberId();

    #endregion
}
