using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;

public sealed record UploadUniquenessValidationContext : UploadValidationContext
{
    private UploadUniquenessValidationContext(
        MembersUploadFields membersFields,
        PolicyMemberFieldsSchema schema) : base(membersFields, schema)
    {
    }

    /// <summary>
    /// Creates a new instance of UploadUniquenessValidationContext with comprehensive validation.
    /// </summary>
    /// <param name="membersFields">The collection of member upload fields to validate for uniqueness</param>
    /// <param name="schema">The schema defining field definitions and validation rules</param>
    /// <returns>A new UploadUniquenessValidationContext instance</returns>
    /// <exception cref="ArgumentNullException">Thrown when any required parameter is null</exception>
    public static UploadUniquenessValidationContext Create(
        MembersUploadFields membersFields,
        PolicyMemberFieldsSchema schema) =>
        new(membersFields, schema);
}
