using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;

/// <summary>
/// Domain representation of validation scope for uniqueness checks.
/// </summary>
public enum ValidationScope
{
    Tenant,
    Policy,
    ContractHolder
}

/// <summary>
/// Domain representation of validation type.
/// </summary>
public enum ValidationType
{
    Unique
}

/// <summary>
/// Domain representation of a field validation rule.
/// </summary>
public sealed record FieldValidationRule(
    string Field,
    ValidationScope Scope,
    ValidationType Type,
    object? Value = null
);

public record UniquenessValidationContext : MemberValidationContext
{
    #region Core Properties

    /// <summary>
    /// The policy ID for uniqueness validation scope.
    /// </summary>
    public required string PolicyId { get; init; }

    /// <summary>
    /// The plan ID for the member being validated.
    /// </summary>
    public required string PlanId { get; init; }

    /// <summary>
    /// The contract holder ID for contract holder scope validation (optional).
    /// </summary>
    public string? ContractHolderId { get; init; }

    /// <summary>
    /// The member class for class-specific validation (optional).
    /// </summary>
    public string? Class { get; init; }

    /// <summary>
    /// Collection of contract holder policy IDs for cross-policy validation.
    /// </summary>
    public IReadOnlyList<string> ContractHolderPolicyIds { get; init; } = [];

    /// <summary>
    /// Flag indicating whether to skip contract holder validation.
    /// Controlled by feature flags at the application layer.
    /// </summary>
    public bool ShouldSkipContractHolderValidation { get; init; }

    #endregion

    #region External Data Properties

    /// <summary>
    /// Pre-resolved valid endorsement IDs for contract holder scope.
    /// </summary>
    public IReadOnlyList<string?>? ContractHolderValidEndorsementIds { get; init; }

    /// <summary>
    /// Pre-resolved contract holder scope endorsements from ALL contract holder policies.
    /// </summary>
    public IReadOnlyList<EndorsementId>? ContractHolderScopeEndorsements { get; init; }

    /// <summary>
    /// Pre-resolved tenant ID for validation context.
    /// </summary>
    public string? TenantId { get; init; }

    /// <summary>
    /// Pre-resolved feature flag values to avoid feature manager dependencies in specifications.
    /// </summary>
    public IReadOnlyDictionary<string, bool>? FeatureFlags { get; init; }

    /// <summary>
    /// Pre-resolved member ID for existing member validation scenarios.
    /// </summary>
    public string? MemberId { get; init; }

    /// <summary>
    /// Pre-resolved policy member ID for existing member validation scenarios.
    /// </summary>
    public string? PolicyMemberId { get; init; }

    /// <summary>
    /// Pre-resolved validation rules for the current validation context.
    /// Contains the specific field validation rules to be applied.
    /// </summary>
    public IReadOnlyList<FieldValidationRule>? ValidationRules { get; init; }

    /// <summary>
    /// Pre-resolved field values for validation.
    /// Contains the actual field values to be validated against the rules.
    /// </summary>
    public IReadOnlyDictionary<string, object>? FieldValues { get; init; }

    #endregion

    #region Factory Method

    /// <summary>
    /// Creates a new builder for constructing UniquenessValidationContext instances with a fluent API.
    /// This is the recommended way to create UniquenessValidationContext instances.
    /// </summary>
    /// <returns>A new UniquenessValidationContextBuilder instance</returns>
    public static UniquenessValidationContextBuilder Builder() => new();

    #endregion

    #region Helper Methods

    /// <summary>
    /// Gets the raw value of a specific field as an object from the member fields.
    /// Returns the unprocessed field value, distinct from the base string-typed GetFieldValue method.
    /// </summary>
    /// <param name="fieldName">The name of the field to retrieve</param>
    /// <returns>The field value if present, null otherwise</returns>
    public object? GetRawFieldValue(string fieldName) =>
        MemberFields.Value.TryGetValue(fieldName, out string? value) ? value : null;

    /// <summary>
    /// Gets the string value of a specific field from the member fields.
    /// </summary>
    /// <param name="fieldName">The name of the field to retrieve</param>
    /// <returns>The field value as string if present, null otherwise</returns>
    public string? GetFieldStringValue(string fieldName) => GetRawFieldValue(fieldName)?.ToString();

    /// <summary>
    /// Determines if a field has a non-empty value.
    /// </summary>
    /// <param name="fieldName">The name of the field to check</param>
    /// <returns>True if the field has a non-empty value, false otherwise</returns>
    public bool HasNonEmptyFieldValue(string fieldName) =>
        !string.IsNullOrWhiteSpace(GetFieldStringValue(fieldName));

    /// <summary>
    /// Gets the field definition for a specific field from the schema.
    /// </summary>
    /// <param name="fieldName">The name of the field to get definition for</param>
    /// <returns>The field definition if found, null otherwise</returns>
    public PolicyMemberFieldDefinition? GetFieldDefinition(string fieldName) =>
        Schema.Fields.FirstOrDefault(f => string.Equals(f.Name, fieldName, StringComparison.OrdinalIgnoreCase));

    /// <summary>
    /// Determines if a field is marked as unique in the schema.
    /// </summary>
    /// <param name="fieldName">The name of the field to check</param>
    /// <returns>True if the field is marked as unique, false otherwise</returns>
    public bool IsFieldUnique(string fieldName) =>
        GetFieldDefinition(fieldName)?.IsUnique ?? false;

    /// <summary>
    /// Gets the field values as a dictionary for uniqueness service calls.
    /// Converts the member fields to the format expected by uniqueness validation services.
    /// </summary>
    /// <returns>Dictionary of field values for uniqueness validation</returns>
    public IDictionary<string, object?> GetFieldValuesForUniquenessValidation() =>
        MemberFields.Value.ToDictionary(kvp => kvp.Key, kvp => (object?)kvp.Value);

    /// <summary>
    /// Gets a feature flag value with a default fallback.
    /// </summary>
    /// <param name="flagName">The name of the feature flag</param>
    /// <param name="defaultValue">The default value if flag is not found</param>
    /// <returns>The feature flag value or default</returns>
    public bool GetFeatureFlag(string flagName, bool defaultValue = false) =>
        FeatureFlags?.TryGetValue(flagName, out bool value) == true ? value : defaultValue;

    /// <summary>
    /// Determines if this context has pre-resolved endorsement IDs for contract holder validation.
    /// </summary>
    /// <returns>True if contract holder endorsement IDs are available, false otherwise</returns>
    public bool HasContractHolderEndorsementIds() =>
        ContractHolderValidEndorsementIds?.Count > 0;

    /// <summary>
    /// Determines if this is an existing member update scenario based on pre-resolved member ID.
    /// </summary>
    /// <returns>True if this is an existing member update, false for new member creation</returns>
    public bool IsExistingMemberUpdate() => !string.IsNullOrWhiteSpace(MemberId);

    /// <summary>
    /// Gets the effective member ID for validation, preferring the pre-resolved value.
    /// </summary>
    /// <returns>The effective member ID for validation purposes</returns>
    public string? GetEffectiveMemberId() => MemberId ?? GetMemberId();

    /// <summary>
    /// Gets the effective policy member ID for validation.
    /// </summary>
    /// <returns>The policy member ID if available, null otherwise</returns>
    public string? GetEffectivePolicyMemberId() => PolicyMemberId;

    /// <summary>
    /// Gets validation rules for a specific scope.
    /// </summary>
    /// <param name="scope">The validation scope to filter by</param>
    /// <returns>List of validation rules for the specified scope</returns>
    public IEnumerable<FieldValidationRule> GetValidationRulesForScope(ValidationScope scope) =>
        ValidationRules?.Where(rule => rule.Scope == scope) ?? [];

    /// <summary>
    /// Gets the field values for validation, preferring pre-resolved values over member fields.
    /// </summary>
    /// <returns>Dictionary of field values for validation</returns>
    public IReadOnlyDictionary<string, object> GetEffectiveFieldValues() => FieldValues ?? GetFieldValuesForUniquenessValidation()
            .Where(kvp => kvp.Value != null)
            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value!);

    #endregion
}

/// <summary>
/// Builder for creating UniquenessValidationContext instances with a fluent API.
/// Provides a more maintainable and readable alternative to the parameter-heavy factory method.
/// </summary>
public sealed class UniquenessValidationContextBuilder
{
    #region Required Fields
    private MemberUploadFields? _memberFields;
    private PolicyDto? _policy;
    private PolicyMemberFieldsSchema? _schema;
    private string? _policyId;
    private string? _planId;
    #endregion

    #region Optional Fields
    private string? _contractHolderId;
    private string? _memberClass;
    private IReadOnlyList<string>? _contractHolderPolicyIds;
    private bool _shouldSkipContractHolderValidation;
    private IReadOnlyList<string?>? _contractHolderValidEndorsementIds;
    private string? _tenantId;
    private IReadOnlyDictionary<string, bool>? _featureFlags;
    private string? _memberId;
    private string? _policyMemberId;
    private IReadOnlyList<FieldValidationRule>? _validationRules;
    private IReadOnlyDictionary<string, object>? _fieldValues;
    private Guid? _endorsementId;
    private IReadOnlyList<EndorsementId>? _contractHolderScopeEndorsements;
    #endregion

    #region Required Parameter Methods

    /// <summary>
    /// Sets the member fields to validate.
    /// </summary>
    /// <param name="memberFields">The member fields containing validation data</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithMemberFields(MemberUploadFields memberFields)
    {
        _memberFields = memberFields;
        return this;
    }

    /// <summary>
    /// Sets the policy containing the member.
    /// </summary>
    /// <param name="policy">The policy providing policy-level context</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithPolicy(PolicyDto policy)
    {
        _policy = policy;
        return this;
    }

    /// <summary>
    /// Sets the schema containing field definitions and validation rules.
    /// </summary>
    /// <param name="schema">The schema for field validation</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithSchema(PolicyMemberFieldsSchema schema)
    {
        _schema = schema;
        return this;
    }

    /// <summary>
    /// Sets the policy ID for uniqueness validation scope.
    /// </summary>
    /// <param name="policyId">The policy ID (cannot be null or empty)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithPolicyId(string policyId)
    {
        _policyId = policyId;
        return this;
    }

    /// <summary>
    /// Sets the plan ID for the member being validated.
    /// </summary>
    /// <param name="planId">The plan ID (cannot be null or empty)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithPlanId(string planId)
    {
        _planId = planId;
        return this;
    }

    #endregion

    #region Optional Parameter Methods

    /// <summary>
    /// Sets the contract holder ID for contract holder scope validation.
    /// </summary>
    /// <param name="contractHolderId">The contract holder ID (optional)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithContractHolderId(string? contractHolderId)
    {
        _contractHolderId = contractHolderId;
        return this;
    }

    /// <summary>
    /// Sets the member class for class-specific validation.
    /// </summary>
    /// <param name="memberClass">The member class (optional)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithMemberClass(string? memberClass)
    {
        _memberClass = memberClass;
        return this;
    }

    /// <summary>
    /// Sets the collection of contract holder policy IDs for cross-policy validation.
    /// </summary>
    /// <param name="contractHolderPolicyIds">The contract holder policy IDs (optional)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithContractHolderPolicyIds(IReadOnlyList<string>? contractHolderPolicyIds)
    {
        _contractHolderPolicyIds = contractHolderPolicyIds;
        return this;
    }

    /// <summary>
    /// Sets whether to skip contract holder validation.
    /// </summary>
    /// <param name="shouldSkip">True to skip contract holder validation</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithSkipContractHolderValidation(bool shouldSkip = true)
    {
        _shouldSkipContractHolderValidation = shouldSkip;
        return this;
    }

    /// <summary>
    /// Sets the pre-resolved valid endorsement IDs for contract holder scope.
    /// </summary>
    /// <param name="endorsementIds">The valid endorsement IDs (optional)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithContractHolderValidEndorsementIds(IReadOnlyList<string?>? endorsementIds)
    {
        _contractHolderValidEndorsementIds = endorsementIds;
        return this;
    }

    /// <summary>
    /// Sets the pre-resolved tenant ID for validation context.
    /// </summary>
    /// <param name="tenantId">The tenant ID (optional)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithTenantId(string? tenantId)
    {
        _tenantId = tenantId;
        return this;
    }

    /// <summary>
    /// Sets the pre-resolved feature flag values.
    /// </summary>
    /// <param name="featureFlags">The feature flag values (optional)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithFeatureFlags(IReadOnlyDictionary<string, bool>? featureFlags)
    {
        _featureFlags = featureFlags;
        return this;
    }

    /// <summary>
    /// Sets the pre-resolved member ID for existing member validation scenarios.
    /// </summary>
    /// <param name="memberId">The member ID (optional)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithMemberId(string? memberId)
    {
        _memberId = memberId;
        return this;
    }

    /// <summary>
    /// Sets the pre-resolved policy member ID for existing member validation scenarios.
    /// </summary>
    /// <param name="policyMemberId">The policy member ID (optional)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithPolicyMemberId(string? policyMemberId)
    {
        _policyMemberId = policyMemberId;
        return this;
    }

    /// <summary>
    /// Sets the pre-resolved validation rules for the current validation context.
    /// </summary>
    /// <param name="validationRules">The validation rules (optional)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithValidationRules(IReadOnlyList<FieldValidationRule>? validationRules)
    {
        _validationRules = validationRules;
        return this;
    }

    /// <summary>
    /// Sets the pre-resolved field values for validation.
    /// </summary>
    /// <param name="fieldValues">The field values (optional)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithFieldValues(IReadOnlyDictionary<string, object>? fieldValues)
    {
        _fieldValues = fieldValues;
        return this;
    }

    /// <summary>
    /// Sets the endorsement ID for the validation context.
    /// </summary>
    /// <param name="endorsementId">The endorsement ID (optional)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithEndorsementId(Guid? endorsementId)
    {
        _endorsementId = endorsementId;
        return this;
    }

    /// <summary>
    /// Sets the pre-resolved contract holder scope endorsements.
    /// </summary>
    /// <param name="endorsements">The contract holder scope endorsements (optional)</param>
    /// <returns>The builder instance for method chaining</returns>
    public UniquenessValidationContextBuilder WithContractHolderScopeEndorsements(IReadOnlyList<EndorsementId>? endorsements)
    {
        _contractHolderScopeEndorsements = endorsements;
        return this;
    }

    #endregion

    #region Build Method

    /// <summary>
    /// Builds the UniquenessValidationContext with the configured parameters.
    /// </summary>
    /// <returns>A new UniquenessValidationContext instance</returns>
    /// <exception cref="InvalidOperationException">Thrown when required parameters are missing</exception>
    /// <exception cref="ArgumentException">Thrown when required string parameters are null or empty</exception>
    /// <exception cref="ArgumentNullException">Thrown when required parameters are null</exception>
    public UniquenessValidationContext Build()
    {
        // Validate required parameters
        if (_memberFields == null)
            throw new InvalidOperationException("MemberFields is required. Call WithMemberFields() before Build().");
        if (_policy == null)
            throw new InvalidOperationException("Policy is required. Call WithPolicy() before Build().");
        if (_schema == null)
            throw new InvalidOperationException("Schema is required. Call WithSchema() before Build().");
        if (string.IsNullOrWhiteSpace(_policyId))
            throw new InvalidOperationException("PolicyId is required. Call WithPolicyId() before Build().");
        if (string.IsNullOrWhiteSpace(_planId))
            throw new InvalidOperationException("PlanId is required. Call WithPlanId() before Build().");

        // Create and return the context instance directly
        return new UniquenessValidationContext
        {
            MemberFields = _memberFields,
            Policy = _policy,
            Schema = _schema,
            PolicyId = _policyId,
            PlanId = _planId,
            ContractHolderId = _contractHolderId,
            Class = _memberClass,
            ContractHolderPolicyIds = _contractHolderPolicyIds ?? [],
            ShouldSkipContractHolderValidation = _shouldSkipContractHolderValidation,
            ContractHolderValidEndorsementIds = _contractHolderValidEndorsementIds,
            ContractHolderScopeEndorsements = _contractHolderScopeEndorsements,
            TenantId = _tenantId,
            FeatureFlags = _featureFlags,
            MemberId = _memberId,
            PolicyMemberId = _policyMemberId,
            ValidationRules = _validationRules,
            FieldValues = _fieldValues,
            EndorsementId = _endorsementId
        };
    }

    #endregion

}