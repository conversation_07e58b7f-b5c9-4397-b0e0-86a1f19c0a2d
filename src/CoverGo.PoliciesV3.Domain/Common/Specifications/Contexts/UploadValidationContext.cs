using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;

/// <summary>
/// Base class for upload validation contexts with strict validation requirements.
/// </summary>
public abstract record UploadValidationContext
{

    /// <summary>
    /// Protected constructor that enforces validation
    /// </summary>
    protected UploadValidationContext(MembersUploadFields membersFields, PolicyMemberFieldsSchema schema)
    {
        ArgumentNullException.ThrowIfNull(membersFields, nameof(membersFields));
        ArgumentNullException.ThrowIfNull(schema, nameof(schema));

        MembersFields = membersFields;
        Schema = schema;
    }

    /// <summary>
    /// The collection of member upload fields to be validated - guaranteed non-null
    /// </summary>
    public MembersUploadFields MembersFields { get; }

    /// <summary>
    /// The schema defining the structure and validation rules - guaranteed non-null
    /// </summary>
    public PolicyMemberFieldsSchema Schema { get; }

    /// <summary>
    /// Gets the total number of members in the upload - guaranteed >= 0
    /// </summary>
    public int MemberCount => MembersFields.Count;

    /// <summary>
    /// Checks if there are members to validate
    /// </summary>
    public bool HasMembersToValidate => MembersFields.HasMembers;
}
