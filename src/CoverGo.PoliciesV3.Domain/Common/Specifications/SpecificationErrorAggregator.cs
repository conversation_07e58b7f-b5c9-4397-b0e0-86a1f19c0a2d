using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications;

/// <summary>
/// Utility class for aggregating and managing validation errors across multiple specifications.
/// Provides helper methods for composite specifications to combine and organize validation results.
/// </summary>
public static class SpecificationErrorAggregator
{
    /// <summary>
    /// Merges multiple row-indexed error dictionaries into a single aggregated dictionary.
    /// Handles overlapping row indices by combining error lists.
    /// </summary>
    /// <param name="errorDictionaries">Multiple error dictionaries to merge</param>
    /// <returns>A single dictionary containing all errors organized by row index</returns>
    public static Dictionary<int, List<ValidationError>> MergeRowIndexedErrors(
        params Dictionary<int, List<ValidationError>>[] errorDictionaries)
    {
        // Validate that no null dictionaries are passed
        if (errorDictionaries.Any(d => d == null))
        {
            throw new ArgumentException("Error dictionaries cannot contain null values.", nameof(errorDictionaries));
        }

        var merged = new Dictionary<int, List<ValidationError>>();

        foreach (Dictionary<int, List<ValidationError>> errorDict in errorDictionaries)
        {
            foreach (KeyValuePair<int, List<ValidationError>> kvp in errorDict)
            {
                if (merged.TryGetValue(kvp.Key, out List<ValidationError>? existingErrors))
                {
                    existingErrors.AddRange(kvp.Value);
                }
                else
                {
                    merged[kvp.Key] = [.. kvp.Value];
                }
            }
        }

        return merged;
    }
}
