using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications;

/// <summary>
/// Immutable record containing pre-resolved external data for domain validation.
/// This pattern keeps infrastructure concerns out of the domain layer by having
/// the application layer resolve external dependencies and pass them to domain specifications.
/// </summary>
public record ResolvedValidationData
{
    // Feature flags
    public bool UseTheSamePlanForEmployeeAndDependents { get; init; }
    public bool OnlyApplyForSmeProducts { get; init; }
    public bool AllowMembersFromOtherContractHolders { get; init; }

    // Product information
    public IReadOnlySet<string>? AvailablePlans { get; init; }
    public bool IsProductSme { get; init; }

    // Policy context
    public IReadOnlyList<string> ContractHolderPolicyIds { get; init; } = [];

    public IReadOnlyList<string> ValidEndorsementIds { get; init; } = [];

    public IReadOnlyList<EndorsementId> ContractHolderScopeEndorsements { get; init; } = [];

    public bool IsPolicyV2 { get; init; }

    // Tenant context (for tenant-scoped validations)
    public string? TenantId { get; init; }

    // Member context (populated after file processing)
    public IReadOnlyDictionary<string, PolicyMember?> DependentMembersCache { get; init; } = new Dictionary<string, PolicyMember?>();
    public IReadOnlySet<string> ExistingIndividualIds { get; init; } = new HashSet<string>();

    // Batch-resolved member data for efficient lookups (NEW)
    /// <summary>
    /// Map of member ID to existing policy member for duplicate detection and state validation.
    /// Key: Member ID, Value: Existing PolicyMember (null if not found).
    /// Used by MemberIdMustFollowBusinessRulesSpecification.
    /// </summary>
    public IReadOnlyDictionary<string, PolicyMember?> ExistingPolicyMembers { get; init; } = new Dictionary<string, PolicyMember?>();

    /// <summary>
    /// Map of member ID to validation states for contract holder validation.
    /// Key: Member ID, Value: List of PolicyMembers across contract holder policies.
    /// Used for cross-contract holder validation rules.
    /// </summary>
    public IReadOnlyDictionary<string, IReadOnlyList<PolicyMember>> MemberValidationStates { get; init; } = new Dictionary<string, IReadOnlyList<PolicyMember>>();

    /// <summary>
    /// Map of member ID to individual existence status for efficient lookups.
    /// Key: Member ID, Value: Whether individual exists in the system.
    /// Used by MemberIdMustFollowBusinessRulesSpecification.
    /// </summary>
    public IReadOnlyDictionary<string, bool> IndividualExistenceMap { get; init; } = new Dictionary<string, bool>();
}