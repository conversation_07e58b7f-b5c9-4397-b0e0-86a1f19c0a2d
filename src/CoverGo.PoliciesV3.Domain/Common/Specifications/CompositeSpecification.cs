using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications;

/// <summary>
/// Helper class to reduce duplication in composite specifications
/// </summary>
internal static class SpecificationErrorCombiner
{
    public static List<ValidationError> CombineErrors(Result left, Result right)
    {
        var errors = new List<ValidationError>();
        if (left.IsFailure) errors.AddRange(left.Errors);
        if (right.IsFailure) errors.AddRange(right.Errors);
        return errors;
    }
}

/// <summary>
/// Base class for creating asynchronous composite specifications that combine multiple business rules.
/// Provides logical operations (AND, OR, NOT) for composing complex async business rule scenarios.
/// This follows the Specification Pattern for Domain-Driven Design.
/// </summary>
/// <typeparam name="T">The type of entity this specification evaluates</typeparam>
public abstract class CompositeSpecification<T> : ISpecification<T>
{
    /// <summary>
    /// Asynchronously evaluates the specification against the given entity.
    /// </summary>
    public abstract Task<Result> IsSatisfiedBy(T entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a human-readable name for this business rule specification.
    /// </summary>
    public abstract string BusinessRuleName { get; }

    /// <summary>
    /// Gets a description of what this specification validates.
    /// </summary>
    public abstract string Description { get; }

    /// <summary>
    /// Combines this specification with another using logical AND.
    /// Both specifications must be satisfied for the result to be successful.
    /// </summary>
    public ISpecification<T> And(ISpecification<T> other) => new AsyncAndSpecification<T>(this, other);

    /// <summary>
    /// Combines this specification with another using logical OR.
    /// Either specification can be satisfied for the result to be successful.
    /// </summary>
    public ISpecification<T> Or(ISpecification<T> other) => new AsyncOrSpecification<T>(this, other);

    /// <summary>
    /// Creates a specification that represents the logical NOT of this specification.
    /// The result will be successful when this specification is not satisfied.
    /// </summary>
    public ISpecification<T> Not() => new AsyncNotSpecification<T>(this);
}

/// <summary>
/// Represents a logical AND combination of two async specifications.
/// </summary>
/// <typeparam name="T">The type of entity this specification evaluates</typeparam>
internal class AsyncAndSpecification<T>(ISpecification<T> left, ISpecification<T> right) : CompositeSpecification<T>
{
    public override string BusinessRuleName => $"{left.BusinessRuleName} AND {right.BusinessRuleName}";
    public override string Description => $"Both conditions must be met: ({left.Description}) AND ({right.Description})";

    public override async Task<Result> IsSatisfiedBy(T entity, CancellationToken cancellationToken = default)
    {
        Result[] results = await Task.WhenAll(
            left.IsSatisfiedBy(entity, cancellationToken),
            right.IsSatisfiedBy(entity, cancellationToken));

        (Result leftResult, Result rightResult) = (results[0], results[1]);

        return leftResult.IsSuccess && rightResult.IsSuccess
            ? Result.Success()
            : Result.Failure(SpecificationErrorCombiner.CombineErrors(leftResult, rightResult));
    }
}

/// <summary>
/// Represents a logical OR combination of two async specifications.
/// </summary>
/// <typeparam name="T">The type of entity this specification evaluates</typeparam>
internal class AsyncOrSpecification<T>(ISpecification<T> left, ISpecification<T> right) : CompositeSpecification<T>
{
    public override string BusinessRuleName => $"{left.BusinessRuleName} OR {right.BusinessRuleName}";
    public override string Description => $"Either condition can be met: ({left.Description}) OR ({right.Description})";

    public override async Task<Result> IsSatisfiedBy(T entity, CancellationToken cancellationToken = default)
    {
        Result[] results = await Task.WhenAll(
            left.IsSatisfiedBy(entity, cancellationToken),
            right.IsSatisfiedBy(entity, cancellationToken));

        (Result leftResult, Result rightResult) = (results[0], results[1]);

        // If either is successful, the OR is satisfied
        if (leftResult.IsSuccess || rightResult.IsSuccess)
            return Result.Success();

        // Both failed, combine all errors
        var errors = new List<ValidationError>();
        errors.AddRange(leftResult.Errors);
        errors.AddRange(rightResult.Errors);

        return Result.Failure(errors);
    }
}

/// <summary>
/// Represents a logical NOT of an async specification.
/// </summary>
/// <typeparam name="T">The type of entity this specification evaluates</typeparam>
internal class AsyncNotSpecification<T>(ISpecification<T> specification) : CompositeSpecification<T>
{
    public override string BusinessRuleName => $"NOT {specification.BusinessRuleName}";
    public override string Description => $"The opposite of: {specification.Description}";

    public override async Task<Result> IsSatisfiedBy(T entity, CancellationToken cancellationToken = default)
    {
        Result result = await specification.IsSatisfiedBy(entity, cancellationToken);

        if (result.IsFailure)
            return Result.Success();

        ValidationError error = Errors.NotSatisfied("specification", BusinessRuleName, specification.BusinessRuleName);

        return Result.Failure(error);
    }
}
