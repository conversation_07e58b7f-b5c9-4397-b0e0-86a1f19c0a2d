using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Common.Specifications;

/// <summary>
/// Result type for batch validation operations that provides validation outcomes
/// with row-indexed error tracking. Used by composite specifications to aggregate
/// validation results from multiple atomic specifications.
/// </summary>
public class BatchValidationResult
{
    /// <summary>
    /// Gets the number of items that passed validation.
    /// </summary>
    public int ValidCount { get; init; }

    /// <summary>
    /// Gets the number of items that failed validation.
    /// </summary>
    public int InvalidCount { get; init; }

    /// <summary>
    /// Gets validation errors indexed by row number (0-based).
    /// Key: Row index, Value: List of validation errors for that row.
    /// </summary>
    public Dictionary<int, List<ValidationError>> RowErrors { get; init; } = [];

    /// <summary>
    /// Gets whether the overall batch validation was successful (no errors).
    /// </summary>
    public bool IsValid => InvalidCount == 0;

    /// <summary>
    /// Creates a successful batch validation result.
    /// </summary>
    /// <param name="validCount">Number of items that passed validation</param>
    /// <returns>A successful BatchValidationResult</returns>
    public static BatchValidationResult Success(int validCount) =>
        new() { ValidCount = validCount, InvalidCount = 0 };

    /// <summary>
    /// Creates a batch validation result with validation errors.
    /// </summary>
    /// <param name="validCount">Number of items that passed validation</param>
    /// <param name="invalidCount">Number of items that failed validation</param>
    /// <param name="errors">Row-indexed validation errors</param>
    /// <returns>A BatchValidationResult with errors</returns>
    public static BatchValidationResult WithErrors(
        int validCount,
        int invalidCount,
        Dictionary<int, List<ValidationError>> errors) =>
        new() { ValidCount = validCount, InvalidCount = invalidCount, RowErrors = errors };
}
