namespace CoverGo.PoliciesV3.Domain.Common.Enums;

/// <summary>
/// Represents currency codes following ISO 4217 standard
/// </summary>
public record CurrencyCode(string Value) : ValueObject<string>(Value)
{
    public static CurrencyCode USD => new("USD");
    public static CurrencyCode EUR => new("EUR");
    public static CurrencyCode GBP => new("GBP");
    public static CurrencyCode JPY => new("JPY");
    public static CurrencyCode CNY => new("CNY");
    public static CurrencyCode HKD => new("HKD");
    public static CurrencyCode SGD => new("SGD");
    public static CurrencyCode AUD => new("AUD");
    public static CurrencyCode CAD => new("CAD");
    public static CurrencyCode CHF => new("CHF");
    public static CurrencyCode THB => new("THB");
    public static CurrencyCode MYR => new("MYR");
    public static CurrencyCode IDR => new("IDR");
    public static CurrencyCode PHP => new("PHP");
    public static CurrencyCode VND => new("VND");
    public static CurrencyCode INR => new("INR");
    public static CurrencyCode KRW => new("KRW");
    public static CurrencyCode TWD => new("TWD");
}
