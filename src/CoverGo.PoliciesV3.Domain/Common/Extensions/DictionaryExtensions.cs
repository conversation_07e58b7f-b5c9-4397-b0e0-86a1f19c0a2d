namespace CoverGo.PoliciesV3.Domain.Common.Extensions;

/// <summary>
/// Extension methods for dictionary operations in the Domain layer
/// Used for safe value retrieval from dictionaries
/// </summary>
public static class DictionaryExtensions
{
    /// <summary>
    /// Safely retrieves a value from a dictionary, returning a default value if the key doesn't exist
    /// </summary>
    /// <typeparam name="TKey">The type of the dictionary keys</typeparam>
    /// <typeparam name="TValue">The type of the dictionary values</typeparam>
    /// <param name="dictionary">The dictionary to search</param>
    /// <param name="key">The key to look for</param>
    /// <param name="defaultValue">The default value to return if key is not found</param>
    /// <returns>The value if found, otherwise the default value</returns>
    public static TValue TryGetValueOrDefault<TKey, TValue>(
        this IDictionary<TKey, TValue> dictionary,
        TKey key,
        TValue defaultValue = default!) => dictionary.TryGetValue(key, out TValue? value)
            ? value
            : defaultValue;

    /// <summary>
    /// Safely retrieves a value from a read-only dictionary, returning a default value if the key doesn't exist
    /// </summary>
    /// <typeparam name="TKey">The type of the dictionary keys</typeparam>
    /// <typeparam name="TValue">The type of the dictionary values</typeparam>
    /// <param name="dictionary">The read-only dictionary to search</param>
    /// <param name="key">The key to look for</param>
    /// <param name="defaultValue">The default value to return if key is not found</param>
    /// <returns>The value if found, otherwise the default value</returns>
    public static TValue TryGetValueOrDefault<TKey, TValue>(
        this IReadOnlyDictionary<TKey, TValue> dictionary,
        TKey key,
        TValue defaultValue = default!) => dictionary.TryGetValue(key, out TValue? value)
            ? value
            : defaultValue;
}
