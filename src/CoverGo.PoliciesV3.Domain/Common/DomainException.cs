namespace CoverGo.PoliciesV3.Domain.Common;

/// <summary>
/// Base class for all domain exceptions that represent business rule violations or domain errors.
/// </summary>
public abstract class DomainException : Exception
{
    protected DomainException(string message) : base(message)
    {
    }

    protected DomainException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// Gets the error code that categorizes this domain exception.
    /// </summary>
    public abstract string Code { get; }
}
