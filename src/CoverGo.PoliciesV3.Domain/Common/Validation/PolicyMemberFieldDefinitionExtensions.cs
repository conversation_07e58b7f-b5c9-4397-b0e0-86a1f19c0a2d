using CoverGo.PoliciesV3.Domain.CustomFields;

namespace CoverGo.PoliciesV3.Domain.Common.Validation;

/// <summary>
/// Extension methods for PolicyMemberFieldDefinition to integrate with the new validation system.
/// These methods provide a convenient way to create validation errors for field definitions.
/// </summary>
public static class PolicyMemberFieldDefinitionExtensions
{
    /// <summary>
    /// Creates a required field validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <returns>A validation error indicating the field is required</returns>
    public static ValidationError CreateRequiredError(this PolicyMemberFieldDefinition field) => Errors.Required(field.Name, field.GetFullLabel());

    /// <summary>
    /// Creates an invalid format validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <returns>A validation error indicating invalid format</returns>
    public static ValidationError CreateInvalidFormatError(this PolicyMemberFieldDefinition field) => Errors.InvalidFormat(field.Name, field.GetFullLabel());

    /// <summary>
    /// Creates an invalid type validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <returns>A validation error indicating invalid type</returns>
    public static ValidationError CreateInvalidTypeError(this PolicyMemberFieldDefinition field) => Errors.InvalidType(field.Name, field.GetFullLabel());

    /// <summary>
    /// Creates a unique violation validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="scope">The scope where uniqueness is required (default: "upload")</param>
    /// <returns>A validation error indicating unique violation</returns>
    public static ValidationError CreateUniqueViolationError(this PolicyMemberFieldDefinition field, string scope = "upload") => Errors.UniqueViolation(field.Name, field.GetFullLabel(), scope);

    /// <summary>
    /// Creates an invalid option validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="availableOptions">The available options</param>
    /// <returns>A validation error indicating invalid option</returns>
    public static ValidationError CreateInvalidOptionError(this PolicyMemberFieldDefinition field, IEnumerable<string> availableOptions) => Errors.InvalidOption(field.Name, availableOptions, field.GetFullLabel());

    /// <summary>
    /// Creates a pattern mismatch validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="pattern">The pattern that was not matched</param>
    /// <returns>A validation error indicating pattern mismatch</returns>
    public static ValidationError CreatePatternMismatchError(this PolicyMemberFieldDefinition field, string pattern) => Errors.PatternMismatch(field.Name, pattern, field.GetFullLabel());

    /// <summary>
    /// Creates an invalid string validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="validation">The validation rule that failed</param>
    /// <returns>A validation error indicating invalid string</returns>
    public static ValidationError CreateInvalidStringError(this PolicyMemberFieldDefinition field, string validation) => Errors.InvalidString(field.Name, validation, field.GetFullLabel());

    /// <summary>
    /// Creates an invalid number validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <returns>A validation error indicating invalid number</returns>
    public static ValidationError CreateInvalidNumberError(this PolicyMemberFieldDefinition field) => Errors.InvalidNumber(field.Name, field.GetFullLabel());

    /// <summary>
    /// Creates an out of range validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="minValue">The minimum allowed value (optional)</param>
    /// <param name="maxValue">The maximum allowed value (optional)</param>
    /// <returns>A validation error indicating value is out of range</returns>
    public static ValidationError CreateOutOfRangeError(this PolicyMemberFieldDefinition field, object? minValue = null, object? maxValue = null) => Errors.OutOfRange(field.Name, minValue, maxValue, field.GetFullLabel());

    /// <summary>
    /// Creates a too long validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="maxLength">The maximum allowed length</param>
    /// <returns>A validation error indicating the value is too long</returns>
    public static ValidationError CreateTooLongError(this PolicyMemberFieldDefinition field, int maxLength) => Errors.TooLong(field.Name, maxLength, field.GetFullLabel());

    /// <summary>
    /// Creates a too short validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="minLength">The minimum required length</param>
    /// <returns>A validation error indicating the value is too short</returns>
    public static ValidationError CreateTooShortError(this PolicyMemberFieldDefinition field, int minLength) => Errors.TooShort(field.Name, minLength, field.GetFullLabel());

    /// <summary>
    /// Creates a not allowed validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <returns>A validation error indicating the field is not allowed</returns>
    public static ValidationError CreateNotAllowedError(this PolicyMemberFieldDefinition field) => Errors.NotAllowed(field.Name, field.GetFullLabel());

    /// <summary>
    /// Creates a member not found validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="memberId">The member ID that was not found</param>
    /// <returns>A validation error indicating member not found</returns>
    public static ValidationError CreateMemberNotFoundError(this PolicyMemberFieldDefinition field, string memberId) => Errors.MemberNotFound(field.Name, memberId, field.GetFullLabel());

    /// <summary>
    /// Creates a member ID taken validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="memberId">The member ID that is taken</param>
    /// <param name="existingPolicyMemberId">The existing policy member ID</param>
    /// <returns>A validation error indicating member ID is taken</returns>
    public static ValidationError CreateMemberIdTakenError(this PolicyMemberFieldDefinition field, string memberId, string existingPolicyMemberId) => Errors.MemberIdTaken(field.Name, memberId, existingPolicyMemberId, field.GetFullLabel());

    /// <summary>
    /// Creates a member not in contract holder validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="memberId">The member ID</param>
    /// <param name="policyId">The policy ID</param>
    /// <returns>A validation error indicating member is not in contract holder</returns>
    public static ValidationError CreateMemberNotInContractHolderError(this PolicyMemberFieldDefinition field, string memberId, string policyId) => Errors.MemberNotInContractHolder(field.Name, memberId, policyId, field.GetFullLabel());

    /// <summary>
    /// Creates an invalid plan ID validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="planId">The invalid plan ID</param>
    /// <param name="availablePlans">The available plan IDs</param>
    /// <param name="productId">The product ID (optional)</param>
    /// <returns>A validation error indicating invalid plan ID</returns>
    public static ValidationError CreateInvalidPlanIdError(this PolicyMemberFieldDefinition field, string planId, IEnumerable<string> availablePlans, string? productId = null) => Errors.InvalidPlanId(field.Name, planId, availablePlans, field.GetFullLabel(), productId);

    /// <summary>
    /// Creates a dependent plan mismatch validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <returns>A validation error indicating dependent plan mismatch</returns>
    public static ValidationError CreateDependentPlanMismatchError(this PolicyMemberFieldDefinition field) => Errors.DependentPlanMismatch(field.Name, field.GetFullLabel());

    /// <summary>
    /// Creates a dependent plan error validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <returns>A validation error indicating dependent plan error</returns>
    public static ValidationError CreateDependentPlanError(this PolicyMemberFieldDefinition field) => Errors.DependentPlanMismatch(field.Name, field.GetFullLabel());

    /// <summary>
    /// Creates an age too low validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="minAge">The minimum required age</param>
    /// <returns>A validation error indicating age is too low</returns>
    public static ValidationError CreateAgeTooLowError(this PolicyMemberFieldDefinition field, int minAge) => Errors.AgeTooLow(field.Name, minAge, field.GetFullLabel());

    /// <summary>
    /// Creates an age too high validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="maxAge">The maximum allowed age</param>
    /// <returns>A validation error indicating age is too high</returns>
    public static ValidationError CreateAgeTooHighError(this PolicyMemberFieldDefinition field, int maxAge) => Errors.AgeTooHigh(field.Name, maxAge, field.GetFullLabel());
}
