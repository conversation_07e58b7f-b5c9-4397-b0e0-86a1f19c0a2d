using CoverGo.PoliciesV3.Domain.Common.Constants;

namespace CoverGo.PoliciesV3.Domain.Common.Validation;

/// <summary>
/// Provides simple factory methods for creating common validation errors.
/// This class offers the most concise API for creating validation errors.
/// </summary>
public static class Errors
{
    #region Field Validation Errors

    /// <summary>
    /// Creates a required field validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError Required(string propertyPath, string? propertyLabel = null) =>
        new(ErrorCodes.Required, propertyPath, propertyLabel);

    /// <summary>
    /// Creates an invalid format field validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError InvalidFormat(string propertyPath, string? propertyLabel = null) =>
        new(ErrorCodes.InvalidFormat, propertyPath, propertyLabel);

    /// <summary>
    /// Creates an invalid type field validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError InvalidType(string propertyPath, string? propertyLabel = null) =>
        new(ErrorCodes.InvalidType, propertyPath, propertyLabel);

    /// <summary>
    /// Creates a unique violation field validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <param name="scope">The scope where uniqueness is required (default: "upload")</param>
    /// <returns>A validation error</returns>
    public static ValidationError UniqueViolation(string propertyPath, string? propertyLabel = null, string scope = ValidationConstants.DefaultValues.DefaultScope) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel).UniqueViolation(scope);

    /// <summary>
    /// Creates an invalid option field validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="availableOptions">The available options</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError InvalidOption(string propertyPath, IEnumerable<string> availableOptions, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel).InvalidOption(availableOptions);

    /// <summary>
    /// Creates a pattern mismatch field validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="pattern">The pattern that was not matched</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError PatternMismatch(string propertyPath, string pattern, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel).PatternMismatch(pattern);

    /// <summary>
    /// Creates an invalid string field validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="validation">The validation rule that failed</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError InvalidString(string propertyPath, string validation, string? propertyLabel = null)
    {
        // Extract pattern from validation rule for context
        string pattern = validation.StartsWith(ValidationConstants.RegexPatterns.RegexPrefix)
            ? validation[ValidationConstants.RegexPatterns.RegexPrefix.Length..]
            : validation.Trim(ValidationConstants.RegexPatterns.PatternDelimiter);

        return ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.Pattern, pattern)
            .Build(ErrorCodes.InvalidString);
    }

    /// <summary>
    /// Creates an invalid number field validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError InvalidNumber(string propertyPath, string? propertyLabel = null) =>
        new(ErrorCodes.InvalidNumber, propertyPath, propertyLabel);

    /// <summary>
    /// Creates an out of range field validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="minValue">The minimum allowed value (optional)</param>
    /// <param name="maxValue">The maximum allowed value (optional)</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError OutOfRange(string propertyPath, object? minValue = null, object? maxValue = null, string? propertyLabel = null)
    {
        // Validate range if both values are provided and are comparable
        if (minValue != null && maxValue != null && minValue is IComparable minComp && maxValue is IComparable maxComp && minComp.CompareTo(maxComp) > 0)
        {
            throw new ArgumentException(ValidationConstants.ExceptionMessages.MinValueGreaterThanMaxValue, nameof(minValue));
        }

        return ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel).OutOfRange(minValue, maxValue);
    }

    /// <summary>
    /// Creates a too long field validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="maxLength">The maximum allowed length</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError TooLong(string propertyPath, int maxLength, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel).TooLong(maxLength);

    /// <summary>
    /// Creates a too short field validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="minLength">The minimum required length</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError TooShort(string propertyPath, int minLength, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel).TooShort(minLength);

    /// <summary>
    /// Creates a not allowed field validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError NotAllowed(string propertyPath, string? propertyLabel = null) =>
        new(ErrorCodes.NotAllowed, propertyPath, propertyLabel);

    #endregion

    #region Member Validation Errors

    /// <summary>
    /// Creates a member not found validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the member ID field</param>
    /// <param name="memberId">The member ID that was not found</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError MemberNotFound(string propertyPath, string memberId, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel).MemberNotFound(memberId);

    /// <summary>
    /// Creates a member ID taken validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the member ID field</param>
    /// <param name="memberId">The member ID that is taken</param>
    /// <param name="existingPolicyMemberId">The existing policy member ID</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError MemberIdTaken(string propertyPath, string memberId, string existingPolicyMemberId, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel).MemberIdTaken(memberId, existingPolicyMemberId);

    /// <summary>
    /// Creates a member not in contract holder validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the member ID field</param>
    /// <param name="memberId">The member ID</param>
    /// <param name="policyId">The policy ID</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError MemberNotInContractHolder(string propertyPath, string memberId, string policyId, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel).MemberNotInContractHolder(memberId, policyId);

    /// <summary>
    /// Creates a member processing failed validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the member field</param>
    /// <param name="rowIndex">The row index where processing failed</param>
    /// <param name="memberId">The member ID (optional)</param>
    /// <param name="errorMessage">The error message</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError MemberProcessingFailed(string propertyPath, int rowIndex, string? memberId, string errorMessage, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.RowIndex, rowIndex)
            .WithContext(ValidationConstants.ContextKeys.MemberId, memberId)
            .WithContext(ValidationConstants.Labels.ErrorMessage, errorMessage)
            .Build(ErrorCodes.MemberProcessingFailed);

    #endregion

    #region Age Validation Errors

    /// <summary>
    /// Creates an age too low validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the age field</param>
    /// <param name="minAge">The minimum required age</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError AgeTooLow(string propertyPath, int minAge, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel).AgeTooLow(minAge);

    /// <summary>
    /// Creates an age too high validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the age field</param>
    /// <param name="maxAge">The maximum allowed age</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError AgeTooHigh(string propertyPath, int maxAge, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel).AgeTooHigh(maxAge);

    /// <summary>
    /// Creates an employee minimum age validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the age field</param>
    /// <param name="minAge">The minimum required age</param>
    /// <param name="actualAge">The actual age of the employee</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError EmployeeMinAge(string propertyPath, int minAge, int actualAge, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.MinAge, minAge)
            .WithContext(ValidationConstants.ContextKeys.ActualAge, actualAge)
            .Build(ErrorCodes.EmployeeMinAge);

    /// <summary>
    /// Creates an employee maximum age validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the age field</param>
    /// <param name="maxAge">The maximum allowed age</param>
    /// <param name="actualAge">The actual age of the employee</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError EmployeeMaxAge(string propertyPath, int maxAge, int actualAge, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.MaxAge, maxAge)
            .WithContext(ValidationConstants.ContextKeys.ActualAge, actualAge)
            .Build(ErrorCodes.EmployeeMaxAge);

    /// <summary>
    /// Creates a child minimum days validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the age field</param>
    /// <param name="minDays">The minimum required days</param>
    /// <param name="actualDays">The actual days since birth</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError ChildMinDays(string propertyPath, int minDays, int actualDays, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.MinDays, minDays)
            .WithContext(ValidationConstants.ContextKeys.ActualDays, actualDays)
            .Build(ErrorCodes.ChildMinDays);

    /// <summary>
    /// Creates a spouse minimum age validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the age field</param>
    /// <param name="minAge">The minimum required age</param>
    /// <param name="actualAge">The actual age of the spouse</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError SpouseMinAge(string propertyPath, int minAge, int actualAge, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.MinAge, minAge)
            .WithContext(ValidationConstants.ContextKeys.ActualAge, actualAge)
            .Build(ErrorCodes.SpouseMinAge);

    #endregion

    #region Plan Validation Errors

    /// <summary>
    /// Creates an invalid plan ID validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the plan ID field</param>
    /// <param name="planId">The invalid plan ID</param>
    /// <param name="availablePlans">The available plan IDs</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <param name="productId">The product ID (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError InvalidPlanId(string propertyPath, string planId, IEnumerable<string> availablePlans, string? propertyLabel = null, string? productId = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel).InvalidPlanId(planId, availablePlans, productId);

    /// <summary>
    /// Creates a dependent plan mismatch validation error (legacy overload).
    /// </summary>
    /// <param name="propertyPath">The path to the plan ID field</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError DependentPlanMismatch(string propertyPath, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel).DependentPlanMismatch();

    #endregion

    #region Upload Content Errors

    /// <summary>
    /// Creates an empty file validation error.
    /// </summary>
    /// <returns>A validation error</returns>
    public static ValidationError EmptyFile() =>
        new(ErrorCodes.EmptyFile, ValidationConstants.PropertyPaths.File, ValidationConstants.Labels.UploadFile);

    /// <summary>
    /// Creates an empty file validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError EmptyFile(string propertyPath, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .Build(ErrorCodes.EmptyFile);

    /// <summary>
    /// Creates a missing columns validation error.
    /// </summary>
    /// <param name="missingColumns">The missing column names</param>
    /// <returns>A validation error</returns>
    public static ValidationError MissingColumns(IEnumerable<string> missingColumns) =>
        ValidationErrorBuilder.For(ValidationConstants.PropertyPaths.Columns).WithLabel(ValidationConstants.Labels.Columns)
            .WithContext(ValidationConstants.ContextKeys.MissingColumns, missingColumns)
            .Build(ErrorCodes.MissingColumns);

    /// <summary>
    /// Creates a missing mandatory columns validation error.
    /// </summary>
    /// <param name="missingColumns">The missing mandatory column names</param>
    /// <returns>A validation error</returns>
    public static ValidationError MissingMandatoryColumns(IEnumerable<string> missingColumns) =>
        ValidationErrorBuilder.For(ValidationConstants.PropertyPaths.Columns).WithLabel(ValidationConstants.Labels.Columns)
            .WithContext(ValidationConstants.ContextKeys.MissingColumns, missingColumns)
            .Build(ErrorCodes.MissingMandatoryColumns);

    /// <summary>
    /// Creates an extra columns validation error.
    /// </summary>
    /// <param name="extraColumns">The extra column names</param>
    /// <returns>A validation error</returns>
    public static ValidationError ExtraColumns(IEnumerable<string> extraColumns) =>
        ValidationErrorBuilder.For(ValidationConstants.PropertyPaths.Columns).WithLabel(ValidationConstants.Labels.Columns)
            .WithContext(ValidationConstants.ContextKeys.ExtraColumns, extraColumns)
            .Build(ErrorCodes.ExtraColumns);

    /// <summary>
    /// Creates a one-of required validation error.
    /// </summary>
    /// <param name="requiredFields">The fields where at least one is required</param>
    /// <returns>A validation error</returns>
    public static ValidationError OneOfRequired(IEnumerable<string> requiredFields) =>
        ValidationErrorBuilder.For(ValidationConstants.PropertyPaths.Fields).WithLabel(ValidationConstants.Labels.Fields)
            .WithContext(ValidationConstants.ContextKeys.RequiredFields, requiredFields)
            .Build(ErrorCodes.MissingOneOfMandatoryColumns);

    /// <summary>
    /// Creates a one-of required validation error with field errors context.
    /// </summary>
    /// <param name="propertyPath">The property path for the validation error</param>
    /// <param name="propertyLabel">The display label for the field group</param>
    /// <param name="requiredFields">The fields where at least one is required</param>
    /// <param name="fieldErrors">Individual field validation errors for context</param>
    /// <returns>A validation error</returns>
    public static ValidationError OneOfRequired(
        string propertyPath,
        string propertyLabel,
        IEnumerable<string> requiredFields,
        IEnumerable<ValidationError> fieldErrors) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.RequiredFields, requiredFields)
            .WithContext(ValidationConstants.ContextKeys.FieldErrors, fieldErrors.ToArray())
            .Build(ErrorCodes.OneOfRequired);

    #endregion

    #region General Errors

    /// <summary>
    /// Creates an unexpected error validation error.
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A validation error</returns>
    public static ValidationError UnexpectedError(string errorMessage) =>
        ValidationErrorBuilder.For(ValidationConstants.PropertyPaths.Operation)
            .WithLabel(ValidationConstants.Labels.Operation)
            .WithContext(ValidationConstants.Labels.ErrorMessage, errorMessage)
            .Build(ErrorCodes.UnexpectedError);

    /// <summary>
    /// Creates a file too large validation error.
    /// </summary>
    /// <param name="actualSize">The actual file size in bytes</param>
    /// <param name="maxSize">The maximum allowed file size in bytes</param>
    /// <returns>A validation error</returns>
    public static ValidationError FileTooLarge(long actualSize, long maxSize) =>
        ValidationErrorBuilder.For(ValidationConstants.PropertyPaths.File)
            .WithLabel(ValidationConstants.Labels.UploadFile)
            .WithContext("ActualSize", FormatFileSize(actualSize))
            .WithContext("MaxSize", FormatFileSize(maxSize))
            .Build(ErrorCodes.FileTooLarge);

    /// <summary>
    /// Formats file size in bytes to human-readable format
    /// </summary>
    /// <param name="bytes">File size in bytes</param>
    /// <returns>Formatted file size string</returns>
    private static string FormatFileSize(long bytes)
    {
        const long KB = 1024;
        const long MB = KB * 1024;
        const long GB = MB * 1024;

        return bytes switch
        {
            >= GB => $"{bytes / (double)GB:F2} GB",
            >= MB => $"{bytes / (double)MB:F2} MB",
            >= KB => $"{bytes / (double)KB:F2} KB",
            _ => $"{bytes} bytes"
        };
    }

    /// <summary>
    /// Creates an invalid file row validation error.
    /// </summary>
    /// <param name="rowIndex">The row index that is invalid</param>
    /// <param name="expectedColumns">The expected number of columns</param>
    /// <param name="actualColumns">The actual number of columns</param>
    /// <returns>A validation error</returns>
    public static ValidationError InvalidFileRow(int rowIndex, int expectedColumns, int actualColumns) =>
        ValidationErrorBuilder.For(ValidationConstants.PropertyPaths.File).WithLabel(ValidationConstants.Labels.UploadFile)
            .WithContext(ValidationConstants.ContextKeys.RowIndex, rowIndex)
            .WithContext(ValidationConstants.ContextKeys.ExpectedColumns, expectedColumns)
            .WithContext(ValidationConstants.ContextKeys.ActualColumns, actualColumns)
            .Build(ErrorCodes.InvalidRow);

    /// <summary>
    /// Creates an unsupported file type validation error for a specific property.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError UnsupportedFileTypeForProperty(string propertyPath, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .Build(ErrorCodes.UnsupportedFileType);

    /// <summary>
    /// Creates a no columns found validation error.
    /// </summary>
    /// <returns>A validation error</returns>
    public static ValidationError NoColumns() =>
        new(ErrorCodes.NoColumn, ValidationConstants.PropertyPaths.File, ValidationConstants.Labels.UploadFile);

    /// <summary>
    /// Creates a no column validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <param name="message">Additional context message (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError NoColumn(string propertyPath, string? propertyLabel = null, string? message = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.Message, message)
            .Build(ErrorCodes.NoColumn);

    /// <summary>
    /// Creates a no members found validation error.
    /// </summary>
    /// <returns>A validation error</returns>
    public static ValidationError NoMembers() =>
        new(ErrorCodes.NoMember, ValidationConstants.PropertyPaths.File, ValidationConstants.Labels.UploadFile);

    /// <summary>
    /// Creates an invalid row validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError InvalidRow(string propertyPath, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .Build(ErrorCodes.InvalidRow);

    /// <summary>
    /// Creates an invalid XLSX file validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError InvalidXlsxFile(string propertyPath, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .Build(ErrorCodes.InvalidXlsxFile);

    /// <summary>
    /// Creates an effective date outside policy dates validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the effective date field</param>
    /// <param name="policyId">The policy ID</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError EffectiveDateOutsidePolicyDates(string propertyPath, string policyId, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.PolicyId, policyId)
            .Build(ErrorCodes.EffectiveDateOutsidePolicyDates);

    #endregion

    #region General Entity Errors

    /// <summary>
    /// Creates a general not found validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="value">The value that was not found</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError NotFound(string propertyPath, string value, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.Value, value)
            .Build(ErrorCodes.NotFound);

    /// <summary>
    /// Creates an invalid state validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="message">The error message describing the invalid state</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError InvalidState(string propertyPath, string message, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.Message, message)
            .Build(ErrorCodes.InvalidState);

    /// <summary>
    /// Creates a policy member upload not found validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="uploadId">The upload ID that was not found</param>
    /// <param name="propertyLabel">The human-readable field label (optional)</param>
    /// <returns>A validation error</returns>
    public static ValidationError PolicyMemberUploadNotFound(string propertyPath, string uploadId, string? propertyLabel = null) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.UploadId, uploadId)
            .Build(ErrorCodes.PolicyMemberUploadNotFound);

    /// <summary>
    /// Creates a not satisfied specification validation error.
    /// </summary>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="propertyLabel">The human-readable field label</param>
    /// <param name="originalRule">The original rule that was not satisfied</param>
    /// <returns>A validation error</returns>
    public static ValidationError NotSatisfied(string propertyPath, string propertyLabel, string originalRule) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.OriginalRule, originalRule)
            .Build(ErrorCodes.NotSatisfied);

    /// <summary>
    /// Creates a domain exception validation error.
    /// </summary>
    /// <param name="errorCode">The domain exception error code</param>
    /// <param name="propertyPath">The path to the field</param>
    /// <param name="propertyLabel">The human-readable field label</param>
    /// <param name="message">The exception message</param>
    /// <returns>A validation error</returns>
    public static ValidationError DomainException(string errorCode, string propertyPath, string propertyLabel, string message) =>
        ValidationErrorBuilder.For(propertyPath).WithLabel(propertyLabel)
            .WithContext(ValidationConstants.ContextKeys.Message, message)
            .Build(errorCode);

    #endregion
}
