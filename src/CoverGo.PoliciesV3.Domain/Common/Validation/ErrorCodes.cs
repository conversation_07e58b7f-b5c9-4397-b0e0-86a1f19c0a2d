namespace CoverGo.PoliciesV3.Domain.Common.Validation;

/// <summary>
/// Standardized error codes for the validation system.
/// These codes are used to categorize validation errors and generate appropriate messages.
/// </summary>
public static class ErrorCodes
{
    #region Field Validation

    /// <summary>
    /// Field is required but was not provided or is empty
    /// </summary>
    public const string Required = "REQUIRED";

    /// <summary>
    /// Field value has invalid format or type
    /// </summary>
    public const string InvalidFormat = "INVALID_FORMAT";

    /// <summary>
    /// Field value has invalid type (specific type validation error)
    /// </summary>
    public const string InvalidType = "INVALID_TYPE";

    /// <summary>
    /// Field value is not unique when uniqueness is required
    /// </summary>
    public const string UniqueViolation = "UNIQUE_VIOLATION";

    /// <summary>
    /// Field value is not one of the allowed options
    /// </summary>
    public const string InvalidOption = "INVALID_OPTION";

    /// <summary>
    /// Field value does not match the required pattern
    /// </summary>
    public const string PatternMismatch = "PATTERN_MISMATCH";

    /// <summary>
    /// String field value fails validation (regex, format, etc.)
    /// </summary>
    public const string InvalidString = "INVALID_STRING";

    /// <summary>
    /// Number field value is not a valid number
    /// </summary>
    public const string InvalidNumber = "INVALID_NUMBER";

    /// <summary>
    /// Field value is out of the allowed range
    /// </summary>
    public const string OutOfRange = "OUT_OF_RANGE";

    /// <summary>
    /// Field value exceeds maximum length
    /// </summary>
    public const string TooLong = "TOO_LONG";

    /// <summary>
    /// Field value is below minimum length
    /// </summary>
    public const string TooShort = "TOO_SHORT";

    /// <summary>
    /// Field is not allowed in the current context
    /// </summary>
    public const string NotAllowed = "NOT_ALLOWED";

    #endregion

    #region Member Validation

    /// <summary>
    /// Member was not found
    /// </summary>
    public const string MemberNotFound = "MEMBER_NOT_FOUND";

    /// <summary>
    /// Member ID is already taken
    /// </summary>
    public const string MemberIdTaken = "MEMBER_ID_TAKEN";

    /// <summary>
    /// Member is not in the contract holder
    /// </summary>
    public const string MemberNotInContractHolder = "MEMBER_NOT_IN_CONTRACT_HOLDER";

    /// <summary>
    /// Member processing failed due to an unexpected error during validation
    /// </summary>
    public const string MemberProcessingFailed = "MEMBER_PROCESSING_FAILED";

    #endregion

    #region Plan Validation

    /// <summary>
    /// Plan ID is not valid
    /// </summary>
    public const string InvalidPlanId = "PRODUCT_PLAN_NOT_FOUND";

    /// <summary>
    /// Dependents must be in the same plan as the employee
    /// </summary>
    public const string DependentPlanMismatch = "DEPENDENT_PLAN_MISMATCH";

    #endregion

    #region Age Validation

    /// <summary>
    /// Age is below minimum required
    /// </summary>
    public const string AgeTooLow = "AGE_TOO_LOW";

    /// <summary>
    /// Age exceeds maximum allowed
    /// </summary>
    public const string AgeTooHigh = "AGE_TOO_HIGH";

    /// <summary>
    /// Employee age is below minimum required age
    /// </summary>
    public const string EmployeeMinAge = "FIELD_MIN_AGE";

    /// <summary>
    /// Employee age is above maximum allowed age
    /// </summary>
    public const string EmployeeMaxAge = "FIELD_MAX_AGE";

    /// <summary>
    /// Child age is below minimum required days
    /// </summary>
    public const string ChildMinDays = "FIELD_MIN_CHILD_DAYS";

    /// <summary>
    /// Spouse age is below minimum required age
    /// </summary>
    public const string SpouseMinAge = "FIELD_MIN_SPOUSE_AGE";

    #endregion

    #region Upload Validation

    /// <summary>
    /// Upload file is empty
    /// </summary>
    public const string EmptyFile = "EMPTY_FILE";

    /// <summary>
    /// Invalid row content in upload file
    /// </summary>
    public const string InvalidRow = "INVALID_ROW";

    /// <summary>
    /// Invalid XLSX file format
    /// </summary>
    public const string InvalidXlsxFile = "INVALID_XLSX_FILE";

    /// <summary>
    /// Invalid CSV file format
    /// </summary>
    public const string InvalidCsvFile = "INVALID_CSV_FILE";

    /// <summary>
    /// Unsupported file type
    /// </summary>
    public const string UnsupportedFileType = "UNSUPPORTED_FILE_TYPE";

    /// <summary>
    /// No columns found in upload file
    /// </summary>
    public const string NoColumn = "NO_COLUMN";

    /// <summary>
    /// No member data found in upload file
    /// </summary>
    public const string NoMember = "NO_MEMBER";

    /// <summary>
    /// Required columns are missing from upload
    /// </summary>
    public const string MissingColumns = "MISSING_COLUMNS";

    /// <summary>
    /// Upload contains extra columns that are not allowed
    /// </summary>
    public const string ExtraColumns = "EXTRA_COLUMNS";

    /// <summary>
    /// At least one of the specified columns is required
    /// </summary>
    public const string OneOfRequired = "ONE_OF_REQUIRED";

    /// <summary>
    /// Missing mandatory columns in upload file
    /// </summary>
    public const string MissingMandatoryColumns = "MISSING_MANDATORY_COLUMNS";

    /// <summary>
    /// Missing one of mandatory columns in upload file
    /// </summary>
    public const string MissingOneOfMandatoryColumns = "MISSING_ONE_OF_MANDATORY_COLUMNS";

    /// <summary>
    /// Extra column found in upload file
    /// </summary>
    public const string ExtraColumn = "EXTRA_COLUMN";

    /// <summary>
    /// File processing failed due to an error during file parsing or reading
    /// </summary>
    public const string FileProcessingFailed = "FILE_PROCESSING_FAILED";

    /// <summary>
    /// Policy member upload was not found
    /// </summary>
    public const string PolicyMemberUploadNotFound = "POLICY_MEMBER_UPLOAD_NOT_FOUND";

    /// <summary>
    /// Policy member upload has invalid status for the requested operation
    /// </summary>
    public const string InvalidPolicyMemberUploadStatus = "INVALID_POLICY_MEMBER_UPLOAD_STATUS";

    /// <summary>
    /// Upload file was not found
    /// </summary>
    public const string UploadFileNotFound = "UPLOAD_FILE_NOT_FOUND";

    /// <summary>
    /// Error occurred during upload file processing
    /// </summary>
    public const string UploadFileProcessingError = "UPLOAD_FILE_PROCESSING_ERROR";

    /// <summary>
    /// Bad schema configuration for upload processing
    /// </summary>
    public const string BadSchemaConfig = "BAD_SCHEMA_CONFIG";

    /// <summary>
    /// Upload has no valid members for import
    /// </summary>
    public const string NoValidMembersForImport = "NO_VALID_MEMBERS_FOR_IMPORT";

    /// <summary>
    /// Upload validation could not be started due to concurrency or locking issues
    /// </summary>
    public const string UploadValidationLocked = "UPLOAD_VALIDATION_LOCKED";

    /// <summary>
    /// Upload file size exceeds the maximum allowed limit
    /// </summary>
    public const string FileTooLarge = "FILE_TOO_LARGE";

    #endregion

    #region Policy Validation

    /// <summary>
    /// Policy was not found
    /// </summary>
    public const string PolicyNotFound = "POLICY_NOT_FOUND";

    /// <summary>
    /// Policy is already issued and cannot be modified
    /// </summary>
    public const string PolicyIssued = "POLICY_ISSUED";

    /// <summary>
    /// Policy contract holder was not found
    /// </summary>
    public const string PolicyContractHolderNotFound = "POLICY_CONTRACT_HOLDER_NOT_FOUND";

    /// <summary>
    /// Policy is missing required ProductId
    /// </summary>
    public const string PolicyProductIdMissing = "POLICY_PRODUCT_ID_MISSING";

    /// <summary>
    /// Policy member was not found
    /// </summary>
    public const string PolicyMemberNotFound = "POLICY_MEMBER_NOT_FOUND";

    /// <summary>
    /// Policy member already exists with the same member ID
    /// </summary>
    public const string PolicyMemberExists = "POLICY_MEMBER_EXISTS";

    /// <summary>
    /// Effective date is outside policy date range
    /// </summary>
    public const string EffectiveDateOutsidePolicyDates = "EFFECTIVE_DATE_OUTSIDE_POLICY_DATES";

    /// <summary>
    /// Policy is already cancelled and cannot be cancelled again
    /// </summary>
    public const string PolicyAlreadyCancelled = "POLICY_ALREADY_CANCELLED";

    /// <summary>
    /// Policy ProductId component is invalid or missing
    /// </summary>
    public const string InvalidProductIdComponent = "INVALID_PRODUCT_ID_COMPONENT";

    /// <summary>
    /// Policy member state has invalid date range
    /// </summary>
    public const string InvalidPolicyMemberStateDate = "INVALID_POLICY_MEMBER_STATE_DATE";

    /// <summary>
    /// Policy member states have overlapping date ranges
    /// </summary>
    public const string PolicyMemberStateOverlap = "POLICY_MEMBER_STATE_OVERLAP";

    #endregion

    #region Endorsement Validation

    /// <summary>
    /// Endorsement cannot be changed due to its current status
    /// </summary>
    public const string EndorsementCannotBeChanged = "ENDORSEMENT_CANNOT_BE_CHANGED";

    /// <summary>
    /// Endorsement was not found
    /// </summary>
    public const string EndorsementNotFound = "ENDORSEMENT_NOT_FOUND";

    #endregion

    #region General

    /// <summary>
    /// An unexpected error occurred during validation
    /// </summary>
    public const string UnexpectedError = "UNEXPECTED_ERROR";

    /// <summary>
    /// Entity or resource was not found
    /// </summary>
    public const string NotFound = "NOT_FOUND";

    /// <summary>
    /// Entity is in an invalid state for the requested operation
    /// </summary>
    public const string InvalidState = "INVALID_STATE";

    /// <summary>
    /// Specification is not satisfied (used in NOT operations)
    /// </summary>
    public const string NotSatisfied = "NOT_SATISFIED";

    /// <summary>
    /// Validation failed with one or more validation errors
    /// </summary>
    public const string ValidationFailed = "VALIDATION_FAILED";

    #endregion
}
