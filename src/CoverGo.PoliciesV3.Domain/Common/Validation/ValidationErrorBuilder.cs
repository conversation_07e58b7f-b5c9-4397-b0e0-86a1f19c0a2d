using System.Collections.ObjectModel;
using CoverGo.PoliciesV3.Domain.Common.Constants;

namespace CoverGo.PoliciesV3.Domain.Common.Validation;

/// <summary>
/// Provides a fluent API for building validation errors with rich context.
/// This builder makes it easy to create complex validation errors with multiple context values.
/// </summary>
public class ValidationErrorBuilder
{
    private readonly string _propertyPath;
    private string? _propertyLabel;
    private readonly Dictionary<string, object?> _context = [];

    /// <summary>
    /// Initializes a new instance of the ValidationErrorBuilder class.
    /// </summary>
    /// <param name="propertyPath">The path to the property that failed validation</param>
    private ValidationErrorBuilder(string propertyPath)
    {
        _propertyPath = propertyPath ?? throw new ArgumentNullException(nameof(propertyPath));
    }

    /// <summary>
    /// Creates a new validation error builder for the specified property path.
    /// </summary>
    /// <param name="propertyPath">The path to the property that failed validation</param>
    /// <returns>A new validation error builder instance</returns>
    /// <exception cref="ArgumentException">Thrown when propertyPath is null or whitespace</exception>
    public static ValidationErrorBuilder For(string propertyPath) => string.IsNullOrWhiteSpace(propertyPath)
            ? throw new ArgumentException("Property path cannot be null or whitespace.", nameof(propertyPath))
            : new ValidationErrorBuilder(propertyPath);

    /// <summary>
    /// Sets the human-readable label for the property.
    /// </summary>
    /// <param name="label">The property label</param>
    /// <returns>This builder for method chaining</returns>
    public ValidationErrorBuilder WithLabel(string? label)
    {
        _propertyLabel = label;
        return this;
    }

    /// <summary>
    /// Adds a context value to the validation error.
    /// </summary>
    /// <param name="key">The context key</param>
    /// <param name="value">The context value</param>
    /// <returns>This builder for method chaining</returns>
    public ValidationErrorBuilder WithContext(string key, object? value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Context key cannot be null or whitespace.", nameof(key));

        _context[key] = value;
        return this;
    }

    /// <summary>
    /// Adds multiple context values to the validation error.
    /// </summary>
    /// <param name="context">The context values to add</param>
    /// <returns>This builder for method chaining</returns>
    public ValidationErrorBuilder WithContext(IReadOnlyDictionary<string, object?> context)
    {
        foreach (KeyValuePair<string, object?> kvp in context)
        {
            _context[kvp.Key] = kvp.Value;
        }
        return this;
    }

    #region Error Creation Methods

    /// <summary>
    /// Creates a required field validation error.
    /// </summary>
    /// <returns>A validation error indicating the field is required</returns>
    public ValidationError Required() => Build(ErrorCodes.Required);

    /// <summary>
    /// Creates an invalid format validation error.
    /// </summary>
    /// <returns>A validation error indicating invalid format</returns>
    public ValidationError InvalidFormat() => Build(ErrorCodes.InvalidFormat);

    /// <summary>
    /// Creates a unique violation validation error.
    /// </summary>
    /// <param name="scope">The scope where uniqueness is required (default: "upload")</param>
    /// <returns>A validation error indicating unique violation</returns>
    public ValidationError UniqueViolation(string scope = ValidationConstants.DefaultValues.DefaultScope) => WithContext(ValidationConstants.ContextKeys.Scope, scope).Build(ErrorCodes.UniqueViolation);

    /// <summary>
    /// Creates an invalid option validation error.
    /// </summary>
    /// <param name="availableOptions">The available options</param>
    /// <returns>A validation error indicating invalid option</returns>
    public ValidationError InvalidOption(IEnumerable<string> availableOptions) => availableOptions == null
            ? throw new ArgumentNullException(nameof(availableOptions))
            : WithContext(ValidationConstants.ContextKeys.AvailableOptions, availableOptions).Build(ErrorCodes.InvalidOption);

    /// <summary>
    /// Creates a pattern mismatch validation error.
    /// </summary>
    /// <param name="pattern">The pattern that was not matched</param>
    /// <returns>A validation error indicating pattern mismatch</returns>
    public ValidationError PatternMismatch(string pattern) => WithContext(ValidationConstants.ContextKeys.Pattern, pattern).Build(ErrorCodes.PatternMismatch);

    /// <summary>
    /// Creates an out of range validation error.
    /// </summary>
    /// <param name="minValue">The minimum allowed value (optional)</param>
    /// <param name="maxValue">The maximum allowed value (optional)</param>
    /// <returns>A validation error indicating value is out of range</returns>
    public ValidationError OutOfRange(object? minValue = null, object? maxValue = null)
    {
        ValidationErrorBuilder builder = this;
        if (minValue != null)
            builder = builder.WithContext(ValidationConstants.ContextKeys.MinValue, minValue);
        if (maxValue != null)
            builder = builder.WithContext(ValidationConstants.ContextKeys.MaxValue, maxValue);
        return builder.Build(ErrorCodes.OutOfRange);
    }

    /// <summary>
    /// Creates a too long validation error.
    /// </summary>
    /// <param name="maxLength">The maximum allowed length</param>
    /// <returns>A validation error indicating the value is too long</returns>
    public ValidationError TooLong(int maxLength) => WithContext(ValidationConstants.ContextKeys.MaxLength, maxLength).Build(ErrorCodes.TooLong);

    /// <summary>
    /// Creates a too short validation error.
    /// </summary>
    /// <param name="minLength">The minimum required length</param>
    /// <returns>A validation error indicating the value is too short</returns>
    public ValidationError TooShort(int minLength) => WithContext(ValidationConstants.ContextKeys.MinLength, minLength).Build(ErrorCodes.TooShort);

    /// <summary>
    /// Creates a not allowed validation error.
    /// </summary>
    /// <returns>A validation error indicating the field is not allowed</returns>
    public ValidationError NotAllowed() => Build(ErrorCodes.NotAllowed);

    /// <summary>
    /// Creates a member not found validation error.
    /// </summary>
    /// <param name="memberId">The member ID that was not found</param>
    /// <returns>A validation error indicating member not found</returns>
    public ValidationError MemberNotFound(string memberId) => WithContext(ValidationConstants.ContextKeys.MemberId, memberId).Build(ErrorCodes.MemberNotFound);

    /// <summary>
    /// Creates a member ID taken validation error.
    /// </summary>
    /// <param name="memberId">The member ID that is taken</param>
    /// <param name="existingPolicyMemberId">The existing policy member ID</param>
    /// <returns>A validation error indicating member ID is taken</returns>
    public ValidationError MemberIdTaken(string memberId, string existingPolicyMemberId) => WithContext(ValidationConstants.ContextKeys.MemberId, memberId)
               .WithContext(ValidationConstants.ContextKeys.ExistingPolicyMemberId, existingPolicyMemberId)
               .Build(ErrorCodes.MemberIdTaken);

    /// <summary>
    /// Creates a member not in contract holder validation error.
    /// </summary>
    /// <param name="memberId">The member ID</param>
    /// <param name="policyId">The policy ID</param>
    /// <returns>A validation error indicating member is not in contract holder</returns>
    public ValidationError MemberNotInContractHolder(string memberId, string policyId) => WithContext(ValidationConstants.ContextKeys.MemberId, memberId)
               .WithContext(ValidationConstants.ContextKeys.PolicyId, policyId)
               .Build(ErrorCodes.MemberNotInContractHolder);

    /// <summary>
    /// Creates an invalid plan ID validation error.
    /// </summary>
    /// <param name="planId">The invalid plan ID</param>
    /// <param name="availablePlans">The available plan IDs</param>
    /// <param name="productId">The product ID (optional)</param>
    /// <returns>A validation error indicating invalid plan ID</returns>
    public ValidationError InvalidPlanId(string planId, IEnumerable<string> availablePlans, string? productId = null) => WithContext(ValidationConstants.ContextKeys.PlanId, planId)
               .WithContext(ValidationConstants.ContextKeys.AvailablePlans, availablePlans)
               .WithContext(ValidationConstants.ContextKeys.ProductId, productId ?? "unknown")
               .Build(ErrorCodes.InvalidPlanId);

    /// <summary>
    /// Creates a dependent plan mismatch validation error.
    /// </summary>
    /// <returns>A validation error indicating dependent plan mismatch</returns>
    public ValidationError DependentPlanMismatch() => Build(ErrorCodes.DependentPlanMismatch);

    /// <summary>
    /// Creates an age too low validation error.
    /// </summary>
    /// <param name="minAge">The minimum required age</param>
    /// <returns>A validation error indicating age is too low</returns>
    public ValidationError AgeTooLow(int minAge) => WithContext(ValidationConstants.ContextKeys.MinAge, minAge).Build(ErrorCodes.AgeTooLow);

    /// <summary>
    /// Creates an age too high validation error.
    /// </summary>
    /// <param name="maxAge">The maximum allowed age</param>
    /// <returns>A validation error indicating age is too high</returns>
    public ValidationError AgeTooHigh(int maxAge) => WithContext(ValidationConstants.ContextKeys.MaxAge, maxAge).Build(ErrorCodes.AgeTooHigh);

    #endregion

    /// <summary>
    /// Builds a validation error with the specified error code.
    /// </summary>
    /// <param name="code">The error code</param>
    /// <returns>A validation error</returns>
    public ValidationError Build(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
            throw new ArgumentException("Error code cannot be null or whitespace.", nameof(code));

        ReadOnlyDictionary<string, object?>? context = _context.Count > 0 ? new ReadOnlyDictionary<string, object?>(_context) : null;
        return new ValidationError(code, _propertyPath, _propertyLabel, context);
    }
}