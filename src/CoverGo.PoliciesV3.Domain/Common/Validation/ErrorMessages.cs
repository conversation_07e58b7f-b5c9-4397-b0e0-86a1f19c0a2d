namespace CoverGo.PoliciesV3.Domain.Common.Validation;

/// <summary>
/// Centralized error message templates for domain exceptions.
/// Provides consistent message formatting across all exception classes.
/// </summary>
public static class ErrorMessages
{
    #region Policy Messages

    /// <summary>
    /// Creates a message for when a policy is not found.
    /// </summary>
    /// <param name="policyId">The policy ID that was not found</param>
    /// <returns>Formatted error message</returns>
    public static string PolicyNotFound(string policyId) => $"Policy {policyId} not found";

    /// <summary>
    /// Creates a message for when a policy is already issued and cannot be modified.
    /// </summary>
    /// <param name="policyId">The policy ID that is issued</param>
    /// <returns>Formatted error message</returns>
    public static string PolicyIssued(string policyId) => $"Policy {policyId} shouldn't be issued";

    /// <summary>
    /// Creates a message for when a policy's contract holder is not found.
    /// </summary>
    /// <param name="policyId">The policy ID whose contract holder was not found</param>
    /// <returns>Formatted error message</returns>
    public static string PolicyContractHolderNotFound(string policyId) => $"Policy {policyId} contract holder not found";

    /// <summary>
    /// Creates a message for when a policy is missing its ProductId.
    /// </summary>
    /// <param name="policyId">The policy ID that is missing ProductId</param>
    /// <returns>Formatted error message</returns>
    public static string PolicyProductIdMissing(string policyId) => $"Policy {policyId} is missing ProductId";

    /// <summary>
    /// Creates a message for when a policy is already cancelled.
    /// </summary>
    /// <param name="policyId">The policy ID that is already cancelled</param>
    /// <returns>Formatted error message</returns>
    public static string PolicyAlreadyCancelled(string policyId) => $"Policy {policyId} is already cancelled";

    /// <summary>
    /// Creates a message for when a policy's ProductId component is invalid.
    /// </summary>
    /// <param name="policyId">The policy ID with invalid ProductId component</param>
    /// <param name="componentName">The name of the invalid component</param>
    /// <returns>Formatted error message</returns>
    public static string InvalidProductIdComponent(string policyId, string componentName) => $"Policy {policyId} ProductId is missing {componentName}";

    #endregion

    #region Member Messages

    /// <summary>
    /// Creates a message for when an individual member is not found.
    /// </summary>
    /// <param name="memberId">The member ID that was not found</param>
    /// <returns>Formatted error message</returns>
    public static string MemberNotFound(string memberId) => $"Individual with member ID {memberId} not found";

    /// <summary>
    /// Creates a message for when a policy member is not found.
    /// </summary>
    /// <param name="memberId">The member ID that was not found</param>
    /// <returns>Formatted error message</returns>
    public static string PolicyMemberNotFound(string memberId) => $"Primary member {memberId} does not exist";

    /// <summary>
    /// Creates a message for when a policy member already exists with the same member ID.
    /// </summary>
    /// <param name="memberId">The member ID that already exists</param>
    /// <param name="policyMemberId">The existing policy member ID</param>
    /// <returns>Formatted error message</returns>
    public static string PolicyMemberExists(string memberId, Guid policyMemberId) => $"Member ID {memberId} is already taken by policy member {policyMemberId}";

    /// <summary>
    /// Creates a message for when a policy member state has invalid date range.
    /// </summary>
    /// <param name="startDate">The start date</param>
    /// <param name="endDate">The end date</param>
    /// <returns>Formatted error message</returns>
    public static string InvalidPolicyMemberStateDate(DateOnly startDate, DateOnly endDate) => $"Invalid state: EndDate {endDate} must be after or equal to StartDate {startDate}";

    /// <summary>
    /// Creates a message for when policy member states have overlapping date ranges.
    /// </summary>
    /// <param name="firstStateEndDate">The end date of the first state</param>
    /// <param name="secondStateStartDate">The start date of the second state</param>
    /// <returns>Formatted error message</returns>
    public static string PolicyMemberStateOverlap(DateOnly firstStateEndDate, DateOnly secondStateStartDate) => $"State overlap detected: State ending {firstStateEndDate} overlaps with state starting {secondStateStartDate}";

    /// <summary>
    /// Creates a message for when a member is associated with a specific policy from a different contract holder.
    /// </summary>
    /// <param name="memberId">The member ID</param>
    /// <param name="policyId">The policy ID from different contract holder</param>
    /// <returns>Formatted error message</returns>
    public static string MemberNotInContractHolder(string memberId, string policyId) => $"Member {memberId} is already associated with policy {policyId} from a different contract holder";

    #endregion

    #region Upload Messages

    /// <summary>
    /// Creates a message for when a policy member upload is not found.
    /// </summary>
    /// <param name="uploadId">The upload ID that was not found</param>
    /// <returns>Formatted error message</returns>
    public static string PolicyMemberUploadNotFound(string uploadId) => $"Policy member upload {uploadId} not found";

    /// <summary>
    /// Creates a message for when a policy member upload has an invalid status.
    /// </summary>
    /// <param name="uploadId">The upload ID</param>
    /// <param name="currentStatus">The current status</param>
    /// <param name="expectedStatuses">The expected statuses</param>
    /// <returns>Formatted error message</returns>
    public static string InvalidPolicyMemberUploadStatus(string uploadId, string currentStatus, IEnumerable<string> expectedStatuses) =>
        $"Policy member upload {uploadId} has invalid status '{currentStatus}' for this operation. Expected one of: {string.Join(", ", expectedStatuses)}";

    /// <summary>
    /// Creates a message for when an upload has no valid members for import.
    /// </summary>
    /// <param name="uploadId">The upload ID with no valid members</param>
    /// <returns>Formatted error message</returns>
    public static string NoValidMembersForImport(string uploadId) => $"Cannot import upload {uploadId} when there are no valid members";

    /// <summary>
    /// Creates a message for when an upload file is not found.
    /// </summary>
    /// <param name="policyId">The policy ID</param>
    /// <param name="path">The file path that was not found</param>
    /// <returns>Formatted error message</returns>
    public static string UploadFileNotFound(string policyId, string path) => $"Upload file path {path} of policy {policyId} not found";

    /// <summary>
    /// Creates a message for when an error occurs during upload file processing.
    /// </summary>
    /// <param name="policyId">The policy ID</param>
    /// <param name="filePath">The file path being processed</param>
    /// <param name="message">The specific error message</param>
    /// <returns>Formatted error message</returns>
    public static string UploadFileProcessingError(string policyId, string filePath, string message) =>
        $"Error processing upload file {filePath} for policy {policyId}: {message}";

    /// <summary>
    /// Creates a message for when upload validation cannot be started due to concurrency or locking issues.
    /// </summary>
    /// <param name="uploadId">The upload ID that could not be locked</param>
    /// <returns>Formatted error message</returns>
    public static string UploadValidationLocked(string uploadId) =>
        $"Could not start validation for upload {uploadId} - it may be locked or modified by another process";

    #endregion

    #region Endorsement Messages

    /// <summary>
    /// Creates a message for when an endorsement cannot be changed due to its status.
    /// </summary>
    /// <param name="policyId">The policy ID</param>
    /// <param name="endorsementId">The endorsement ID</param>
    /// <param name="status">The current status preventing the change</param>
    /// <returns>Formatted error message</returns>
    public static string EndorsementCannotBeChanged(string policyId, string endorsementId, string status) =>
        $"Endorsement {endorsementId} for policy {policyId} cannot be changed due to status {status}";

    /// <summary>
    /// Creates a message for when an effective date is outside policy date range.
    /// </summary>
    /// <param name="policyId">The policy ID</param>
    /// <returns>Formatted error message</returns>
    public static string EffectiveDateOutsidePolicyDates(string policyId) => $"Effective date is outside policy {policyId} date range";

    /// <summary>
    /// Creates a message for when an endorsement is not found.
    /// </summary>
    /// <param name="policyId">The policy ID</param>
    /// <param name="endorsementId">The endorsement ID that was not found</param>
    /// <returns>Formatted error message</returns>
    public static string EndorsementNotFound(string policyId, string endorsementId) => $"Endorsement {endorsementId} not found for policy {policyId}";

    #endregion

    #region Product Messages

    /// <summary>
    /// Creates a message for when a plan ID is not valid for a product.
    /// </summary>
    /// <param name="planId">The invalid plan ID</param>
    /// <param name="productId">The product ID</param>
    /// <returns>Formatted error message</returns>
    public static string InvalidPlanId(string planId, string productId) => $"Plan {planId} not found on product {productId}";

    #endregion
}
