using System.Collections;

namespace CoverGo.PoliciesV3.Domain.Common.Validation;

/// <summary>
/// Represents the result of an operation that can either succeed with a value or fail with validation errors.
/// This implements the Result pattern for better error handling than exceptions.
/// </summary>
/// <typeparam name="T">The type of the success value</typeparam>
public readonly struct Result<T> : IReadOnlyList<ValidationError>, IEquatable<Result<T>>
{
    private readonly T? _value;
    private readonly ValidationError[]? _errors;

    /// <summary>
    /// Initializes a successful result with a value.
    /// </summary>
    /// <param name="value">The success value</param>
    private Result(T value)
    {
        _value = value;
        _errors = null;
        IsSuccess = true;
    }

    /// <summary>
    /// Initializes a failed result with validation errors.
    /// </summary>
    /// <param name="errors">The validation errors</param>
    private Result(ValidationError[] errors)
    {
        _value = default;
        _errors = errors;
        IsSuccess = false;
    }

    /// <summary>
    /// Gets a value indicating whether the operation was successful.
    /// </summary>
    public bool IsSuccess { get; }

    /// <summary>
    /// Gets a value indicating whether the operation failed.
    /// </summary>
    public bool IsFailure => !IsSuccess;

    /// <summary>
    /// Gets the success value. Throws if the result is a failure.
    /// </summary>
    /// <exception cref="InvalidOperationException">Thrown when accessing Value on a failed result</exception>
    public T Value => IsSuccess ? _value! : throw new InvalidOperationException("Cannot access Value on a failed result. Check IsSuccess first.");

    /// <summary>
    /// Gets the validation errors. Empty if the result is successful.
    /// </summary>
    public IReadOnlyList<ValidationError> Errors => _errors ?? [];

    /// <summary>
    /// Gets the number of validation errors.
    /// </summary>
    public int Count => _errors?.Length ?? 0;

    /// <summary>
    /// Gets the validation error at the specified index.
    /// </summary>
    /// <param name="index">The zero-based index of the error</param>
    /// <returns>The validation error at the specified index</returns>
    /// <exception cref="ArgumentOutOfRangeException">Thrown when the index is out of range</exception>
    public ValidationError this[int index] =>
        _errors != null && index >= 0 && index < _errors.Length
        ? _errors[index]
        : throw new ArgumentOutOfRangeException(nameof(index), index, "Index is out of range");

    #region Factory Methods

    /// <summary>
    /// Creates a successful result with the specified value.
    /// </summary>
    /// <param name="value">The success value</param>
    /// <returns>A successful result</returns>
    public static Result<T> Success(T value) => new(value);

    /// <summary>
    /// Creates a failed result with a single validation error.
    /// </summary>
    /// <param name="error">The validation error</param>
    /// <returns>A failed result</returns>
    public static Result<T> Failure(ValidationError error) => error == null ? throw new ArgumentNullException(nameof(error)) : new([error]);

    /// <summary>
    /// Creates a failed result with multiple validation errors.
    /// </summary>
    /// <param name="errors">The validation errors</param>
    /// <returns>A failed result</returns>
    public static Result<T> Failure(params ValidationError[] errors)
    {
        if (errors == null)
            throw new ArgumentNullException(nameof(errors));

        if (errors.Length == 0)
            throw new ArgumentException("At least one error is required", nameof(errors));

        return new(errors);
    }

    /// <summary>
    /// Creates a failed result with multiple validation errors.
    /// </summary>
    /// <param name="errors">The validation errors</param>
    /// <returns>A failed result</returns>
    public static Result<T> Failure(IEnumerable<ValidationError> errors)
    {
        ArgumentNullException.ThrowIfNull(errors);
        ValidationError[] errorArray = [.. errors];
        return errorArray.Length == 0 ? throw new ArgumentException("At least one error is required", nameof(errors)) : new(errorArray);
    }

    #endregion

    #region Utility Methods

    /// <summary>
    /// Executes an action if the result is successful.
    /// </summary>
    /// <param name="action">The action to execute with the value</param>
    /// <returns>This result for method chaining</returns>
    public Result<T> OnSuccess(Action<T> action)
    {
        if (IsSuccess)
            action(_value!);
        return this;
    }

    /// <summary>
    /// Executes an action if the result is a failure.
    /// </summary>
    /// <param name="action">The action to execute with the errors</param>
    /// <returns>This result for method chaining</returns>
    public Result<T> OnFailure(Action<IReadOnlyList<ValidationError>> action)
    {
        if (IsFailure)
            action(Errors);
        return this;
    }

    /// <summary>
    /// Transforms the success value to a new type.
    /// </summary>
    /// <typeparam name="TNew">The new value type</typeparam>
    /// <param name="transform">The transformation function</param>
    /// <returns>A new result with the transformed value or the same errors</returns>
    public Result<TNew> Map<TNew>(Func<T, TNew> transform) => IsSuccess ? Result<TNew>.Success(transform(_value!)) : Result<TNew>.Failure(_errors!);

    /// <summary>
    /// Transforms the success value to a new result.
    /// </summary>
    /// <typeparam name="TNew">The new value type</typeparam>
    /// <param name="transform">The transformation function that returns a result</param>
    /// <returns>A new result or the same errors</returns>
    public Result<TNew> Bind<TNew>(Func<T, Result<TNew>> transform) => IsSuccess ? transform(_value!) : Result<TNew>.Failure(_errors!);

    /// <summary>
    /// Gets the value if successful, or returns the default value if failed.
    /// </summary>
    /// <param name="defaultValue">The default value to return on failure</param>
    /// <returns>The success value or the default value</returns>
    public T GetValueOrDefault(T defaultValue = default!) => IsSuccess ? _value! : defaultValue;

    /// <summary>
    /// Throws a ValidationException if the result is a failure.
    /// </summary>
    /// <exception cref="ValidationException">Thrown when the result contains validation errors</exception>
    public void ThrowIfFailure()
    {
        if (IsFailure)
            throw new ValidationException(Errors);
    }

    #endregion

    #region IReadOnlyList Implementation

    /// <summary>
    /// Gets an enumerator that iterates through the validation errors.
    /// </summary>
    /// <returns>An enumerator for the validation errors</returns>
    public IEnumerator<ValidationError> GetEnumerator() => (Errors as IEnumerable<ValidationError>).GetEnumerator();

    /// <summary>
    /// Gets an enumerator that iterates through the validation errors.
    /// </summary>
    /// <returns>An enumerator for the validation errors</returns>
    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();

    #endregion

    #region Implicit Conversions

    /// <summary>
    /// Implicitly converts a value to a successful result.
    /// </summary>
    /// <param name="value">The value</param>
    /// <returns>A successful result</returns>
    public static implicit operator Result<T>(T value) => Success(value);

    /// <summary>
    /// Implicitly converts a validation error to a failed result.
    /// </summary>
    /// <param name="error">The validation error</param>
    /// <returns>A failed result</returns>
    public static implicit operator Result<T>(ValidationError error) => Failure(error);

    /// <summary>
    /// Implicitly converts validation errors to a failed result.
    /// </summary>
    /// <param name="errors">The validation errors</param>
    /// <returns>A failed result</returns>
    public static implicit operator Result<T>(ValidationError[] errors) => Failure(errors);

    #endregion

    /// <summary>
    /// Returns a string representation of this result.
    /// </summary>
    public override string ToString()
    {
        if (IsSuccess)
            return $"Success: {_value}";

        string errorCountText = Count == 1 ? "error" : "errors";
        return $"Failure: {Count} {errorCountText} - {string.Join("; ", Errors.Select(e => e.Message))}";
    }

    /// <summary>
    /// Determines whether the specified object is equal to the current result.
    /// </summary>
    public override bool Equals(object? obj) => obj is Result<T> other && Equals(other);

    /// <summary>
    /// Determines whether the specified result is equal to the current result.
    /// </summary>
    public bool Equals(Result<T> other) => IsSuccess == other.IsSuccess && (IsSuccess
            ? EqualityComparer<T>.Default.Equals(_value, other._value)
            : _errors != null && other._errors != null && _errors.SequenceEqual(other._errors));

    /// <summary>
    /// Returns a hash code for the current result.
    /// </summary>
    public override int GetHashCode()
    {
        if (IsSuccess)
            return HashCode.Combine(IsSuccess, _value);

        var hashCode = new HashCode();
        hashCode.Add(IsSuccess);
        if (_errors != null)
        {
            foreach (ValidationError error in _errors)
                hashCode.Add(error);
        }
        return hashCode.ToHashCode();
    }

    /// <summary>
    /// Determines whether two results are equal.
    /// </summary>
    public static bool operator ==(Result<T> left, Result<T> right) => left.Equals(right);

    /// <summary>
    /// Determines whether two results are not equal.
    /// </summary>
    public static bool operator !=(Result<T> left, Result<T> right) => !left.Equals(right);
}

/// <summary>
/// Non-generic result for operations that don't return a value.
/// </summary>
public readonly struct Result : IReadOnlyList<ValidationError>, IEquatable<Result>
{
    private readonly ValidationError[]? _errors;

    /// <summary>
    /// Initializes a successful result.
    /// </summary>
    private Result(bool success)
    {
        _errors = null;
        IsSuccess = success;
    }

    /// <summary>
    /// Initializes a failed result with validation errors.
    /// </summary>
    /// <param name="errors">The validation errors</param>
    private Result(ValidationError[] errors)
    {
        _errors = errors;
        IsSuccess = false;
    }

    /// <summary>
    /// Gets a value indicating whether the operation was successful.
    /// </summary>
    public bool IsSuccess { get; }

    /// <summary>
    /// Gets a value indicating whether the operation failed.
    /// </summary>
    public bool IsFailure => !IsSuccess;

    /// <summary>
    /// Gets the validation errors. Empty if the result is successful.
    /// </summary>
    public IReadOnlyList<ValidationError> Errors => _errors ?? [];

    /// <summary>
    /// Gets the number of validation errors.
    /// </summary>
    public int Count => _errors?.Length ?? 0;

    /// <summary>
    /// Gets the validation error at the specified index.
    /// </summary>
    /// <param name="index">The zero-based index of the error</param>
    /// <returns>The validation error at the specified index</returns>
    public ValidationError this[int index]
    {
        get
        {
            if (index < 0 || index >= Count)
                throw new ArgumentOutOfRangeException(nameof(index), "Index is out of range");

            return _errors![index];
        }
    }

    #region Factory Methods

    /// <summary>
    /// Creates a successful result.
    /// </summary>
    /// <returns>A successful result</returns>
    public static Result Success() => new(true);

    /// <summary>
    /// Creates a failed result with a single validation error.
    /// </summary>
    /// <param name="error">The validation error</param>
    /// <returns>A failed result</returns>
    public static Result Failure(ValidationError error) => error == null ? throw new ArgumentNullException(nameof(error)) : new([error]);

    /// <summary>
    /// Creates a failed result with multiple validation errors.
    /// </summary>
    /// <param name="errors">The validation errors</param>
    /// <returns>A failed result</returns>
    public static Result Failure(params ValidationError[] errors)
    {
        if (errors == null)
            throw new ArgumentNullException(nameof(errors));

        if (errors.Length == 0)
            throw new ArgumentException("At least one error is required", nameof(errors));

        return new(errors);
    }

    /// <summary>
    /// Creates a failed result with multiple validation errors.
    /// </summary>
    /// <param name="errors">The validation errors</param>
    /// <returns>A failed result</returns>
    public static Result Failure(IEnumerable<ValidationError> errors)
    {
        ArgumentNullException.ThrowIfNull(errors);
        ValidationError[] errorArray = [.. errors];
        return errorArray.Length == 0 ? throw new ArgumentException("At least one error is required", nameof(errors)) : new(errorArray);
    }

    #endregion

    #region Utility Methods

    /// <summary>
    /// Executes an action if the result is a failure.
    /// </summary>
    /// <param name="action">The action to execute with the errors</param>
    /// <returns>This result for method chaining</returns>
    public Result OnFailure(Action<IReadOnlyList<ValidationError>> action)
    {
        if (IsFailure)
            action(Errors);
        return this;
    }

    /// <summary>
    /// Throws a ValidationException if the result is a failure.
    /// </summary>
    /// <exception cref="ValidationException">Thrown when the result contains validation errors</exception>
    public void ThrowIfFailure()
    {
        if (IsFailure)
            throw new ValidationException(Errors);
    }

    #endregion

    #region IReadOnlyList Implementation

    /// <summary>
    /// Gets an enumerator that iterates through the validation errors.
    /// </summary>
    /// <returns>An enumerator for the validation errors</returns>
    public IEnumerator<ValidationError> GetEnumerator() => (Errors as IEnumerable<ValidationError>).GetEnumerator();

    /// <summary>
    /// Gets an enumerator that iterates through the validation errors.
    /// </summary>
    /// <returns>An enumerator for the validation errors</returns>
    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();

    #endregion

    #region Implicit Conversions

    /// <summary>
    /// Implicitly converts a validation error to a failed result.
    /// </summary>
    /// <param name="error">The validation error</param>
    /// <returns>A failed result</returns>
    public static implicit operator Result(ValidationError error) => Failure(error);

    /// <summary>
    /// Implicitly converts validation errors to a failed result.
    /// </summary>
    /// <param name="errors">The validation errors</param>
    /// <returns>A failed result</returns>
    public static implicit operator Result(ValidationError[] errors) => Failure(errors);

    #endregion

    /// <summary>
    /// Returns a string representation of this result.
    /// </summary>
    public override string ToString()
    {
        if (IsSuccess)
            return "Success";

        string errorCountText = Count == 1 ? "error" : "errors";
        return $"Failure: {Count} {errorCountText} - {string.Join("; ", Errors.Select(e => e.Message))}";
    }

    /// <summary>
    /// Determines whether the specified object is equal to the current result.
    /// </summary>
    public override bool Equals(object? obj) => obj is Result other && Equals(other);

    /// <summary>
    /// Determines whether the specified result is equal to the current result.
    /// </summary>
    public bool Equals(Result other) => IsSuccess == other.IsSuccess && (IsSuccess || _errors != null && other._errors != null && _errors.SequenceEqual(other._errors));

    /// <summary>
    /// Returns a hash code for the current result.
    /// </summary>
    public override int GetHashCode()
    {
        if (IsSuccess)
            return HashCode.Combine(IsSuccess);

        var hashCode = new HashCode();
        hashCode.Add(IsSuccess);
        if (_errors != null)
        {
            foreach (ValidationError error in _errors)
                hashCode.Add(error);
        }
        return hashCode.ToHashCode();
    }

    /// <summary>
    /// Determines whether two results are equal.
    /// </summary>
    public static bool operator ==(Result left, Result right) => left.Equals(right);

    /// <summary>
    /// Determines whether two results are not equal.
    /// </summary>
    public static bool operator !=(Result left, Result right) => !left.Equals(right);
}
