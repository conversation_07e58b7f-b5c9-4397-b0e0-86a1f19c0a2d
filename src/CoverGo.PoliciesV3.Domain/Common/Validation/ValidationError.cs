using CoverGo.PoliciesV3.Domain.Common.Extensions;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using HotChocolate;
using System.Collections.ObjectModel;

namespace CoverGo.PoliciesV3.Domain.Common.Validation;

/// <summary>
/// Represents a validation error with rich context information.
/// This is the single error type used throughout the validation system.
/// </summary>
public sealed record ValidationError
{
    [GraphQLIgnore]
    private static readonly IReadOnlyDictionary<string, object?> EmptyContext =
        new ReadOnlyDictionary<string, object?>(new Dictionary<string, object?>());

    /// <summary>
    /// Initializes a new instance of the ValidationError record.
    /// </summary>
    /// <param name="code">The standardized error code</param>
    /// <param name="propertyPath">The path to the property that failed validation</param>
    /// <param name="propertyLabel">The human-readable label for the property (optional)</param>
    /// <param name="context">Additional context information (optional)</param>
    public ValidationError(
        string code,
        string propertyPath,
        string? propertyLabel = null,
        IReadOnlyDictionary<string, object?>? context = null)
    {
        if (string.IsNullOrWhiteSpace(code))
            throw new ArgumentException("Error code cannot be null or empty", nameof(code));

        if (string.IsNullOrWhiteSpace(propertyPath))
            throw new ArgumentException("Property path cannot be null or empty", nameof(propertyPath));

        Code = code;
        PropertyPath = propertyPath;
        PropertyLabel = string.IsNullOrWhiteSpace(propertyLabel) ? propertyPath : propertyLabel;
        Context = context ?? EmptyContext;
        Message = GenerateMessage(code, PropertyLabel, Context);
    }

    /// <summary>
    /// Gets the standardized error code that categorizes this validation error.
    /// </summary>
    public string Code { get; init; }

    /// <summary>
    /// Gets the human-readable error message.
    /// </summary>
    public string Message { get; init; }

    /// <summary>
    /// Gets the path to the property that failed validation.
    /// </summary>
    public string PropertyPath { get; init; }

    /// <summary>
    /// Gets the human-readable label for the property (optional).
    /// </summary>
    public string? PropertyLabel { get; init; }

    /// <summary>
    /// Gets additional context information for the validation error.
    /// </summary>
    [GraphQLIgnore]
    public IReadOnlyDictionary<string, object?> Context { get; init; }

    /// <summary>
    /// Creates a new ValidationError with additional context.
    /// </summary>
    /// <param name="key">The context key</param>
    /// <param name="value">The context value</param>
    /// <returns>A new ValidationError with the additional context</returns>
    [GraphQLIgnore]
    public ValidationError WithContext(string key, object? value)
    {
        var newContext = Context.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        newContext[key] = value;
        return this with { Context = new ReadOnlyDictionary<string, object?>(newContext) };
    }

    /// <summary>
    /// Creates a new ValidationError with multiple additional context values.
    /// </summary>
    /// <param name="additionalContext">The additional context to add</param>
    /// <returns>A new ValidationError with the additional context</returns>
    [GraphQLIgnore]
    public ValidationError WithContext(IReadOnlyDictionary<string, object?> additionalContext)
    {
        var newContext = Context.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        foreach (KeyValuePair<string, object?> kvp in additionalContext)
        {
            newContext[kvp.Key] = kvp.Value;
        }
        return this with { Context = new ReadOnlyDictionary<string, object?>(newContext) };
    }

    /// <summary>
    /// Generates an appropriate error message based on the error code and context.
    /// </summary>
    /// <param name="code">The error code</param>
    /// <param name="displayName">The display name for the property</param>
    /// <param name="context">The context information</param>
    /// <returns>A formatted error message</returns>
    [GraphQLIgnore]
    private static string GenerateMessage(string code, string displayName, IReadOnlyDictionary<string, object?> context) => code switch
    {
        ErrorCodes.Required => $"{displayName} is required",
        ErrorCodes.InvalidFormat => $"{displayName} has an invalid format",
        ErrorCodes.UniqueViolation => GetUniqueViolationMessage(displayName, context),
        ErrorCodes.InvalidOption => GetInvalidOptionMessage(displayName, context),
        ErrorCodes.PatternMismatch => GetPatternMismatchMessage(displayName, context),
        ErrorCodes.OutOfRange => GetOutOfRangeMessage(displayName, context),
        ErrorCodes.TooLong => GetTooLongMessage(displayName, context),
        ErrorCodes.TooShort => GetTooShortMessage(displayName, context),
        ErrorCodes.NotAllowed => $"{displayName} is not allowed in this context",
        ErrorCodes.MemberNotFound => GetMemberNotFoundMessage(context),
        ErrorCodes.MemberIdTaken => GetMemberIdTakenMessage(context),
        ErrorCodes.MemberNotInContractHolder => GetMemberNotInContractHolderMessage(context),
        ErrorCodes.InvalidPlanId => GetInvalidPlanIdMessage(context),
        ErrorCodes.DependentPlanMismatch => "Dependents must be in the same plan as the employee",
        ErrorCodes.AgeTooLow => GetAgeTooLowMessage(context),
        ErrorCodes.AgeTooHigh => GetAgeTooHighMessage(context),
        ErrorCodes.EmployeeMinAge => GetEmployeeMinAgeMessage(context),
        ErrorCodes.EmployeeMaxAge => GetEmployeeMaxAgeMessage(context),
        ErrorCodes.ChildMinDays => GetChildMinDaysMessage(context),
        ErrorCodes.SpouseMinAge => GetSpouseMinAgeMessage(context),
        ErrorCodes.EmptyFile => "File content is empty",
        ErrorCodes.NoColumn => "No headers found in file",
        ErrorCodes.NoMember => "No member data found in file",
        ErrorCodes.InvalidRow => GetInvalidRowMessage(context),
        ErrorCodes.InvalidXlsxFile => GetInvalidFileFormatMessage("XLSX", context),
        ErrorCodes.InvalidCsvFile => GetInvalidFileFormatMessage("CSV", context),
        ErrorCodes.UnsupportedFileType => GetUnsupportedFileTypeMessage(context),
        ErrorCodes.MissingColumns => GetMissingColumnsMessage(context),
        ErrorCodes.MissingMandatoryColumns => GetMissingColumnsMessage(context),
        ErrorCodes.ExtraColumns => GetExtraColumnsMessage(context),
        ErrorCodes.ExtraColumn => GetExtraColumnsMessage(context),
        ErrorCodes.OneOfRequired => GetOneOfRequiredMessage(context),
        ErrorCodes.MissingOneOfMandatoryColumns => GetOneOfRequiredMessage(context),
        ErrorCodes.EffectiveDateOutsidePolicyDates => GetEffectiveDateOutsidePolicyDatesMessage(displayName, context),
        ErrorCodes.UnexpectedError => GetUnexpectedErrorMessage(context),
        ErrorCodes.NotFound => GetNotFoundMessage(displayName, context),
        ErrorCodes.InvalidState => GetInvalidStateMessage(displayName, context),
        ErrorCodes.NotSatisfied => GetNotSatisfiedMessage(displayName, context),
        ErrorCodes.BadSchemaConfig => GetBadSchemaConfigMessage(context),
        ErrorCodes.PolicyNotFound => GetPolicyNotFoundMessage(context),
        ErrorCodes.PolicyIssued => GetPolicyIssuedMessage(context),
        ErrorCodes.PolicyContractHolderNotFound => GetPolicyContractHolderNotFoundMessage(context),
        ErrorCodes.PolicyProductIdMissing => GetPolicyProductIdMissingMessage(context),
        ErrorCodes.PolicyMemberNotFound => GetPolicyMemberNotFoundMessage(context),
        ErrorCodes.PolicyMemberExists => GetPolicyMemberExistsMessage(context),
        ErrorCodes.PolicyAlreadyCancelled => GetPolicyAlreadyCancelledMessage(context),
        ErrorCodes.InvalidProductIdComponent => GetInvalidProductIdComponentMessage(context),
        ErrorCodes.InvalidPolicyMemberStateDate => GetInvalidPolicyMemberStateDateMessage(context),
        ErrorCodes.PolicyMemberStateOverlap => GetPolicyMemberStateOverlapMessage(context),
        ErrorCodes.EndorsementCannotBeChanged => GetEndorsementCannotBeChangedMessage(context),
        ErrorCodes.EndorsementNotFound => GetEndorsementNotFoundMessage(context),
        ErrorCodes.NoValidMembersForImport => GetNoValidMembersForImportMessage(context),
        ErrorCodes.InvalidPolicyMemberUploadStatus => GetInvalidPolicyMemberUploadStatusMessage(context),
        ErrorCodes.PolicyMemberUploadNotFound => GetPolicyMemberUploadNotFoundMessage(context),
        ErrorCodes.UploadFileNotFound => GetUploadFileNotFoundMessage(context),
        ErrorCodes.UploadFileProcessingError => GetUploadFileProcessingErrorMessage(context),
        ErrorCodes.ValidationFailed => GetValidationFailedMessage(context),
        _ => $"{displayName} validation failed"
    };

    #region Message Generation Helpers

    private static string GetUniqueViolationMessage(string displayName, IReadOnlyDictionary<string, object?> context)
    {
        string? scope = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Scope)?.ToString() ?? ValidationConstants.DefaultValues.DefaultScope;
        return $"{displayName} must be unique within {scope}";
    }

    private static string GetInvalidOptionMessage(string displayName, IReadOnlyDictionary<string, object?> context)
    {
        object? optionsValue = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.AvailableOptions);
        return optionsValue is IEnumerable<string> options
            ? $"{displayName} must be one of: {string.Join(", ", options)}"
            : $"{displayName} has invalid option";
    }

    private static string GetPatternMismatchMessage(string displayName, IReadOnlyDictionary<string, object?> context)
    {
        object? patternValue = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Pattern);
        return patternValue != null
            ? $"{displayName} does not match required pattern: {patternValue}"
            : $"{displayName} does not match required pattern";
    }

    private static string GetOutOfRangeMessage(string displayName, IReadOnlyDictionary<string, object?> context)
    {
        object? minValue = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MinValue);
        object? maxValue = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MaxValue);
        bool hasMin = minValue != null;
        bool hasMax = maxValue != null;

        if (hasMin && hasMax)
        {
            return $"{displayName} must be between {minValue} and {maxValue}";
        }

        if (hasMin)
        {
            return $"{displayName} must be at least {minValue}";
        }

        if (hasMax)
        {
            return $"{displayName} must be at most {maxValue}";
        }

        return $"{displayName} is out of range";
    }

    private static string GetTooLongMessage(string displayName, IReadOnlyDictionary<string, object?> context)
    {
        object? maxLength = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MaxLength);
        return maxLength != null
            ? $"{displayName} cannot exceed {maxLength} characters"
            : $"{displayName} is too long";
    }

    private static string GetTooShortMessage(string displayName, IReadOnlyDictionary<string, object?> context)
    {
        object? minLength = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MinLength);
        return minLength != null
            ? $"{displayName} must be at least {minLength} characters"
            : $"{displayName} is too short";
    }

    private static string GetMemberNotFoundMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? memberId = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MemberId);
        return memberId != null ? $"Primary Member with ID '{memberId}' was not found" : "Member was not found";
    }

    private static string GetMemberIdTakenMessage(IReadOnlyDictionary<string, object?> context)
    {
        string? memberId = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MemberId)?.ToString() ?? "unknown";
        string? existingId = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.ExistingPolicyMemberId)?.ToString() ?? "unknown";
        return $"Member ID '{memberId}' is already taken by policy member {existingId}";
    }

    private static string GetMemberNotInContractHolderMessage(IReadOnlyDictionary<string, object?> context)
    {
        string? memberId = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MemberId)?.ToString() ?? "unknown";
        string? policyId = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.PolicyId)?.ToString() ?? "unknown";
        return $"Member '{memberId}' is not in contract holder for policy {policyId}";
    }

    private static string GetInvalidPlanIdMessage(IReadOnlyDictionary<string, object?> context)
    {
        string? planId = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.PlanId)?.ToString() ?? "unknown";
        string? productId = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.ProductId)?.ToString() ?? "unknown";
        return $"Plan {planId} not found on product {productId}";
    }

    private static string GetAgeTooLowMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? minAge = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MinAge);
        return minAge != null ? $"Employee age must be at least {minAge} years" : "Employee age is too low";
    }

    private static string GetAgeTooHighMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? maxAge = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MaxAge);
        return maxAge != null ? $"Employee age cannot exceed {maxAge} years" : "Employee age is too high";
    }

    private static string GetEmployeeMinAgeMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? minAge = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MinAge);
        return minAge != null
            ? $"You can not employ someone below {minAge} years old"
            : "Employee age is below minimum required";
    }

    private static string GetEmployeeMaxAgeMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? maxAge = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MaxAge);
        return maxAge != null
            ? $"You can not employ someone above {maxAge} years old"
            : "Employee age is above maximum allowed";
    }

    private static string GetChildMinDaysMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? minDays = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MinDays);
        if (minDays is int days)
        {
            string suffix = days > 1 ? "s" : "";
            return $"Child minimum age should be {days} day{suffix}";
        }

        return "Child age is below minimum required days";
    }

    private static string GetSpouseMinAgeMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? minAge = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MinAge);
        return minAge != null
            ? $"Spouse minimum age should be {minAge} years old"
            : "Spouse age is below minimum required";
    }

    private static string GetMissingColumnsMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? columnsValue = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.MissingColumns);
        return columnsValue is IEnumerable<string> columns
            ? $"Mandatory column(s) are missing: {string.Join(", ", columns)}"
            : "Mandatory columns are missing";
    }

    private static string GetExtraColumnsMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? columnsValue = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.ExtraColumns);
        return columnsValue is IEnumerable<string> columns
            ? $"Please remove column(s): {string.Join(", ", columns)} in your upload file and reupload"
            : "Extra columns found in upload file";
    }

    private static string GetOneOfRequiredMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? fieldsValue = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.RequiredFields);
        return fieldsValue is IEnumerable<string> fields
            ? $"Missing one of mandatory columns: {string.Join(", ", fields)}"
            : "Missing one of mandatory columns";
    }

    private static string GetUnexpectedErrorMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? errorMessage = context.TryGetValueOrDefault(ValidationConstants.Labels.ErrorMessage);
        return errorMessage?.ToString() ?? "Failed to parse file content";
    }

    private static string GetInvalidRowMessage(IReadOnlyDictionary<string, object?> context)
    {
        // Check for custom error message first
        object? errorMessage = context.TryGetValueOrDefault(ValidationConstants.Labels.ErrorMessage);
        if (errorMessage != null)
            return errorMessage.ToString()!;

        object? rowIndexValue = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.RowIndex);
        return rowIndexValue is int rowIndex
            ? $"Invalid content at row index {rowIndex}"
            : "Invalid row content in file";
    }

    private static string GetEffectiveDateOutsidePolicyDatesMessage(string displayName, IReadOnlyDictionary<string, object?> context)
    {
        object? policyId = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.PolicyId);
        return policyId != null
            ? $"{displayName} is outside of Policy {policyId} start date and end date"
            : $"{displayName} is outside of policy start date and end date";
    }

    private static string GetNotFoundMessage(string displayName, IReadOnlyDictionary<string, object?> context)
    {
        object? value = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Value);
        return value != null
            ? $"{displayName} '{value}' was not found"
            : $"{displayName} was not found";
    }

    private static string GetInvalidStateMessage(string displayName, IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message != null
            ? $"{displayName}: {message}"
            : $"{displayName} is in an invalid state";
    }

    private static string GetInvalidFileFormatMessage(string fileType, IReadOnlyDictionary<string, object?> context)
    {
        object? errorMessage = context.TryGetValueOrDefault(ValidationConstants.Labels.ErrorMessage);
        return errorMessage != null
            ? $"Invalid {fileType} file format: {errorMessage}"
            : $"Invalid {fileType} file format";
    }

    private static string GetUnsupportedFileTypeMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? fileType = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.FileType);
        return fileType != null
            ? $"Unsupported file type: {fileType}"
            : "Unsupported file type";
    }

    private static string GetNotSatisfiedMessage(string displayName, IReadOnlyDictionary<string, object?> context)
    {
        string? originalRule = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.OriginalRule)?.ToString();
        return originalRule != null
            ? $"{displayName} does not satisfy the business rule: {originalRule}"
            : $"{displayName} does not satisfy the required business rule";
    }

    private static string GetBadSchemaConfigMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Schema configuration error";
    }

    private static string GetPolicyNotFoundMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Policy not found";
    }

    private static string GetPolicyIssuedMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Policy is already issued and cannot be modified";
    }

    private static string GetPolicyContractHolderNotFoundMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Policy contract holder not found";
    }

    private static string GetPolicyProductIdMissingMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Policy is missing required ProductId";
    }

    private static string GetPolicyMemberNotFoundMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Policy member not found";
    }

    private static string GetPolicyMemberExistsMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Policy member already exists";
    }

    private static string GetPolicyAlreadyCancelledMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Policy is already cancelled";
    }

    private static string GetInvalidProductIdComponentMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Policy ProductId component is invalid or missing";
    }

    private static string GetInvalidPolicyMemberStateDateMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Policy member state has invalid date range";
    }

    private static string GetPolicyMemberStateOverlapMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Policy member states have overlapping date ranges";
    }

    private static string GetEndorsementCannotBeChangedMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Endorsement cannot be changed due to its current status";
    }

    private static string GetEndorsementNotFoundMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Endorsement not found";
    }

    private static string GetNoValidMembersForImportMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Upload has no valid members for import";
    }

    private static string GetInvalidPolicyMemberUploadStatusMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Policy member upload has invalid status for the requested operation";
    }

    private static string GetPolicyMemberUploadNotFoundMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Policy member upload not found";
    }

    private static string GetUploadFileNotFoundMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Upload file not found";
    }

    private static string GetUploadFileProcessingErrorMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Error occurred during upload file processing";
    }

    private static string GetValidationFailedMessage(IReadOnlyDictionary<string, object?> context)
    {
        object? message = context.TryGetValueOrDefault(ValidationConstants.ContextKeys.Message);
        return message?.ToString() ?? "Validation failed";
    }

    #endregion

    /// <summary>
    /// Returns a string representation of this validation error.
    /// </summary>
    public override string ToString() => $"[{Code}] {PropertyPath}: {Message}";
}