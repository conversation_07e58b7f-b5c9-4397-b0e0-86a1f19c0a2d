namespace CoverGo.PoliciesV3.Domain.Common.Validation;

/// <summary>
/// Exception thrown when validation fails and exceptions are preferred over the Result pattern.
/// This exception contains all validation errors that occurred.
/// </summary>
public class ValidationException : DomainException
{
    /// <summary>
    /// Initializes a new instance of the ValidationException class with validation errors.
    /// </summary>
    /// <param name="errors">The validation errors</param>
    public ValidationException(IReadOnlyList<ValidationError> errors)
        : base(CreateMessage(errors))
    {
        Errors = errors ?? throw new ArgumentNullException(nameof(errors));

        if (errors.Count == 0)
            throw new ArgumentException("Cannot create ValidationException with no errors", nameof(errors));
    }

    /// <summary>
    /// Initializes a new instance of the ValidationException class with a single validation error.
    /// </summary>
    /// <param name="error">The validation error</param>
    public ValidationException(ValidationError error)
        : this([error])
    {
    }

    /// <summary>
    /// Initializes a new instance of the ValidationException class with multiple validation errors.
    /// </summary>
    /// <param name="errors">The validation errors</param>
    private ValidationException(params ValidationError[] errors)
        : this((IReadOnlyList<ValidationError>)errors)
    {
    }

    /// <summary>
    /// Gets the validation errors that caused this exception.
    /// </summary>
    public IReadOnlyList<ValidationError> Errors { get; }

    /// <inheritdoc />
    public override string Code => ErrorCodes.ValidationFailed;

    /// <summary>
    /// Creates an appropriate error message from the validation errors.
    /// </summary>
    /// <param name="errors">The validation errors</param>
    /// <returns>A formatted error message</returns>
    private static string CreateMessage(IReadOnlyList<ValidationError> errors) => errors.Count switch
    {
        0 => "Validation failed",
        1 => $"Validation failed: {errors[0].Message}",
        _ => $"Validation failed with {errors.Count} error(s): {string.Join("; ", errors.Select(e => e.Message))}"
    };

    /// <summary>
    /// Gets the first validation error for a specific property path, or null if not found.
    /// </summary>
    /// <param name="propertyPath">The property path</param>
    /// <returns>The first validation error for the property, or null</returns>
    public ValidationError? GetErrorForProperty(string propertyPath) => Errors.FirstOrDefault(e => e.PropertyPath == propertyPath);

    /// <summary>
    /// Gets all validation errors for a specific property path.
    /// </summary>
    /// <param name="propertyPath">The property path</param>
    /// <returns>All validation errors for the property</returns>
    public IEnumerable<ValidationError> GetErrorsForProperty(string propertyPath) => Errors.Where(e => e.PropertyPath == propertyPath);

    /// <summary>
    /// Checks if there are any validation errors for a specific property path.
    /// </summary>
    /// <param name="propertyPath">The property path</param>
    /// <returns>True if there are errors for the property, false otherwise</returns>
    public bool HasErrorsForProperty(string propertyPath) => Errors.Any(e => e.PropertyPath == propertyPath);

    /// <summary>
    /// Gets all unique error codes from the validation errors.
    /// </summary>
    /// <returns>A set of unique error codes</returns>
    public HashSet<string> GetErrorCodes() => [.. Errors.Select(e => e.Code)];

    /// <summary>
    /// Gets all unique property paths from the validation errors.
    /// </summary>
    /// <returns>A set of unique property paths</returns>
    public HashSet<string> GetPropertyPaths() => [.. Errors.Select(e => e.PropertyPath)];
}
