namespace CoverGo.PoliciesV3.Domain.Common;

/// <summary>
/// Represents a unit type for operations that don't return a meaningful value.
/// Used with Result<Unit> pattern for operations that can succeed or fail but don't return data.
/// </summary>
public readonly struct Unit : IEquatable<Unit>
{
    /// <summary>
    /// The singleton instance of Unit.
    /// </summary>
    public static readonly Unit Value = new();

    /// <summary>
    /// Determines whether the specified Unit is equal to the current Unit.
    /// Since Unit has no state, all instances are equal.
    /// </summary>
    /// <param name="other">The Unit to compare with the current Unit</param>
    /// <returns>Always true since all Unit instances are equal</returns>
    public bool Equals(Unit other) => true;

    /// <summary>
    /// Determines whether the specified object is equal to the current Unit.
    /// </summary>
    /// <param name="obj">The object to compare with the current Unit</param>
    /// <returns>True if the object is a Unit; otherwise, false</returns>
    public override bool Equals(object? obj) => obj is Unit;

    /// <summary>
    /// Returns the hash code for this Unit.
    /// Since all Unit instances are equal, they all have the same hash code.
    /// </summary>
    /// <returns>A constant hash code</returns>
    public override int GetHashCode() => 0;

    /// <summary>
    /// Returns a string representation of the Unit.
    /// </summary>
    /// <returns>A string representation of the Unit</returns>
    public override string ToString() => "()";

    /// <summary>
    /// Determines whether two Unit instances are equal.
    /// </summary>
    /// <param name="left">The first Unit to compare</param>
    /// <param name="right">The second Unit to compare</param>
    /// <returns>Always true since all Unit instances are equal</returns>
    public static bool operator ==(Unit left, Unit right) => true;

    /// <summary>
    /// Determines whether two Unit instances are not equal.
    /// </summary>
    /// <param name="left">The first Unit to compare</param>
    /// <param name="right">The second Unit to compare</param>
    /// <returns>Always false since all Unit instances are equal</returns>
    public static bool operator !=(Unit left, Unit right) => false;
}
