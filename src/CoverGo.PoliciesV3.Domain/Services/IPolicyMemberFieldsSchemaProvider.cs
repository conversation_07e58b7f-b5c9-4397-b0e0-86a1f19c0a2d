using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Domain.Services;

/// <summary>
/// Provider interface for retrieving and processing policy member field schemas
/// This combines Product/Census/Member schemas and can be reused across multiple features
/// </summary>
public interface IPolicyMemberFieldsSchemaProvider
{
    /// <summary>
    /// Gets the base custom fields schema for a specific policy configuration
    /// Used by: Manual member addition, Member management, Member uploads
    /// </summary>
    /// <param name="contractHolderId">Optional contract holder ID</param>
    /// <param name="productId">Product ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The base policy member fields schema</returns>
    Task<PolicyMemberFieldsSchema> GetCustomFieldsSchema(
        string? contractHolderId,
        ProductId productId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the schema with upload-specific processing applied
    /// Used by: Member upload features
    /// </summary>
    /// <param name="contractHolderId">Optional contract holder ID</param>
    /// <param name="productId">Product ID</param>
    /// <param name="endorsementId">Optional endorsement ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The processed schema for member uploads</returns>
    Task<PolicyMemberFieldsSchema> GetMemberUploadSchema(
        string? contractHolderId,
        ProductId productId,
        EndorsementId? endorsementId,
        CancellationToken cancellationToken = default);
}
