using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Domain.Services;

/// <summary>
/// Domain service for validating policy member uniqueness across different scopes
/// Encapsulates business rules for uniqueness validation that were previously in infrastructure layer
/// </summary>
public interface IPolicyMemberUniquenessService
{
    /// <summary>
    /// Validates uniqueness within tenant scope
    /// Business rule: Certain fields must be unique across all policies within a tenant
    /// </summary>
    Task<List<string>> ValidateTenantScopeUniquenessAsync(
        PolicyId currentPolicyId,
        string? currentMemberId,
        PolicyMemberId? currentPolicyMemberId,
        Dictionary<string, object> fieldValues,
        List<string> fieldNames,
        List<EndorsementId> validEndorsementIds,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates uniqueness within policy scope
    /// Business rule: Certain fields must be unique within a single policy
    /// </summary>
    Task<List<string>> ValidatePolicyScopeUniquenessAsync(
        PolicyId currentPolicyId,
        string? currentMemberId,
        PolicyMemberId? currentPolicyMemberId,
        Dictionary<string, object> fieldValues,
        List<string> fieldNames,
        List<EndorsementId> validEndorsementIds,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates uniqueness within contract holder scope
    /// Business rule: Certain fields must be unique across all policies for a contract holder
    /// </summary>
    Task<List<string>> ValidateContractHolderScopeUniquenessAsync(
        string? currentMemberId,
        PolicyMemberId? currentPolicyMemberId,
        List<PolicyId> contractHolderPolicyIds,
        List<EndorsementId> validEndorsementIds,
        Dictionary<string, object> fieldValues,
        List<string> fieldNames,
        CancellationToken cancellationToken = default);

}