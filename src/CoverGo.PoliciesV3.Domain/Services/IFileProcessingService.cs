using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Domain.Services;

/// <summary>
/// Domain service for file processing operations during policy member upload validation.
/// Handles file retrieval, parsing, and data extraction with proper error handling.
/// This service contains business logic for file processing rules and delegates infrastructure concerns.
/// </summary>
public interface IFileProcessingService
{
    /// <summary>
    /// Processes an upload file and extracts member data for validation.
    /// Business rules:
    /// - File must exist and be accessible
    /// - File must be parseable using configured parsers
    /// - Must return structured member data for validation processing
    /// </summary>
    /// <param name="policyId">Policy ID for error context</param>
    /// <param name="filePath">Path to the upload file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File processing result with member data or error information</returns>
    Task<FileProcessingResult> ProcessUploadFileAsync(
        string policyId,
        string filePath,
        CancellationToken cancellationToken);
}
