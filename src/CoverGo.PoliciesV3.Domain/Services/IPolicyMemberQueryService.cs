using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Domain.Services;

/// <summary>
/// Domain service for complex policy member queries that contain business logic
/// Encapsulates business rules for member retrieval that were previously in infrastructure layer
/// </summary>
public interface IPolicyMemberQueryService
{
    /// <summary>
    /// Gets active policy members for given policies
    /// Business rule: Active members are those with approved underwriting results or no underwriting result
    /// </summary>
    Task<List<PolicyMember>> GetActiveMembersAsync(
        List<PolicyId> policyIds,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets member validation states across multiple policies for validation purposes
    /// Business rule: Returns latest member for each policy with proper endorsement filtering
    /// </summary>
    Task<List<PolicyMember>?> GetMemberValidationStatesAsync(
        string memberId,
        List<PolicyId> policyIds,
        List<EndorsementId> validEndorsementIds,
        CancellationToken cancellationToken);

    /// <summary>
    /// Gets current policy member state for a specific policy
    /// Business rule: Returns the most recently modified member with valid endorsements
    /// </summary>
    Task<PolicyMember?> GetPolicyMemberCurrentStateAsync(
        string memberId,
        PolicyId policyId,
        List<EndorsementId> validEndorsementIds,
        CancellationToken cancellationToken);

    /// <summary>
    /// Gets member validation states by member ID across all policies
    /// Business rule: Returns latest member for each policy grouped by policy ID
    /// </summary>
    Task<List<PolicyMember>> GetMemberValidationStatesByMemberIdAsync(
        string memberId,
        CancellationToken cancellationToken);

    /// <summary>
    /// Gets current policy member states for multiple member IDs in batch
    /// Business rule: Optimized batch processing with proper endorsement filtering
    /// </summary>
    Task<Dictionary<string, PolicyMember?>> GetPolicyMembersBatchAsync(
        List<string> memberIds,
        PolicyId policyId,
        List<EndorsementId> validEndorsementIds,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets member validation states for multiple member IDs in batch
    /// Business rule: Returns latest member for each policy grouped by policy ID for each member
    /// </summary>
    Task<Dictionary<string, List<PolicyMember>>> GetMemberValidationStatesBatchAsync(
        List<string> memberIds,
        CancellationToken cancellationToken = default);
}