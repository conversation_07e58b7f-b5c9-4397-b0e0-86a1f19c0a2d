using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;

/// <summary>
/// Exception thrown when an endorsement cannot be changed due to its status
/// </summary>
public class EndorsementCanNotBeChangedException(string policyId, string endorsementId, string status) : DomainException(ErrorMessages.EndorsementCannotBeChanged(policyId, endorsementId, status))
{
    public override string Code => ErrorCodes.EndorsementCannotBeChanged;

    public string PolicyId { get; } = policyId;
    public string EndorsementId { get; } = endorsementId;
    public string Status { get; } = status;
}
