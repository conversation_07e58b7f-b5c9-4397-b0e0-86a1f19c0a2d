using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;

/// <summary>
/// Exception thrown when effective date is outside policy date range
/// </summary>
public class EffectiveDateOutsidePolicyDatesException : DomainException
{
    public EffectiveDateOutsidePolicyDatesException(string policyId)
        : base(ErrorMessages.EffectiveDateOutsidePolicyDates(policyId))
    {
        PolicyId = policyId;
    }

    public EffectiveDateOutsidePolicyDatesException(string policyId, Exception innerException)
        : base(ErrorMessages.EffectiveDateOutsidePolicyDates(policyId), innerException)
    {
        PolicyId = policyId;
    }

    public override string Code => ErrorCodes.EffectiveDateOutsidePolicyDates;

    public string PolicyId { get; }
}
