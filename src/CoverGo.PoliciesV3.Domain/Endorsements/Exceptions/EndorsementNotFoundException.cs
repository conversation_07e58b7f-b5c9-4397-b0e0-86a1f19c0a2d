using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;

/// <summary>
/// Exception thrown when an endorsement cannot be found
/// </summary>
public class EndorsementNotFoundException : DomainException
{
    public EndorsementNotFoundException(string policyId, string endorsementId)
        : base(ErrorMessages.EndorsementNotFound(policyId, endorsementId))
    {
        PolicyId = policyId;
        EndorsementId = endorsementId;
    }

    public EndorsementNotFoundException(string policyId, string endorsementId, Exception innerException)
        : base(ErrorMessages.EndorsementNotFound(policyId, endorsementId), innerException)
    {
        PolicyId = policyId;
        EndorsementId = endorsementId;
    }

    public override string Code => ErrorCodes.EndorsementNotFound;

    public string PolicyId { get; }
    public string EndorsementId { get; }
}
