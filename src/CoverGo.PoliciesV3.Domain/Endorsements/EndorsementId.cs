using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.Endorsements;

/// <summary>
/// Value object that represents an id of endorsement.
/// </summary>
public record EndorsementId(Guid Value) : ValueObject<Guid>(Value)
{
    public static EndorsementId Empty => new(Guid.Empty);
    public static EndorsementId New => new(Guid.CreateVersion7());

    /// <summary>
    /// Creates an EndorsementId from a Guid
    /// </summary>
    public static EndorsementId From(Guid value) => new(value);

    /// <summary>
    /// Implicit conversion from Guid to EndorsementId
    /// </summary>
    public static implicit operator EndorsementId(Guid value) => new(value);

    /// <summary>
    /// Implicit conversion from string to EndorsementId
    /// </summary>
    public static implicit operator EndorsementId(string value) =>
        string.IsNullOrWhiteSpace(value) ? throw new ArgumentException("EndorsementId cannot be null or empty", nameof(value))
        : new(Guid.Parse(value));

    /// <summary>
    /// Implicit conversion from EndorsementId to Guid
    /// </summary>
    public static implicit operator Guid(EndorsementId endorsementId) => endorsementId.Value;
}

