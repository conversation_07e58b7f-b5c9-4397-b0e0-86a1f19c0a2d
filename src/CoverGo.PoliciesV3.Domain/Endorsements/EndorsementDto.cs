namespace CoverGo.PoliciesV3.Domain.Endorsements;

/// <summary>
/// Simplified endorsement data transfer object containing only essential endorsement information
/// used for policy validation and business logic.
/// </summary>
public sealed record EndorsementDto
{
    /// <summary>
    /// Unique endorsement identifier
    /// </summary>
    public required string Id { get; init; }

    /// <summary>
    /// Endorsement status (e.g., "APPROVED", "REJECTED", "CANCELED", "PENDING")
    /// Used for validation to determine if endorsement can be changed
    /// </summary>
    public required string Status { get; init; }

    /// <summary>
    /// Determines if this endorsement can be changed based on its status
    /// </summary>
    public bool CanBeChanged => Status switch
    {
        "APPROVED" => false,
        "REJECTED" => false,
        "CANCELED" => false,
        _ => true
    };

    /// <summary>
    /// Determines if this endorsement is approved
    /// </summary>
    public bool IsApproved => Status == "APPROVED";
}
