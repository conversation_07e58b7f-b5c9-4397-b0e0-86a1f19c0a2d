using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.FileProcessing;

/// <summary>
/// Infrastructure implementation of file processing service for policy member upload validation.
/// </summary>
public class FileProcessingService(
    IFileSystemService fileSystemService,
    IFileParserFactory fileParserFactory,
    ILogger<FileProcessingService> logger) : IFileProcessingService
{
    private readonly IFileSystemService fileSystemService = fileSystemService;
    private readonly IFileParserFactory fileParserFactory = fileParserFactory;
    private readonly ILogger<FileProcessingService> logger = logger;

    /// <summary>
    /// Processes an upload file and extracts member data for validation.
    /// </summary>
    public async Task<FileProcessingResult> ProcessUploadFileAsync(
        string policyId,
        string filePath,
        CancellationToken cancellationToken)
    {
        logger.LogInformation("Getting upload file data for PolicyId {PolicyId}, FilePath {FilePath}", policyId,
            filePath);

        try
        {
            // Get file content from file system service
            byte[]? fileContent =
                await fileSystemService.GetFileByPath(filePath, cancellationToken).ConfigureAwait(false);
            if (fileContent == null)
            {
                logger.LogWarning("File not found for FilePath {FilePath}", filePath);
                throw new UploadFileNotFoundException(policyId, filePath);
            }

            logger.LogInformation("Retrieved file content for FilePath {FilePath}, Size: {FileSize} bytes",
                filePath, fileContent.Length);

            // Parse file content using existing file parser infrastructure
            IFileParser parser = fileParserFactory.CreateParser(fileContent);

            FileParseResult parseResult = await parser.ParseFileAsync(fileContent, cancellationToken).ConfigureAwait(false);
            logger.LogInformation("Parsed file content, MembersRawData count: {MembersRawDataCount}",
                parseResult.Count);

            return FileProcessingResult.Success(parseResult.Contents, fileContent.Length);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during GetUploadFileData for PolicyId {PolicyId}, FilePath {FilePath}", policyId,
                filePath);
            throw;
        }
    }
}
