using System.Text;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

namespace CoverGo.PoliciesV3.Infrastructure.FileProcessing;

public sealed class CsvFileParser : IFileParser
{
    private const char Delimiter = ',';

    public FileParseResult ParseFile(byte[] fileContent)
    {
        try
        {
            string[] rows = GetRows(fileContent);

            if (rows.Length == 0)
                throw new BadFileContentException([Errors.EmptyFile("file", ValidationConstants.Labels.UploadFile)]);

            string[] headers = rows[0].Split(Delimiter, StringSplitOptions.TrimEntries);
            var contents = new List<IReadOnlyDictionary<string, string?>>();

            for (int rowIndex = 1; rowIndex < rows.Length; rowIndex++)
            {
                string[] data = rows[rowIndex].Split(Delimiter, StringSplitOptions.TrimEntries);

                if (data.Length != headers.Length)
                    throw new BadFileContentException([Errors.InvalidFileRow(rowIndex, headers.Length, data.Length)]);

                var dict = new Dictionary<string, string?>();
                for (int i = 0; i < headers.Length; i++)
                {
                    dict[headers[i]] = string.IsNullOrWhiteSpace(data[i]) ? null : data[i];
                }

                contents.Add(dict);
            }

            var result = new FileParseResult
            {
                Headers = [.. headers],
                Contents = contents
            };

            // Initialize the headers set for optimized lookups
            result.InitializeHeadersSet();

            return result;
        }
        catch (BadFileContentException)
        {
            throw;
        }
        catch (Exception exception)
        {
            throw new BadFileContentException([Errors.UnexpectedError($"Failed to parse CSV file: {exception.Message}")], exception);
        }
    }

    public async Task<FileParseResult> ParseFileAsync(byte[] fileContent, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() => ParseFile(fileContent), cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Gets rows from file content using memory-efficient string processing.
    /// Uses StringReader for line-by-line processing to reduce memory allocations.
    /// </summary>
    private static string[] GetRows(byte[] fileContent)
    {
        if (fileContent == null || fileContent.Length == 0)
            return [];

        string content = Encoding.UTF8.GetString(fileContent);
        var rows = new List<string>();

        using var reader = new StringReader(content);
        string? line;
        while ((line = reader.ReadLine()) != null)
        {
            string trimmedLine = line.Trim();
            if (!string.IsNullOrEmpty(trimmedLine))
            {
                rows.Add(trimmedLine);
            }
        }

        return [.. rows];
    }
}