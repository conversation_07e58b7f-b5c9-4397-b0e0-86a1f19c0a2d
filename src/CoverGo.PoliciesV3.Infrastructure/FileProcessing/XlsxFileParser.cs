using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace CoverGo.PoliciesV3.Infrastructure.FileProcessing;

public sealed class XlsxFileParser : IFileParser
{
    public FileParseResult ParseFile(byte[] fileContent)
    {
        try
        {
            using XSSFWorkbook workbook = CreateWorkbook(fileContent);
            ISheet sheet = GetFirstSheet(workbook);
            IRow headerRow = sheet.GetRow(sheet.FirstRowNum) ?? throw new BadFileContentException([Errors.NoColumn("file", ValidationConstants.Labels.UploadFile, "No header row found in Excel file")]);

            string[] headers = [.. headerRow.Select(GetCellStringValue)];
            var contents = new List<IReadOnlyDictionary<string, string?>>();

            for (int rowIndex = sheet.FirstRowNum + 1; rowIndex <= sheet.LastRowNum; rowIndex++)
            {
                IRow? row = sheet.GetRow(rowIndex);
                if (row == null || IsEmptyRow(row))
                    continue;

                var dict = new Dictionary<string, string?>();
                for (int colIndex = 0; colIndex < headers.Length; colIndex++)
                {
                    string cellValue = GetCellValue(row, colIndex);
                    dict[headers[colIndex]] = string.IsNullOrWhiteSpace(cellValue) ? null : cellValue;
                }
                contents.Add(dict);
            }

            var result = new FileParseResult
            {
                Headers = [.. headers],
                Contents = contents
            };

            // Initialize the headers set for optimized lookups
            result.InitializeHeadersSet();

            return result;
        }
        catch (BadFileContentException)
        {
            throw;
        }
        catch (Exception exception)
        {
            throw new BadFileContentException([Errors.InvalidXlsxFile("file", ValidationConstants.Labels.UploadFile)], exception);
        }
    }

    public async Task<FileParseResult> ParseFileAsync(byte[] fileContent, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() => ParseFile(fileContent), cancellationToken).ConfigureAwait(false);
    }

    private static XSSFWorkbook CreateWorkbook(byte[] fileContent) => fileContent == null || fileContent.Length == 0
            ? throw new BadFileContentException([Errors.EmptyFile("file", ValidationConstants.Labels.UploadFile)])
            : new XSSFWorkbook(new MemoryStream(fileContent));

    private static ISheet GetFirstSheet(XSSFWorkbook workbook) => workbook.NumberOfSheets == 0
            ? throw new BadFileContentException([Errors.InvalidXlsxFile("file", ValidationConstants.Labels.UploadFile)])
            : workbook.GetSheetAt(0);

    private static string GetCellStringValue(ICell? cell) => cell?.StringCellValue ?? string.Empty;

    private static string GetCellValue(IRow row, int columnIndex)
    {
        ICell? cell = row.GetCell(columnIndex);
        if (cell == null)
            return string.Empty;

        return cell.CellType switch
        {
            CellType.Numeric => GetNumericCellValue(cell),
            CellType.Boolean => cell.BooleanCellValue.ToString(),
            CellType.String => cell.StringCellValue,
            CellType.Formula => GetFormulaValue(cell),
            CellType.Blank => string.Empty,
            _ => string.Empty
        };
    }

    private static string GetNumericCellValue(ICell cell)
    {
        return DateUtil.IsCellDateFormatted(cell)
            ? string.Format("{0:yyyy-MM-dd}", cell.DateCellValue)
            : cell.NumericCellValue.ToString();
    }

    private static string GetFormulaValue(ICell cell)
    {
        try
        {
            return cell.CachedFormulaResultType switch
            {
                CellType.Numeric => GetNumericCellValue(cell),
                CellType.String => cell.StringCellValue,
                CellType.Boolean => cell.BooleanCellValue.ToString(),
                CellType.Blank => string.Empty,
                _ => string.Empty
            };
        }
        catch
        {
            return string.Empty;
        }
    }

    private static bool IsEmptyRow(IRow row) => row.Cells.All(IsBlankCell);

    private static bool IsBlankCell(ICell cell) => cell.CellType == CellType.Blank ||
               cell.CellType == CellType.String && string.IsNullOrWhiteSpace(cell.StringCellValue);
}