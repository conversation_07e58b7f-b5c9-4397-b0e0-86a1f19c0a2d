using System.Text.Json;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Validators;
using CoverGo.PoliciesV3.Domain.CustomFields.Validation;
using CoverGo.PoliciesV3.Infrastructure.Caching.Dtos;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.Caching.Extensions;

/// <summary>
/// Extension methods for mapping between PolicyMemberFieldsSchema domain objects and DTOs.
/// Handles the conversion to/from cacheable DTOs while preserving all business logic.
/// </summary>
public static class PolicyMemberFieldsSchemaMappingExtensions
{
    #region Domain to DTO Mapping

    /// <summary>
    /// Converts a PolicyMemberFieldsSchema domain object to a cacheable DTO.
    /// </summary>
    public static PolicyMemberFieldsSchemaDto ToDto(this PolicyMemberFieldsSchema schema)
    {
        ArgumentNullException.ThrowIfNull(schema);

        return new PolicyMemberFieldsSchemaDto
        {
            SystemMemberFields = [.. schema.MemberSystemFields.Select(ToDto)],
            CustomMemberFields = [.. schema.MemberCustomFields.Select(ToDto)],
            ProductFields = [.. schema.ProductFields.Select(ToDto)],
            CensusFields = [.. schema.CensusFields.Select(ToDto)],
            OneOfValidations = [.. schema.OneOfValidations.Select(ToDto)]
        };
    }

    /// <summary>
    /// Converts a PolicyMemberFieldDefinition to a DTO.
    /// </summary>
    public static PolicyMemberFieldDefinitionDto ToDto(this PolicyMemberFieldDefinition field)
    {
        ArgumentNullException.ThrowIfNull(field);

        return new PolicyMemberFieldDefinitionDto
        {
            Name = field.Name,
            Label = field.Label,
            IsRequired = field.IsRequired,
            IsUnique = field.IsUnique,
            IsRequiredForDependent = field.IsRequiredForDependent,
            IsBenefitField = field.IsBenefitField,
            Type = field.Type.ToDto(),
            Condition = field.Condition?.Condition,
            ParentFieldName = field.Parent?.Name
        };
    }

    /// <summary>
    /// Converts an IFieldType to a DTO using type discrimination.
    /// </summary>
    public static FieldTypeDto ToDto(this IFieldType fieldType)
    {
        ArgumentNullException.ThrowIfNull(fieldType);

        return fieldType switch
        {
            StringFieldType stringType => new StringFieldTypeDto
            {
                Validations = stringType.Validations,
                ValidationInfo = stringType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList(),
                Options = stringType.Options?.Select(o => new StringOptionDto
                {
                    Value = o.Value,
                    Label = o.Label,
                    Name = o.Name ?? o.Label, // Use Name if available, fallback to Label
                    Key = o.Key
                }).ToList()
            },
            BooleanFieldType booleanType => new BooleanFieldTypeDto
            {
                Validations = booleanType.Validations,
                ValidationInfo = booleanType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList()
            },
            DateFieldType dateType => new DateFieldTypeDto
            {
                Validations = dateType.Validations,
                ValidationInfo = dateType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList()
            },
            NumberFieldType numberType => new NumberFieldTypeDto
            {
                Validations = numberType.Validations,
                ValidationInfo = numberType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList(),
                Options = numberType.Options?.Select(o => new NumberOptionDto
                {
                    Value = o.Value,
                    Label = o.Label,
                    Name = o.Name ?? o.Label, // Use Name if available, fallback to Label
                    Key = o.Key
                }).ToList()
            },
            AddressFieldType addressType => new AddressFieldTypeDto
            {
                Validations = addressType.Validations,
                ValidationInfo = addressType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList(),
                InnerFieldDefinitions = [.. addressType.InnerFieldDefinitions.Select(ToDtoWithoutParent)]
            },
            ObjectFieldType objectType => new ObjectFieldTypeDto
            {
                Validations = objectType.Validations,
                ValidationInfo = objectType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList(),
                InnerFieldDefinitions = [.. objectType.InnerFieldDefinitions.Select(ToDtoWithoutParent)],
                CheckExtraFields = objectType.CheckExtraFields
            },
            FormulaFieldType formulaType => new FormulaFieldTypeDto
            {
                Validations = formulaType.Validations,
                ValidationInfo = formulaType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList(),
                FormulaType = formulaType.Func?.GetType().Name,
                FormulaParameters = SerializeFormulaFunc(formulaType.Func, logger: null)
            },
            FilesFieldType filesType => new FilesFieldTypeDto
            {
                Validations = filesType.Validations,
                ValidationInfo = filesType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList()
            },
            _ => throw new NotSupportedException($"Field type {fieldType.GetType().Name} is not supported for caching")
        };
    }

    /// <summary>
    /// Converts a CustomFieldOneOfValidation to a DTO.
    /// </summary>
    public static CustomFieldOneOfValidationDto ToDto(this CustomFieldOneOfValidation validation)
    {
        ArgumentNullException.ThrowIfNull(validation);

        return new CustomFieldOneOfValidationDto
        {
            Validations = [.. validation.Validations.Select(v => new CustomFieldRequiredValidationDto
            {
                FieldName = v.Field.Name,
                FieldLabel = v.Field.Label
            })]
        };
    }

    /// <summary>
    /// Converts a PolicyMemberFieldDefinition to a DTO without circular references.
    /// Used for nested field definitions to prevent infinite loops.
    /// </summary>
    public static PolicyMemberFieldDefinitionDto ToDtoWithoutParent(this PolicyMemberFieldDefinition field)
    {
        ArgumentNullException.ThrowIfNull(field);

        return new PolicyMemberFieldDefinitionDto
        {
            Name = field.Name,
            Label = field.Label,
            IsRequired = field.IsRequired,
            IsUnique = field.IsUnique,
            IsRequiredForDependent = field.IsRequiredForDependent,
            IsBenefitField = field.IsBenefitField,
            Type = field.Type.ToDtoWithoutCircularRefs(),
            Condition = field.Condition?.Condition,
            ParentFieldName = null // Avoid circular references
        };
    }

    /// <summary>
    /// Converts an IFieldType to a DTO without circular references.
    /// Used for nested field definitions to prevent infinite loops.
    /// </summary>
    public static FieldTypeDto ToDtoWithoutCircularRefs(this IFieldType fieldType)
    {
        ArgumentNullException.ThrowIfNull(fieldType);

        return fieldType switch
        {
            StringFieldType stringType => new StringFieldTypeDto
            {
                Validations = stringType.Validations,
                ValidationInfo = stringType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList(),
                Options = stringType.Options?.Select(o => new StringOptionDto
                {
                    Value = o.Value,
                    Label = o.Label,
                    Name = o.Name ?? o.Label,
                    Key = o.Key
                }).ToList()
            },
            BooleanFieldType booleanType => new BooleanFieldTypeDto
            {
                Validations = booleanType.Validations,
                ValidationInfo = booleanType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList()
            },
            DateFieldType dateType => new DateFieldTypeDto
            {
                Validations = dateType.Validations,
                ValidationInfo = dateType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList()
            },
            NumberFieldType numberType => new NumberFieldTypeDto
            {
                Validations = numberType.Validations,
                ValidationInfo = numberType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList(),
                Options = numberType.Options?.Select(o => new NumberOptionDto
                {
                    Value = o.Value,
                    Label = o.Label,
                    Name = o.Name ?? o.Label,
                    Key = o.Key
                }).ToList()
            },
            AddressFieldType addressType => new AddressFieldTypeDto
            {
                Validations = addressType.Validations,
                ValidationInfo = addressType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList(),
                InnerFieldDefinitions = [] // Empty to prevent circular references
            },
            ObjectFieldType objectType => new ObjectFieldTypeDto
            {
                Validations = objectType.Validations,
                ValidationInfo = objectType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList(),
                InnerFieldDefinitions = [], // Empty to prevent circular references
                CheckExtraFields = objectType.CheckExtraFields
            },
            FormulaFieldType formulaType => new FormulaFieldTypeDto
            {
                Validations = formulaType.Validations,
                ValidationInfo = formulaType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList(),
                FormulaType = formulaType.Func?.GetType().Name,
                FormulaParameters = SerializeFormulaFunc(formulaType.Func, logger: null)
            },
            FilesFieldType filesType => new FilesFieldTypeDto
            {
                Validations = filesType.Validations,
                ValidationInfo = filesType.ValidationInfo?.Select(v => new ValidationInfoDto
                {
                    Name = v.Name,
                    Arguments = [.. v.Arguments]
                }).ToList()
            },
            _ => throw new NotSupportedException($"Field type {fieldType.GetType().Name} is not supported for caching")
        };
    }



    #endregion

    #region DTO to Domain Mapping

    /// <summary>
    /// Converts a PolicyMemberFieldsSchemaDto back to a domain object.
    /// </summary>
    public static PolicyMemberFieldsSchema ToDomain(this PolicyMemberFieldsSchemaDto dto)
    {
        ArgumentNullException.ThrowIfNull(dto);

        var fieldLookup = new Dictionary<string, PolicyMemberFieldDefinition>();
        var memberFields = new List<PolicyMemberFieldDefinition>();
        var productFields = new List<PolicyMemberFieldDefinition>();
        var censusFields = new List<PolicyMemberFieldDefinition>();
        var fieldsWithParents = new List<(PolicyMemberFieldDefinitionDto dto, PolicyMemberFieldDefinition field)>();

        // Single pass: process all field collections simultaneously
        ProcessFieldCollection(dto.SystemMemberFields, fieldLookup, memberFields, fieldsWithParents);
        ProcessFieldCollection(dto.CustomMemberFields, fieldLookup, memberFields, fieldsWithParents);
        ProcessFieldCollection(dto.ProductFields, fieldLookup, productFields, fieldsWithParents);
        ProcessFieldCollection(dto.CensusFields, fieldLookup, censusFields, fieldsWithParents);

        // Second pass: update fields with parent relationships (only for fields that have parents)
        foreach ((PolicyMemberFieldDefinitionDto fieldDto, PolicyMemberFieldDefinition field) in fieldsWithParents)
        {
            if (fieldLookup.TryGetValue(fieldDto.ParentFieldName!, out PolicyMemberFieldDefinition? parentField))
            {
                PolicyMemberFieldDefinition updatedField = field with { Parent = parentField };
                fieldLookup[field.Name] = updatedField;

                // Update the field in the appropriate collection
                UpdateFieldInCollection(memberFields, field, updatedField);
                UpdateFieldInCollection(productFields, field, updatedField);
                UpdateFieldInCollection(censusFields, field, updatedField);
            }
        }

        var oneOfValidations = dto.OneOfValidations
            .Select(v => v.ToDomain(fieldLookup))
            .ToList();

        return new PolicyMemberFieldsSchema(
            memberFields,
            productFields,
            censusFields,
            oneOfValidations);
    }

    /// <summary>
    /// Converts a PolicyMemberFieldDefinitionDto to a domain object.
    /// </summary>
    public static PolicyMemberFieldDefinition ToDomain(
        this PolicyMemberFieldDefinitionDto dto,
        Dictionary<string, PolicyMemberFieldDefinition>? parentLookup)
    {
        ArgumentNullException.ThrowIfNull(dto);

        PolicyMemberFieldDefinition? parent = null;
        if (!string.IsNullOrEmpty(dto.ParentFieldName) && parentLookup != null)
        {
            parentLookup.TryGetValue(dto.ParentFieldName, out parent);
        }

        return new PolicyMemberFieldDefinition
        {
            Name = dto.Name,
            Label = dto.Label,
            IsRequired = dto.IsRequired,
            IsUnique = dto.IsUnique,
            IsRequiredForDependent = dto.IsRequiredForDependent,
            IsBenefitField = dto.IsBenefitField,
            Type = dto.Type.ToDomain(),
            Condition = string.IsNullOrEmpty(dto.Condition) ? null : new CustomFieldCondition(dto.Condition),
            Parent = parent
        };
    }

    /// <summary>
    /// Converts a FieldTypeDto back to an IFieldType domain object.
    /// </summary>
    public static IFieldType ToDomain(this FieldTypeDto dto)
    {
        ArgumentNullException.ThrowIfNull(dto);

        return dto switch
        {
            StringFieldTypeDto stringDto => new StringFieldType
            {
                Validations = stringDto.Validations,
                Options = stringDto.Options?.Select(o => new StringOption
                {
                    Value = o.Value,
                    Label = o.Label,
                    Name = o.Name,
                    Key = o.Key
                }).ToList()
            },
            BooleanFieldTypeDto booleanDto => new BooleanFieldType
            {
                Validations = booleanDto.Validations
            },
            DateFieldTypeDto dateDto => new DateFieldType
            {
                Validations = dateDto.Validations
            },
            NumberFieldTypeDto numberDto => new NumberFieldType
            {
                Validations = numberDto.Validations,
                Options = numberDto.Options?.Select(o => new NumberOption
                {
                    Value = o.Value,
                    Label = o.Label,
                    Name = o.Name,
                    Key = o.Key
                }).ToList()
            },
            ObjectFieldTypeDto objectDto => new ObjectFieldType(
                objectDto.InnerFieldDefinitions.Select(f => f.ToDomain(parentLookup: null)).ToList(),
                objectDto.CheckExtraFields)
            {
                Validations = objectDto.Validations
            },
            FormulaFieldTypeDto formulaDto => new FormulaFieldType
            {
                Validations = formulaDto.Validations,
                Func = DeserializeFormulaFunc(formulaDto.FormulaType, formulaDto.FormulaParameters, logger: null)
            },
            AddressFieldTypeDto addressDto => new AddressFieldType(
                [.. addressDto.InnerFieldDefinitions.Select(f => f.ToDomain(parentLookup: null))])
            {
                Validations = addressDto.Validations
            },
            FilesFieldTypeDto filesDto => new FilesFieldType
            {
                Validations = filesDto.Validations
            },
            _ => throw new NotSupportedException($"DTO type {dto.GetType().Name} is not supported for domain conversion")
        };
    }

    /// <summary>
    /// Converts a CustomFieldOneOfValidationDto back to a domain object.
    /// </summary>
    public static CustomFieldOneOfValidation ToDomain(
        this CustomFieldOneOfValidationDto dto,
        Dictionary<string, PolicyMemberFieldDefinition> fieldLookup)
    {
        ArgumentNullException.ThrowIfNull(dto);
        ArgumentNullException.ThrowIfNull(fieldLookup);

        var validations = dto.Validations
            .Where(v => fieldLookup.ContainsKey(v.FieldName))
            .Select(v => new CustomFieldRequiredValidation
            {
                Field = fieldLookup[v.FieldName]
            })
            .ToList();

        return new CustomFieldOneOfValidation
        {
            Validations = validations
        };
    }

    /// <summary>
    /// Processes a field collection in a single pass, creating domain objects and tracking parent relationships.
    /// </summary>
    private static void ProcessFieldCollection(
        IEnumerable<PolicyMemberFieldDefinitionDto> fieldDtos,
        Dictionary<string, PolicyMemberFieldDefinition> fieldLookup,
        List<PolicyMemberFieldDefinition> targetCollection,
        List<(PolicyMemberFieldDefinitionDto dto, PolicyMemberFieldDefinition field)> fieldsWithParents)
    {
        foreach (PolicyMemberFieldDefinitionDto fieldDto in fieldDtos)
        {
            PolicyMemberFieldDefinition field = fieldDto.ToDomain(parentLookup: null);
            fieldLookup[field.Name] = field;
            targetCollection.Add(field);

            // Track fields that have parent relationships for second pass
            if (!string.IsNullOrEmpty(fieldDto.ParentFieldName))
            {
                fieldsWithParents.Add((fieldDto, field));
            }
        }
    }

    /// <summary>
    /// Updates a field reference in the target collection.
    /// </summary>
    private static void UpdateFieldInCollection(
        List<PolicyMemberFieldDefinition> collection,
        PolicyMemberFieldDefinition oldField,
        PolicyMemberFieldDefinition newField)
    {
        int index = collection.FindIndex(f => ReferenceEquals(f, oldField));
        if (index >= 0)
        {
            collection[index] = newField;
        }
    }

    #endregion

    #region Private Helper Methods

    /// <summary>
    /// Serializes a formula function to JSON string for caching.
    /// Returns null if the formula function cannot be serialized.
    /// </summary>
    private static string? SerializeFormulaFunc(IFormulaFunc? formulaFunc, ILogger? logger = null)
    {
        if (formulaFunc == null)
            return null;

        try
        {
            // For JoinFormula, we can serialize the structure
            if (formulaFunc is JoinFormula joinFormula)
            {
                var formulaData = new
                {
                    Type = "JoinFormula",
                    Inputs = joinFormula.Inputs.Select(SerializeFormulaParameter).ToList(),
                    Separator = SerializeFormulaParameter(joinFormula.Separator)
                };
                return JsonSerializer.Serialize(formulaData);
            }

            // For other formula types, we cannot serialize the logic
            return null;
        }
        catch (JsonException ex)
        {
            logger?.LogWarning(ex, "Failed to serialize formula function due to JSON serialization error. FormulaType: {FormulaType}",
                formulaFunc.GetType().Name);
            return null;
        }
        catch (ArgumentException ex)
        {
            logger?.LogWarning(ex, "Failed to serialize formula function due to invalid arguments. FormulaType: {FormulaType}",
                formulaFunc.GetType().Name);
            return null;
        }
        catch (NotSupportedException ex)
        {
            logger?.LogWarning(ex, "Formula function type is not supported for serialization. FormulaType: {FormulaType}",
                formulaFunc.GetType().Name);
            return null;
        }
        catch (InvalidOperationException ex)
        {
            logger?.LogWarning(ex, "Invalid operation during formula function serialization. FormulaType: {FormulaType}",
                formulaFunc.GetType().Name);
            return null;
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "Unexpected error during formula function serialization. FormulaType: {FormulaType}",
                formulaFunc.GetType().Name);
            return null;
        }
    }

    /// <summary>
    /// Serializes a formula parameter for caching.
    /// </summary>
    private static object SerializeFormulaParameter(IFormulaParameter parameter)
    {
        return parameter switch
        {
            FormulaData data => new { Type = "FormulaData", Path = data.Path },
            FormulaValue value => new { Type = "FormulaValue", Value = value.Value },
            _ => new { Type = "Unknown", Value = (object?)null }
        };
    }

    /// <summary>
    /// Deserializes a formula function from JSON string.
    /// Returns null if the formula cannot be reconstructed.
    /// </summary>
    private static JoinFormula? DeserializeFormulaFunc(string? formulaType, string? formulaParameters, ILogger? logger = null)
    {
        if (string.IsNullOrEmpty(formulaParameters))
            return null;

        try
        {
            if (formulaType == "JoinFormula")
            {
                using var doc = JsonDocument.Parse(formulaParameters);
                JsonElement root = doc.RootElement;

                if (root.TryGetProperty("Inputs", out JsonElement inputsElement) &&
                    root.TryGetProperty("Separator", out JsonElement separatorElement))
                {
                    List<IFormulaParameter> inputs = [.. inputsElement.EnumerateArray()
                        .Select(DeserializeFormulaParameter)
                        .Where(p => p != null)
                        .Cast<IFormulaParameter>()];

                    IFormulaParameter? separator = DeserializeFormulaParameter(separatorElement);
                    if (separator != null)
                    {
                        return new JoinFormula
                        {
                            Inputs = inputs,
                            Separator = separator
                        };
                    }
                }
            }

            return null;
        }
        catch (JsonException ex)
        {
            logger?.LogWarning(ex, "Failed to deserialize formula function due to JSON parsing error. FormulaType: {FormulaType}, ParametersLength: {ParametersLength}",
                formulaType, formulaParameters.Length);
            return null;
        }
        catch (ArgumentException ex)
        {
            logger?.LogWarning(ex, "Failed to deserialize formula function due to invalid arguments. FormulaType: {FormulaType}",
                formulaType);
            return null;
        }
        catch (ObjectDisposedException ex)
        {
            logger?.LogWarning(ex, "Failed to deserialize formula function due to disposed JsonDocument. FormulaType: {FormulaType}",
                formulaType);
            return null;
        }
        catch (InvalidOperationException ex)
        {
            logger?.LogWarning(ex, "Invalid operation during formula function deserialization. FormulaType: {FormulaType}",
                formulaType);
            return null;
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "Unexpected error during formula function deserialization. FormulaType: {FormulaType}, ParametersLength: {ParametersLength}",
                formulaType, formulaParameters.Length);
            return null;
        }
    }

    /// <summary>
    /// Deserializes a formula parameter from JSON.
    /// </summary>
    private static IFormulaParameter? DeserializeFormulaParameter(JsonElement element)
    {
        if (element.TryGetProperty("Type", out JsonElement typeElement))
        {
            string? type = typeElement.GetString();
            return type switch
            {
                "FormulaData" when element.TryGetProperty("Path", out JsonElement pathElement) =>
                    new FormulaData { Path = pathElement.GetString() ?? string.Empty },
                "FormulaValue" when element.TryGetProperty("Value", out JsonElement valueElement) =>
                    new FormulaValue { Value = valueElement.ValueKind == JsonValueKind.Null ? null : valueElement.ToString() },
                _ => null
            };
        }
        return null;
    }

    #endregion
}
