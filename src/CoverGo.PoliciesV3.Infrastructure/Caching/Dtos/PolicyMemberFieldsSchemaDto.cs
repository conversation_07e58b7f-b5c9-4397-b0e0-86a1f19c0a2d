using System.Text.Json.Serialization;

namespace CoverGo.PoliciesV3.Infrastructure.Caching.Dtos;

/// <summary>
/// Cacheable DTO for PolicyMemberFieldsSchema that avoids interface serialization issues.
/// This DTO contains only serializable data needed to reconstruct the domain object.
/// </summary>
public sealed record PolicyMemberFieldsSchemaDto
{
    /// <summary>
    /// System member fields (well-known fields)
    /// </summary>
    public required List<PolicyMemberFieldDefinitionDto> SystemMemberFields { get; init; }

    /// <summary>
    /// Custom member fields (non-well-known fields)
    /// </summary>
    public required List<PolicyMemberFieldDefinitionDto> CustomMemberFields { get; init; }

    /// <summary>
    /// Product-specific fields
    /// </summary>
    public required List<PolicyMemberFieldDefinitionDto> ProductFields { get; init; }

    /// <summary>
    /// Census-specific fields
    /// </summary>
    public required List<PolicyMemberFieldDefinitionDto> CensusFields { get; init; }

    /// <summary>
    /// One-of validation rules
    /// </summary>
    public required List<CustomFieldOneOfValidationDto> OneOfValidations { get; init; }
}

/// <summary>
/// Cacheable DTO for PolicyMemberFieldDefinition with concrete field type information.
/// </summary>
public sealed record PolicyMemberFieldDefinitionDto
{
    /// <summary>
    /// Field name/key
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// UI friendly label
    /// </summary>
    public required string Label { get; init; }

    /// <summary>
    /// Whether the field is required
    /// </summary>
    public required bool IsRequired { get; init; }

    /// <summary>
    /// Whether the field must be unique
    /// </summary>
    public bool IsUnique { get; init; }

    /// <summary>
    /// Whether the field is required for dependents
    /// </summary>
    public bool IsRequiredForDependent { get; init; }

    /// <summary>
    /// Whether this field is a benefit field
    /// </summary>
    public bool? IsBenefitField { get; init; }

    /// <summary>
    /// Field type information with discriminator
    /// </summary>
    public required FieldTypeDto Type { get; init; }

    /// <summary>
    /// Conditional logic as string (to be parsed when reconstructing)
    /// </summary>
    public string? Condition { get; init; }

    /// <summary>
    /// Parent field name for nested fields
    /// </summary>
    public string? ParentFieldName { get; init; }
}

/// <summary>
/// Base DTO for field types with type discrimination for proper deserialization.
/// </summary>
[JsonPolymorphic(TypeDiscriminatorPropertyName = "fieldType")]
[JsonDerivedType(typeof(StringFieldTypeDto), "string")]
[JsonDerivedType(typeof(BooleanFieldTypeDto), "boolean")]
[JsonDerivedType(typeof(DateFieldTypeDto), "date")]
[JsonDerivedType(typeof(NumberFieldTypeDto), "number")]
[JsonDerivedType(typeof(ObjectFieldTypeDto), "object")]
[JsonDerivedType(typeof(FormulaFieldTypeDto), "formula")]
[JsonDerivedType(typeof(AddressFieldTypeDto), "address")]
[JsonDerivedType(typeof(FilesFieldTypeDto), "files")]
public abstract record FieldTypeDto
{
    /// <summary>
    /// Validation rules string (e.g., "required|unique")
    /// </summary>
    public string? Validations { get; init; }

    /// <summary>
    /// Structured validation information
    /// </summary>
    public List<ValidationInfoDto>? ValidationInfo { get; init; }
}

/// <summary>
/// DTO for string field types with options support
/// </summary>
public sealed record StringFieldTypeDto : FieldTypeDto
{
    /// <summary>
    /// Available string options
    /// </summary>
    public List<StringOptionDto>? Options { get; init; }
}

/// <summary>
/// DTO for string options
/// </summary>
public sealed record StringOptionDto
{
    public required string Value { get; init; }
    public string? Label { get; init; }
    public string? Name { get; init; }
    public string? Key { get; init; }
}

/// <summary>
/// DTO for boolean field types
/// </summary>
public sealed record BooleanFieldTypeDto : FieldTypeDto;

/// <summary>
/// DTO for date field types
/// </summary>
public sealed record DateFieldTypeDto : FieldTypeDto;

/// <summary>
/// DTO for number field types
/// </summary>
public sealed record NumberFieldTypeDto : FieldTypeDto
{
    /// <summary>
    /// Available number options
    /// </summary>
    public List<NumberOptionDto>? Options { get; init; }
}

/// <summary>
/// DTO for number options
/// </summary>
public sealed record NumberOptionDto
{
    public required object Value { get; init; }
    public string? Label { get; init; }
    public string? Name { get; init; }
    public string? Key { get; init; }
}

/// <summary>
/// DTO for object field types with nested field definitions
/// </summary>
public sealed record ObjectFieldTypeDto : FieldTypeDto
{
    /// <summary>
    /// Nested field definitions
    /// </summary>
    public required List<PolicyMemberFieldDefinitionDto> InnerFieldDefinitions { get; init; }

    /// <summary>
    /// Whether to check for extra fields during validation
    /// </summary>
    public bool CheckExtraFields { get; init; }
}

/// <summary>
/// DTO for formula field types
/// </summary>
public sealed record FormulaFieldTypeDto : FieldTypeDto
{
    /// <summary>
    /// Formula function type (e.g., "join", "calculate")
    /// </summary>
    public string? FormulaType { get; init; }

    /// <summary>
    /// Serialized formula parameters as JSON string
    /// </summary>
    public string? FormulaParameters { get; init; }
}

/// <summary>
/// DTO for address field types (inherits from object field type)
/// </summary>
public sealed record AddressFieldTypeDto : FieldTypeDto
{
    /// <summary>
    /// Nested field definitions for address components
    /// </summary>
    public required List<PolicyMemberFieldDefinitionDto> InnerFieldDefinitions { get; init; }

    /// <summary>
    /// Address fields always check for extra fields
    /// </summary>
    public bool CheckExtraFields => true;
}

/// <summary>
/// DTO for files field types
/// </summary>
public sealed record FilesFieldTypeDto : FieldTypeDto;

/// <summary>
/// DTO for one-of validation rules
/// </summary>
public sealed record CustomFieldOneOfValidationDto
{
    /// <summary>
    /// Required field validations
    /// </summary>
    public required List<CustomFieldRequiredValidationDto> Validations { get; init; }
}

/// <summary>
/// DTO for required field validation
/// </summary>
public sealed record CustomFieldRequiredValidationDto
{
    /// <summary>
    /// Field name being validated
    /// </summary>
    public required string FieldName { get; init; }

    /// <summary>
    /// Field label for error messages
    /// </summary>
    public required string FieldLabel { get; init; }
}

/// <summary>
/// DTO for validation information
/// </summary>
public sealed record ValidationInfoDto
{
    /// <summary>
    /// Validation rule name (e.g., "required", "regex")
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// Validation arguments
    /// </summary>
    public required List<string> Arguments { get; init; }
}
