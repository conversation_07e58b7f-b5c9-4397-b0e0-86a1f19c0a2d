﻿<Project Sdk="Microsoft.NET.Sdk">

  <!-- Make internal classes visible to test assemblies -->
  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>CoverGo.PoliciesV3.Tests.Unit</_Parameter1>
    </AssemblyAttribute>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>CoverGo.PoliciesV3.Tests.Integration</_Parameter1>
    </AssemblyAttribute>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>DynamicProxyGenAssembly2, PublicKey=0024000004800000940000000602000000240000525341310004000001000100c547cac37abd99c8db225ef2f6c8a3602f3b3606cc9891605d02baa56104f4cfc0734aa39b93bf7852f7d9266654753cc297e7d2edfe0bac1cdcf9f717241550e0a7b191195b7667bb4f64bcb8e2121380fd1d9d46ad2d92d2d15605093924cceaf74c4861eff62abf69b9291ed0a340e113be11e6a7d3113e92484cf7045cc7</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.BuildingBlocks.DataAccess" />
    <PackageReference Include="CoverGo.BuildingBlocks.DataAccess.Mongo" />
    <PackageReference Include="CoverGo.BuildingBlocks.DataAccess.PostgreSql" />
    <PackageReference Include="CoverGo.BuildingBlocks.MessageBus.Abstractions" />
    <PackageReference Include="CoverGo.BuildingBlocks.MessageBus.Dapr" />
    <PackageReference Include="CoverGo.BuildingBlocks.Scheduler.Hangfire" />
    <PackageReference Include="CoverGo.FeatureManagement" />
    <PackageReference Include="CoverGo.Users.Client" />
    <PackageReference Include="GraphQL.Client" />
    <PackageReference Include="GraphQL.Client.Serializer.SystemTextJson" />
    <PackageReference Include="Humanizer.Core" />
    <PackageReference Include="Microsoft.AspNetCore.HeaderPropagation" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
    <PackageReference Include="Microsoft.Extensions.Http.Resilience" />
    <PackageReference Include="NPOI" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
    <PackageReference Include="StackExchange.Redis" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CoverGo.PoliciesV3.Application\CoverGo.PoliciesV3.Application.csproj" />
    <ProjectReference Include="..\CoverGo.PoliciesV3.Domain\CoverGo.PoliciesV3.Domain.csproj" />
    <PackageReference Include="CoverGo.Cases.Client.Rest" />
    <PackageReference Include="CoverGo.FileSystem.Client" />
    <PackageReference Include="CoverGo.Policies.Client" />
    <PackageReference Include="CoverGo.Products.Client" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Migrations/" />
  </ItemGroup>
</Project>
