using System.Text.Json;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Common.DTOs;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;
using CoverGo.Users.Client;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.ExternalServices;

public class UsersService(HttpClient httpClient, TenantId tenantId, ILogger<UsersService> logger) : IUsersService
{
    readonly UsersClient _usersClient = new(httpClient);
    readonly TenantId _tenantId = tenantId;
    private readonly ILogger<UsersService> _logger = logger;

    public async Task<JsonElement> GetCompanyContractHolderMembersFieldsById(string id, CancellationToken cancellationToken)
    {
        Company? company = (await _usersClient.Companies_QueryAsync(_tenantId.Value, new QueryArgumentsOfCompanyWhere { Where = new CompanyWhere { Id = id } }, cancellationToken))?.SingleOrDefault();

        return company?.Fields == null
            ? JsonSchemaProcessor.EmptyJsonElement
            : JsonSchemaProcessor.ConvertSchemaObjectToJsonElement(company.Fields);
    }

    public async Task<HashSet<string>> GetMemberIdsWithoutExistingIndividual(HashSet<string> memberIds, CancellationToken cancellationToken)
    {
        List<Individual>? individuals = await QueryIndividuals(new QueryArgumentsOfIndividualWhere { Where = new IndividualWhere { InternalCode_in = [.. memberIds] } }, cancellationToken);
        HashSet<string> memberIdsWithIndividuals = individuals?.Select(i => i.InternalCode!).Where(code => code != null).ToHashSet() ?? [];
        memberIds.ExceptWith(memberIdsWithIndividuals);
        return memberIds;
    }

    public async Task<List<DuplicatedIndividual>?> GetDuplicatedIndividualsByEmails(Dictionary<string, string> emailToMemberIdDictionary, CancellationToken cancellationToken)
    {
        var emails = emailToMemberIdDictionary.Select(d => d.Key).ToList();
        List<Individual>? individuals = await QueryIndividuals(new QueryArgumentsOfIndividualWhere { Where = new IndividualWhere { Email_in = emails } }, cancellationToken);
        IEnumerable<Individual>? duplicateIndividuals = individuals?.Where(i =>
        {
            emailToMemberIdDictionary.TryGetValue(i.Email!, out string? memberId);
            return memberId is null || memberId != i.InternalCode;
        });
        return duplicateIndividuals?.Select(i => new DuplicatedIndividual
        {
            Email = i.Email,
            DuplicatedByEntityId = i.Id?.ToString()
        }).ToList();
    }

    public virtual async Task<List<Individual>> QueryIndividuals(QueryArgumentsOfIndividualWhere where, CancellationToken cancellationToken) =>
        await _usersClient.Individuals_QueryAsync(_tenantId.Value, where, cancellationToken) ?? [];

    public async Task<List<Individual>> QueryIndividualsByEmails(List<string> emails, CancellationToken cancellationToken) => emails.Count == 0
            ? []
            : await QueryIndividuals(new QueryArgumentsOfIndividualWhere
            {
                Where = new IndividualWhere { Email_in = emails }
            }, cancellationToken);
}
