﻿using CoverGo.FileSystem.Client;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.ExternalServices;

public class FileSystemService(HttpClient httpClient, TenantId tenantId, ILogger<FileSystemService> logger) : IFileSystemService
{
    readonly FileSystemClient _fileSystemClient = new(httpClient);
    readonly TenantId _tenantId = tenantId;
    private readonly ILogger<FileSystemService> _logger = logger;

    public async Task<byte[]?> GetFileByPath(string path, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting file content for path {Path}", path);

        ResultOfByteOf? getResult = await _fileSystemClient.FileSystem_GetAsync(_tenantId.Value, null, new GetFileCommand { Key = path, IsPublic = false }, cancellationToken);

        if (getResult == null || !getResult.IsSuccess)
        {
            _logger.LogError("Failed to get file for path {path}. ||| Message: {message}", path, string.Join(',', getResult?.Errors ?? []));
            return null;
        }

        byte[]? fileContent = getResult.Value;
        _logger.LogDebug("Retrieved file content for path {Path}, size: {Size} bytes", path, fileContent?.Length ?? 0);

        return fileContent;
    }
}
