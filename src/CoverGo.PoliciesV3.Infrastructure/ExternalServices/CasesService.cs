using System.Text.Json;
using CoverGo.Cases.Client.Rest;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.ExternalServices;

public class CasesService(HttpClient httpClient, TenantId tenantId, ILogger<CasesService> logger) : ICasesService
{
    readonly CasesRestClient _casesClient = new(httpClient);
    readonly TenantId _tenantId = tenantId;
    private readonly ILogger<CasesService> _logger = logger;

    public async Task<JsonElement> GetMemberDataSchema(CancellationToken cancellationToken)
    {
        List<DataSchema>? memberDataSchemas = await _casesClient.DataSchemas_GetAllAsync(_tenantId.Value, new()
        {
            Type = "member"
        }, cancellationToken);

        DataSchema? schema = memberDataSchemas?.FirstOrDefault(it => it.Name == "member");

        return schema?.Schema == null ? JsonSchemaProcessor.EmptyJsonElement : JsonSchemaProcessor.ConvertSchemaObjectToJsonElement(schema.Schema!);
    }
}
