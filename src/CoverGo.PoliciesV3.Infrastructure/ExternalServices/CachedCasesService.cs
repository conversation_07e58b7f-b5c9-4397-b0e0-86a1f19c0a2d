using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Caching;
using System.Text.Json;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Services.Interfaces;

namespace CoverGo.PoliciesV3.Infrastructure.ExternalServices;

public class CachedCasesService(ITenantProvider tenantProvider, ICasesService inner, CacheProvider cache) : ICasesService
{
    private const string MemberSchemaCacheKey = "schema:member:{tenant}";
    private static readonly TimeSpan MemberDataSchemaTtl = TimeSpan.FromHours(6);

    public async Task<JsonElement> GetMemberDataSchema(CancellationToken cancellationToken)
    {
        if (tenantProvider.TryGetCurrent(out TenantId? tenantId))
        {
            string cachedKey = MemberSchemaCacheKey.Replace("{tenant}", tenantId.Value);
            JsonElement cached = await cache.GetAsync<JsonElement>(cachedKey, cancellationToken);
            if (cached.ValueKind != JsonValueKind.Undefined)
                return cached;
            JsonElement schema = await inner.GetMemberDataSchema(cancellationToken);
            if (schema.ValueKind != JsonValueKind.Undefined)
                await cache.SetAsync(cachedKey, schema, MemberDataSchemaTtl, cancellationToken);
            return schema;
        }
        throw new InvalidOperationException("Current tenant is not set.");
    }
}
