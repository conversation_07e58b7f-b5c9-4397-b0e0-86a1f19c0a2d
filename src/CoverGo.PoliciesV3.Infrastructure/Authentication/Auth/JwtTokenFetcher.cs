using CoverGo.DomainUtils;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Infrastructure.Authentication.Auth.Interface;
using CoverGo.Proxies.Auth;

namespace CoverGo.PoliciesV3.Infrastructure.Authentication.Auth;

/// <summary>
/// Implementation of JWT token fetcher for service account authentication in Hangfire jobs.
/// </summary>
public class JwtTokenFetcher(IAuthService authService) : IJwtTokenFetcher
{
    /// <summary>
    /// Gets a service account internal access token for the specified tenant.
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The access token or null if not available</returns>
    public async Task<string?> GetServiceAccountInternalAccessToken(TenantId tenantId, CancellationToken cancellationToken = default)
    {
        string serviceAccountEmail = $"service_account_{tenantId.Value}@covergo.com";
        Token? token = await GetInternalAccessToken(tenantId, serviceAccountEmail, cancellationToken);

        return token?.AccessToken;
    }

    /// <summary>
    /// Gets an internal access token for the specified tenant and email.
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="email">The service account email</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The token or null if not available</returns>
    private async Task<Token?> GetInternalAccessToken(TenantId tenantId, string email, CancellationToken cancellationToken)
    {
        const string adminPortalClientId = "admin_portal";

        string? adminLoginId = (await authService.GetLoginIdsAsync(tenantId.Value,
            new QueryArguments { Where = new LoginWhere { Email_in = new List<string> { email } } }, cancellationToken)).FirstOrDefault();

        if (adminLoginId is null)
        {
            return null;
        }

        Token token = await authService.GetInternalAccessTokenAsync(tenantId.Value, adminLoginId, adminPortalClientId, cancellationToken);
        return token;
    }
}
