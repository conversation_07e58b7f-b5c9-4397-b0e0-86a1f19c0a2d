using CoverGo.Multitenancy;

namespace CoverGo.PoliciesV3.Infrastructure.Authentication.Auth.Interface;

/// <summary>
/// Interface for fetching JWT tokens for service account authentication in Hangfire jobs.
/// </summary>
public interface IJwtTokenFetcher
{
    /// <summary>
    /// Gets a service account internal access token for the specified tenant.
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The access token or null if not available</returns>
    Task<string?> GetServiceAccountInternalAccessToken(TenantId tenantId, CancellationToken cancellationToken = default);
}
