using CoverGo.BuildingBlocks.MessageBus.Abstractions;
using CoverGo.BuildingBlocks.MessageBus.Abstractions.Interceptors;
using CoverGo.BuildingBlocks.MessageBus.Contracts;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;

namespace CoverGo.PoliciesV3.Infrastructure.Authentication.Auth;

/// <summary>
/// Transport message interceptor that forwards JWT tokens in message context for Hangfire jobs.
/// This ensures that authentication context is properly propagated across message boundaries.
/// </summary>
internal sealed class JwtMessageForwarder(
    IHttpContextAccessor httpContextAccessor,
    IMessageContextAccessor messageContextAccessor) : ITransportMessageInterceptor
{
    /// <summary>
    /// Intercepts transport messages to add JWT authentication headers.
    /// </summary>
    /// <typeparam name="T">The message type</typeparam>
    /// <param name="message">The transport message being sent</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The intercepted message with authentication headers</returns>
    public ValueTask<TransportMessage<T>> Intercept<T>(TransportMessage<T> message, CancellationToken cancellationToken = default) where T : Message
    {
        // First try to get Authorization header from HTTP context (for web requests)
        if (httpContextAccessor.HttpContext != null &&
            !StringValues.IsNullOrEmpty(httpContextAccessor.HttpContext.Request.Headers.Authorization))
        {
            message.Headers["Authorization"] = httpContextAccessor.HttpContext.Request.Headers.Authorization.ToString();
        }
        // Fallback to message context (for background jobs/message processing)
        else if (messageContextAccessor.MessageContext != null &&
            messageContextAccessor.MessageContext.Headers.TryGetValue("Authorization", out string? authHeader) &&
            !string.IsNullOrEmpty(authHeader))
        {
            message.Headers["Authorization"] = authHeader;
        }

        return ValueTask.FromResult(message);
    }
}
