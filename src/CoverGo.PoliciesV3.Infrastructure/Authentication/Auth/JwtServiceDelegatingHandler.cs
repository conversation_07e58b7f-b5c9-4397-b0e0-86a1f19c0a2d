using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Infrastructure.Authentication.Auth.Interface;

namespace CoverGo.PoliciesV3.Infrastructure.Authentication.Auth;

/// <summary>
/// Delegating handler that automatically adds JWT service account tokens to HTTP requests in Hangfire jobs.
/// This handler fetches a service account token for the current tenant and adds it to the Authorization header.
/// </summary>
internal sealed class JwtServiceDelegatingHandler(IJwtTokenFetcher tokenFetcher, ITenantProvider tenantProvider) : DelegatingHandler
{
    /// <summary>
    /// Sends an HTTP request with automatic JWT token injection for service account authentication.
    /// </summary>
    /// <param name="request">The HTTP request message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The HTTP response message</returns>
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        // Only add token if we have a current tenant context
        if (!tenantProvider.TryGetCurrent(out TenantId? tenantId))
        {
            return await base.SendAsync(request, cancellationToken);
        }

        // Fetch service account token for the current tenant
        string? token = await tokenFetcher.GetServiceAccountInternalAccessToken(tenantId, cancellationToken);
        if (!string.IsNullOrEmpty(token))
        {
            // Add the token to the request headers
            request.Headers.Authorization = new("Bearer", token);
        }

        return await base.SendAsync(request, cancellationToken);
    }
}
