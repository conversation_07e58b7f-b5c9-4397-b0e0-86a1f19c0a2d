using CoverGo.BuildingBlocks.MessageBus.Abstractions;

namespace CoverGo.PoliciesV3.Infrastructure.Authentication.Auth;

/// <summary>
/// Delegating handler that propagates JWT tokens from the message context to HTTP requests in Hangfire jobs.
/// This handler is used when the Authorization header should be propagated from the original request context.
/// </summary>
internal sealed class JwtDelegatingHandler(IMessageContextAccessor messageContextAccessor) : DelegatingHandler
{
    /// <summary>
    /// Sends an HTTP request with JWT token propagation from the message context.
    /// </summary>
    /// <param name="request">The HTTP request message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The HTTP response message</returns>
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        // Only add Authorization header if it's not already present and we have it in the message context
        if (!request.Headers.Contains("Authorization") &&
            messageContextAccessor.MessageContext?.Headers.TryGetValue("Authorization", out string? authHeader) == true &&
            !string.IsNullOrEmpty(authHeader))
        {
            request.Headers.Add("Authorization", authHeader);
        }

        return await base.SendAsync(request, cancellationToken);
    }
}
