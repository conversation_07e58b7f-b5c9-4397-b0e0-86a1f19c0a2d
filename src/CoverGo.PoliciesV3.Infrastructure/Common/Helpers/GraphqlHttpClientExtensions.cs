using GraphQL;
using GraphQL.Client.Http;
using System.Net;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace CoverGo.PoliciesV3.Infrastructure.Common.Helpers;

static class GraphQlHttpClientExtensions
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true,
        Converters = { new JsonStringEnumConverter() }
    };

    public static async Task<TResponse> SendMutationAndEnsureAsync<TResponse>(this GraphQLHttpClient client, string mutation, CancellationToken? cancellationToken = null) where TResponse : class
    {
        GraphQLResponse<JsonElement> response = await client.SendMutationAsync<JsonElement>(new GraphQLHttpRequest(mutation), cancellationToken ?? CancellationToken.None);
        Validate(response);

        if (response.Data.ValueKind == JsonValueKind.Object)
        {
            JsonProperty firstProperty = response.Data.EnumerateObject().FirstOrDefault();
            if (firstProperty.Value.ValueKind == JsonValueKind.Object)
            {
                JsonProperty secondProperty = firstProperty.Value.EnumerateObject().FirstOrDefault();
                return JsonSerializer.Deserialize<TResponse>(secondProperty.Value.GetRawText(), JsonOptions) ?? throw new ArgumentException("Cannot deserialize result");
            }
        }

        throw new ArgumentException("Cannot deserialize result");
    }

    public static async Task<TResponse> SendQueryAndEnsureAsync<TResponse>(this GraphQLHttpClient client, string query, CancellationToken? cancellationToken = null) where TResponse : class
    {
        try
        {
            GraphQLResponse<JsonElement> response = await client.SendQueryAsync<JsonElement>(new GraphQLHttpRequest(query), cancellationToken ?? CancellationToken.None);
            Validate(response);

            if (response.Data.ValueKind == JsonValueKind.Object)
            {
                JsonProperty firstProperty = response.Data.EnumerateObject().FirstOrDefault();
                if (firstProperty.Value.ValueKind == JsonValueKind.Object)
                {
                    return JsonSerializer.Deserialize<TResponse>(firstProperty.Value.GetRawText(), JsonOptions) ?? throw new ArgumentException("Cannot deserialize result");
                }
            }

            throw new ArgumentException("Cannot deserialize result");
        }
        catch (GraphQLHttpRequestException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            throw new InvalidOperationException($"The requested resource was not found. GraphQL query: {query}. Status: {ex.StatusCode}. Response: {ex.Content}", ex);
        }
    }

    static void Validate<TResponse>(GraphQLResponse<TResponse> response)
    {
        if (response.Errors?.Length > 0)
        {
            GraphQLError error = response.Errors[0];
            string exceptionMessage = error.Message;
            if (error.Extensions?.TryGetValue("message", out object? extraMessage) == true)
            {
                exceptionMessage += Environment.NewLine + extraMessage;
            }
            throw new InvalidOperationException(exceptionMessage);
        }
    }
}