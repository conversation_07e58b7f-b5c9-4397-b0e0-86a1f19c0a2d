using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Helpers;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CoverGo.PoliciesV3.Infrastructure.Common.Extensions;

/// <summary>
/// Extension methods for EntityTypeBuilder that provide compile-time safe database naming
/// using strongly-typed property expressions and automatic snake_case conversion.
/// </summary>
public static class EntityTypeBuilderExtensions
{
    #region Index Extensions

    /// <summary>
    /// Creates an index with automatically generated name using compile-time safe property expression.
    /// </summary>
    public static IndexBuilder<TEntity> HasIndexWithAutoName<TEntity>(
        this EntityTypeBuilder<TEntity> builder,
        Expression<Func<TEntity, object?>> indexExpression)
        where TEntity : class
    {
        string propertyName = GetPropertyName(indexExpression);
        string indexName = DatabaseNamingHelpers.GetIndexName<TEntity>(propertyName);

        return builder.HasIndex(indexExpression)
                     .HasDatabaseName(indexName);
    }

    /// <summary>
    /// Creates a composite index with automatically generated name using property names.
    /// </summary>
    public static IndexBuilder<TEntity> HasCompositeIndexWithAutoName<TEntity>(
        this EntityTypeBuilder<TEntity> builder,
        params string[] propertyNames)
        where TEntity : class
    {
        string indexName = DatabaseNamingHelpers.GetCompositeIndexName<TEntity>(propertyNames);
        return builder.HasIndex(propertyNames).HasDatabaseName(indexName);
    }

    /// <summary>
    /// Creates a unique index with automatically generated name using compile-time safe property expression.
    /// </summary>
    public static IndexBuilder<TEntity> HasUniqueIndexWithAutoName<TEntity>(
        this EntityTypeBuilder<TEntity> builder,
        Expression<Func<TEntity, object?>> indexExpression)
        where TEntity : class
    {
        string propertyName = GetPropertyName(indexExpression);
        string indexName = DatabaseNamingHelpers.GetUniqueIndexName<TEntity>(propertyName);

        return builder.HasIndex(indexExpression)
                     .HasDatabaseName(indexName)
                     .IsUnique();
    }

    #endregion

    #region Foreign Key Extensions

    /// <summary>
    /// Configures a foreign key relationship with automatically generated constraint name.
    /// </summary>
    public static ReferenceCollectionBuilder<TParent, TChild> HasForeignKeyWithAutoName<TParent, TChild>(
        this EntityTypeBuilder<TChild> childBuilder,
        Expression<Func<TChild, object?>> foreignKeyExpression,
        Expression<Func<TParent, IEnumerable<TChild>?>> navigationExpression)
        where TParent : class
        where TChild : class
    {
        string foreignKeyPropertyName = GetPropertyName(foreignKeyExpression);
        string constraintName = DatabaseNamingHelpers.GetForeignKeyName<TChild, TParent>(foreignKeyPropertyName);

        return childBuilder.HasOne<TParent>()
                          .WithMany(navigationExpression)
                          .HasForeignKey(foreignKeyExpression)
                          .HasConstraintName(constraintName);
    }

    /// <summary>
    /// Configures a self-referencing foreign key relationship with automatically generated constraint name.
    /// </summary>
    public static ReferenceCollectionBuilder<TEntity, TEntity> HasSelfReferencingForeignKeyWithAutoName<TEntity>(
        this EntityTypeBuilder<TEntity> builder,
        Expression<Func<TEntity, object?>> foreignKeyExpression,
        Expression<Func<TEntity, IEnumerable<TEntity>?>>? navigationExpression = null)
        where TEntity : class
    {
        string foreignKeyPropertyName = GetPropertyName(foreignKeyExpression);
        string constraintName = DatabaseNamingHelpers.GetSelfReferencingForeignKeyName<TEntity>(foreignKeyPropertyName);

        return builder.HasOne<TEntity>()
                     .WithMany(navigationExpression)
                     .HasForeignKey(foreignKeyExpression)
                     .HasConstraintName(constraintName);
    }

    #endregion

    #region Primary Key Extensions

    /// <summary>
    /// Configures a primary key with automatically generated constraint name using snake_case convention.
    /// </summary>
    public static KeyBuilder HasPrimaryKeyWithAutoName<TEntity>(
        this EntityTypeBuilder<TEntity> builder,
        Expression<Func<TEntity, object?>> keyExpression)
        where TEntity : class
    {
        string primaryKeyName = DatabaseNamingHelpers.GetPrimaryKeyName<TEntity>();
        return builder.HasKey(keyExpression)
                     .HasName(primaryKeyName);
    }

    #endregion

    #region Property Extensions

    /// <summary>
    /// Configures a property with automatically generated column name using snake_case convention.
    /// </summary>
    public static PropertyBuilder<TProperty> HasColumnWithAutoName<TEntity, TProperty>(
        this EntityTypeBuilder<TEntity> builder,
        Expression<Func<TEntity, TProperty>> propertyExpression)
        where TEntity : class
    {
        string propertyName = GetPropertyName(propertyExpression);
        string columnName = DatabaseNamingHelpers.GetColumnName<TEntity>(propertyName);

        return builder.Property(propertyExpression)
                     .HasColumnName(columnName);
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Extracts the property name from a lambda expression.
    /// </summary>
    private static string GetPropertyName<T>(Expression<Func<T, object?>> expression) => expression.Body switch
    {
        MemberExpression member => member.Member.Name,
        UnaryExpression { Operand: MemberExpression member } => member.Member.Name,
        _ => throw new ArgumentException($"Expression '{expression}' refers to a method, not a property.")
    };

    /// <summary>
    /// Extracts the property name from a lambda expression with specific return type.
    /// </summary>
    private static string GetPropertyName<T, TProperty>(Expression<Func<T, TProperty>> expression) => expression.Body switch
    {
        MemberExpression member => member.Member.Name,
        UnaryExpression { Operand: MemberExpression member } => member.Member.Name,
        _ => throw new ArgumentException($"Expression '{expression}' refers to a method, not a property.")
    };
    
    #endregion
}
