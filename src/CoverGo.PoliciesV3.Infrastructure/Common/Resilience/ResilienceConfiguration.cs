using System.ComponentModel.DataAnnotations;

namespace CoverGo.PoliciesV3.Infrastructure.Common.Resilience;

/// <summary>
/// Configuration options for circuit breaker policies applied to external service clients
/// </summary>
public class ResilienceConfiguration
{
    public const string SectionName = "Resilience";
    
    /// <summary>
    /// Number of consecutive failures before the circuit breaker opens
    /// </summary>
    [Range(1, 100)]
    public int HandledEventsAllowedBeforeBreaking { get; set; } = 5;
    
    /// <summary>
    /// Duration the circuit breaker stays open before attempting to close
    /// </summary>
    [Range(1, 300)]
    public int BreakDurationSeconds { get; set; } = 30;
    
    /// <summary>
    /// Minimum number of requests required in the time window before circuit breaker can open
    /// </summary>
    [Range(1, 1000)]
    public int MinimumThroughput { get; set; } = 10;
    
    /// <summary>
    /// Time window for tracking failures
    /// </summary>
    [Range(1, 300)]
    public int SamplingDurationSeconds { get; set; } = 60;
    
    /// <summary>
    /// Failure ratio threshold (0.0 to 1.0) before circuit breaker opens
    /// </summary>
    [Range(0.0, 1.0)]
    public double FailureRatio { get; set; } = 0.5;
    
    /// <summary>
    /// Service-specific circuit breaker overrides
    /// </summary>
    public Dictionary<string, ServiceResilienceConfiguration> Services { get; set; } = [];
}

/// <summary>
/// Service-specific circuit breaker configuration that overrides global settings
/// </summary>
public class ServiceResilienceConfiguration
{
    /// <summary>
    /// Whether circuit breaker is enabled for this service
    /// </summary>
    public bool Enabled { get; set; } = true;
    
    /// <summary>
    /// Number of consecutive failures before the circuit breaker opens
    /// </summary>
    public int? HandledEventsAllowedBeforeBreaking { get; set; }
    
    /// <summary>
    /// Duration the circuit breaker stays open before attempting to close
    /// </summary>
    public int? BreakDurationSeconds { get; set; }
    
    /// <summary>
    /// Minimum number of requests required in the time window before circuit breaker can open
    /// </summary>
    public int? MinimumThroughput { get; set; }
    
    /// <summary>
    /// Time window for tracking failures
    /// </summary>
    public int? SamplingDurationSeconds { get; set; }
    
    /// <summary>
    /// Failure ratio threshold (0.0 to 1.0) before circuit breaker opens
    /// </summary>
    public double? FailureRatio { get; set; }
}