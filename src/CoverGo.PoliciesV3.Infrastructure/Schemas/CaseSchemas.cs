namespace CoverGo.PoliciesV3.Infrastructure.Schemas;

/// <summary>
/// Schema for case data
/// </summary>
public class CasesDataSchema
{
    public Dictionary<string, PolicyMemberCustomField>? Properties { get; init; }
}

/// <summary>
/// Formula definition for custom fields
/// </summary>
public class PolicyMemberCustomFieldFormula
{
    public string? Id { get; init; }
    public string? Name { get; init; }
    public string? Label { get; init; }
    public PolicyMemberCustomFieldFormulaProperties? Props { get; init; }
    public List<PolicyMemberCustomFieldFormulaChild?>? Children { get; init; }
}

/// <summary>
/// Formula properties
/// </summary>
public class PolicyMemberCustomFieldFormulaProperties
{
    public required string FormulaName { get; init; }
}

/// <summary>
/// Base class for formula children
/// </summary>
public abstract class PolicyMemberCustomFieldFormulaChild
{
    public string? Id { get; init; }
    public required string Name { get; init; }
    public string? Label { get; init; }

    public abstract object ToParameter();
}

/// <summary>
/// Formula value child
/// </summary>
public class PolicyMemberCustomFieldFormulaValueChild : PolicyMemberCustomFieldFormulaChild
{
    public const string Discriminator = "value";
    public required PolicyMemberCustomFieldFormulaValueProperties Props { get; init; }

    public override object ToParameter() => new { Props.Value };
}

/// <summary>
/// Formula value properties
/// </summary>
public class PolicyMemberCustomFieldFormulaValueProperties
{
    public required object? Value { get; init; }
}

/// <summary>
/// Formula data child
/// </summary>
public class PolicyMemberCustomFieldFormulaDataChild : PolicyMemberCustomFieldFormulaChild
{
    public const string Discriminator = "data";
    public required PolicyMemberCustomFieldFormulaDataProperties Props { get; init; }

    public override object ToParameter() => new { Props.Path };
}

/// <summary>
/// Formula data properties
/// </summary>
public class PolicyMemberCustomFieldFormulaDataProperties
{
    public required string Path { get; init; }
}
