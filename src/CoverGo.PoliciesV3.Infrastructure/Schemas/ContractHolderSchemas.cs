namespace CoverGo.PoliciesV3.Infrastructure.Schemas;

/// <summary>
/// Schema for contract holder census level fields
/// </summary>
public class ContractHoldersCensusLevelSchema
{
    public IList<ContractHoldersCensusLevelField>? CensusLevel { get; init; }
}

/// <summary>
/// Individual field definition for contract holder census
/// </summary>
public class ContractHoldersCensusLevelField
{
    public string? Id { get; set; }
    public string? Name { get; set; }
    public bool IsRequired { get; set; }
    public IList<string>? Values { get; set; }
}
