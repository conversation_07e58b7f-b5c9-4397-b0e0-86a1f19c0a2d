using System.Collections.Immutable;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Values;
using CoverGo.PoliciesV3.Infrastructure.Schemas;

namespace CoverGo.PoliciesV3.Infrastructure.Extensions;

/// <summary>
/// Extension methods for converting schema types to domain field types
/// </summary>
public static class CustomFieldExtensions
{
    /// <summary>
    /// Converts PolicyMemberCustomField to CustomFieldTypeBase
    /// </summary>
    public static CustomFieldTypeBase? ToCustomFieldType(this PolicyMemberCustomField field)
    {
        CustomFieldTypeBase type = field.Type switch
        {
            PolicyMemberCustomFieldType.String => field.Meta?.FieldType switch
            {
                "date" => new DateFieldType(),
                "dynamic" => CreateFormulaFieldType(field.Meta),
                _ => new StringFieldType
                {
                    Options = field.Meta?.Options?.Count > 0
                        ? CreateStringOptions(field.Meta.Options)
                        : null,
                    Validations = field.Meta?.Validations
                }
            },
            PolicyMemberCustomFieldType.Boolean => new BooleanFieldType(),
            PolicyMemberCustomFieldType.Number => new NumberFieldType
            {
                Options = field.Meta?.Options?.Count > 0
                    ? CreateNumberOptions(field.Meta.Options)
                    : null
            },
            PolicyMemberCustomFieldType.Address => CreateAddressFieldType(field),
            PolicyMemberCustomFieldType.Files => new FilesFieldType(),
            PolicyMemberCustomFieldType.Formula => CreateFormulaFieldType(field.Meta),
            PolicyMemberCustomFieldType.Object => new ObjectFieldType(Enumerable.Empty<PolicyMemberFieldDefinition>().ToImmutableList()),
            _ => throw new InvalidOperationException("Failed to get field type from the schema")
        };

        return type;
    }

    private static FormulaFieldType CreateFormulaFieldType(PolicyMemberCustomFieldMeta? meta)
    {
        if (meta?.Formula == null || meta.Formula.Count == 0)
        {
            return new FormulaFieldType { Func = null };
        }

        PolicyMemberCustomFieldFormula? formula = meta.Formula[0];
        if (formula?.Children == null || formula.Children.Count == 0)
        {
            return new FormulaFieldType { Func = null };
        }

        var inputs = formula.Children
            .Take(formula.Children.Count - 1)
            .Where(child => child != null)
            .Select(child => CreateFormulaParameter(child!))
            .ToList();

        PolicyMemberCustomFieldFormulaChild? separator = formula.Children.LastOrDefault();
        IFormulaParameter separatorParam = separator != null ? CreateFormulaParameter(separator) : new FormulaValue { Value = "" };

        return new FormulaFieldType
        {
            Func = new JoinFormula
            {
                Inputs = inputs,
                Separator = separatorParam
            }
        };
    }

    private static IFormulaParameter CreateFormulaParameter(PolicyMemberCustomFieldFormulaChild child) => child switch
    {
        PolicyMemberCustomFieldFormulaValueChild valueChild => new FormulaValue { Value = valueChild.Props.Value },
        PolicyMemberCustomFieldFormulaDataChild dataChild => new FormulaData { Path = dataChild.Props.Path },
        _ => new FormulaValue { Value = null }
    };

    private static AddressFieldType CreateAddressFieldType(PolicyMemberCustomField field)
    {
        if (field.Properties == null)
            throw new InvalidOperationException("Address type should have defined properties");

        var fieldDefinitions = new List<PolicyMemberFieldDefinition>();
        foreach ((string propertyName, PolicyMemberCustomField property) in field.Properties)
        {
            CustomFieldTypeBase? fieldType = property.ToCustomFieldType();
            if (fieldType == null) continue;

            var fieldDefinition = new PolicyMemberFieldDefinition
            {
                Name = propertyName,
                Label = property.Meta?.Label ?? propertyName,
                Type = fieldType,
                IsRequired = property.IsRequired,
                IsUnique = property.IsUnique,
                IsBenefitField = property.IsBenefitField
            };

            fieldDefinitions.Add(fieldDefinition);
        }

        return new AddressFieldType(fieldDefinitions);
    }

    private static List<StringOption> CreateStringOptions(List<PolicyMemberCustomFieldOption> options)
    {
        var stringOptions = new List<StringOption>(options.Count);

        foreach (PolicyMemberCustomFieldOption option in options)
        {
            stringOptions.Add(new StringOption
            {
                Value = option.Value.ToString() ?? string.Empty,
                Label = option.Name,
                Name = option.Name,
                Key = option.Key
            });
        }

        return stringOptions;
    }

    private static List<NumberOption> CreateNumberOptions(List<PolicyMemberCustomFieldOption> options)
    {
        var numberOptions = new List<NumberOption>(options.Count);

        foreach (PolicyMemberCustomFieldOption option in options)
        {
            // Handle JsonElement values from deserialized JSON
            object value = option.Value is System.Text.Json.JsonElement jsonElement
                ? ConvertJsonElementToValue(jsonElement)
                : option.Value;

            numberOptions.Add(new NumberOption
            {
                Value = NumberValue.Create(value),
                Label = option.Name ?? string.Empty,
                Name = option.Name,
                Key = option.Key
            });
        }

        return numberOptions;
    }

    private static object ConvertJsonElementToValue(System.Text.Json.JsonElement element) => element.ValueKind switch
    {
        System.Text.Json.JsonValueKind.Number => element.TryGetInt64(out long longValue) ? longValue : element.GetDouble(),
        System.Text.Json.JsonValueKind.String => element.GetString() ?? string.Empty,
        System.Text.Json.JsonValueKind.True => true,
        System.Text.Json.JsonValueKind.False => false,
        System.Text.Json.JsonValueKind.Null => null!,
        _ => element.ToString()
    };
}
