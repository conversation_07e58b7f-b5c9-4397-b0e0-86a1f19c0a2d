using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Constants;
using CoverGo.BuildingBlocks.Domain.Core.Audit;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations;

public static class EntityAuditConfigurationHelper
{
    public static void ConfigureEntityAudit<T>(this EntityTypeBuilder<T> builder)
        where T : class, IAuditableEntity => builder.OwnsOne(p => p.EntityAuditInfo, audit =>
                                                      {
                                                          audit.Property(a => a.CreatedAt)
                                                              .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(EntityAuditInfo.CreatedAt)))
                                                              .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.TimestampWithTimeZone)
                                                              .IsRequired();

                                                          audit.Property(a => a.CreatedBy)
                                                              .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(EntityAuditInfo.CreatedBy)))
                                                              .HasMaxLength(100)
                                                              .IsRequired();

                                                          audit.Property(a => a.LastModifiedAt)
                                                              .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(EntityAuditInfo.LastModifiedAt)))
                                                              .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.TimestampWithTimeZone)
                                                              .IsRequired(false);

                                                          audit.Property(a => a.LastModifiedBy)
                                                              .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(EntityAuditInfo.LastModifiedBy)))
                                                              .HasMaxLength(100)
                                                              .IsRequired(false);

                                                          audit.Property(a => a.DeletedAt)
                                                              .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(EntityAuditInfo.DeletedAt)))
                                                              .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.TimestampWithTimeZone)
                                                              .IsRequired(false);

                                                          audit.Property(a => a.DeletedBy)
                                                              .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(EntityAuditInfo.DeletedBy)))
                                                              .IsRequired(false);
                                                      });
}
