using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Constants;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Helpers;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Infrastructure.Common.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.Endorsements;

public class EndorsementConfiguration : IEntityTypeConfiguration<Endorsement>
{
    public void Configure(EntityTypeBuilder<Endorsement> builder)
    {
        // Table mapping
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(Endorsement)));
        builder.HasPrimaryKeyWithAutoName(x => x.Id);

        #region Primary and Foreign Keys

        builder.Property(x => x.Id)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Endorsement.Id)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        builder.Property(x => x.PolicyId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Endorsement.PolicyId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired();

        #endregion

        #region Properties

        builder.Property(x => x.Status)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Endorsement.Status)))
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(x => x.Type)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Endorsement.Type)))
            .HasMaxLength(100)
            .IsRequired(false);

        builder.Property(x => x.EffectiveDate)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Endorsement.EffectiveDate)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.TimestampWithTimeZone)
            .IsRequired(false);

        #endregion

        #region Indexes

        builder.HasIndexWithAutoName(x => x.PolicyId);
        builder.HasIndexWithAutoName(x => x.Status);

        #endregion

        #region Relationships

        builder.HasOne(x => x.Policy)
            .WithMany(x => x.Endorsements)
            .HasForeignKey(x => x.PolicyId)
            .HasConstraintName(DatabaseNamingHelpers.GetForeignKeyName<Endorsement, Policy>(nameof(Endorsement.PolicyId)))
            .OnDelete(DeleteBehavior.Cascade);

        #endregion

        builder.ConfigureEntityAudit();
    }
}
