using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Constants;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Helpers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.Common.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PolicyMemberUploadEntity = CoverGo.PoliciesV3.Domain.PolicyMemberUploads.PolicyMemberUpload;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMemberUploads;

public class PolicyMemberUploadValidationErrorConfiguration : IEntityTypeConfiguration<PolicyMemberUploadValidationError>
{
    public void Configure(EntityTypeBuilder<PolicyMemberUploadValidationError> builder)
    {
        // Table mapping
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(PolicyMemberUploadValidationError)));
        builder.HasPrimaryKeyWithAutoName(x => x.Id);

        #region Primary and Foreign Keys

        builder.Property(x => x.Id)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.Id)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.PolicyMemberUploadId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.PolicyMemberUploadId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired();

        #endregion

        #region Required Properties

        builder.Property(x => x.RowIndex)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.RowIndex)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired();

        builder.Property(x => x.Code)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.Code)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar100)
            .IsRequired();

        builder.Property(x => x.Message)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.Message)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Text)
            .IsRequired();

        #endregion

        #region Audit Configuration

        builder.OwnsOne(p => p.EntityAuditInfo, audit =>
        {
            audit.Property(a => a.CreatedAt)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.CreatedAt)))
                .IsRequired();

            audit.Property(a => a.CreatedBy)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.CreatedBy)))
                .HasMaxLength(100)
                .IsRequired();

            audit.Property(a => a.LastModifiedAt)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.LastModifiedAt)))
                .IsRequired(false);

            audit.Property(a => a.LastModifiedBy)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.LastModifiedBy)))
                .HasMaxLength(100)
                .IsRequired(false);

            audit.Property(a => a.DeletedAt)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.DeletedAt)))
                .IsRequired(false);

            audit.Property(a => a.DeletedBy)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.DeletedBy)))
                .IsRequired(false);
        });

        #endregion

        #region Indexes

        builder.HasIndexWithAutoName(x => x.PolicyMemberUploadId);

        builder.HasIndexWithAutoName(x => x.RowIndex);

        builder.HasIndexWithAutoName(x => x.Code);

        builder.HasCompositeIndexWithAutoName(
            nameof(PolicyMemberUploadValidationError.PolicyMemberUploadId),
            nameof(PolicyMemberUploadValidationError.RowIndex));

        #endregion

        #region Relationships

        builder.HasOne(x => x.PolicyMemberUpload)
            .WithMany(x => x.ValidationErrors)
            .HasForeignKey(x => x.PolicyMemberUploadId)
            .HasConstraintName(DatabaseNamingHelpers.GetForeignKeyName<PolicyMemberUploadValidationError, PolicyMemberUploadEntity>(nameof(PolicyMemberUploadValidationError.PolicyMemberUploadId)))
            .OnDelete(DeleteBehavior.Cascade);

        #endregion
    }
}