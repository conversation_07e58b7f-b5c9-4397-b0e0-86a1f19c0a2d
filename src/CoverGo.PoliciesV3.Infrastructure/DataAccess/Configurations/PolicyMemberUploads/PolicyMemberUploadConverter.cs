using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMemberUploads;

public abstract class PolicyMemberUploadConverter
{
    public class IdConverter() : ValueConverter<PolicyMemberUploadId, Guid>(id => id.Value, value => new PolicyMemberUploadId(value));
    public class StatusConverter() : ValueConverter<PolicyMemberUploadStatus, string>(status => status.Value, value => new PolicyMemberUploadStatus(value));
}

public abstract class PolicyMemberUploadValidationErrorConverter
{
    public class IdConverter() : ValueConverter<PolicyMemberUploadValidationErrorId, Guid>(id => id.Value, value => new PolicyMemberUploadValidationErrorId(value));
}
