using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Constants;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.Common.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMemberUploads;

public class PolicyMemberUploadConfiguration : IEntityTypeConfiguration<PolicyMemberUpload>
{
    public void Configure(EntityTypeBuilder<PolicyMemberUpload> builder)
    {
        // Table mapping
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(PolicyMemberUpload)));
        builder.HasPrimaryKeyWithAutoName(x => x.Id);

        #region Primary and Foreign Keys

        builder.Property(x => x.Id)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.Id)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        builder.Property(x => x.PolicyId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.PolicyId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired();

        builder.Property(x => x.EndorsementId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.EndorsementId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired(false);

        #endregion

        #region Required Properties

        builder.Property(x => x.Path)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.Path)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Text)
            .IsRequired();

        builder.Property(x => x.MembersCount)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.MembersCount)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired();

        #endregion

        #region Optional Properties

        builder.Property(x => x.ValidMembersCount)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.ValidMembersCount)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired(false);

        builder.Property(x => x.InvalidMembersCount)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.InvalidMembersCount)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired(false);

        #endregion

        #region Status Property

        builder.Property(x => x.Status)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.Status)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar50)
            .HasDefaultValue(PolicyMemberUploadStatus.REGISTERED)
            .IsRequired();

        #endregion

        builder.ConfigureEntityAudit();

        #region Indexes

        builder.HasIndexWithAutoName(x => x.PolicyId);

        builder.HasIndexWithAutoName(x => x.EndorsementId);

        builder.HasIndexWithAutoName(x => x.Status);

        builder.HasCompositeIndexWithAutoName(
            nameof(PolicyMemberUpload.PolicyId),
            nameof(PolicyMemberUpload.Status));

        #endregion
    }
}