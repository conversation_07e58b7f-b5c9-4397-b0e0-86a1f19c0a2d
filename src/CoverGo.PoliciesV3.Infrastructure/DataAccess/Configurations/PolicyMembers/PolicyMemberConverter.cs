using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMembers;

public abstract class PolicyMemberConverter
{
    public class IdConverter() : ValueConverter<PolicyMemberId, Guid>(id => id.Value, value => value);
    public class NullableIdConverter() : ValueConverter<PolicyMemberId?, Guid?>(x => x != null ? x.Value : null, x => x);
    public class ValidationResultConverter() : ValueConverter<PolicyMemberValidationResult, string>(x => x.Value, x => new PolicyMemberValidationResult(x));
    public class UnderwritingResultConverter() : ValueConverter<PolicyMemberUnderwritingResult, string>(x => x.Value, x => new PolicyMemberUnderwritingResult(x));
}

public abstract class PolicyMemberStateConverter
{
    public class IdConverter() : ValueConverter<PolicyMemberStateId, Guid>(id => id.Value, value => value);
}
