using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Constants;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Helpers;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Infrastructure.Common.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMembers;

public class PolicyMemberConfiguration : IEntityTypeConfiguration<PolicyMember>
{
    public void Configure(EntityTypeBuilder<PolicyMember> builder)
    {
        // Table mapping
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(PolicyMember)));
        builder.HasPrimaryKeyWithAutoName(x => x.Id);

        #region Primary and Foreign Keys

        builder.Property(x => x.Id)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.Id)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        builder.Property(x => x.PolicyId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.PolicyId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired();

        builder.Property(x => x.DependentOfId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.DependentOfId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired(false);

        builder.Property(x => x.IndividualId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.IndividualId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired(false);

        #endregion

        #region Required Properties

        builder.Property(x => x.MemberId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.MemberId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255)
            .IsRequired();

        #endregion

        #region Boolean Properties

        builder.Property(x => x.IsRemoved)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.IsRemoved)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean)
            .HasDefaultValue(false)
            .IsRequired();

        builder.Property(x => x.IsPrinted)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.IsPrinted)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean)
            .HasDefaultValue(false)
            .IsRequired();

        builder.Property(x => x.IsRenewed)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.IsRenewed)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean)
            .HasDefaultValue(false)
            .IsRequired();

        #endregion

        #region Optional Properties

        builder.Property(x => x.CertificateNumber)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.CertificateNumber)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255)
            .IsRequired(false);

        #endregion

        builder.ConfigureEntityAudit();

        #region Indexes

        builder.HasIndexWithAutoName(x => x.PolicyId);

        builder.HasIndexWithAutoName(x => x.DependentOfId);

        builder.HasIndexWithAutoName(x => x.IndividualId);

        #endregion

        #region Relationships

        builder.HasOne(x => x.Policy)
            .WithMany(x => x.PolicyMembers)
            .HasForeignKey(x => x.PolicyId)
            .HasConstraintName(DatabaseNamingHelpers.GetForeignKeyName<PolicyMember, Policy>(nameof(PolicyMember.PolicyId)))
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(x => x.DependentOf)
            .WithMany()
            .HasForeignKey(x => x.DependentOfId)
            .HasConstraintName(DatabaseNamingHelpers.GetSelfReferencingForeignKeyName<PolicyMember>(nameof(PolicyMember.DependentOfId)))
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(x => x.States)
            .WithOne(x => x.PolicyMember)
            .HasForeignKey(x => x.PolicyMemberId)
            .OnDelete(DeleteBehavior.Cascade);

        #endregion
    }
}