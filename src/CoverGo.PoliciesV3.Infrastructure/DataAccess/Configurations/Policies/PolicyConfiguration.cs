using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Constants;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Helpers;
using CoverGo.PoliciesV3.Infrastructure.Common.Extensions;
using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.Policies;

public class PolicyConfiguration : IEntityTypeConfiguration<Policy>
{
    public void Configure(EntityTypeBuilder<Policy> builder)
    {
        // Table mapping
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(Policy)));
        builder.HasPrimaryKeyWithAutoName(x => x.Id);

        #region Primary and Foreign Keys

        builder.Property(x => x.Id)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.Id)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        builder.Property(x => x.ContractHolderId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.ContractHolderId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired(false);

        #endregion

        #region Required Properties

        builder.Property(x => x.OriginalPolicyNumber)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.OriginalPolicyNumber)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255)
            .IsRequired();

        #endregion

        #region Date Properties

        builder.Property(x => x.StartDate)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.StartDate)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Date)
            .IsRequired(false);

        builder.Property(x => x.EndDate)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.EndDate)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Date)
            .IsRequired(false);

        builder.Property(x => x.IssueDate)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.IssueDate)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Date)
            .IsRequired(false);

        #endregion

        #region Boolean Properties

        builder.Property(x => x.IsIssued)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.IsIssued)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean)
            .HasDefaultValue(false)
            .IsRequired();

        builder.Property(x => x.IsPremiumOverridden)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.IsPremiumOverridden)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean)
            .HasDefaultValue(false)
            .IsRequired();

        builder.Property(x => x.IsRenewal)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.IsRenewal)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean)
            .HasDefaultValue(false)
            .IsRequired();

        #endregion

        #region Enum Properties

        builder.Property(x => x.Status)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.Status)))
            .IsRequired();

        #endregion

        #region Optional Properties

        builder.Property(x => x.CancellationReason)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.CancellationReason)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255)
            .IsRequired(false);

        #endregion

        #region JSONB Properties

        builder.Property(x => x.ProductId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.ProductId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, JsonSchemaProcessor.DatabaseOptions),
                v => v == null ? null : JsonSerializer.Deserialize<ProductId>(v, JsonSchemaProcessor.DatabaseOptions))
            .IsRequired(false);

        builder.Property(x => x.Fields)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.Fields)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .HasConversion(
                v => JsonSerializer.Serialize(v, JsonSchemaProcessor.DatabaseOptions),
                v => JsonSerializer.Deserialize<List<PolicyField>>(v, JsonSchemaProcessor.DatabaseOptions) ?? new List<PolicyField>())
            .IsRequired();

        #endregion

        builder.ConfigureEntityAudit();

        #region Indexes

        builder.HasUniqueIndexWithAutoName(x => x.OriginalPolicyNumber);

        builder.HasIndexWithAutoName(x => x.Status);

        #endregion

        #region Relationships

        builder.HasMany(x => x.PolicyMembers)
            .WithOne(x => x.Policy)
            .HasForeignKey(x => x.PolicyId)
            .HasConstraintName(DatabaseNamingHelpers.GetForeignKeyName<PolicyMember, Policy>(nameof(PolicyMember.PolicyId)))
            .OnDelete(DeleteBehavior.Cascade);

        #endregion
    }
}