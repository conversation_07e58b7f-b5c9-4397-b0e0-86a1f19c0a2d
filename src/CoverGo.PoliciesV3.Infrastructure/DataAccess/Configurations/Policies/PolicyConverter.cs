using CoverGo.PoliciesV3.Domain.Policies;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.Policies;

public abstract class PolicyConverter
{
    public class IdConverter() : ValueConverter<PolicyId, Guid>(id => id.Value, value => value);
    public class StatusConverter() : ValueConverter<PolicyStatus, string>(x => x.Value, x => new PolicyStatus(x));
}
