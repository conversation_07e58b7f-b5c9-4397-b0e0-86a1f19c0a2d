using Microsoft.EntityFrameworkCore;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Extensions;

/// <summary>
/// Simple extensions for handling concurrency conflicts
/// </summary>
public static class ConcurrencyExtensions
{
    /// <summary>
    /// Handles concurrency conflicts by reloading entity data from database.
    /// Use this for simple automatic conflict resolution.
    /// </summary>
    public static async Task ReloadFromDatabaseAsync(this DbUpdateConcurrencyException ex)
    {
        foreach (Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry in ex.Entries)
        {
            await entry.ReloadAsync();
        }
    }
}
