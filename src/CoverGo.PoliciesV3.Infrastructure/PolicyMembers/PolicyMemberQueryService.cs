using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.Extensions;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.PolicyMembers;

/// <summary>
/// Domain service implementation for complex policy member queries
/// </summary>
public class PolicyMemberQueryService(
    IPolicyMemberDataRepository policyMemberDataRepository,
    ILogger<PolicyMemberQueryService> logger) : IPolicyMemberQueryService
{
    public async Task<List<PolicyMember>> GetActiveMembersAsync(
        List<PolicyId> policyIds,
        CancellationToken cancellationToken = default)
    {
        if (policyIds.Count == 0)
            return [];

        try
        {
            List<PolicyMember> policyMembers = await policyMemberDataRepository.GetByPolicyIdsAsync(
                policyIds,
                cancellationToken);

            // Apply business rule: Active members are those with approved underwriting or no underwriting result
            return [.. policyMembers
                    .AsQueryable()
                    .WhereNotRemoved()
                    .Where(pm => pm.States.Any(s =>
                        s.EndorsementId == null ||
                        s.UnderwritingResult == null ||
                        s.UnderwritingResult == PolicyMemberUnderwritingResult.ManualApproved ||
                        s.UnderwritingResult == PolicyMemberUnderwritingResult.Approved))];
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting active members for policies: {PolicyIds}",
                string.Join(", ", policyIds.Select(p => p.Value)));
            throw;
        }
    }

    public async Task<List<PolicyMember>?> GetMemberValidationStatesAsync(
        string memberId,
        List<PolicyId> policyIds,
        List<EndorsementId> validEndorsementIds,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(memberId) || policyIds.Count == 0)
            return null;

        try
        {
            List<PolicyMember> policyMembers = await policyMemberDataRepository.GetByMemberIdAsync(
                memberId,
                cancellationToken);

            // Filter by policy IDs and apply business rules
            var filteredMembers = policyMembers
                .AsQueryable()
                .Where(pm => policyIds.Contains(pm.PolicyId))
                .WhereNotRemoved()
                .Where(pm => pm.HasValidEndorsements(validEndorsementIds, true))
                .ToList();

            if (filteredMembers.Count == 0)
                return null;

            // Business rule: Get the latest member for each policy
            var latestMembers = filteredMembers
                .GroupBy(pm => pm.PolicyId)
                .Select(group => group
                    .OrderByDescending(pm => pm.EntityAuditInfo.LastModifiedAt ?? pm.EntityAuditInfo.CreatedAt)
                    .First())
                .ToList();

            return latestMembers;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting member validation states for memberId: {MemberId}", memberId);
            return new List<PolicyMember>();
        }
    }

    public async Task<PolicyMember?> GetPolicyMemberCurrentStateAsync(
        string memberId,
        PolicyId policyId,
        List<EndorsementId> validEndorsementIds,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(memberId))
            return null;

        try
        {
            List<PolicyMember> policyMembers = await policyMemberDataRepository.GetByMemberIdAsync(
                memberId,
                cancellationToken);

            // Apply business rules: filter by policy, endorsements, and get most recent
            PolicyMember? policyMember = policyMembers
                .AsQueryable()
                .Where(pm => pm.PolicyId == policyId)
                .WhereNotRemoved()
                .Where(pm => pm.HasValidEndorsements(validEndorsementIds, true))
                .OrderByDescending(pm => pm.EntityAuditInfo.LastModifiedAt ?? pm.EntityAuditInfo.CreatedAt)
                .FirstOrDefault();

            return policyMember;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting policy member current state for memberId: {MemberId}", memberId);
            return null;
        }
    }

    public async Task<List<PolicyMember>> GetMemberValidationStatesByMemberIdAsync(
        string memberId,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(memberId))
            return [];

        try
        {
            List<PolicyMember> policyMembers = await policyMemberDataRepository.GetByMemberIdAsync(
                memberId,
                cancellationToken);

            // Apply business rules: filter removed members and get latest for each policy
            var latestMembers = policyMembers
                .AsQueryable()
                .WhereNotRemoved()
                .OrderByDescending(pm => pm.EntityAuditInfo.LastModifiedAt ?? pm.EntityAuditInfo.CreatedAt)
                .GroupBy(pm => pm.PolicyId)
                .Select(group => group.First())
                .ToList();

            return latestMembers;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting member validation states by memberId: {MemberId}", memberId);
            return [];
        }
    }

    public async Task<Dictionary<string, PolicyMember?>> GetPolicyMembersBatchAsync(
        List<string> memberIds,
        PolicyId policyId,
        List<EndorsementId> validEndorsementIds,
        CancellationToken cancellationToken = default)
    {
        if (memberIds.Count == 0)
            return [];

        try
        {
            // Single query to get all policy members for the given member IDs and policy
            List<PolicyMember> allMembers = await policyMemberDataRepository.GetByMemberIdsAndPolicyAsync(
                memberIds,
                policyId,
                cancellationToken);

            // Apply business rules: filter by endorsements and get most recent for each member
            var filteredMembers = allMembers
                .AsQueryable()
                .WhereNotRemoved()
                .Where(pm => pm.HasValidEndorsements(validEndorsementIds, true))
                .OrderByDescending(pm => pm.EntityAuditInfo.LastModifiedAt ?? pm.EntityAuditInfo.CreatedAt)
                .ToList();

            // Group by member ID and get the most recent member for each member ID
            var memberStates = filteredMembers
                .GroupBy(pm => pm.MemberId)
                .ToDictionary(
                    group => group.Key,
                    group => group.First()
                );

            // Ensure all requested member IDs are in the result (with null for not found)
            var result = memberIds.ToDictionary(
                memberId => memberId,
                memberId => memberStates.TryGetValue(memberId, out PolicyMember? member) ? member : null
            );

            logger.LogDebug("Batch loaded {FoundCount} out of {RequestedCount} members for policy {PolicyId}",
                memberStates.Count, memberIds.Count, policyId);

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in GetPolicyMembersBatchAsync for policy {PolicyId} with {MemberCount} members",
                policyId, memberIds.Count);

            // Return dictionary with all null values on error
            return memberIds.ToDictionary(id => id, _ => (PolicyMember?)null);
        }
    }

    public async Task<Dictionary<string, List<PolicyMember>>> GetMemberValidationStatesBatchAsync(
        List<string> memberIds,
        CancellationToken cancellationToken = default)
    {
        if (memberIds.Count == 0)
            return [];

        try
        {
            // Single batch query to get all policy members for the given member IDs
            List<PolicyMember> allMembers = await policyMemberDataRepository.GetByMemberIdsAsync(
                memberIds,
                cancellationToken);

            // Apply business rules: filter removed members and get latest for each policy per member
            var memberValidationStates = allMembers
                .AsQueryable()
                .WhereNotRemoved()
                .GroupBy(pm => pm.MemberId)
                .ToDictionary(
                    memberGroup => memberGroup.Key,
                    memberGroup => memberGroup
                        .OrderByDescending(pm => pm.EntityAuditInfo.LastModifiedAt ?? pm.EntityAuditInfo.CreatedAt)
                        .GroupBy(pm => pm.PolicyId)
                        .Select(policyGroup => policyGroup.First())
                        .ToList()
                );

            // Ensure all requested member IDs are in the result (with empty list for not found)
            var result = memberIds.ToDictionary(
                memberId => memberId,
                memberId => memberValidationStates.TryGetValue(memberId, out List<PolicyMember>? states) ? states : []
            );

            int totalStatesFound = memberValidationStates.Values.Sum(states => states.Count);
            logger.LogDebug("Batch loaded validation states for {MemberCount} members with {TotalStates} total states",
                memberValidationStates.Count, totalStatesFound);

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in GetMemberValidationStatesBatchAsync with {MemberCount} members: {Error}",
                memberIds.Count, ex.Message);

            // Return dictionary with empty lists on error
            return memberIds.ToDictionary(id => id, _ => new List<PolicyMember>());
        }
    }
}
