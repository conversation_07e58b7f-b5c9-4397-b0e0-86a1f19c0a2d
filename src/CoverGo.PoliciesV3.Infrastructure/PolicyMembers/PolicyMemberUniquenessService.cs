using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.Extensions;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.PolicyMembers;

/// <summary>
/// Domain service implementation for policy member uniqueness validation
/// </summary>
public class PolicyMemberUniquenessService(
    IPolicyMemberDataRepository policyMemberDataRepository,
    ILogger<PolicyMemberUniquenessService> logger) : IPolicyMemberUniquenessService
{
    public async Task<List<string>> ValidateTenantScopeUniquenessAsync(
        PolicyId currentPolicyId,
            string? currentMemberId,
            PolicyMemberId? currentPolicyMemberId,
            Dictionary<string, object> fieldValues,
            List<string> fieldNames,
            List<EndorsementId> validEndorsementIds,
            CancellationToken cancellationToken = default)
    {
        List<(string Name, object Value)> validFieldValues = GetValidFieldValues(fieldValues, fieldNames);
        if (validFieldValues.Count == 0)
            return [];

        try
        {
            // Get all policy members excluding current policy and current member
            List<PolicyMember> policyMembers = await policyMemberDataRepository.GetByExcludingPolicyAsync(
                currentPolicyId,
                cancellationToken);

            IEnumerable<PolicyMember> filteredMembers = FilterMembersForValidation(
                policyMembers,
                currentMemberId,
                currentPolicyMemberId,
                validEndorsementIds,
                includeNullEndorsements: true);

            return FindDuplicateFields(filteredMembers, validFieldValues);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error validating tenant scope uniqueness for policy {PolicyId}", currentPolicyId);
            throw;
        }
    }

    public async Task<List<string>> ValidatePolicyScopeUniquenessAsync(
        PolicyId currentPolicyId,
        string? currentMemberId,
        PolicyMemberId? currentPolicyMemberId,
        Dictionary<string, object> fieldValues,
        List<string> fieldNames,
        List<EndorsementId> validEndorsementIds,
        CancellationToken cancellationToken = default)
    {
        List<(string Name, object Value)> validFieldValues = GetValidFieldValues(fieldValues, fieldNames);
        if (validFieldValues.Count == 0)
            return [];

        try
        {
            // Get all policy members for the current policy
            List<PolicyMember> policyMembers = await policyMemberDataRepository.GetByPolicyIdAsync(
                currentPolicyId,
                cancellationToken);

            IEnumerable<PolicyMember> filteredMembers = FilterMembersForValidation(
                policyMembers,
                currentMemberId,
                currentPolicyMemberId,
                validEndorsementIds,
                includeNullEndorsements: true);

            return FindDuplicateFields(filteredMembers, validFieldValues);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error validating policy scope uniqueness for policy {PolicyId}", currentPolicyId);
            throw;
        }
    }

    public async Task<List<string>> ValidateContractHolderScopeUniquenessAsync(
        string? currentMemberId,
        PolicyMemberId? currentPolicyMemberId,
        List<PolicyId> contractHolderPolicyIds,
        List<EndorsementId> validEndorsementIds,
        Dictionary<string, object> fieldValues,
        List<string> fieldNames,
        CancellationToken cancellationToken = default)
    {
        List<(string Name, object Value)> validFieldValues = GetValidFieldValues(fieldValues, fieldNames);
        if (validFieldValues.Count == 0)
            return [];

        try
        {
            // Get all policy members for contract holder policies
            List<PolicyMember> policyMembers = await policyMemberDataRepository.GetByPolicyIdsAsync(
                contractHolderPolicyIds,
                cancellationToken);

            IEnumerable<PolicyMember> filteredMembers = FilterMembersForValidation(
                policyMembers,
                currentMemberId,
                currentPolicyMemberId,
                validEndorsementIds,
                includeNullEndorsements: true)
                .Where(pm => pm.HasApprovedStates())
                .Where(pm => !string.IsNullOrEmpty(pm.MemberId));

            return FindDuplicateFields(filteredMembers, validFieldValues);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error validating contract holder scope uniqueness");
            throw;
        }
    }


    #region Private Helper Methods

    private static List<(string Name, object Value)> GetValidFieldValues(
        Dictionary<string, object> fieldValues,
        List<string> fieldNames)
    {
        var validFieldTuples = new List<(string Name, object Value)>();

        foreach (string fieldName in fieldNames)
        {
            if (fieldValues.TryGetValue(fieldName, out object? fieldValue) &&
                !string.IsNullOrEmpty(fieldValue?.ToString()))
            {
                validFieldTuples.Add((Name: fieldName, Value: fieldValue));
            }
        }

        return validFieldTuples;
    }

    private static IEnumerable<PolicyMember> FilterMembersForValidation(
        IEnumerable<PolicyMember> policyMembers,
        string? currentMemberId,
        PolicyMemberId? currentPolicyMemberId,
        List<EndorsementId> validEndorsementIds,
        bool includeNullEndorsements) => policyMembers
            .AsQueryable()
            .WhereNotRemoved()
            .Where(pm => pm.HasValidEndorsements(validEndorsementIds, includeNullEndorsements))
            .Where(pm => !pm.IsExcludedFromValidation(currentMemberId, currentPolicyMemberId)); // Double-check after filtering

    private static List<string> FindDuplicateFields(
        IEnumerable<PolicyMember> members,
        List<(string Name, object Value)> validFieldValues)
    {
        var latestMembers = PolicyMember.GetLatestByMemberId(members)
            .AsQueryable()
            .WhereNotRemoved()
            .ToList();

        return [.. from fieldValue in validFieldValues
                    let hasDuplicate = latestMembers.Any(pm =>
                        pm.HasMatchingFieldValue(fieldValue.Name, fieldValue.Value.ToString() ?? ""))
                    where hasDuplicate
                    select fieldValue.Name];
    }

    #endregion
}
