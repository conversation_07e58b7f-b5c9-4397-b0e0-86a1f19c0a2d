using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;
using Microsoft.Extensions.Logging;
using DomainProductId = CoverGo.PoliciesV3.Domain.Policies.ProductId;
using ClientProductId = CoverGo.Products.Client.ProductId;

namespace CoverGo.PoliciesV3.Infrastructure.CustomFields;

/// <summary>
/// Repository implementation for retrieving raw policy member field schemas from data sources.
/// Focuses on data access and delegates schema processing to JsonSerializationHelpers.
/// </summary>
public class PolicyMemberFieldsSchemaRepository(
    IUsersService usersService,
    ICasesService casesService,
    IProductService productService,
    ILogger<PolicyMemberFieldsSchemaRepository> logger) : IPolicyMemberFieldsSchemaRepository
{
    #region Public Methods

    /// <summary>
    /// Gets the raw custom fields schema from data sources (combines Product/Census/Member schemas)
    /// </summary>
    public async Task<PolicyMemberFieldsSchema> GetCustomFieldsSchema(
        string? contractHolderId,
        DomainProductId productId,
        CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Getting custom fields schema for ContractHolderId: {ContractHolderId}, ProductId: {ProductId}",
            contractHolderId, productId);

        cancellationToken.ThrowIfCancellationRequested();

        // Fetch data from all sources concurrently for better performance
        (IReadOnlyList<PolicyMemberFieldDefinition> censusFields, IReadOnlyList<PolicyMemberFieldDefinition> productFields, IReadOnlyList<PolicyMemberFieldDefinition> memberFields) = await FetchSchemaDataConcurrently(
            contractHolderId, productId, cancellationToken);

        // Create and return the schema
        PolicyMemberFieldsSchema schema = new(
            memberFields,
            productFields,
            censusFields,
            oneOfValidations: null // Will be handled by provider
        );

        logger.LogInformation("Schema created with {MemberCount} member, {ProductCount} product, {CensusCount} census fields",
            schema.MemberFields.Count, schema.ProductFields?.Count ?? 0, schema.CensusFields?.Count ?? 0);

        return schema;
    }

    #endregion

    #region Private Methods - Data Fetching

    /// <summary>
    /// Fetches schema data from all sources concurrently for better performance
    /// </summary>
    private async Task<(
        IReadOnlyList<PolicyMemberFieldDefinition> censusFields,
        IReadOnlyList<PolicyMemberFieldDefinition> productFields,
        IReadOnlyList<PolicyMemberFieldDefinition> memberFields)> FetchSchemaDataConcurrently(
        string? contractHolderId,
        DomainProductId productId,
        CancellationToken cancellationToken)
    {
        // Create tasks for concurrent execution
        Task<IReadOnlyList<PolicyMemberFieldDefinition>> censusTask = GetContractHolderMembersFieldsAsync(contractHolderId, cancellationToken);
        Task<IReadOnlyList<PolicyMemberFieldDefinition>> productTask = GetProductMembersFieldsAsync(productId, cancellationToken);
        Task<IReadOnlyList<PolicyMemberFieldDefinition>> memberTask = GetMembersFieldsAsync(cancellationToken);

        // Wait for all tasks to complete
        await Task.WhenAll(censusTask, productTask, memberTask);

        // Return results
        return (
            censusFields: await censusTask,
            productFields: await productTask,
            memberFields: await memberTask
        );
    }

    /// <summary>
    /// Gets contract holder members fields from UsersService using optimized processing
    /// </summary>
    private async Task<IReadOnlyList<PolicyMemberFieldDefinition>> GetContractHolderMembersFieldsAsync(
        string? contractHolderId,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(contractHolderId))
        {
            logger.LogDebug("Contract holder ID is null or empty, skipping census fields retrieval");
            return [];
        }

        System.Text.Json.JsonElement fieldsJson = await usersService.GetCompanyContractHolderMembersFieldsById(contractHolderId, cancellationToken);
        return JsonSchemaProcessor.ProcessContractHolderMembersFields(fieldsJson, logger);
    }

    /// <summary>
    /// Gets product members fields from ProductService using optimized processing
    /// </summary>
    private async Task<IReadOnlyList<PolicyMemberFieldDefinition>> GetProductMembersFieldsAsync(
        DomainProductId productId,
        CancellationToken cancellationToken)
    {
        ClientProductId clientProductId = CreateClientProductId(productId);
        string? schemaJson = await productService.GetProductMemberSchema(clientProductId, cancellationToken);
        return JsonSchemaProcessor.ProcessProductMembersFields(schemaJson, logger);
    }

    /// <summary>
    /// Gets members fields from CasesService using optimized processing
    /// </summary>
    private async Task<IReadOnlyList<PolicyMemberFieldDefinition>> GetMembersFieldsAsync(
        CancellationToken cancellationToken)
    {
        System.Text.Json.JsonElement schemaJson = await casesService.GetMemberDataSchema(cancellationToken);
        return JsonSchemaProcessor.ProcessMembersFields(schemaJson, logger);
    }

    #endregion

    #region Private Methods - Utilities

    /// <summary>
    /// Creates a client product ID from domain product ID
    /// </summary>
    private static ClientProductId CreateClientProductId(DomainProductId productId) => new()
    {
        Plan = productId.Plan,
        Type = productId.Type,
        Version = productId.Version
    };

    #endregion
}