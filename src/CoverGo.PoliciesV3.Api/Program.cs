using CoverGo.BuildingBlocks.Auth.Extensions;
using CoverGo.BuildingBlocks.Bootstrapper.ApiBootstrapper;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.BuildingBlocks.Scheduler.Hangfire;
using CoverGo.PoliciesV3.Api.GraphQL;
using CoverGo.PoliciesV3.Application;
using CoverGo.PoliciesV3.Infrastructure;
using CoverGo.PoliciesV3.Infrastructure.DataAccess;
using Microsoft.EntityFrameworkCore;
using Npgsql;

WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

if (await ConfigureHandleDatabaseMigration(builder, args)) return;

ApiServiceBootstrapper webAppBuilder = ConfigureServices(builder);

WebApplication app = webAppBuilder.BuildWebApp();
ConfigureMiddleware(app, builder.Configuration);

app.Run();

static async Task<bool> ConfigureHandleDatabaseMigration(WebApplicationBuilder builder, string[] args)
{
    if (!args.Contains("--migrate-all"))
        return false;

    // Create a logger for migration operations
    using ILoggerFactory loggerFactory = LoggerFactory.Create(logging =>
    {
        logging.AddConsole();
        logging.SetMinimumLevel(LogLevel.Information);
    });
    ILogger logger = loggerFactory.CreateLogger("DatabaseMigration");

    bool dropDatabases = args.Contains("--drop-databases");
    List<string> configuredTenants = builder.Configuration.GetSection("Tenants").Get<List<string>>() ?? [];

    logger.LogInformation("Starting database migration for {TenantCount} tenants. Drop databases: {DropDatabases}",
        configuredTenants.Count, dropDatabases);

    try
    {
        foreach (string configuredTenant in configuredTenants)
        {
            logger.LogInformation("Processing migration for tenant: {TenantName}", configuredTenant);

            try
            {
                NpgsqlDataSource dataSource = PostgreSqlRegister.GetDataSourceForTenant(builder.Configuration, configuredTenant);

                var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
                optionsBuilder.UseNpgsql(dataSource);

                await using var dbContext = new ApplicationDbContext(optionsBuilder.Options);

                if (dropDatabases)
                {
                    logger.LogInformation("Dropping database for tenant: {TenantName}", configuredTenant);
                    await dbContext.Database.EnsureDeletedAsync();
                    logger.LogInformation("Successfully dropped database for tenant: {TenantName}", configuredTenant);
                }

                logger.LogInformation("Applying migrations for tenant: {TenantName}", configuredTenant);
                await dbContext.Database.MigrateAsync();

                logger.LogInformation("Successfully completed migration for tenant: {TenantName}", configuredTenant);
            }
            catch (Exception tenantException)
            {
                logger.LogError(tenantException, "Failed to migrate database for tenant: {TenantName}. Error: {ErrorMessage}",
                    configuredTenant, tenantException.Message);
            }
        }

        logger.LogInformation("Database migration process completed successfully for all tenants");
        return true;
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Critical error during database migration process: {ErrorMessage}", ex.Message);
        return false;
    }
}

static ApiServiceBootstrapper ConfigureServices(WebApplicationBuilder builder)
{
    bool runJobs = builder.Configuration.GetValue<bool>("RunJobs");

    return ApiServiceBootstrapper.Initialize(builder)
        .WithCoreSetup()
        .WithMultiTenantContext()
        .WithAuthentication()
        .WithLogging()
        .WithMetrics()
        .WithTracing()
        .WithCoreHealthCheck()
        .WithServiceConfiguration(services =>
        {
            ConfigureCoreServices(services, builder.Configuration);
            ConfigureJobServices(services, builder.Configuration, runJobs);
            ConfigureApiServices(services);
        });
}

static void ConfigureCoreServices(IServiceCollection services, IConfiguration configuration)
{
    services.AddMemoryCache();
    services.AddMultiTenantFeatureManagement(configuration.GetSection("FeatureManagement"));
    services.AddHttpContextAccessor();
    services.AddInfrastructure(configuration);
    services.AddApplication();
    services.AddCoverGoAuthorization(configuration);
    services.AddCoverGoGraphQL();

    services.AddHeaderPropagation(options =>
    {
        options.Headers.Add("Authorization");
        options.Headers.Add("Tenant");
    });
}

static void ConfigureJobServices(IServiceCollection services, IConfiguration configuration, bool runJobs)
{
    if (runJobs)
    {
        services.AddHangfireSchedulerForJobs(configuration);
    }
}

static void ConfigureApiServices(IServiceCollection services)
{
    services.AddControllers();
    services.AddEndpointsApiExplorer();
    services.AddSwaggerGen(ConfigureSwagger);
}

static void ConfigureSwagger(Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions options)
{
    options.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "CoverGo Policies V3 API",
        Version = "v1",
        Description = "REST APIs for Policy Member Upload error reporting"
    });

    options.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    options.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
    {
        {
            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Reference = new Microsoft.OpenApi.Models.OpenApiReference
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            []
        }
    });
}

static void ConfigureMiddleware(WebApplication app, IConfiguration configuration)
{
    bool runJobs = configuration.GetValue<bool>("RunJobs");

    app.UseHeaderPropagation();

    ConfigureSwaggerMiddleware(app);
    ConfigureJobMiddleware(app, runJobs);
    ConfigureApiEndpoints(app);
}

static void ConfigureSwaggerMiddleware(WebApplication app)
{
    if (!app.Environment.IsDevelopment())
        return;

    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "CoverGo Policies V3 API v1");
        c.RoutePrefix = "swagger";
    });
}

static void ConfigureJobMiddleware(WebApplication app, bool runJobs)
{
    if (!runJobs)
        return;

    app.AddHangfireDashboards();
}

static void ConfigureApiEndpoints(WebApplication app)
{
    app.MapGraphQL();
    app.MapControllers();
}

// This is to make autogenerated class Program public. It is required to allow it to be used for WebApplicationFactory<Program>.
// https://learn.microsoft.com/en-us/aspnet/core/test/integration-tests?view=aspnetcore-8.0#basic-tests-with-the-default-webapplicationfactory
public partial class Program { }