{"Serilog": {"Enrich": ["WithSpan", "FromLogContext"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Compact.RenderedCompactJsonFormatter, Serilog.Formatting.Compact"}}]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost:5432;Database={tenant}_mydatabase;Username=********;Password=********", "mongo": "mongodb://localhost:27017/policies"}, "FeatureManagement": {"UseEffectiveDateInAddPolicyMember": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "asia_preprod", "asia_uat", "asia_prod", "big_policies_dev"]}}]}, "UseTheSamePlanForEmployeeAndDependents": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "asia_uat", "asia_preprod", "asia_prod", "big_policies_dev"]}}]}, "OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "asia_uat", "asia_preprod", "asia_prod", "big_policies_dev"]}}]}, "SkipContractHolderUniqueRulesWhenAddPolicyMember": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["big_policies_dev"]}}]}, "AllowMembersFromOtherContractHolders": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "asia_preprod", "asia_uat", "asia_prod", "big_policies_dev"]}}]}}, "Tenants": ["coverHealth_dev"], "RunJobs": false, "serviceUrls": {"logging": "http://localhost:9200/", "auth": "http://localhost:60000/", "users": "http://localhost:60010/", "cases": "http://localhost:60600/", "filesystem": "http://localhost:61872", "policies": "http://localhost:60050/", "products": "http://localhost:60020/"}}