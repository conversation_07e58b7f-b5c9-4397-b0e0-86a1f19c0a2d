{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Tenants": ["covergo", "big_policies_dev"], "ConnectionStrings": {"Redis": "localhost:6379", "mongo": ""}, "RunJobs": true, "FeatureManagement": {"UseEffectiveDateInAddPolicyMember": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "asia_preprod", "asia_uat", "asia_prod", "big_policies_dev"]}}]}, "UseTheSamePlanForEmployeeAndDependents": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "asia_uat", "asia_preprod", "asia_prod", "big_policies_dev"]}}]}, "OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "asia_uat", "asia_preprod", "asia_prod", "big_policies_dev"]}}]}, "SkipContractHolderUniqueRulesWhenAddPolicyMember": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["big_policies_dev"]}}]}, "AllowMembersFromOtherContractHolders": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "asia_preprod", "asia_uat", "asia_prod", "big_policies_dev"]}}]}}, "ObservabilityConfiguration": {"CollectorUrl": "http://localhost:4317", "ServiceName": "covergo-policies-V3", "Timeout": 1000}, "CircuitBreaker": {"HandledEventsAllowedBeforeBreaking": 5, "BreakDurationSeconds": 30, "MinimumThroughput": 10, "SamplingDurationSeconds": 60, "FailureRatio": 0.5, "Services": null}}