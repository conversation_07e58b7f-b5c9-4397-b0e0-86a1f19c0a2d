namespace CoverGo.PoliciesV3.Api.GraphQL.Common.Types;

/// <summary>
/// Custom user error interface type that will get the proper prefix applied by naming conventions.
/// The class name "UserError" will result in "policies_UserError" via naming conventions.
/// </summary>
public class UserError : InterfaceType
{
    protected override void Configure(IInterfaceTypeDescriptor descriptor)
    {
        // Don't set explicit name - let naming conventions handle it
        // Class name "UserError" + naming convention = "policies_UserError"
        descriptor.Field("message").Type<NonNullType<StringType>>();
        descriptor.Field("code").Type<NonNullType<StringType>>();
    }
}
