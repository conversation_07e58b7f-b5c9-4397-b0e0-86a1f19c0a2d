namespace CoverGo.PoliciesV3.Api.GraphQL.Common;

/// <summary>
/// Constants for GraphQL operation type names and schema configuration to avoid magic strings
/// </summary>
public static class OperationTypeNames
{
    /// <summary>
    /// Name for the GraphQL schema
    /// </summary>
    public const string SchemaName = "policies";

    /// <summary>
    /// Name for the root Query operation type
    /// </summary>
    public const string Query = "Query";

    /// <summary>
    /// Name for the root Mutation operation type
    /// </summary>
    public const string Mutation = "Mutation";

    /// <summary>
    /// Name for the root Subscription operation type
    /// </summary>
    public const string Subscription = "Subscription";
}
