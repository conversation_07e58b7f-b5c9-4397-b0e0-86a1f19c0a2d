using System.Text.Json;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Api.GraphQL.Common;

/// <summary>
/// Helper class for mapping flexible field inputs to PolicyField objects
/// Supports multiple input formats while preserving GraphQL Any type flexibility
/// </summary>
public static class FieldMappingHelper
{
    /// <summary>
    /// Maps flexible field input to PolicyField list, supporting multiple formats:
    /// - Dictionary<string, object?> (JSON object from GraphQL)
    /// - List of field value pairs
    /// - JsonElement (when GraphQL sends JSON)
    /// - String (JSON string)
    /// - null (no fields)
    /// </summary>
    public static List<PolicyField> MapToPolicyFields(object? fieldsInput)
    {
        if (fieldsInput == null)
            return new List<PolicyField>();

        return fieldsInput switch
        {
            // Case 1: Dictionary (JSON object from GraphQL) - most common case
            Dictionary<string, object?> dict => MapDictionaryToPolicyFields(dict),

            // Case 2: JsonElement (when GraphQL sends JSON)
            JsonElement jsonElement => MapJsonElementToPolicyFields(jsonElement),

            // Case 3: String (JSON string)
            string jsonString => MapJsonStringToPolicyFields(jsonString),

            // Case 4: Any other object - try to convert to dictionary
            _ => MapObjectToPolicyFields(fieldsInput)
        };
    }

    /// <summary>
    /// Maps dictionary to PolicyField list with enhanced type conversion
    /// </summary>
    private static List<PolicyField> MapDictionaryToPolicyFields(Dictionary<string, object?> dict)
    {
        return dict.Select(kvp => new PolicyField
        {
            Key = kvp.Key,
            Value = ConvertValueToAppropriateType(kvp.Value)
        }).ToList();
    }

    /// <summary>
    /// Maps JsonElement to PolicyField list
    /// </summary>
    private static List<PolicyField> MapJsonElementToPolicyFields(JsonElement jsonElement)
    {
        return jsonElement.ValueKind switch
        {
            JsonValueKind.Object => jsonElement.EnumerateObject()
                .Select(prop => new PolicyField
                {
                    Key = prop.Name,
                    Value = ExtractJsonValue(prop.Value)
                }).ToList(),

            JsonValueKind.Array => jsonElement.EnumerateArray()
                .Select((item, index) => new PolicyField
                {
                    Key = $"field_{index}",
                    Value = ExtractJsonValue(item)
                }).ToList(),

            _ => new List<PolicyField>()
        };
    }

    /// <summary>
    /// Extracts value from JsonElement with proper type handling
    /// </summary>
    private static object? ExtractJsonValue(JsonElement element)
    {
        return element.ValueKind switch
        {
            JsonValueKind.String => element.GetString(),
            JsonValueKind.Number => element.TryGetInt32(out int intValue) ? intValue : element.GetDecimal(),
            JsonValueKind.True => true,
            JsonValueKind.False => false,
            JsonValueKind.Null => null,
            JsonValueKind.Array => element.EnumerateArray().Select(ExtractJsonValue).ToList(),
            JsonValueKind.Object => element.GetRawText(), // Keep as JSON string for complex objects
            _ => element.GetRawText()
        };
    }

    /// <summary>
    /// Maps JSON string to PolicyField list
    /// </summary>
    private static List<PolicyField> MapJsonStringToPolicyFields(string jsonString)
    {
        try
        {
            JsonElement jsonElement = JsonSerializer.Deserialize<JsonElement>(jsonString);
            return MapJsonElementToPolicyFields(jsonElement);
        }
        catch (JsonException)
        {
            // If JSON parsing fails, treat as a single field with the string as value
            return new List<PolicyField>
            {
                new() { Key = "value", Value = jsonString }
            };
        }
    }

    /// <summary>
    /// Maps any object to PolicyField list
    /// </summary>
    private static List<PolicyField> MapObjectToPolicyFields(object obj)
    {
        // Try to convert to JSON and then parse
        try
        {
            string jsonString = JsonSerializer.Serialize(obj);
            return MapJsonStringToPolicyFields(jsonString);
        }
        catch
        {
            // Fallback: treat the object as a single field
            return new List<PolicyField>
            {
                new() { Key = "value", Value = obj.ToString() }
            };
        }
    }

    /// <summary>
    /// Converts a value to the most appropriate type for PolicyField storage
    /// Handles primitive types, dates, and complex objects
    /// </summary>
    private static object? ConvertValueToAppropriateType(object? value)
    {
        if (value == null)
            return null;

        return value switch
        {
            // Handle primitive types
            string or int or long or double or decimal or bool => value,
            
            // Handle date types
            DateTime dateTime => dateTime.ToString("yyyy-MM-ddTHH:mm:ss"),
            DateOnly dateOnly => dateOnly.ToString("yyyy-MM-dd"),
            
            // Handle collections
            IEnumerable<object> collection => collection.ToList(),
            
            // Handle complex objects - serialize to JSON string
            _ => JsonSerializer.Serialize(value)
        };
    }

    /// <summary>
    /// Simple mapping for common use case (dictionary only)
    /// </summary>
    public static List<PolicyField> MapSimple(object? fieldsInput)
    {
        if (fieldsInput == null)
            return new List<PolicyField>();

        if (fieldsInput is Dictionary<string, object?> dict)
            return dict.Select(kvp => new PolicyField
            {
                Key = kvp.Key,
                Value = ConvertValueToAppropriateType(kvp.Value)
            }).ToList();

        // Fallback for other types
        return new List<PolicyField>
        {
            new() { Key = "value", Value = fieldsInput.ToString() }
        };
    }

    /// <summary>
    /// Transforms PolicyField collection back to dictionary for GraphQL output
    /// This is the reverse operation of MapToPolicyFields
    /// </summary>
    public static Dictionary<string, object?> TransformToDictionary(IEnumerable<PolicyField>? policyFields)
    {
        if (policyFields == null || !policyFields.Any())
            return new Dictionary<string, object?>();

        try
        {
            return policyFields.ToDictionary(
                field => field.Key,
                field => field.Value
            );
        }
        catch (Exception)
        {
            // If there are duplicate keys, handle gracefully
            var result = new Dictionary<string, object?>();
            foreach (PolicyField field in policyFields)
                if (!result.ContainsKey(field.Key))
                    result[field.Key] = field.Value;
            return result;
        }
    }
} 