using CoverGo.PoliciesV3.Application.Common.DTOs;

namespace CoverGo.PoliciesV3.Api.GraphQL.Common;

/// <summary>
/// GraphQL input wrapper for FieldValuePair DTO with GraphQL-specific attributes
/// </summary>
[GraphQLDescription("A key-value pair for additional fields")]
public class FieldValuePairInput
{
    /// <summary>
    /// Gets or sets the field name/key
    /// </summary>
    [GraphQLDescription("Field name")]
    public required string Key { get; set; }

    /// <summary>
    /// Gets or sets the field value
    /// </summary>
    [GraphQLDescription("Field value")]
    public object? Value { get; set; }

    /// <summary>
    /// Converts GraphQL input to application DTO
    /// </summary>
    public FieldValuePair ToDto() => new() { Key = Key, Value = Value };

    /// <summary>
    /// Converts a list of GraphQL inputs to application DTOs
    /// </summary>
    public static List<FieldValuePair> ToDtos(List<FieldValuePairInput> inputs) =>
        [.. inputs.Select(input => input.ToDto())];

    /// <summary>
    /// Converts GraphQL inputs to dictionary (legacy method for backward compatibility)
    /// </summary>
    public static Dictionary<string, object?> ToDictionary(List<FieldValuePairInput> fieldValuePairs)
    {
        var dictionary = new Dictionary<string, object?>();
        foreach (FieldValuePairInput pair in fieldValuePairs)
            dictionary[pair.Key] = pair.Value;
        return dictionary;
    }
}
