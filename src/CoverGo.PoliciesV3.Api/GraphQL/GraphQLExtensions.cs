using CoverGo.Extensions.DependencyInjection;
using CoverGo.PoliciesV3.Api.GraphQL.Common;
using CoverGo.PoliciesV3.Api.GraphQL.Common.Types;
using CoverGo.PoliciesV3.Api.GraphQL.Conventions;
using HotChocolate.Execution.Configuration;
using HotChocolate.Types.Descriptors;

namespace CoverGo.PoliciesV3.Api.GraphQL;

public static class GraphQLExtensions
{
    #region Main Configuration

    /// <summary>
    /// Configures the GraphQL server for CoverGo with all necessary components
    /// </summary>
    public static IServiceCollection AddCoverGoGraphQL(this IServiceCollection services)
    {
        services.ConfigureGraphQLNamingOptions();

        MutationConventionOptions mutationOptions = CreateMutationConventionOptions();

        _ = services.AddGraphQLServer()
            .AddCoreGraphQLConfiguration(mutationOptions)
            .AddGraphQLConventions()
            .AddGraphQLSecurity()
            .AddGraphQLOperationTypes()
            .AddGraphQLTypes();

        return services;
    }

    #endregion

    #region Configuration Methods

    /// <summary>
    /// Configures GraphQL naming options for consistent type naming
    /// </summary>
    private static IServiceCollection ConfigureGraphQLNamingOptions(this IServiceCollection services) =>
        services.Configure<GraphQLNamingOptions>(options =>
        {
            options.SchemaPrefix = Common.OperationTypeNames.SchemaName;
            options.ApplyToObjectTypes = true;
            options.ApplyToInputTypes = true;
        });

    /// <summary>
    /// Creates mutation convention options with consistent naming patterns
    /// </summary>
    private static MutationConventionOptions CreateMutationConventionOptions() =>
        new()
        {
            InputArgumentName = "input",
            InputTypeNamePattern = $"{Common.OperationTypeNames.SchemaName}_{{MutationName}}Input",
            PayloadTypeNamePattern = $"{Common.OperationTypeNames.SchemaName}_{{MutationName}}Payload",
            PayloadErrorTypeNamePattern = $"{Common.OperationTypeNames.SchemaName}_{{MutationName}}Error",
            PayloadErrorsFieldName = "errors",
            ApplyToAllMutations = true
        };

    #endregion

    #region Core GraphQL Configuration

    /// <summary>
    /// Adds core GraphQL configuration including observability, schema settings, and mutation conventions
    /// </summary>
    private static IRequestExecutorBuilder AddCoreGraphQLConfiguration(
        this IRequestExecutorBuilder builder,
        MutationConventionOptions mutationOptions) =>
        builder
            .AddCoverGoObservability()
            .ConfigureSchema(sb => sb.ModifyOptions(opts => opts.StrictValidation = false))
            .AddMutationConventions(mutationOptions);

    #endregion

    #region GraphQL Conventions

    /// <summary>
    /// Adds GraphQL naming conventions and type interceptors
    /// </summary>
    private static IRequestExecutorBuilder AddGraphQLConventions(this IRequestExecutorBuilder builder) =>
        builder
            .AddConvention<INamingConventions, PoliciesNamingConventions>()
            .TryAddTypeInterceptor<ErrorTypeNamingInterceptor>();

    #endregion

    #region Security Configuration

    /// <summary>
    /// Adds GraphQL security features including authorization
    /// </summary>
    private static IRequestExecutorBuilder AddGraphQLSecurity(this IRequestExecutorBuilder builder) =>
        builder
            .AddAuthorization()
            .ModifyRequestOptions(opt => opt.IncludeExceptionDetails = true)
            .AddHttpRequestInterceptor<IdentityContextProviderInterceptor>();

    #endregion

    #region Operation Types
    /// <summary>
    /// Configures GraphQL operation types (Query and Mutation)
    /// </summary>
    private static IRequestExecutorBuilder AddGraphQLOperationTypes(this IRequestExecutorBuilder builder) =>
        builder
            .AddMutationType(x => x.Name(Common.OperationTypeNames.Mutation))
            .AddQueryType(x => x.Name(Common.OperationTypeNames.Query));

    #endregion

    #region Type Registration

    /// <summary>
    /// Registers all GraphQL types including external types, domain types, and error types
    /// </summary>
    private static IRequestExecutorBuilder AddGraphQLTypes(this IRequestExecutorBuilder builder) =>
        builder
            .AddTypes()
            .AddDomainScalarTypes()
            .AddDomainEnumTypes()
            .IgnoreDomainEventTypes()
            .AddErrorInterfaceType<UserError>();
    #endregion
}