namespace CoverGo.PoliciesV3.Api.GraphQL.Conventions;

/// <summary>
/// Configuration options for GraphQL type naming conventions
/// </summary>
public class GraphQLNamingOptions
{
    /// <summary>
    /// The prefix to apply to GraphQL object types (default: "policies")
    /// </summary>
    public string SchemaPrefix { get; set; } = Common.OperationTypeNames.SchemaName;

    /// <summary>
    /// Whether to apply naming conventions to object types (default: true)
    /// </summary>
    public bool ApplyToObjectTypes { get; set; } = true;

    /// <summary>
    /// Whether to apply naming conventions to input types (default: false, handled by mutation conventions)
    /// </summary>
    public bool ApplyToInputTypes { get; set; } = false;
}
