using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using HotChocolate.Execution.Configuration;
using CoverGo.PoliciesV3.Api.GraphQL.Common;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.BuildingBlocks.Domain.Core.DomainEvents;
using CoverGo.PoliciesV3.Api.GraphQL.Infrastructure;
using CoverGo.PoliciesV3.Api.PolicyMemberUploads.Services;

namespace CoverGo.PoliciesV3.Api.GraphQL.Conventions;

public static class GraphQLTypeAutoRegistration
{
    /// <summary>
    /// Registers all scalar types following domain conventions
    /// </summary>
    public static IRequestExecutorBuilder AddDomainScalarTypes(this IRequestExecutorBuilder builder)
    {
        // Basic scalar types
        builder.BindRuntimeType<uint, UnsignedIntType>()
               .BindRuntimeType<Guid, IdType>();

        // Domain ID types - simple registration
        builder.AddAllDomainIds();

        // Type converters for complex conversions
        RegisterTypeConverters(builder);

        return builder;
    }

    /// <summary>
    /// Registers all enum types
    /// </summary>
    public static IRequestExecutorBuilder AddDomainEnumTypes(this IRequestExecutorBuilder builder)
    {
        // Register PolicyStatus and UnderwritingStatus as strings (existing behavior)
        builder.BindRuntimeType<PolicyStatus, StringType>()
               .AddTypeConverter<string?, PolicyStatus?>(v => v != null ? new(v) : null)
               .AddTypeConverter<PolicyStatus?, string?>(v => v?.Value);

        builder.BindRuntimeType<UnderwritingStatus, StringType>()
               .AddTypeConverter<string?, UnderwritingStatus?>(v => v != null ? new(v) : null)
               .AddTypeConverter<UnderwritingStatus?, string?>(v => v?.Value);

        // Use feature-specific enum registration service
        return builder.AddPolicyMemberUploadEnums();
    }

    /// <summary>
    /// Registers type converters for complex conversions
    /// </summary>
    private static void RegisterTypeConverters(IRequestExecutorBuilder builder)
    {
        // Register converter for AnyType to ICollection<PolicyField> conversion
        // This enables GraphQL to automatically convert flexible field inputs to PolicyField collections
        builder.AddTypeConverter<object?, ICollection<PolicyField>>(input =>
            FieldMappingHelper.MapToPolicyFields(input));
    }

    /// </summary>
    public static IRequestExecutorBuilder IgnoreDomainEventTypes(this IRequestExecutorBuilder builder)
    {
        // Bind IDomainEvent to a string type to prevent auto-discovery issues
        // This prevents HotChocolate from trying to create input types for IDomainEvent
        builder.BindRuntimeType<IDomainEvent, StringType>();

        // Prevent PolicyMemberState from being used as input type since it has computed properties
        // Bind it to StringType to prevent auto-discovery issues similar to IDomainEvent
        builder.BindRuntimeType<PolicyMemberState, StringType>();

        return builder;
    }
}
