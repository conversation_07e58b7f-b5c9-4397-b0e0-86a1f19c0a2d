using HotChocolate.Types.Descriptors;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace CoverGo.PoliciesV3.Api.GraphQL.Conventions;

public class PoliciesNamingConventions(IOptions<GraphQLNamingOptions>? options = null) : DefaultNamingConventions
{
    #region Fields

    private readonly GraphQLNamingOptions _options = options?.Value ?? new GraphQLNamingOptions();
    private readonly ConcurrentDictionary<Type, bool> _isInternalTypeCache = new();

    #endregion

    #region Public API - GraphQL Naming Convention Overrides

    /// <summary>
    /// Applies naming convention to GraphQL types by adding the configured prefix.
    /// </summary>
    public override string GetTypeName(Type type, TypeKind kind)
    {
        ArgumentNullException.ThrowIfNull(type);
        string defaultName = base.GetTypeName(type, kind);

        return ShouldApplyPrefix(kind, defaultName, type)
            ? $"{_options.SchemaPrefix}_{defaultName}"
            : defaultName;
    }




    public override string GetTypeName(Type type)
    {
        ArgumentNullException.ThrowIfNull(type);
        string defaultName = base.GetTypeName(type);

        return ShouldApplyPrefix(TypeKind.Object, defaultName, type)
            ? $"{_options.SchemaPrefix}_{defaultName}"
            : defaultName;
    }



    #endregion

    #region Core Filtering Logic

    private bool ShouldApplyPrefix(TypeKind kind, string typeName, Type? type) =>
        IsSupportedTypeKind(kind) &&
        IsTypeKindEnabled(kind) &&
        !ShouldSkipType(typeName, type);

    #endregion

    #region Type Kind Validation

    /// <summary>
    /// Checks if the type kind is supported for prefixing
    /// </summary>
    private static bool IsSupportedTypeKind(TypeKind kind) =>
        kind is TypeKind.Object or TypeKind.Union or TypeKind.Interface or TypeKind.InputObject or TypeKind.Enum;

    /// <summary>
    /// Checks if the type kind is enabled in configuration
    /// </summary>
    private bool IsTypeKindEnabled(TypeKind kind) => kind switch
    {
        TypeKind.Object => _options.ApplyToObjectTypes,
        TypeKind.InputObject => _options.ApplyToInputTypes,
        TypeKind.Union or TypeKind.Interface or TypeKind.Enum => true, // Always enabled for these types
        _ => false
    };

    #endregion

    #region Type Exclusion Rules

    /// <summary>
    /// Determines if a type should be skipped (not prefixed) based on various exclusion rules
    /// </summary>
    private bool ShouldSkipType(string typeName, Type? type) =>
        IsRootType(typeName) ||
        HasExistingPrefix(typeName) ||
        IsBuiltInGraphQLType(typeName) ||
        IsMutationConventionType(typeName) ||
        IsInternalFrameworkType(type);

    /// <summary>
    /// Checks if this is a root GraphQL type (Query, Mutation, Subscription)
    /// </summary>
    private static bool IsRootType(string typeName) =>
        typeName is "Query" or "Mutation" or "Subscription";

    /// <summary>
    /// Checks if the type name already has our configured prefix
    /// </summary>
    private bool HasExistingPrefix(string typeName) =>
        typeName.StartsWith($"{_options.SchemaPrefix}_", StringComparison.Ordinal);

    /// <summary>
    /// Checks if this is a built-in GraphQL type (scalars, introspection types)
    /// </summary>
    private static bool IsBuiltInGraphQLType(string typeName) =>
        typeName.StartsWith("__", StringComparison.Ordinal) || // GraphQL introspection types
        typeName is "String" or "Int" or "Float" or "Boolean" or "ID" or
                   "DateTime" or "Date" or "UUID" or "Byte" or "Short" or
                   "Long" or "Decimal" or "Url" or "EmailAddress";

    /// <summary>
    /// Checks if this type is already handled by mutation conventions (has underscore + Input/Payload suffix)
    /// </summary>
    private static bool IsMutationConventionType(string typeName) =>
        typeName.Contains('_') && (typeName.EndsWith("Input", StringComparison.Ordinal) ||
                                  typeName.EndsWith("Payload", StringComparison.Ordinal));

    /// <summary>
    /// Checks if this is an internal framework type that shouldn't be prefixed
    /// </summary>
    private bool IsInternalFrameworkType(Type? type) =>
        type != null && IsInternalType(type);

    #endregion

    #region Internal Type Detection with Caching

    /// <summary>
    /// Checks if this is a HotChocolate/System/Microsoft internal type that shouldn't be prefixed.
    /// Uses a cache for performance optimization.
    /// </summary>
    private bool IsInternalType(Type type)
    {
        if (_isInternalTypeCache.TryGetValue(type, out bool isInternal))
            return isInternal;

        string? namespaceName = type.Namespace;
        bool result = namespaceName != null &&
                      (namespaceName.StartsWith("HotChocolate", StringComparison.Ordinal) ||
                       namespaceName.StartsWith("System", StringComparison.Ordinal) ||
                       namespaceName.StartsWith("Microsoft", StringComparison.Ordinal));

        _isInternalTypeCache[type] = result;
        return result;
    }

    #endregion
}
