using HotChocolate.Configuration;
using HotChocolate.Types.Descriptors.Definitions;
using Microsoft.Extensions.Options;

namespace CoverGo.PoliciesV3.Api.GraphQL.Conventions;

/// <summary>
/// Type interceptor that ensures error types use the correct naming convention.
/// This addresses cases where mutation convention error types might not follow
/// the standard naming patterns applied by PoliciesNamingConventions.
/// </summary>
/// <remarks>
/// Initializes a new instance of the ErrorTypeNamingInterceptor.
/// </remarks>
/// <param name="options">GraphQL naming options configuration</param>
/// <param name="logger">Logger for tracking error type registration</param>
public class ErrorTypeNamingInterceptor(IOptions<GraphQLNamingOptions>? options = null, ILogger<ErrorTypeNamingInterceptor>? logger = null) : TypeInterceptor
{
    #region Constants

    private static class ErrorTypeConstants
    {
        public const string ErrorSuffix = "Error";
        public const string MessageFieldName = "message";
        public const string CodeFieldName = "code";
        public const string PrefixSeparator = "_";
    }

    #endregion

    #region Fields

    private readonly GraphQLNamingOptions _options = options?.Value ?? new GraphQLNamingOptions();
    private readonly ILogger<ErrorTypeNamingInterceptor>? _logger = logger;
    private readonly HashSet<string> _processedTypes = new();

    #endregion

    #region TypeInterceptor Overrides

    /// <summary>
    /// Called before type name is completed - the optimal point for applying naming conventions
    /// </summary>
    public override void OnBeforeCompleteName(
        ITypeCompletionContext completionContext,
        DefinitionBase definition)
    {
        ArgumentNullException.ThrowIfNull(definition);

        if (definition is ObjectTypeDefinition objectDef)
        {
            if (ShouldApplyNamingConvention(objectDef))
            {
                _logger?.LogInformation("Applying naming convention to error type: {OriginalName}", objectDef.Name);
                ApplyErrorTypeNamingConvention(objectDef);
                _logger?.LogInformation("Applied naming convention. New name: {NewName}", objectDef.Name);
            }
        }
    }

    #endregion

    #region Private Methods



    /// <summary>
    /// Determines if the naming convention should be applied to the given type definition
    /// </summary>
    /// <param name="definition">The object type definition to check</param>
    /// <returns>True if naming convention should be applied</returns>
    private bool ShouldApplyNamingConvention(ObjectTypeDefinition definition)
    {
        ArgumentNullException.ThrowIfNull(definition);

        bool isErrorType = IsErrorType(definition);
        bool hasPrefix = HasPrefixAlready(definition);
        bool isGenerated = IsGeneratedErrorType(definition);
        bool isValidationException = IsValidationException(definition);

        // Exclude ValidationException to prevent duplicate registration with ValidationError
        bool shouldApply = isErrorType && !hasPrefix && isGenerated && !isValidationException;

        if (shouldApply)
        {
            // Check if we've already processed this type
            if (_processedTypes.Contains(definition.Name ?? ""))
            {
                _logger?.LogWarning("Duplicate processing detected for type: {TypeName}. This type has already been processed!", definition.Name);
            }
            else
            {
                _processedTypes.Add(definition.Name ?? "");
            }
        }

        return shouldApply;
    }

    /// <summary>
    /// Checks if the type definition represents an error type based on naming convention
    /// </summary>
    /// <param name="definition">The object type definition to check</param>
    /// <returns>True if this is an error type</returns>
    private static bool IsErrorType(ObjectTypeDefinition definition)
    {
        string? typeName = definition.Name;
        bool isErrorType = !string.IsNullOrEmpty(typeName) &&
               typeName.EndsWith(ErrorTypeConstants.ErrorSuffix, StringComparison.Ordinal);

        return isErrorType;
    }

    /// <summary>
    /// Checks if the type already has the required prefix
    /// </summary>
    /// <param name="definition">The object type definition to check</param>
    /// <returns>True if the type already has the prefix</returns>
    private bool HasPrefixAlready(ObjectTypeDefinition definition)
    {
        string? typeName = definition.Name;
        string expectedPrefix = $"{_options.SchemaPrefix}{ErrorTypeConstants.PrefixSeparator}";

        bool hasPrefix = !string.IsNullOrEmpty(typeName) &&
               typeName.StartsWith(expectedPrefix, StringComparison.Ordinal);

        return hasPrefix;
    }

    /// <summary>
    /// Determines if this is a generated error type that should have naming convention applied
    /// by checking for standard error fields
    /// </summary>
    /// <param name="definition">The object type definition to check</param>
    /// <returns>True if this is a generated error type</returns>
    private static bool IsGeneratedErrorType(ObjectTypeDefinition definition) => HasStandardErrorFields(definition);

    /// <summary>
    /// Checks if the type has standard error fields (message and/or code)
    /// which indicates it's a generated error type from mutation conventions
    /// </summary>
    /// <param name="definition">The object type definition to check</param>
    /// <returns>True if the type has standard error fields</returns>
    private static bool HasStandardErrorFields(ObjectTypeDefinition definition)
    {
        bool hasMessageField = definition.Fields.Any(field =>
            string.Equals(field.Name, ErrorTypeConstants.MessageFieldName, StringComparison.OrdinalIgnoreCase));

        bool hasCodeField = definition.Fields.Any(field =>
            string.Equals(field.Name, ErrorTypeConstants.CodeFieldName, StringComparison.OrdinalIgnoreCase));

        return hasMessageField || hasCodeField;
    }

    /// <summary>
    /// Checks if the type definition represents ValidationException to exclude it from processing
    /// </summary>
    /// <param name="definition">The object type definition to check</param>
    /// <returns>True if this is ValidationException</returns>
    private static bool IsValidationException(ObjectTypeDefinition definition)
    {
        // Check if the runtime type is ValidationException
        if (definition.RuntimeType?.Name == "ValidationException")
        {
            return true;
        }

        // Check if the type name contains ValidationException
        if (definition.Name?.Contains("ValidationException", StringComparison.OrdinalIgnoreCase) == true)
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// Applies the error type naming convention by adding the configured prefix
    /// </summary>
    /// <param name="definition">The object type definition to modify</param>
    private void ApplyErrorTypeNamingConvention(ObjectTypeDefinition definition)
    {
        string? originalName = definition.Name;
        if (string.IsNullOrEmpty(originalName))
        {
            _logger?.LogWarning("Cannot apply naming convention to type with null or empty name");
            return;
        }

        // Ensure the schema prefix is valid for GraphQL naming
        string cleanPrefix = string.IsNullOrWhiteSpace(_options.SchemaPrefix)
            ? "policies"
            : _options.SchemaPrefix.Trim();

        string newName = $"{cleanPrefix}{ErrorTypeConstants.PrefixSeparator}{originalName}";

        definition.Name = newName;
    }

    #endregion
}