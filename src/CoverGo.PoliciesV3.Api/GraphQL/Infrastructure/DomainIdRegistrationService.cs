using System.Collections.Concurrent;
using System.Linq.Expressions;
using System.Reflection;
using HotChocolate.Execution.Configuration;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Api.GraphQL.Infrastructure;

public static class DomainIdRegistrationService
{
    private static readonly ConcurrentDictionary<Type, Delegate> ToStringConverters = new();
    private static readonly ConcurrentDictionary<Type, Delegate> FromStringConverters = new();

    public static IRequestExecutorBuilder AddAllDomainIds(this IRequestExecutorBuilder builder)
    {
        builder.AddDomainIdOptimized<PolicyId>();
        builder.AddDomainIdOptimized<PolicyMemberId>();
        builder.AddDomainIdOptimized<PolicyMemberStateId>();
        builder.AddDomainIdOptimized<PolicyMemberUploadId>();
        builder.AddDomainIdOptimized<EndorsementId>();

        return builder;
    }

    public static IRequestExecutorBuilder AddDomainIdOptimized<T>(this IRequestExecutorBuilder builder)
        where T : class
    {
        builder.BindRuntimeType<T, IdType>();

        Func<T, string> toStringConverter = GetOrCreateToStringConverter<T>();
        Func<string, T> fromStringConverter = GetOrCreateFromStringConverter<T>();

        builder.AddTypeConverter<T, string>(domainId => toStringConverter(domainId));
        builder.AddTypeConverter<string, T>(stringValue => fromStringConverter(stringValue));

        return builder;
    }

    private static Func<T, string> GetOrCreateToStringConverter<T>() where T : class
    {
        return (Func<T, string>)ToStringConverters.GetOrAdd(typeof(T), _ =>
        {
            ParameterExpression domainParam = Expression.Parameter(typeof(T), "domainId");
            PropertyInfo valueProperty = typeof(T).GetProperty("Value")
                ?? throw new InvalidOperationException($"Type {typeof(T).Name} must have a Value property");

            MemberExpression valueAccess = Expression.Property(domainParam, valueProperty);
            MethodInfo toStringMethod = typeof(Guid).GetMethod("ToString", Type.EmptyTypes)!;
            MethodCallExpression toStringCall = Expression.Call(valueAccess, toStringMethod);

            return Expression.Lambda<Func<T, string>>(toStringCall, domainParam).Compile();
        });
    }

    private static Func<string, T> GetOrCreateFromStringConverter<T>() where T : class
    {
        return (Func<string, T>)FromStringConverters.GetOrAdd(typeof(T), _ =>
        {
            ParameterExpression stringParam = Expression.Parameter(typeof(string), "stringValue");
            MethodInfo parseMethod = typeof(Guid).GetMethod("Parse", [typeof(string)])!;
            MethodCallExpression parseCall = Expression.Call(parseMethod, stringParam);

            ConstructorInfo constructor = typeof(T).GetConstructor([typeof(Guid)])
                ?? throw new InvalidOperationException($"Type {typeof(T).Name} must have a constructor that accepts a Guid parameter");

            NewExpression constructorCall = Expression.New(constructor, parseCall);

            return Expression.Lambda<Func<string, T>>(constructorCall, stringParam).Compile();
        });
    }
}
