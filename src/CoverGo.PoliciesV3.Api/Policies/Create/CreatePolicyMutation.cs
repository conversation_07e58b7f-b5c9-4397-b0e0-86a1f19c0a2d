// using CoverGo.PoliciesV3.Application.Policy.Create;
// using CoverGo.PoliciesV3.Domain.Policy;

// namespace CoverGo.PoliciesV3.Api.Policies.Create;

// [ExtendObjectType(typeof(Mutation))]
// public class CreatePolicyMutation
// {
//     [GraphQLDescription("Create a new policy")]
//     public async Task<CreatePolicyResponse> CreatePolicy(
//         [GraphQLDescription("Policy creation details")] CreatePolicyInput input,
//         [Service] CreatePolicyHandler handler,
//         CancellationToken cancellationToken)
//     {
//         var request = new CreatePolicyRequest
//         {
//             StartDate = input.StartDate,
//             EndDate = input.EndDate,
//             ProductId = input.ProductId != null ? new ProductId(input.ProductId.Plan, input.ProductId.Type, input.ProductId.Version) : null,
//             Fields = input.Fields != null ? FieldValuePair.ToPolicyFields(input.Fields) : []
//         };

//         return await handler.Handle(request, cancellationToken);
//     }
// }
