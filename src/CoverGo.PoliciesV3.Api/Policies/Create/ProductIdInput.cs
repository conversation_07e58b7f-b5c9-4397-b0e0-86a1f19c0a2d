// namespace CoverGo.PoliciesV3.Api.Policies.Create;

// [GraphQLDescription("Input for product information")]
// public class ProductIdInput
// {
//     [GraphQLDescription("Product plan name")]
//     public required string Plan { get; set; }

//     [GraphQLDescription("Product type")]
//     public required string Type { get; set; }

//     [GraphQLDescription("Product version")]
//     public required string Version { get; set; }
// }
