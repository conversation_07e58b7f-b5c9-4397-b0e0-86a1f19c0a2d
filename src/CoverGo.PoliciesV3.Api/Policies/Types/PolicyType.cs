using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Api.Policies.Types;

public class PolicyType : ObjectType<Policy>
{
    protected override void Configure(IObjectTypeDescriptor<Policy> descriptor)
    {
        descriptor.Ignore(x => x.PolicyMembers); // TODO: Remove later
        descriptor.Ignore(x => x.DomainEvents);
        descriptor.Ignore(x => x.RowVersion);

        descriptor.Field(x => x.EntityAuditInfo).Name("auditInfo");
    }
}