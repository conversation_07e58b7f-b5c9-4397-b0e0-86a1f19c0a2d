//using CoverGo.PoliciesV3.Application.Common.CQS;
//using CoverGo.PoliciesV3.Application.Policy.CreatePolicy;
//using HotChocolate.Authorization;

//namespace CoverGo.PoliciesV3.Api.Policies;

//[MutationType]
//public static class PolicyMutations
//{
//    [Authorize]
//    public static async Task<CreatePolicyResult> CreatePolicy(
//        [Service] ICommandHandler<CreatePolicyCommand, CreatePolicyResult> handler,
//        CreatePolicyCommand input,
//        CancellationToken cancellationToken
//        ) => await handler.Handle(input, cancellationToken);
//}
