//using CoverGo.PoliciesV3.Domain.Policy;
//using CoverGo.PoliciesV3.Infrastructure.DataAccess;
//using HotChocolate.Authorization;
//using Microsoft.EntityFrameworkCore;

//namespace CoverGo.PoliciesV3.Api.Policies;

//[QueryType]
//public static class PolicyQueries
//{
//    [Authorize]
//    public static async Task<Policy> GetPolicy(
//        [Service] ApplicationDbContext ctx,
//        PolicyId id,
//        CancellationToken cancellationToken) => await ctx.Policies
//            .FirstAsync(x => x.Id == id, cancellationToken);
//}