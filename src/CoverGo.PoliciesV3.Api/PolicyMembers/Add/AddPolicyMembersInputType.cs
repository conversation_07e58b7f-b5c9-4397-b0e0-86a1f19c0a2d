using CoverGo.PoliciesV3.Application.PolicyMembers.CreatePolicyMembers;

namespace CoverGo.PoliciesV3.Api.PolicyMembers.Add;

public class AddPolicyMembersInputType : InputObjectType<CreatePolicyMembersCommand>
{
    protected override void Configure(IInputObjectTypeDescriptor<CreatePolicyMembersCommand> descriptor)
    {
        descriptor.Name("policies_AddPolicyMembersInput");
        
        descriptor.Field(x => x.PolicyId)
            .Type<NonNullType<StringType>>()
            .Description("ID of the policy");
            
        descriptor.Field(x => x.PolicyMembers)
            .Type<NonNullType<ListType<NonNullType<PolicyMemberToAddInputType>>>>()
            .Description("List of policy members to add");
            
        descriptor.Field(x => x.SkipMemberValidation)
            .Type<BooleanType>()
            .Description("Whether to skip member validation");
    }
}

public class PolicyMemberToAddInputType : InputObjectType<PolicyMemberToCreate>
{
    protected override void Configure(IInputObjectTypeDescriptor<PolicyMemberToCreate> descriptor)
    {
        descriptor.Name("policies_PolicyMemberToAddInput");
        
        descriptor.Field(x => x.MemberId)
            .Type<StringType>()
            .Description("Member ID (optional, will be generated if not provided)");
            
        descriptor.Field(x => x.PlanId)
            .Type<NonNullType<StringType>>()
            .Description("Plan ID for the policy member");
            
        descriptor.Field(x => x.Class)
            .Type<StringType>()
            .Description("Class for the policy member");
            
        descriptor.Field(x => x.Fields)
            .Type<AnyType>()
            .Description("Additional fields for the policy member as a JSON object");
            
        descriptor.Field(x => x.DependentOf)
            .Type<StringType>()
            .Description("ID of the dependent policy member (if applicable)");
    }
} 