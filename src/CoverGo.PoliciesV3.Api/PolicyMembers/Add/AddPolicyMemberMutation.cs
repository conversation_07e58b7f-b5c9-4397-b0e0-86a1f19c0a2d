using System.Security.Claims;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.PoliciesV3.Application.Common.Constants;
using CoverGo.PoliciesV3.Application.PolicyMembers.CreatePolicyMember;
using CoverGo.PoliciesV3.Application.PolicyMembers.GetPolicyMemberById;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.PoliciesV3.Api.PolicyMembers.Add;

[MutationType]
public static class AddPolicyMemberMutation
{
    [Authorize]
    [GraphQLDescription("Add a policy member as per the existing schema on old policies service")]
    [UseMutationConvention(PayloadFieldName = "result")]
    //TODO: Manage these error types from existing validation in old service
    //[Error(typeof(MemberNotFoundException))]
    //[Error(typeof(PolicyMemberExistsException))]
    //[Error(typeof(ProductPlanNotFoundException))]
    //[Error(typeof(ProductNotFoundException))]
    //[Error(typeof(MemberNotInContractHolderException))]
    ////[Error(typeof(FieldInvalidOptionException))]
    ////[Error(typeof(FieldNoExtraAllowedException))]
    ////[Error(typeof(FieldInvalidTypeException))]
    ////[Error(typeof(FieldInvalidDateException))]
    ////[Error(typeof(FieldMinAgeException))]
    ////[Error(typeof(FieldInvalidNumberException))]
    ////[Error(typeof(FieldRequiredException))]
    ////[Error(typeof(FieldOneOfException))]
    //[Error(typeof(FieldUniqueException))]
    //[Error(typeof(PolicyMemberNotFoundException))]
    //[Error(typeof(PolicyMemberNotPrimaryException))]

    [Error(typeof(PolicyNotFoundException))]
    [Error(typeof(PolicyIssuedException))]
    [Error(typeof(PolicyContractHolderNotFoundException))]
    [Error(typeof(PolicyProductIdMissingException))]
    [Error(typeof(InvalidProductIdComponentException))]
    [Error(typeof(UploadValidationLockedException))]
    [Error(typeof(EndorsementNotFoundException))]
    [Error(typeof(EndorsementCanNotBeChangedException))]
    [Error(typeof(EffectiveDateOutsidePolicyDatesException))]
    [Error(typeof(BadSchemaConfigException))]
    [Error(typeof(ValidationException))]
    public static async Task<PolicyMemberState> AddPolicyMember(
        CreatePolicyMemberCommand input,
        [GlobalState] ClaimsIdentity identity,
        [Service] IPermissionValidator permissionValidator,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default)
    {
        await permissionValidator.AuthorizeAsync(identity, new PermissionRequest(PermissionConstants.Policies.Update, PermissionConstants.Policies.Write).WithTargetIds(input.PolicyId.ToString()));

        CreatePolicyMemberResponse response = await mediator.Send(input, cancellationToken);

        // Query back the policy member to get its current state
        var query = new GetPolicyMemberByIdQuery
        {
            PolicyMemberId = response.PolicyMemberId
        };

        GetPolicyMemberByIdResponse queryResponse = await mediator.Send(query, cancellationToken);
        // Return the current state, or throw an exception if no state exists
        if (queryResponse.CurrentState == null)
        {
            throw new InvalidOperationException($"Policy member {response.PolicyMemberId} was created but has no current state.");
        }

        return queryResponse.CurrentState;
    }
}