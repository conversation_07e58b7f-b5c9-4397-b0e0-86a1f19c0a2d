using CoverGo.PoliciesV3.Api.GraphQL.Common;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Api.PolicyMembers.Add;

public class PolicyMemberStateType : ObjectType<PolicyMemberState>
{
    protected override void Configure(IObjectTypeDescriptor<PolicyMemberState> descriptor)
    {
        descriptor.Name("policies_PolicyMemberState");
        descriptor.Ignore(x => x.RowVersion);

        descriptor.Field(x => x.Id).Type<NonNullType<IdType>>();
        descriptor.Field(x => x.PolicyMemberId).Type<NonNullType<IdType>>();
        descriptor.Field(x => x.StartDate).Type<NonNullType<DateType>>();
        descriptor.Field(x => x.EndDate).Type<NonNullType<DateType>>();
        descriptor.Field(x => x.PlanId).Type<NonNullType<StringType>>();
        descriptor.Field(x => x.Class).Type<StringType>();

        // Resolver for Fields property to transform PolicyField collection to JSON object
        descriptor.Field("fields")
            .Type<AnyType>()
            .Description("Additional fields for the policy member state as a JSON object")
            .Resolve(context =>
            {
                PolicyMemberState policyMemberState = context.Parent<PolicyMemberState>();

                // Use the helper method for consistent transformation
                return FieldMappingHelper.TransformToDictionary(policyMemberState.Fields);
            });

        //TODO: Add other fields as needed and handle fields resolved on old policies
    }
}