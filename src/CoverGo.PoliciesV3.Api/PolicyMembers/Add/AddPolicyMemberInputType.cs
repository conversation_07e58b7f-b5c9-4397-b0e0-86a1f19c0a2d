using CoverGo.PoliciesV3.Application.PolicyMembers.CreatePolicyMember;

namespace CoverGo.PoliciesV3.Api.PolicyMembers.Add;

/// <summary>
/// Custom input type for AddPolicyMember that extends CreatePolicyMemberCommand
/// This allows us to use CreatePolicyMemberCommand directly while handling Any type conversion
/// </summary>
public class AddPolicyMemberInputType : InputObjectType<CreatePolicyMemberCommand>
{
    protected override void Configure(IInputObjectTypeDescriptor<CreatePolicyMemberCommand> descriptor)
    {
        descriptor.Name("AddPolicyMemberInput");
        
        // Configure the Fields property to use Any type in GraphQL
        descriptor.Field(x => x.Fields)
            .Type<AnyType>()
            .Description("Additional fields for the policy member as a JSON object");
            
        // Add descriptions for other fields
        descriptor.Field(x => x.MemberId)
            .Description("Member ID of the policy member");
            
        descriptor.Field(x => x.PolicyId)
            .Description("ID of the policy");
            
        descriptor.Field(x => x.DependentOfId)
            .Description("ID of the dependent policy member (if applicable)");
            
        descriptor.Field(x => x.IndividualId)
            .Description("ID of the individual (if applicable)");
            
        descriptor.Field(x => x.StartDate)
            .Description("Start date for the policy member");
            
        descriptor.Field(x => x.EndDate)
            .Description("End date for the policy member");
            
        descriptor.Field(x => x.PlanId)
            .Description("Plan ID for the policy member");
    }
} 