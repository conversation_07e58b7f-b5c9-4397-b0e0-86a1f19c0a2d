using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.PoliciesV3.Application.Common.Constants;
using CoverGo.PoliciesV3.Application.PolicyMembers.CreatePolicyMembers;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using HotChocolate.Authorization;
using System.Security.Claims;
using MediatR;

namespace CoverGo.PoliciesV3.Api.PolicyMembers.Add;

[MutationType]
public static class AddPolicyMembersMutation
{
    [Authorize]
    [GraphQLDescription("Add multiple policy members to a policy")]
    [UseMutationConvention(PayloadFieldName = "result")]
    [Error(typeof(PolicyNotFoundException))]
    [Error(typeof(PolicyIssuedException))]
    [Error(typeof(PolicyContractHolderNotFoundException))]
    [Error(typeof(PolicyProductIdMissingException))]
    [Error(typeof(InvalidProductIdComponentException))]
    [Error(typeof(UploadValidationLockedException))]
    [Error(typeof(EndorsementNotFoundException))]
    [Error(typeof(EndorsementCanNotBeChangedException))]
    [Error(typeof(EffectiveDateOutsidePolicyDatesException))]
    [Error(typeof(BadSchemaConfigException))]
    [Error(typeof(ValidationException))]
    [Error(typeof(MemberNotFoundException))]
    [Error(typeof(PolicyMemberExistsException))]
    [Error(typeof(MemberNotInContractHolderException))]
    [Error(typeof(PolicyMemberNotFoundException))]
    
    // TODO: The following error types are defined in the GraphQL schema but not yet implemented in the domain layer.
    // They need to be created as domain exceptions and added to the [Error] attributes above:
    // - ProductNotFoundError (needs domain implementation)
    // - PolicyMemberNotPrimaryError (needs domain implementation)
    // - FieldInvalidOptionError (needs domain implementation)
    // - FieldNoExtraAllowedError (needs domain implementation)
    // - FieldInvalidTypeError (needs domain implementation)
    // - FieldInvalidDateError (needs domain implementation)
    // - FieldMinAgeError (needs domain implementation)
    // - FieldInvalidNumberError (needs domain implementation)
    // - FieldRequiredError (needs domain implementation)
    // - FieldUniqueError (needs domain implementation)
    // - FieldOneOfError (needs domain implementation)
    // - MemberNotFoundError (needs domain implementation)
    // - MemberNotInContractHolderError (needs domain implementation)
    public static async Task<CreatePolicyMembersResponse> AddPolicyMembers(
        CreatePolicyMembersCommand input,
        [GlobalState] ClaimsIdentity identity,
        [Service] IPermissionValidator permissionValidator,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default)
    {
        await permissionValidator.AuthorizeAsync(identity, new PermissionRequest(PermissionConstants.Policies.Update, PermissionConstants.Policies.Write).WithTargetIds(input.PolicyId));

        return await mediator.Send(input, cancellationToken);
    }
}