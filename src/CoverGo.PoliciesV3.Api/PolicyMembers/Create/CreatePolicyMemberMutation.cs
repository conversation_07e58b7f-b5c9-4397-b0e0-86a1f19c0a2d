// using CoverGo.PoliciesV3.Application.PolicyMember.Create;

// namespace CoverGo.PoliciesV3.Api.PoliciesMember.Create;

// [ExtendObjectType(typeof(Mutation))]
// public class CreatePolicyMemberMutation
// {
//     [GraphQLDescription("Create a new policy member")]
//     public async Task<CreatePolicyMemberResponse> CreatePolicyMember(
//         [GraphQLDescription("Policy member creation details")] CreatePolicyMemberInput input,
//         [Service] CreatePolicyMemberHandler handler,
//         CancellationToken cancellationToken)
//     {
//         var request = new CreatePolicyMemberRequest
//         {
//             MemberId = input.MemberId,
//             PolicyId = input.PolicyId,
//             DependentOfId = input.DependentOfId,
//             IndividualId = input.IndividualId,
//             PlanId = input.PlanId,
//             StartDate = input.StartDate,
//             EndDate = input.EndDate,
//             Fields = input.Fields != null ? FieldValuePair.ToPolicyFields(input.Fields) : []
//         };

//         return await handler.Handle(request, cancellationToken);
//     }
// }
