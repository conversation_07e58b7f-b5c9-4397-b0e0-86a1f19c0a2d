// namespace CoverGo.PoliciesV3.Api.PoliciesMember.Create;

// [GraphQLDescription("Input for creating a policy member")]
// public class CreatePolicyMemberInput
// {
//     [GraphQLDescription("Member ID of the policy member")]
//     public required string MemberId { get; set; }

//     [GraphQLDescription("ID of the policy")]
//     public required Guid PolicyId { get; set; }

//     [GraphQLDescription("ID of the dependent policy member, if applicable")]
//     public Guid? DependentOfId { get; set; }

//     [GraphQLDescription("ID of the individual, if applicable")]
//     public Guid? IndividualId { get; set; }

//     [GraphQLDescription("Start date of the policy member coverage")]
//     public DateOnly? StartDate { get; set; }

//     [GraphQLDescription("End date of the policy member coverage")]
//     public DateOnly? EndDate { get; set; }

//     [GraphQLDescription("Plan ID for the policy member")]
//     public required string PlanId { get; set; }

//     [GraphQLDescription("Additional fields for the policy member")]
//     public List<FieldValuePair>? Fields { get; set; }
// }