using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Api.PolicyMembers.Types;

public class PolicyMemberType : ObjectType<PolicyMember>
{
    protected override void Configure(IObjectTypeDescriptor<PolicyMember> descriptor)
    {
        descriptor.Ignore(x => x.DomainEvents);
        descriptor.Ignore(x => x.RowVersion);

        descriptor.Field(x => x.EntityAuditInfo).Name("auditInfo");
    }
}