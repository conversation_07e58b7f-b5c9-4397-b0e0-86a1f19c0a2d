// using CoverGo.PoliciesV3.Application.PolicyMember.GetPaged;

// namespace CoverGo.PoliciesV3.Api.PoliciesMember.GetPaged;

// [ExtendObjectType(typeof(Query))]
// public class GetPagedPolicyMembersQuery
// {
//     [GraphQLName("policyMembers")]
//     [GraphQLDescription("Get policy members with pagination and filtering options")]
//     public async Task<GetPagedPolicyMembersResponse> GetPagedPolicyMembers(
//         [GraphQLDescription("Query parameters for paged policy members")] GetPagedPolicyMembersInput input,
//         [Service] GetPagedPolicyMembersHandler handler,
//         CancellationToken cancellationToken)
//     {
//         var request = new GetPagedPolicyMembersRequest
//         {
//             PolicyId = input.PolicyId,
//             MemberId = input.MemberId,
//             StartDateFrom = input.StartDateFrom,
//             StartDateTo = input.StartDateTo,
//             EndDateFrom = input.EndDateFrom,
//             EndDateTo = input.EndDateTo,
//             Page = input.Page,
//             PageSize = input.PageSize,
//             SortBy = input.SortBy,
//             SortDirection = input.SortDirection
//         };

//         return await handler.Handle(request, cancellationToken);
//     }
// }