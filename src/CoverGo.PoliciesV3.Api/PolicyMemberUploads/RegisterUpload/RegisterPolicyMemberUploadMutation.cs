using System.Security.Claims;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.PoliciesV3.Application.Common.Constants;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.RegisterPolicyMemberUpload;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.PoliciesV3.Api.PolicyMemberUploads.RegisterUpload;

[MutationType]
public static class RegisterPolicyMemberUploadMutation
{
    [Authorize]
    [GraphQLDescription("Register a policy member upload")]
    [UseMutationConvention(PayloadFieldName = "result")]
    [Error(typeof(PolicyNotFoundException))]
    [Error(typeof(PolicyIssuedException))]
    [Error(typeof(PolicyContractHolderNotFoundException))]
    [Error(typeof(PolicyProductIdMissingException))]
    [Error(typeof(InvalidProductIdComponentException))]
    [Error(typeof(UploadFileNotFoundException))]
    [Error(typeof(BadFileContentException))]
    [Error(typeof(BadSchemaConfigException))]
    [Error(typeof(EndorsementNotFoundException))]
    public static async Task<PolicyMemberUpload> RegisterPolicyMemberUpload(
        RegisterPolicyMemberUploadInput input,
        [GlobalState] ClaimsIdentity identity,
        [Service] IPermissionValidator permissionValidator,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default)
    {
        await permissionValidator.AuthorizeAsync(identity, new PermissionRequest(PermissionConstants.Policies.Update, PermissionConstants.Policies.Write).WithTargetIds(input.PolicyId));
        var command = new RegisterPolicyMemberUploadCommand
        {
            PolicyId = input.PolicyId,
            EndorsementId = !string.IsNullOrEmpty(input.EndorsementId) ? (EndorsementId)input.EndorsementId : null,
            Path = input.Path
        };

        RegisterPolicyMemberUploadResponse response = await mediator.Send(command, cancellationToken);
        return response.PolicyMemberUpload;
    }
}