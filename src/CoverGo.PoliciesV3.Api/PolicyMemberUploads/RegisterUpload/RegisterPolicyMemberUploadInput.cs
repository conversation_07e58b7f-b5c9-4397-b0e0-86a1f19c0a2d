namespace CoverGo.PoliciesV3.Api.PolicyMemberUploads.RegisterUpload;

/// <summary>
/// Input type for registering a policy member upload
/// This matches the schema expected by the Policies service for proper routing
/// </summary>
public class RegisterPolicyMemberUploadInput
{
    /// <summary>
    /// The ID of the policy to upload members for
    /// </summary>
    public string PolicyId { get; set; } = string.Empty;

    /// <summary>
    /// Optional endorsement ID
    /// </summary>
    public string? EndorsementId { get; set; }

    /// <summary>
    /// Path to the uploaded file
    /// </summary>
    public string Path { get; set; } = string.Empty;
} 