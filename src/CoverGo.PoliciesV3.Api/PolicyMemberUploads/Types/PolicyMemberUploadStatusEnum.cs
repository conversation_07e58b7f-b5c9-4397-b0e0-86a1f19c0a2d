namespace CoverGo.PoliciesV3.Api.PolicyMemberUploads.Types;

/// <summary>
/// Simple GraphQL enum for PolicyMemberUploadStatus
/// This mirrors the domain value object but as a proper GraphQL enum
/// </summary>
public enum PolicyMemberUploadStatusEnum
{
    REGISTERED,
    <PERSON><PERSON><PERSON><PERSON>ING,
    VAL<PERSON>ATING_ERROR,
    VA<PERSON><PERSON><PERSON><PERSON>,
    IMPORTING,
    IMPORTING_ERROR,
    IMPORTED,
    REVERTING,
    REVERTED,
    CANCELING,
    CANCELED,
    FAILED
}
