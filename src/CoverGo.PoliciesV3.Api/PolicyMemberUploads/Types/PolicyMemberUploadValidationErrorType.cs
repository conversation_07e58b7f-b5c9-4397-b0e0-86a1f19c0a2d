using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Api.PolicyMemberUploads.Types;

public class PolicyMemberUploadValidationErrorType : ObjectType<PolicyMemberUploadValidationError>
{
    protected override void Configure(IObjectTypeDescriptor<PolicyMemberUploadValidationError> descriptor)
    {
        descriptor.Name("policies_PolicyMemberUploadValidationError");

        descriptor.Field(x => x.RowIndex)
            .Type<NonNullType<IntType>>();

        descriptor.Field(x => x.Code)
            .Type<NonNullType<StringType>>();

        descriptor.Field(x => x.Message)
            .Type<NonNullType<StringType>>();

        // Hide internal fields from GraphQL
        descriptor.Ignore(x => x.Id);
        descriptor.Ignore(x => x.PolicyMemberUploadId);
        descriptor.Ignore(x => x.PolicyMemberUpload);
        descriptor.Ignore(x => x.DomainEvents);
        descriptor.Ignore(x => x.EntityAuditInfo);
    }
} 