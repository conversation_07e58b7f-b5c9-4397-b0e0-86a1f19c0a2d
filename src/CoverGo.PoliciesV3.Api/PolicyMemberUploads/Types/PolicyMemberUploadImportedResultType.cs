using System.Text.Json;
using CoverGo.PoliciesV3.Api.GraphQL.Common.Types;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Api.PolicyMemberUploads.Types;

public class PolicyMemberUploadImportedResultType : ObjectType<PolicyMemberUploadImportedResult>
{
    protected override void Configure(IObjectTypeDescriptor<PolicyMemberUploadImportedResult> descriptor)
    {
        descriptor.Name("policies_PolicyMemberUploadImportedResult");

        descriptor.Field(x => x.RowIndex)
            .Type<NonNullType<IntType>>();

        descriptor.Field(x => x.Success)
            .Type<NonNullType<BooleanType>>();

        descriptor.Field(x => x.PolicyMemberId)
            .Type<StringType>();

        descriptor.Field("importingError")
            .Type<UserError>()
            .Resolve(context =>
            {
                PolicyMemberUploadImportedResult parent = context.Parent<PolicyMemberUploadImportedResult>();

                // Only return error if import was not successful and we have error JSON
                if (parent.Success || string.IsNullOrEmpty(parent.ImportingErrorJson))
                    return null;

                try
                {
                    JsonElement errorData = JsonSerializer.Deserialize<JsonElement>(parent.ImportingErrorJson);

                    string code = errorData.TryGetProperty("Code", out JsonElement codeElement)
                        ? codeElement.GetString() ?? "UNKNOWN_ERROR"
                        : "UNKNOWN_ERROR";

                    string message = errorData.TryGetProperty("Message", out JsonElement messageElement)
                        ? messageElement.GetString() ?? "An unknown error occurred"
                        : "An unknown error occurred";

                    return new { Code = code, Message = message };
                }
                catch (JsonException)
                {
                    // Fallback if JSON parsing fails
                    return new { Code = "PARSE_ERROR", Message = "Failed to parse error information" };
                }
            });

        // Hide internal fields from GraphQL
        descriptor.Ignore(x => x.Id);
        descriptor.Ignore(x => x.PolicyMemberUploadId);
        descriptor.Ignore(x => x.PolicyMemberUpload);
        descriptor.Ignore(x => x.ImportingErrorJson);
    }
}