using System.Collections.Concurrent;
using System.Linq.Expressions;
using System.Reflection;
using HotChocolate.Execution.Configuration;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Api.PolicyMemberUploads.Types;

namespace CoverGo.PoliciesV3.Api.PolicyMemberUploads.Services;

public static class PolicyMemberUploadEnumRegistrationService
{
    private static readonly ConcurrentDictionary<Type, Delegate> ToEnumConverters = new();
    private static readonly ConcurrentDictionary<Type, Delegate> FromEnumConverters = new();

    public static IRequestExecutorBuilder AddPolicyMemberUploadEnums(this IRequestExecutorBuilder builder)
    {
        builder.AddDomainEnum<PolicyMemberUploadStatus, PolicyMemberUploadStatusEnum>();

        return builder;
    }


    public static IRequestExecutorBuilder AddDomainEnum<TDomain, TGraphQL>(this IRequestExecutorBuilder builder)
        where TDomain : class
        where TGraphQL : struct, Enum
    {
        builder.BindRuntimeType<TDomain, EnumType<TGraphQL>>();

        Func<TDomain, TGraphQL> toEnumConverter = GetOrCreateToEnumConverter<TDomain, TGraphQL>();
        Func<TGraphQL, TDomain> fromEnumConverter = GetOrCreateFromEnumConverter<TDomain, TGraphQL>();

        builder.AddTypeConverter<TDomain, TGraphQL>(domain => toEnumConverter(domain));
        builder.AddTypeConverter<TGraphQL, TDomain>(graphqlEnum => fromEnumConverter(graphqlEnum));

        return builder;
    }

    private static Func<TDomain, TGraphQL> GetOrCreateToEnumConverter<TDomain, TGraphQL>()
        where TDomain : class
        where TGraphQL : struct, Enum
    {
        return (Func<TDomain, TGraphQL>)ToEnumConverters.GetOrAdd(typeof(TDomain), _ =>
        {
            ParameterExpression domainParam = Expression.Parameter(typeof(TDomain), "domain");
            PropertyInfo valueProperty = typeof(TDomain).GetProperty("Value")
                ?? throw new InvalidOperationException($"Type {typeof(TDomain).Name} must have a Value property");

            MemberExpression valueAccess = Expression.Property(domainParam, valueProperty);
            MethodInfo parseMethod = typeof(Enum).GetMethod("Parse", [typeof(Type), typeof(string)])!;
            ConstantExpression enumType = Expression.Constant(typeof(TGraphQL));

            MethodCallExpression parseCall = Expression.Call(parseMethod, enumType, valueAccess);
            UnaryExpression castResult = Expression.Convert(parseCall, typeof(TGraphQL));

            return Expression.Lambda<Func<TDomain, TGraphQL>>(castResult, domainParam).Compile();
        });
    }

    private static Func<TGraphQL, TDomain> GetOrCreateFromEnumConverter<TDomain, TGraphQL>()
        where TDomain : class
        where TGraphQL : struct, Enum
    {
        return (Func<TGraphQL, TDomain>)FromEnumConverters.GetOrAdd(typeof(TDomain), _ =>
        {
            ParameterExpression enumParam = Expression.Parameter(typeof(TGraphQL), "graphqlEnum");
            MethodInfo toStringMethod = typeof(TGraphQL).GetMethod("ToString", Type.EmptyTypes)!;
            MethodCallExpression toStringCall = Expression.Call(enumParam, toStringMethod);

            ConstructorInfo constructor = typeof(TDomain).GetConstructor([typeof(string)])
                ?? throw new InvalidOperationException($"Type {typeof(TDomain).Name} must have a constructor that accepts a string parameter");

            NewExpression constructorCall = Expression.New(constructor, toStringCall);

            return Expression.Lambda<Func<TGraphQL, TDomain>>(constructorCall, enumParam).Compile();
        });
    }
}
