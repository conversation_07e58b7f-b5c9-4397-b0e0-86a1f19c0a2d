using System.Security.Claims;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.PoliciesV3.Application.Common.Constants;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.CancelUpload;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.PoliciesV3.Api.PolicyMemberUploads.CancelUpload;

[MutationType]
public static class CancelPolicyMemberUploadMutation
{
    [Authorize]
    [GraphQLDescription("Cancel a policy member upload")]
    [UseMutationConvention(PayloadFieldName = "result")]
    [Error(typeof(PolicyNotFoundException))]
    [Error(typeof(PolicyIssuedException))]
    [Error(typeof(PolicyContractHolderNotFoundException))]
    [Error(typeof(EndorsementNotFoundException))]
    [Error(typeof(EndorsementCanNotBeChangedException))]
    [Error(typeof(EffectiveDateOutsidePolicyDatesException))]
    [Error(typeof(PolicyMemberUploadNotFoundException))]
    [Error(typeof(InvalidPolicyMemberUploadStatusException))]
    public static async Task<PolicyMemberUpload> CancelPolicyMemberUpload(
        string policyId,
        string uploadId,
        [GlobalState] ClaimsIdentity identity,
        [Service] IPermissionValidator permissionValidator,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default)
    {
        await permissionValidator.AuthorizeAsync(identity, new PermissionRequest(PermissionConstants.Policies.Update, PermissionConstants.Policies.Write).WithTargetIds(policyId));

        var command = new CancelPolicyMemberUploadCommand
        {
            PolicyId = policyId,
            UploadId = uploadId
        };

        Domain.Common.Validation.Result<CancelPolicyMemberUploadResponse> result = await mediator.Send(command, cancellationToken);

        return result.IsFailure
            ? throw new ValidationException([.. result.Errors])
            : result.Value.PolicyMemberUpload;
    }
}