using System.Security.Claims;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.PoliciesV3.Application.Common.Constants;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.PoliciesV3.Api.PolicyMemberUploads.ValidateUpload;

[MutationType]
public static class ValidatePolicyMemberUploadMutation
{
    [Authorize]
    [GraphQLDescription("Validate a policy member upload")]
    [UseMutationConvention(PayloadFieldName = "result")]
    [Error(typeof(PolicyNotFoundException))]
    [Error(typeof(PolicyIssuedException))]
    [Error(typeof(PolicyContractHolderNotFoundException))]
    [Error(typeof(PolicyProductIdMissingException))]
    [Error(typeof(InvalidProductIdComponentException))]
    [Error(typeof(PolicyMemberUploadNotFoundException))]
    [Error(typeof(InvalidPolicyMemberUploadStatusException))]
    [Error(typeof(UploadValidationLockedException))]
    [Error(typeof(EndorsementNotFoundException))]
    [Error(typeof(EndorsementCanNotBeChangedException))]
    [Error(typeof(EffectiveDateOutsidePolicyDatesException))]
    [Error(typeof(BadSchemaConfigException))]
    [Error(typeof(ValidationException))]
    public static async Task<PolicyMemberUpload> ValidatePolicyMemberUpload(
        string policyId,
        string uploadId,
        [GlobalState] ClaimsIdentity identity,
        [Service] IPermissionValidator permissionValidator,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default)
    {
        await permissionValidator.AuthorizeAsync(identity, new PermissionRequest(PermissionConstants.Policies.Update, PermissionConstants.Policies.Write).WithTargetIds(policyId));

        var command = new ValidatePolicyMemberUploadCommand
        {
            PolicyId = policyId,
            UploadId = uploadId
        };

        Domain.Common.Validation.Result<ValidatePolicyMemberUploadResponse> result = await mediator.Send(command, cancellationToken);

        return result.IsFailure
            ? throw new ValidationException([.. result.Errors])
            : result.Value.PolicyMemberUpload;
    }
}