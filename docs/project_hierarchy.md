# CoverGo Policies V3 - Project Hierarchy Structure

## Overview

This document outlines the complete hierarchy structure of the CoverGo Policies V3 service, which follows Clean Architecture principles with .NET 9.0 and GraphQL implementation.

## Root Structure

```
src/
├── CoverGo.PoliciesV3.Api/                  # 🌐 API Layer - GraphQL & Web API
├── CoverGo.PoliciesV3.Application/          # 📋 Application Layer - Use Cases & Commands/Queries
├── CoverGo.PoliciesV3.Domain/               # 🏛️ Domain Layer - Business Logic & Entities
└── CoverGo.PoliciesV3.Infrastructure/       # 🔧 Infrastructure Layer - Data Access & External Services
```

## Architecture Layers Detail

### 🌐 API Layer (`CoverGo.PoliciesV3.Api`)

```
CoverGo.PoliciesV3.Api/
├── Program.cs                                # Application entry point
├── CoverGo.PoliciesV3.Api.csproj           # Project file
├── CoverGo.PoliciesV3.Api.http             # HTTP test requests
├── Properties/                              # Assembly info
│   └── ModuleInfo.cs
├── appsettings*.json                        # Configuration files (6 environments)
├── GraphQL/                                 # GraphQL implementation
│   ├── Common/                              # Shared GraphQL components
│   │   ├── FieldValuePairInput.cs
│   │   ├── OperationTypeNames.cs
│   │   └── Types/UserError.cs
│   ├── Conventions/                         # GraphQL conventions & auto-registration
│   │   ├── ErrorTypeNamingInterceptor.cs
│   │   ├── GraphQLNamingOptions.cs
│   │   ├── GraphQLTypeAutoRegistration.cs
│   │   └── PoliciesNamingConventions.cs
│   ├── Debug/                               # Debugging queries
│   │   └── DebugQueries.cs
│   ├── GraphQLExtensions.cs                 # Service registration
│   ├── Mutation.cs                          # Root mutation type
│   ├── Query.cs                             # Root query type
│   ├── Policies/                            # Policy operations
│   │   ├── Create/
│   │   ├── GetPaged/
│   │   ├── PolicyMutations.cs
│   │   ├── PolicyQueries.cs
│   │   └── Types/
│   ├── PolicyMemberUploads/                 # Member Upload operations
│   │   └── Types/
│   └── PolicyMembers/                       # Member operations
│       ├── CancelUpload/
│       ├── Create/
│       ├── GetPaged/
│       ├── RegisterUpload/
│       ├── ValidateUpload/
│       └── Types/
└── REST/                                    # REST API implementation
    └── PolicyMemberUploads/
        └── PolicyMemberUploadsController.cs
```

### 📋 Application Layer (`CoverGo.PoliciesV3.Application`)

```
CoverGo.PoliciesV3.Application/
├── ApplicationRegister.cs                   # DI registration
├── CoverGo.PoliciesV3.Application.csproj
├── Common/                                  # Shared application components
│   ├── CQS/                                 # CQRS interfaces
│   │   ├── ICommand.cs
│   │   └── IQuery.cs
│   ├── Constants/
│   │   └── PermissionConstants.cs
│   ├── DTOs/                                # Data transfer objects
│   │   └── PolicyMembers/
│   ├── Interfaces/                          # Application interfaces
│   │   ├── IFileParser.cs
│   │   ├── IFileParserFactory.cs
│   │   ├── IPolicyMemberFieldsSchemaProvider.cs
│   │   └── IPolicyMemberFieldsSchemaRepository.cs
│   ├── IPaginatedRepository.cs              # Pagination interface
│   └── PageResult.cs                        # Generic pagination result
├── Features/                                # Feature-based organization (CQRS)
│   ├── Policies/
│   │   ├── CreatePolicy/
│   │   └── GetPagedPolicies/
│   └── PolicyMembers/
│       ├── CancelPolicyMemberUpload/
│       ├── CreatePolicyMember/
│       ├── GetPagedPolicyMembers/
│       ├── RegisterPolicyMemberUpload/
│       ├── ValidatePolicyMemberUpload/
│       └── IPolicyMemberUploadRepository.cs
├── Mapping/
│   └── LegacyPolicyMapping/
│       └── LegacyPolicyProfile.cs
└── Services/                                # Application services interfaces
    ├── ICasesService.cs
    ├── IFilesystemService.cs
    ├── ILegacyPolicyService.cs
    ├── IPolicyMemberUploadValidationErrorExporterService.cs
    ├── IProductService.cs
    ├── IUsersService.cs
    ├── MemberDataResults.cs
    └── PolicyMemberValidationDataService.cs
```

### 🏛️ Domain Layer (`CoverGo.PoliciesV3.Domain`)

```
CoverGo.PoliciesV3.Domain/
├── CoverGo.PoliciesV3.Domain.csproj
├── Common/                                  # Domain shared kernel
│   ├── Caching/
│   │   └── ICacheKey.cs
│   ├── Constants/
│   │   └── ValidationConstants.cs
│   ├── DomainEvent.cs
│   ├── DomainException.cs
│   ├── Enums/
│   ├── Extensions/
│   ├── Specifications/                      # Core Specification Pattern
│   │   ├── CompositeSpecification.cs
│   │   ├── IAsyncSpecification.cs
│   │   ├── ISpecification.cs
│   │   └── SpecificationResult.cs
│   ├── Unit.cs
│   ├── ValueObject.cs
│   └── Validation/                          # Domain validation framework
│       ├── ErrorCodes.cs
│       ├── Errors.cs
│       ├── Result.cs
│       ├── ValidationError.cs
│       ├── ValidationErrorBuilder.cs
│       └── ValidationException.cs
├── CustomFields/                            # Dynamic field system
│   ├── CustomFieldCondition.cs
│   ├── CustomFieldDefinition.cs
│   ├── IdentificationIdField.cs
│   ├── PolicyMemberFieldDefinition.cs
│   ├── PolicyMemberFieldsSchema.cs
│   ├── Extensions/
│   ├── FieldTypes/
│   ├── Services/
│   └── Validation/
├── Endorsements/                            # Endorsement aggregate
│   ├── Endorsement.cs
│   ├── EndorsementDto.cs
│   ├── EndorsementId.cs
│   ├── EndorsementStatus.cs
│   └── Exceptions/
├── Policies/                                # Policy aggregate
│   ├── Policy.cs                            # Main aggregate root
│   ├── PolicyDto.cs
│   ├── PolicyField.cs
│   ├── PolicyId.cs
│   ├── PolicyStatus.cs
│   ├── ProductId.cs
│   ├── Events/
│   └── Exceptions/
├── PolicyMembers/                           # Policy member aggregate
│   ├── PolicyMember.cs                      # Aggregate root
│   ├── PolicyMemberId.cs
│   ├── IPolicyMemberDataRepository.cs       # Repository interface
│   ├── Builders/
│   ├── Enums/
│   ├── Events/
│   ├── Exceptions/
│   └── Specifications/                      # Advanced validation specifications
│       ├── Composite/
│       ├── Contexts/
│       └── Individual/
├── PolicyMemberUploads/                     # Upload aggregate
│   ├── PolicyMemberUpload.cs                # Upload aggregate root
│   ├── PolicyMemberUploadId.cs
│   ├── PolicyMemberUploadStatus.cs
│   ├── PolicyMemberUploadImportedResult.cs
│   ├── PolicyMemberUploadValidationError.cs
│   ├── PolicyMemberUploadValidationErrorId.cs
│   ├── MembersUploadFields.cs
│   ├── MemberUploadFields.cs
│   ├── PolicyMemberUploadWellKnowFields.cs
│   ├── Events/
│   ├── Exceptions/
│   └── Specifications/
├── Products/
│   └── Exceptions/
├── Services/                                # Domain services interfaces
│   ├── IFileProcessingService.cs
│   ├── IPolicyMemberFieldsSchemaProvider.cs
│   ├── IPolicyMemberQueryService.cs
│   └── IPolicyMemberUniquenessService.cs
└── ValueObjects/                            # Domain value objects
```

### 🔧 Infrastructure Layer (`CoverGo.PoliciesV3.Infrastructure`)

```
CoverGo.PoliciesV3.Infrastructure/
├── InfrastructureRegister.cs               # DI registration
├── CoverGo.PoliciesV3.Infrastructure.csproj
├── Caching/
│   ├── Dtos/
│   └── Extensions/
├── Common/
│   ├── Caching/
│   ├── Constants/
│   ├── Extensions/
│   ├── Helpers/
│   └── Resilience/
├── CustomFields/
│   ├── Extensions/
│   ├── Providers/
│   ├── Serialization/
│   └── Services/
├── DataAccess/                              # Entity Framework implementation
│   ├── ApplicationDbContext.cs              # Main EF DbContext
│   ├── Configurations/
│   ├── Extensions/
│   └── PostgreSql/
├── Extensions/
├── ExternalServices/
├── FeatureManagement/
├── FileParser/
├── FileProcessing/
│   ├── Parsers/
│   └── Services/
├── Jobs/
│   └── Auth/
├── PolicyMembers/
│   └── Services/
├── Providers/
│   └── PolicyMemberFieldsSchemaProvider.cs
├── Repositories/                            # Repository implementations
│   ├── PolicyMemberDataRepository.cs
│   ├── PolicyMemberFieldsSchemaRepository.cs
│   └── PolicyMemberUploadRepository.cs
├── Schemas/                                 # External service schemas
│   ├── CaseSchemas.cs
│   ├── ContractHolderSchemas.cs
│   └── ProductSchemas.cs
└── Services/                                # Infrastructure service implementations
    ├── CachedCasesService.cs
    ├── CachedPolicyMemberFieldsSchemaProvider.cs
    ├── CasesService.cs
    ├── FileProcessingService.cs
    ├── FilesystemService.cs
    ├── LegacyPolicyService.cs
    ├── PolicyMemberQueryService.cs
    ├── PolicyMemberUniquenessService.cs
    ├── PolicyMemberUploadValidationErrorExporterService.cs
    ├── ProductService.cs
    └── UsersService.cs
```

### 🧪 Test Layer (`tests/`)

```
tests/
├── Directory.Build.props                    # Test-specific build configuration
├── CoverGo.PoliciesV3.Tests.Integration/   # Integration tests
└── CoverGo.PoliciesV3.Tests.Unit/          # Unit tests
```

## Architecture Layers

### 1. API Layer (`CoverGo.PoliciesV3.Api`)

- **Purpose**: Web API and GraphQL endpoint exposure
- **Key Components**:
  - GraphQL schema definitions and resolvers
  - HTTP request/response handling
  - Authentication and authorization
  - Configuration management
  - Dependency injection setup

### 2. Application Layer (`CoverGo.PoliciesV3.Application`)

- **Purpose**: Business logic and use cases
- **Expected Components** (referenced in dependencies):
  - Command and query handlers (CQRS pattern)
  - Business rules and validation
  - Application services
  - DTOs and mapping profiles

### 3. Domain Layer (`CoverGo.PoliciesV3.Domain`)

- **Purpose**: Core business entities and domain logic
- **Expected Components**:
  - Domain entities (Policy, PolicyMember, etc.)
  - Value objects
  - Domain services
  - Business rules and invariants

### 4. Infrastructure Layer (`CoverGo.PoliciesV3.Infrastructure`)

- **Purpose**: Data access and external integrations
- **Expected Components**:
  - Entity Framework DbContext
  - Repository implementations
  - External service integrations
  - Data access configurations

## Key Features Identified

### Async Validation Architecture

#### Advanced Concurrent Processing

- **IConcurrentMemberProcessor**: Interface for configurable concurrent validation
- **ValidationConstants**: Centralized concurrency and timeout configuration
- **Resource Management**: SemaphoreSlim-based throttling with proper disposal
- **Work Distribution**: OrderablePartitioner for optimized batch processing

#### Comprehensive Async Testing

- **ValidatePolicyMemberUploadHandlerAsyncArchitectureTests**: Sophisticated async validation tests
- **Race Condition Prevention**: Concurrent safety verification
- **Resource Leak Testing**: Memory and disposal pattern validation
- **Cancellation Coordination**: CancellationToken propagation testing
- **Exception Handling**: Error context preservation and graceful degradation

### GraphQL Operations

#### Queries

- **GetPagedPolicies**: Retrieve policies with pagination support
- **GetPagedPolicyMembers**: Retrieve policy members with pagination

#### Mutations

- **CreatePolicy**: Create new insurance policies
- **CreatePolicyMember**: Add members to policies
- **RegisterPolicyMemberUpload**: Initialize bulk member upload process
- **ValidatePolicyMemberUpload**: Validate uploaded member data
- **CancelPolicyMemberUpload**: Cancel ongoing upload operations

### Technology Stack

#### Core Framework

- **.NET 9.0**: Latest .NET framework
- **ASP.NET Core**: Web framework
- **HotChocolate**: GraphQL server implementation

#### Data Access

- **Entity Framework Core**: Primary ORM
- **PostgreSQL**: Relational database (via Npgsql)
- **MongoDB**: Document database for specific use cases

#### Architecture Patterns

- **Clean Architecture**: Layered architecture with dependency inversion
- **CQRS**: Command Query Responsibility Segregation (via MediatR)
- **Repository Pattern**: Data access abstraction
- **Specification Pattern**: Domain business rules encapsulation
- **Async/Await**: Comprehensive async patterns with concurrent processing
- **Result Pattern**: Functional error handling throughout all layers

#### Cross-Cutting Concerns

- **AutoMapper**: Object-to-object mapping
- **FluentValidation**: Input validation
- **Serilog**: Structured logging
- **OpenTelemetry**: Observability and distributed tracing

#### Security & Authentication

- **JWT Bearer**: Token-based authentication
- **Microsoft Identity**: Authentication integration
- **Azure Key Vault**: Secret management

#### File Processing

- **NPOI**: Excel file processing for member uploads
- **SharpCompress**: File compression utilities

#### Testing & Development

- **Multiple Environment Configs**: Development, Staging, Production
- **HTTP Test Files**: API testing utilities
- **Feature Management**: Microsoft Feature Flags

## Configuration Files

| File                                 | Purpose                              |
| ------------------------------------ | ------------------------------------ |
| `appsettings.json`                   | Base application configuration       |
| `appsettings.Development.json`       | Development environment overrides    |
| `appsettings.Development.Tests.json` | Test environment configuration       |
| `appsettings.Staging.json`           | Staging environment configuration    |
| `appsettings.Staging.CI.json`        | CI/CD staging configuration          |
| `appsettings.Production.json`        | Production environment configuration |

---

_This hierarchy represents a modern, enterprise-grade .NET application following industry best practices for maintainability, scalability, and testability._

---

## 🎯 Advanced Validation Architecture Highlights

### Unified Error Handling System

- **ValidationError**: Universal error type with automatic message generation
- **Result Pattern**: Functional composition for error handling across all layers
- **ErrorCodes**: Standardized error categorization (REQUIRED, OUT_OF_RANGE, UNIQUE_VIOLATION, etc.)
- **Smart Context**: Rich error context for debugging and user feedback

### High-Performance Async Processing

- **Configurable Concurrency**: Thread-safe validation with customizable limits
- **Resource Management**: Proper disposal patterns and memory optimization
- **Batch Processing**: Optimized for large-scale member upload validation
- **Pipeline Orchestration**: Sequential and parallel validation stages

### Enterprise Testing Standards

- **Async Architecture Testing**: Comprehensive validation of concurrent patterns
- **Resource Leak Prevention**: Memory and disposal validation
- **Race Condition Testing**: Thread-safety verification
- **Performance Testing**: Scalability and timeout validation

This architecture enables reliable, high-performance validation of complex business rules while maintaining excellent developer experience and system maintainability.
