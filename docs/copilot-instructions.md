# CoverGo Policies V3 - AI Coding Instructions

## Project Overview

This is a .NET 9 microservice implementing Clean Architecture patterns for insurance policy management. It's part of CoverGo's distributed system, handling policies, policy members, and upload processing with multi-tenant PostgreSQL, Redis caching, and GraphQL APIs.

## 🎯 Mandatory Coding Guidelines

### DDD Layer Separation (CRITICAL - Never Violate)

**Domain Layer ONLY**: Business logic, domain entities, value objects, domain services, business rules, specifications
**Application Layer ONLY**: Orchestration, use cases, CQRS handlers, application services (NO business logic)
**Infrastructure Layer ONLY**: Data persistence, external service integration, repositories (data access operations ONLY)

```csharp
// ✅ CORRECT: Business logic in Domain
public class Policy // Domain Entity
{
    public Result<PolicyMember> AddMember(MemberDetails details, IBusinessRules rules)
    {
        // Business validation and logic here
    }
}

// ❌ WRONG: Business logic in Application/Infrastructure
public class CreatePolicyHandler // Application
{
    public async Task<Result> Handle(Command cmd)
    {
        // ONLY orchestration - delegate to domain
        return policy.AddMember(details, businessRules);
    }
}
```

### Type Safety & Validation Patterns

```csharp
// ✅ ALWAYS: Fail-fast validation with strongly-typed IDs
public class PolicyId : DomainId<PolicyId>
{
    private PolicyId(Guid value) : base(value) { }
    public static PolicyId New => new(Guid.NewGuid());
    public static PolicyId From(Guid value) => new(value);
}

// ✅ ALWAYS: Enforce non-null state at creation
public class CreatePolicyCommand : ICommand<Result<Policy>>
{
    public required string PolicyNumber { get; init; }
    public required ProductId ProductId { get; init; }

    // Constructor validation
    public CreatePolicyCommand()
    {
        ArgumentNullException.ThrowIfNull(PolicyNumber);
        ArgumentNullException.ThrowIfNull(ProductId);
    }
}

// ❌ FORBIDDEN: Defensive null checks when proper typing available
public Result ProcessPolicy(Policy policy)
{
    if (policy == null) return Result.Failure(...); // WRONG - use required/nullable types
}
```

### Eliminate All Magic Strings

```csharp
// ✅ ALWAYS: Use constants and strongly-typed values
public static class ConfigurationConstants
{
    public static class ConnectionStrings
    {
        public const string DefaultConnection = "DefaultConnection";
    }
}

public static class ErrorCodes
{
    public const string PolicyNotFound = "POLICY_NOT_FOUND";
    public const string InvalidStatus = "INVALID_STATUS";
}

// ❌ FORBIDDEN: Magic strings anywhere
throw new ArgumentException("Policy not found"); // WRONG
var connectionString = configuration["DefaultConnection"]; // WRONG
```

### Builder Pattern for Complex Objects

```csharp
// ✅ PREFERRED: Builder pattern for 4+ parameters
public class PolicyBuilder
{
    public PolicyBuilder WithPolicyNumber(string number) => ...;
    public PolicyBuilder WithProduct(ProductId productId) => ...;
    public PolicyBuilder WithContractHolder(string id) => ...;
    public Policy Build() => new Policy(...);
}

// ❌ FORBIDDEN: Factory methods with excessive parameters
public static Policy CreatePolicy(string number, ProductId product,
    string contractHolder, DateTime effective, DateTime expiry,
    decimal premium, string currency, PolicyStatus status,
    List<PolicyMember> members, Dictionary<string, object> fields) // WRONG - too many params
```

## Architecture & Key Patterns

### Clean Architecture Layers

- **API Layer** (`CoverGo.PoliciesV3.Api`): GraphQL endpoints, HotChocolate schema, authentication
- **Application Layer** (`CoverGo.PoliciesV3.Application`): CQRS commands/queries, orchestration ONLY (NO business logic)
- **Domain Layer** (`CoverGo.PoliciesV3.Domain`): Entities, value objects, domain services, specifications, business rules
- **Infrastructure Layer** (`CoverGo.PoliciesV3.Infrastructure`): PostgreSQL repositories, external services, data persistence ONLY

### CQRS Pattern Implementation

- Commands: `ICommand<TResponse>` with `ICommandHandler<TCommand, TResponse>`
- Queries: `IQuery<TResponse>` with `IQueryHandler<TQuery, TResponse>`
- Features organized by aggregate: `Features/Policies/CreatePolicy/`, `Features/PolicyMembers/ValidateUpload/`
- Auto-validation via `ValidationCommandHandlerDecorator` using FluentValidation
- **CRITICAL**: Handlers ONLY orchestrate - ALL business logic in Domain layer

### Advanced Async Validation Architecture

```csharp
// Concurrent processing with resource management
public interface IConcurrentMemberProcessor
{
    Task<Dictionary<int, List<ValidationError>>> ProcessMembersAsync(
        int memberCount,
        Func<int, Task<List<ValidationError>>> memberValidator,
        CancellationToken cancellationToken);
}

// Configuration-driven concurrency control
public static class ValidationConstants
{
    public static class Concurrency
    {
        public const int DefaultMaxConcurrentValidations = 20;
        public const int DefaultMemberBatchSize = 1000;
    }
}

// SemaphoreSlim-based throttling implementation
public class ConcurrentMemberProcessor : IConcurrentMemberProcessor
{
    private readonly SemaphoreSlim _semaphore;
    // Uses OrderablePartitioner for optimized work distribution
}
```

### Multi-Tenant Architecture

```csharp
// Connection strings use template replacement: "Server=localhost;Database=policies_{tenant}"
string connectionString = template.Replace("{tenant}", tenantId);
// Cached data sources per tenant for performance
NpgsqlDataSource dataSource = GetDataSourceForTenant(configuration, tenantName);
```

### Domain-Driven Design Patterns

- **Aggregates**: `Policy`, `PolicyMember`, `PolicyMemberUpload` with strong consistency boundaries
- **Value Objects**: `PolicyId`, `PolicyStatus`, `PolicyMemberId` (strongly-typed IDs)
- **Domain Events**: `PolicyCreatedEvent`, `PolicyMemberCreatedEvent` for side effects
- **Specifications**: Complex business rules in `Domain/Specifications/` (e.g., `MemberFieldsMustMatchSchemaSpecification`)
- **Domain Services**: Business logic that doesn't belong to a single entity
- **Result Pattern**: Functional error handling with `Result<T>` throughout all layers

### External Service Integration Patterns

```csharp
// Tenant-aware HTTP clients with JWT authentication
public class LegacyPolicyService(HttpClient httpClient, TenantId tenantId) : ILegacyPolicyService
{
    private readonly PoliciesClient _client = new(httpClient);

    // All external calls include tenant context
    public async Task<PolicyDto?> GetPolicyById(string policyId, CancellationToken ct) =>
        await _client.Policy_GetPolicyAsync(tenantId.Value, policyId, null, ct);
}

// GraphQL client extensions for consistent error handling
var response = await client.SendQueryAndEnsureAsync<TResponse>(query, cancellationToken);
// Automatically validates GraphQL errors and deserializes response

// JWT token propagation in Hangfire jobs
public class JwtServiceDelegatingHandler : DelegatingHandler
{
    // Automatically injects service account tokens for current tenant
}
```

## Essential Development Workflows

### Database Operations

```bash
# Run migrations for all tenants
dotnet run --project src/CoverGo.PoliciesV3.Api -- --migrate-all

# Add new migration
dotnet ef migrations add MigrationName --project src/CoverGo.PoliciesV3.Infrastructure --startup-project src/CoverGo.PoliciesV3.Api

# Update database manually
dotnet ef database update --project src/CoverGo.PoliciesV3.Infrastructure --startup-project src/CoverGo.PoliciesV3.Api
```

### Build & Test Commands

```bash
dotnet build              # Build entire solution
dotnet test               # Run all tests
dotnet watch run          # Hot reload development
dotnet publish            # Create production build
```

### GraphQL Development

- Schema uses "policies\_" prefix for types: `policies_CreatePolicyInput`, `policies_PolicyType`
- Mutation conventions: `{prefix}_{MutationName}Input/Payload/Error` pattern
- Use `[ExtendObjectType(typeof(Query))]` and `[ExtendObjectType(typeof(Mutation))]` for schema extensions
- Error types auto-prefixed by `ErrorTypeNamingInterceptor`
- Centralized type registration via `GraphQLTypeAutoRegistration`

```csharp
// Domain ID registration
builder.BindRuntimeType<PolicyId, IdType>()
       .BindRuntimeType<PolicyMemberId, IdType>();

// String value object registration
builder.BindRuntimeType<PolicyStatus, StringType>()
       .AddTypeConverter<string?, PolicyStatus?>(v => v != null ? new(v) : null);
```

## Critical Code Patterns

### Repository Pattern with Caching

```csharp
// Always use the interface, caching is automatically applied via decorator
IPaginatedRepository<Policy, PolicyId> repository;

// Batch operations for performance
await repository.InsertBatchAsync(policies, cancellationToken);
await repository.UpdateBatchAsync(policies, cancellationToken);

// Cache keys include tenant ID: "{EntityName}:{tenantId}:{id}"
```

### Validation System - Universal Error Handling

```csharp
// Use Result pattern consistently with unified error handling
public async Task<Result<TResponse>> Handle(TCommand command, CancellationToken ct)
{
    // Method 1: Errors factory (recommended - simplest)
    if (string.IsNullOrEmpty(command.Email))
        return Result.Failure(Errors.Required("member.email", "Email Address"));

    // Method 2: ValidationErrorBuilder for complex context
    if (command.Age < 18)
    {
        var error = ValidationErrorBuilder.For("member.age")
            .WithLabel("Age")
            .WithContext("MinValue", 18)
            .OutOfRange(18, 65);
        return Result.Failure(error);
    }

    // Method 3: Direct constructor for full control
    if (memberExists)
    {
        var error = new ValidationError(
            ErrorCodes.MemberIdTaken,
            "memberId",
            "Member ID",
            new Dictionary<string, object?> { ["ExistingId"] = existingId }
        );
        return Result.Failure(error);
    }

    return result; // Success case
}
```

### Async Concurrency Patterns

```csharp
// Batch processing with configurable concurrency
private async Task<HashSet<string>> ProcessBatchesAsync(
    IAsyncEnumerable<(IReadOnlyList<T> Batch, int StartIndex)> batches,
    CancellationToken cancellationToken = default)
{
    var batchTasks = new List<Task<HashSet<string>>>();

    await foreach (var batch in batches.WithCancellation(cancellationToken))
    {
        batchTasks.Add(ProcessSingleBatchAsync(batch.Batch, cancellationToken));
    }

    // Process with concurrency control
    using var semaphore = new SemaphoreSlim(ValidationConstants.Concurrency.DefaultMaxConcurrentValidations);
    Task<HashSet<string>>[] concurrentTasks = batchTasks.Select(async task =>
    {
        await semaphore.WaitAsync(cancellationToken);
        try { return await task; }
        finally { semaphore.Release(); }
    }).ToArray();

    HashSet<string>[] results = await Task.WhenAll(concurrentTasks);
    // Merge results...
}
```

### Strongly-Typed Configuration

```csharp
// Use constants, never magic strings
ConfigurationConstants.ConnectionStrings.DefaultConnection
DatabaseConstants.PostgreSql.ColumnTypes.Uuid
DatabaseNamingHelpers.GetTableName<Policy>() // "policies"
```

### PostgreSQL Naming Conventions

```csharp
// All database names auto-generated in snake_case
DatabaseNamingHelpers.GetIndexName<Policy>("PolicyNumber")        // "ix_policies_policy_number"
DatabaseNamingHelpers.GetForeignKeyName<PolicyMember, Policy>("PolicyId") // "fk_policy_members_policies_policy_id"
```

## 🚫 Anti-Patterns & Forbidden Practices

### What NOT to Do

❌ **FORBIDDEN: Magic strings in any form** - Always use constants from `ConfigurationConstants`, `DatabaseConstants`, `ErrorCodes`
❌ **FORBIDDEN: Business logic in Infrastructure/Application layers** - Use domain entities, value objects, domain services
❌ **FORBIDDEN: Defensive null checks when proper typing available** - Use `required`, nullable types, fail-fast validation
❌ **FORBIDDEN: Factory methods with excessive parameters (4+)** - Use builder pattern with fluent interface
❌ **FORBIDDEN: Bypassing repository pattern** - Use `IPaginatedRepository<T, TId>`, never direct EF Context
❌ **FORBIDDEN: Ignoring tenant context** - All data access must be tenant-aware
❌ **FORBIDDEN: Mixing architectural layers** - No domain logic in controllers, no infrastructure in domain
❌ **FORBIDDEN: Skipping validation** - Use `Result<T>` pattern, implement `IValidator<T>` for commands
❌ **FORBIDDEN: Suppressing compiler warnings without fixing root causes**

### Validation Checklist

Before Submitting Code:

- [ ] All business logic is in appropriate domain layer
- [ ] No magic strings present anywhere
- [ ] Proper DDD layer separation maintained
- [ ] Fail-fast validation implemented where needed
- [ ] Strongly-typed IDs used for all entities
- [ ] All compiler warnings resolved properly
- [ ] ErrorCodes and Errors classes used consistently
- [ ] Builder pattern used for complex object construction
- [ ] Async patterns use proper cancellation and resource management

## File Organization Conventions

### Feature Structure (Application Layer)

```
Features/
  Policies/
    CreatePolicy/
      CreatePolicyCommand.cs       # Input contract
      CreatePolicyHandler.cs       # Business logic
      CreatePolicyValidator.cs     # Validation rules
    GetPagedPolicies/              # Query operations
      GetPagedPoliciesHandler.cs
      GetPagedPoliciesQuery.cs
      GetPagedPoliciesResponse.cs
  PolicyMembers/
    CreatePolicyMember/            # Member creation
    GetPagedPolicyMembers/         # Member queries
    ValidateUpload/                # Upload validation feature
      ValidatePolicyMemberUploadCommand.cs
      ValidatePolicyMemberUploadHandler.cs
      ValidatePolicyMemberUploadResponse.cs
    RegisterPolicyMemberUpload/    # Upload registration
    CancelUpload/                  # Upload cancellation
```

### Domain Organization

```
Domain/
  Policies/
    Policy.cs                     # Aggregate root
    PolicyId.cs                   # Value object
    Events/PolicyCreatedEvent.cs  # Domain events
    Exceptions/PolicyNotFoundException.cs
  PolicyMembers/                  # Member aggregate
    PolicyMember.cs               # Aggregate root
    PolicyMemberId.cs             # Strongly-typed ID
    Events/PolicyMemberCreatedEvent.cs
    Exceptions/PolicyMemberNotFoundException.cs
  PolicyMemberUploads/            # Upload aggregate
    PolicyMemberUpload.cs         # Upload processing
    PolicyMemberUploadStatus.cs   # Upload states
    PolicyMemberUploadValidationError.cs
  Specifications/                 # Business rules (80+ specifications)
    Core/ISpecification.cs        # Specification pattern infrastructure
    Member/                       # Individual member validation
      MemberFieldsMustMatchSchemaSpecification.cs
      MemberMustHaveValidPlanIdSpecification.cs
      MemberIdMustFollowBusinessRulesSpecification.cs
    Upload/                       # Upload-wide validation
      UploadMustHaveUniqueEmailsSpecification.cs
      UploadMustHaveUniqueIdentificationSpecification.cs
    Composite/                    # Multi-tier validation orchestration
      CompleteUploadValidationSpecification.cs
      UploadWideValidationSpecification.cs
      IndividualMemberValidationSpecification.cs
  Common/Validation/              # Unified validation framework
    Result.cs                     # Result pattern implementation
    ValidationError.cs            # Universal error type
    ValidationErrorBuilder.cs     # Fluent error construction
    ErrorCodes.cs                 # Standardized error codes
    Errors.cs                     # Error factory methods
```

### Infrastructure Data Access

```
Infrastructure/
  DataAccess/
    ApplicationDbContext.cs       # EF Core context
    Configurations/               # Entity configurations
      Policies/PolicyConfiguration.cs
      PolicyMembers/PolicyMemberConfiguration.cs
      PolicyMemberUploads/PolicyMemberUploadConfiguration.cs
    PostgreSql/                   # Repository implementations
      PostgreSqlRepository.cs     # Generic repository
      PostgreSqlCachedRepository.cs # Redis decorator
  FileParser/                     # File processing
    FileParserFactory.cs          # Parser factory
    CsvFileParser.cs              # CSV processing
    XlsxFileParser.cs             # Excel processing
  Services/                       # Infrastructure service implementations
    MemberValidationService.cs    # Validation orchestration
    PolicyMemberUniquenessService.cs
    UploadValidationService.cs
    ValidationOrchestrationService.cs
```

## External Integration Patterns

### GraphQL Service Calls

```csharp
// Use extensions for consistent error handling
var response = await client.SendQueryAndEnsureAsync<TResponse>(query, cancellationToken);
// Automatically validates GraphQL errors and deserializes response
```

### Multi-Tenant Service Registration

```csharp
// Services auto-resolve tenant context from request headers
services.AddScoped<TenantId>(provider => /* resolved from context */);
// Database connections automatically use correct tenant
```

## Performance & Caching Guidelines

### Redis Caching Strategy

- Automatic cache-aside pattern via `PostgreSqlCachedRepository`
- TTL: 60 minutes default, configurable per entity type
- Cache invalidation on writes (or removal during transactions)
- Batch operations maintain cache consistency

### PostgreSQL Optimization

- Use `QuerySplittingBehavior.SplitQuery` for complex joins
- PostgreSQL `xmin` system column for optimistic concurrency (mapped to `RowVersion` property)
- Connection pooling with `NpgsqlDataSource` cached per tenant

### Async Patterns

- All repository methods are async with `CancellationToken`
- Use `ConfigureAwait(false)` in infrastructure layer
- Parallel processing in validation services using `Task.WhenAll()`
- Configurable concurrency limits with `SemaphoreSlim`
- Use `OrderablePartitioner` for optimized work distribution

## Testing Conventions

### Unit Tests Structure

```csharp
// Pattern: {ClassUnderTest}Tests.cs
public class ValidatePolicyMemberUploadHandlerTests
{
    [Fact]
    [Trait("Ticket", "CH-25857")] // Link to Jira tickets
    public async Task Handle_WithValidUpload_ShouldReturnSuccess()
}
```

### Integration Tests

- Use `TestBase` with `CustomWebApplicationFactory`
- Test database per tenant with cleanup
- GraphQL client testing via `GraphQLHttpClient`

### Async Architecture Testing

```csharp
// Comprehensive async validation tests covering:
// - Race condition prevention
// - Resource leak testing
// - Cancellation coordination
// - Exception handling with context preservation
// - Performance under concurrent load
public class ValidatePolicyMemberUploadHandlerAsyncArchitectureTests
{
    [Fact]
    public async Task Handle_WithConcurrentValidations_ShouldPreventRaceConditions()
    {
        // Test concurrent execution without deadlocks
        // Verify proper resource management
        // Ensure data consistency under load
    }
}
```

## Common Anti-Patterns to Avoid

❌ **Don't use magic strings** - Always use constants from `ConfigurationConstants`, `DatabaseConstants`  
❌ **Don't bypass repository pattern** - Use `IPaginatedRepository<T, TId>`, never direct EF Context  
❌ **Don't ignore tenant context** - All data access must be tenant-aware  
❌ **Don't mix layers** - No domain logic in controllers, no infrastructure in domain  
❌ **Don't skip validation** - Use `Result<T>` pattern, implement `IValidator<T>` for commands

## Key Dependencies & Frameworks

- **HotChocolate 13.x**: GraphQL server with mutation conventions
- **Entity Framework Core 9.x**: Data access with PostgreSQL provider
- **FluentValidation**: Command validation with auto-decoration
- **Hangfire**: Background job processing with tenant context
- **Redis**: Distributed caching via `CacheProvider` abstraction
- **CoverGo.BuildingBlocks**: Internal shared libraries for CQRS, multitenancy, observability

Remember: This service emphasizes type safety, performance, and maintainability through strong architectural boundaries and consistent patterns.

## 🛡️ Validation System Architecture

### Core Concept: Unified Error Handling

**One Error Type, One Pattern, Everywhere** - Every validation failure uses the same `ValidationError` structure with automatic message generation and the same `Result` pattern.

```
Error Code + Property Path + Context = Auto-Generated Message
     ↓              ↓            ↓              ↓
   REQUIRED  +  "member.email"  +   {}     = "Email is required"
 OUT_OF_RANGE + "member.age"   + {Min:18}  = "Age must be at least 18"
MEMBER_ID_TAKEN + "memberId"   + {ExistingId} = "Member ID 'EMP001' is already taken"
```

### Three Ways to Create Validation Errors

#### 1. Errors Factory (Recommended - Simplest)

```csharp
// Field validation
var error = Errors.Required("member.email", "Email Address");
var error = Errors.OutOfRange("member.age", 18, 65, "Age");
var error = Errors.UniqueViolation("member.email", "Email", "upload");

// Business rules
var error = Errors.MemberNotFound("primaryMemberId", memberId, "Primary Member");
var error = Errors.InvalidPlanId("planId", planId, availablePlans, "Plan");

// Upload errors
var error = Errors.MissingColumns(missingColumns);
var error = Errors.ExtraColumns(extraColumns);
```

#### 2. ValidationErrorBuilder (For Complex Context)

```csharp
var error = ValidationErrorBuilder
    .For("member.salary")
    .WithLabel("Annual Salary")
    .WithContext("Currency", "USD")
    .WithContext("MinValue", 30000)
    .WithContext("MaxValue", 500000)
    .OutOfRange(30000, 500000);
```

#### 3. Direct Constructor (Full Control)

```csharp
var error = new ValidationError(
    ErrorCodes.MemberIdTaken,
    "memberId",
    "Member ID",
    new Dictionary<string, object?> {
        ["MemberId"] = "EMP001",
        ["ExistingPolicyMemberId"] = "PM123"
    }
);
// Auto-generates: "Member ID 'EMP001' is already taken by policy member PM123"
```

### Smart Message Generation Examples

```csharp
// Required field
Code: "REQUIRED", Path: "member.email", Label: "Email Address"
→ Message: "Email Address is required"

// Out of range with context
Code: "OUT_OF_RANGE", Path: "member.age", Context: {MinValue: 18, MaxValue: 65}
→ Message: "Age must be between 18 and 65"

// Business rule with complex context
Code: "MEMBER_ID_TAKEN", Path: "memberId", Context: {MemberId: "EMP001", ExistingId: "PM123"}
→ Message: "Member ID 'EMP001' is already taken by policy member PM123"

// Upload validation
Code: "MISSING_COLUMNS", Context: {MissingColumns: ["Email", "Phone"]}
→ Message: "Mandatory column(s) are missing: Email, Phone"
```

### Result Pattern Implementation

```csharp
// Success cases
Result<Policy> result = policy;  // Implicit conversion
var result = Result<Policy>.Success(policy);

// Failure cases
Result<Policy> result = validationError;  // Implicit conversion
var result = Result<Policy>.Failure(errors);

// Functional composition
return ValidateInput(request)
    .Bind(data => ProcessBusinessRules(data))
    .Map(data => CreateEntity(data));

// Chaining operations
result.OnSuccess(policy => logger.LogInfo($"Policy {policy.Id} created"))
      .OnFailure(errors => logger.LogError($"Validation failed: {errors.Count} errors"));
```

### Three-Tier Validation Architecture

1. **CompleteUploadValidationSpecification** - Orchestrates entire upload validation
2. **UploadWideValidationSpecification** - Cross-record business rules (uniqueness, dependencies)
3. **IndividualMemberValidationSpecification** - Single record validation (schema, business rules)

### Domain Specifications Pattern

```csharp
public class MemberAgeSpecification : IAsyncSpecification<MemberValidationContext>
{
    public async Task<Result> IsSatisfiedBy(MemberValidationContext context, CancellationToken cancellationToken = default)
    {
        // Use Task.CompletedTask for in-memory operations to maintain async pattern consistency
        await Task.CompletedTask;

        var plan = context.Plan;
        var age = context.Member.CalculateAge();

        if (age < plan.MinAge)
            return Result.Failure(Errors.AgeTooLow("age", plan.MinAge, "Age"));

        if (age > plan.MaxAge)
            return Result.Failure(Errors.AgeTooHigh("age", plan.MaxAge, "Age"));

        return Result.Success();
    }
}
```

### Validation in CQRS Handlers

```csharp
public class ValidatePolicyMemberUploadHandler
{
    public async Task<Result<ValidateUploadResponse>> Handle(ValidateUploadCommand request)
    {
        var context = CompleteValidationContext.Create(upload, policy, resolvedData);

        // Three-tier validation orchestration
        BatchValidationResult result = await _completeValidationSpec.ValidateBatchAsync(context);

        return result.IsValid
            ? Result<ValidateUploadResponse>.Success(new ValidateUploadResponse(upload))
            : Result<ValidateUploadResponse>.Failure(result.GetAllErrors());
    }
}
```

### Validation System Benefits

✅ **Consistency** - Same error structure everywhere: API, Domain, Infrastructure  
✅ **Developer Experience** - IntelliSense-friendly factory methods, automatic message generation  
✅ **Maintainability** - Centralized error message logic, easy to update formats globally  
✅ **API Integration** - GraphQL error extensions use ValidationError structure  
✅ **Testability** - Easy to assert on specific error codes, predictable error messages

---

## 📊 Project Features & GraphQL Operations

### Core Operations

#### Queries

- **GetPagedPolicies**: Retrieve policies with pagination support
- **GetPagedPolicyMembers**: Retrieve policy members with pagination

#### Mutations

- **CreatePolicy**: Create new insurance policies
- **CreatePolicyMember**: Add members to policies
- **RegisterPolicyMemberUpload**: Initialize bulk member upload process
- **ValidatePolicyMemberUpload**: Validate uploaded member data
- **CancelPolicyMemberUpload**: Cancel ongoing upload operations

### Technology Stack

- **.NET 9.0** with ASP.NET Core and **HotChocolate 13.x** GraphQL
- **Entity Framework Core 9.x** with PostgreSQL (via Npgsql) and MongoDB
- **Clean Architecture** with **CQRS** (via MediatR) and **Repository Pattern**
- **AutoMapper**, **FluentValidation**, **Serilog**, **OpenTelemetry**
- **JWT Bearer** authentication, **Azure Key Vault** secret management
- **NPOI** for Excel processing, **SharpCompress** for file compression

---

Remember: This service emphasizes **unified error handling**, **type safety**, **performance**, and **maintainability** through strong architectural boundaries and consistent validation patterns.
