# CoverGo Policies V3 - Unified Validation Error System

## 🎯 Core Concept: Unified Error Handling

This system provides **one consistent way** to handle all validation errors across the entire application - from field validation to complex business rules. Every validation error uses the same structure, the same creation patterns, and the same Result pattern.

## 🏗️ System Overview

```
Error Code + Property Path + Context = Auto-Generated Message
     ↓              ↓            ↓              ↓
   REQUIRED  +  "member.email"  +   {}     = "Email is required"
 OUT_OF_RANGE + "member.age"   + {Min:18}  = "Age must be at least 18"
MEMBER_ID_TAKEN + "memberId"   + {ExistingId} = "Member ID 'EMP001' is already taken by policy member PM123"
```

### Key Innovation: **Smart Message Generation**

ValidationError **automatically generates human-readable messages** based on:

- **ErrorCode**: Standardized codes (REQUIRED, OUT_OF_RANGE, etc.)
- **PropertyPath**: Field location ("member.email", "dependent.age")
- **PropertyLabel**: Display name ("Email Address", "Age")
- **Context**: Additional data (min/max values, existing IDs, etc.)

---

## 🔧 Core Components

### 1. ValidationError - The Universal Error Type

```csharp
public sealed record ValidationError(
    string Code,           // REQUIRED, OUT_OF_RANGE, MEMBER_NOT_FOUND
    string PropertyPath,   // member.email, dependent.age
    string? PropertyLabel, // "Email Address", "Age"
    IReadOnlyDictionary<string, object?> Context // {MinAge: 18, MaxAge: 65}
)
{
    public string Message { get; } // Auto-generated: "Age must be between 18 and 65"
}
```

### 2. ValidationConstants - Centralized Configuration

```csharp
public static class ValidationConstants
{
    public static class Concurrency
    {
        public const int DefaultMaxConcurrentValidations = 20;
        public const int DefaultMemberBatchSize = 1000;
    }

    public static class Timeouts
    {
        public const int RegexTimeoutMilliseconds = 3000;
        public const int DefaultValidationTimeoutMinutes = 30;
    }
}
```

### 2. Result Pattern - Unified Return Type

```csharp
// Success
Result<Policy> result = policy;
Result operation = Result.Success();

// Failure
Result<Policy> result = validationError;
Result operation = validationError;

// Functional composition
return ValidateInput(request)
    .Bind(data => ProcessBusinessRules(data))
    .Map(data => CreateEntity(data));
```

### 3. IConcurrentMemberProcessor - Async Validation Architecture

```csharp
public interface IConcurrentMemberProcessor
{
    Task<Dictionary<int, List<ValidationError>>> ProcessMembersAsync(
        int memberCount,
        Func<int, Task<List<ValidationError>>> memberValidator,
        CancellationToken cancellationToken);
}

public class ConcurrentMemberProcessor : IConcurrentMemberProcessor
{
    private readonly int _maxConcurrentValidations;

    // Uses SemaphoreSlim and Partitioner.Create for optimized concurrent processing
    // Configurable concurrency limits with ValidationConstants.DefaultMaxConcurrentValidations
}
```

### 3. ErrorCodes - Standardized Categories

The system uses a comprehensive set of standardized error codes, categorized by domain area.

```csharp
public static class ErrorCodes
{
    #region Field Validation
    public const string Required = "REQUIRED";
    public const string InvalidFormat = "INVALID_FORMAT";
    public const string InvalidType = "INVALID_TYPE";
    public const string UniqueViolation = "UNIQUE_VIOLATION";
    public const string InvalidOption = "INVALID_OPTION";
    public const string PatternMismatch = "PATTERN_MISMATCH";
    public const string InvalidString = "INVALID_STRING";
    public const string InvalidNumber = "INVALID_NUMBER";
    public const string OutOfRange = "OUT_OF_RANGE";
    public const string TooLong = "TOO_LONG";
    public const string TooShort = "TOO_SHORT";
    public const string NotAllowed = "NOT_ALLOWED";
    #endregion

    #region Member Validation
    public const string MemberNotFound = "MEMBER_NOT_FOUND";
    public const string MemberIdTaken = "MEMBER_ID_TAKEN";
    public const string MemberNotInContractHolder = "MEMBER_NOT_IN_CONTRACT_HOLDER";
    public const string MemberProcessingFailed = "MEMBER_PROCESSING_FAILED";
    #endregion

    #region Plan Validation
    public const string InvalidPlanId = "PRODUCT_PLAN_NOT_FOUND";
    public const string DependentPlanMismatch = "DEPENDENT_PLAN_MISMATCH";
    #endregion

    #region Age Validation
    public const string AgeTooLow = "AGE_TOO_LOW";
    public const string AgeTooHigh = "AGE_TOO_HIGH";
    public const string EmployeeMinAge = "FIELD_MIN_AGE";
    public const string EmployeeMaxAge = "FIELD_MAX_AGE";
    public const string ChildMinDays = "FIELD_MIN_CHILD_DAYS";
    public const string SpouseMinAge = "FIELD_MIN_SPOUSE_AGE";
    #endregion

    #region Upload Validation
    public const string EmptyFile = "EMPTY_FILE";
    public const string InvalidRow = "INVALID_ROW";
    public const string InvalidXlsxFile = "INVALID_XLSX_FILE";
    public const string InvalidCsvFile = "INVALID_CSV_FILE";
    public const string UnsupportedFileType = "UNSUPPORTED_FILE_TYPE";
    public const string NoColumn = "NO_COLUMN";
    public const string NoMember = "NO_MEMBER";
    public const string MissingColumns = "MISSING_COLUMNS";
    public const string ExtraColumns = "EXTRA_COLUMNS";
    public const string OneOfRequired = "ONE_OF_REQUIRED";
    public const string MissingMandatoryColumns = "MISSING_MANDATORY_COLUMNS";
    public const string MissingOneOfMandatoryColumns = "MISSING_ONE_OF_MANDATORY_COLUMNS";
    public const string ExtraColumn = "EXTRA_COLUMN";
    public const string FileProcessingFailed = "FILE_PROCESSING_FAILED";
    public const string PolicyMemberUploadNotFound = "POLICY_MEMBER_UPLOAD_NOT_FOUND";
    public const string InvalidPolicyMemberUploadStatus = "INVALID_POLICY_MEMBER_UPLOAD_STATUS";
    public const string UploadFileNotFound = "UPLOAD_FILE_NOT_FOUND";
    public const string UploadFileProcessingError = "UPLOAD_FILE_PROCESSING_ERROR";
    public const string BadSchemaConfig = "BAD_SCHEMA_CONFIG";
    public const string NoValidMembersForImport = "NO_VALID_MEMBERS_FOR_IMPORT";
    public const string UploadValidationLocked = "UPLOAD_VALIDATION_LOCKED";
    #endregion

    #region Policy Validation
    public const string PolicyNotFound = "POLICY_NOT_FOUND";
    public const string PolicyIssued = "POLICY_ISSUED";
    public const string PolicyContractHolderNotFound = "POLICY_CONTRACT_HOLDER_NOT_FOUND";
    public const string PolicyProductIdMissing = "POLICY_PRODUCT_ID_MISSING";
    public const string PolicyMemberNotFound = "POLICY_MEMBER_NOT_FOUND";
    public const string PolicyMemberExists = "POLICY_MEMBER_EXISTS";
    public const string EffectiveDateOutsidePolicyDates = "EFFECTIVE_DATE_OUTSIDE_POLICY_DATES";
    public const string PolicyAlreadyCancelled = "POLICY_ALREADY_CANCELLED";
    public const string InvalidProductIdComponent = "INVALID_PRODUCT_ID_COMPONENT";
    public const string InvalidPolicyMemberStateDate = "INVALID_POLICY_MEMBER_STATE_DATE";
    public const string PolicyMemberStateOverlap = "POLICY_MEMBER_STATE_OVERLAP";
    #endregion

    #region Endorsement Validation
    public const string EndorsementCannotBeChanged = "ENDORSEMENT_CANNOT_BE_CHANGED";
    public const string EndorsementNotFound = "ENDORSEMENT_NOT_FOUND";
    #endregion

    #region General
    public const string UnexpectedError = "UNEXPECTED_ERROR";
    public const string NotFound = "NOT_FOUND";
    public const string InvalidState = "INVALID_STATE";
    public const string NotSatisfied = "NOT_SATISFIED";
    public const string ValidationFailed = "VALIDATION_FAILED";
    #endregion
}
```

---

## 🛠️ Three Ways to Create Errors

### 1. **Errors Factory** (Recommended - Simplest)

```csharp
// Field validation
var error = Errors.Required("member.email", "Email Address");
var error = Errors.OutOfRange("member.age", 18, 65, "Age");
var error = Errors.UniqueViolation("member.email", "Email", "upload");

// Business rules
var error = Errors.MemberNotFound("primaryMemberId", memberId, "Primary Member");
var error = Errors.InvalidPlanId("planId", planId, availablePlans, "Plan");

// Upload errors
var error = Errors.MissingColumns(missingColumns);
var error = Errors.ExtraColumns(extraColumns);
```

### 2. **ValidationErrorBuilder** (For Complex Context)

```csharp
var error = ValidationErrorBuilder
    .For("member.salary")
    .WithLabel("Annual Salary")
    .WithContext("Currency", "USD")
    .WithContext("MinValue", 30000)
    .WithContext("MaxValue", 500000)
    .OutOfRange(30000, 500000);
```

### 3. **Direct Constructor** (Full Control)

```csharp
var error = new ValidationError(
    ErrorCodes.MemberIdTaken,
    "memberId",
    "Member ID",
    new Dictionary<string, object?> {
        ["MemberId"] = "EMP001",
        ["ExistingPolicyMemberId"] = "PM123"
    }
);
// Auto-generates: "Member ID 'EMP001' is already taken by policy member PM123"
```

---

## 🔄 Smart Message Generation Examples

The system automatically generates contextual messages:

```csharp
// Required field
Code: "REQUIRED", Path: "member.email", Label: "Email Address"
→ Message: "Email Address is required"

// Out of range with context
Code: "OUT_OF_RANGE", Path: "member.age", Label: "Age", Context: {MinValue: 18, MaxValue: 65}
→ Message: "Age must be between 18 and 65"

// Business rule with complex context
Code: "MEMBER_ID_TAKEN", Path: "memberId", Context: {MemberId: "EMP001", ExistingPolicyMemberId: "PM123"}
→ Message: "Member ID 'EMP001' is already taken by policy member PM123"

// Upload validation
Code: "MISSING_COLUMNS", Path: "columns", Context: {MissingColumns: ["Email", "Phone"]}
→ Message: "Mandatory column(s) are missing: Email, Phone"

// Plan validation
Code: "PRODUCT_PLAN_NOT_FOUND", Path: "planId", Context: {PlanId: "BASIC", ProductId: "HEALTH001"}
→ Message: "Plan BASIC not found on product HEALTH001"
```

---

## 📋 Usage Patterns

### Simple Field Validation

```csharp
public Result ValidateInput(CreateMemberRequest request)
{
    var errors = new List<ValidationError>();

    if (string.IsNullOrEmpty(request.Email))
        errors.Add(Errors.Required("email", "Email Address"));

    if (request.Age < 18 || request.Age > 65)
        errors.Add(Errors.OutOfRange("age", 18, 65, "Age"));

    return errors.Any() ? Result.Failure(errors) : Result.Success();
}
```

### Business Rule Validation

```csharp
public async Task<Result> ValidateMemberUniqueness(string memberId)
{
    var existing = await _repository.FindByMemberIdAsync(memberId);
    if (existing != null)
    {
        return Result.Failure(
            Errors.MemberIdTaken("memberId", memberId, existing.Id.Value, "Member ID")
        );
    }
    return Result.Success();
}
```

### Upload File Validation

```csharp
public Result ValidateUploadStructure(UploadFile file)
{
    var errors = new List<ValidationError>();

    // Check required columns
    var missingColumns = GetMissingRequiredColumns(file.Headers);
    if (missingColumns.Any())
        errors.Add(Errors.MissingColumns(missingColumns));

    // Check for extra columns
    var extraColumns = GetExtraColumns(file.Headers);
    if (extraColumns.Any())
        errors.Add(Errors.ExtraColumns(extraColumns));

    return errors.Any() ? Result.Failure(errors) : Result.Success();
}
```

### Domain Specifications

```csharp
public class MemberAgeSpecification : IAsyncSpecification<MemberValidationContext>
{
    public async Task<Result> IsSatisfiedBy(MemberValidationContext context, CancellationToken cancellationToken = default)
    {
        // Use Task.CompletedTask for in-memory operations to maintain async pattern consistency
        await Task.CompletedTask;

        var plan = context.Plan;
        var age = context.Member.CalculateAge();

        if (age < plan.MinAge)
            return Result.Failure(Errors.AgeTooLow("age", plan.MinAge, "Age"));

        if (age > plan.MaxAge)
            return Result.Failure(Errors.AgeTooHigh("age", plan.MaxAge, "Age"));

        return Result.Success();
    }
}
```

---

## 🚀 Advanced Async Validation Architecture

### Concurrent Member Processing with Resource Management

The system includes sophisticated async validation architecture for high-performance batch processing:

```csharp
public class IndividualMemberValidationSpecification : IAsyncSpecification<IndividualMemberValidationContext>
{
    private readonly IConcurrentMemberProcessor memberProcessor;

    public async Task<Result<BatchValidationResult>> IsSatisfiedBy(
        IndividualMemberValidationContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Process members concurrently with configurable limits
            Dictionary<int, List<ValidationError>> errorDictionary = await memberProcessor.ProcessMembersAsync(
                context.MemberCount,
                memberIndex => ValidateIndividualMemberAsync(context, memberIndex, cancellationToken),
                cancellationToken);

            int validCount = context.MemberCount - errorDictionary.Count;
            int invalidCount = errorDictionary.Count;

            logger.LogInformation("Individual member validation completed. Valid: {ValidCount}, Invalid: {InvalidCount}",
                validCount, invalidCount);

            return new BatchValidationResult
            {
                ValidCount = validCount,
                InvalidCount = invalidCount,
                RowErrors = errorDictionary
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in individual member validation");
            throw;
        }
    }
}
```

### Key Architectural Features

#### ✅ **Configurable Concurrency Control**

- **MaxConcurrentValidations**: 20 (default) - Controls parallel validation threads
- **DefaultMemberBatchSize**: 1000 - Optimizes memory usage for large uploads
- **RegexTimeoutMilliseconds**: 3000 - Prevents regex DoS attacks

#### ✅ **Resource Management**

- `SemaphoreSlim` for thread-safe concurrency limiting
- `OrderablePartitioner` for optimized work distribution
- `ConcurrentDictionary` for thread-safe error collection
- Proper disposal patterns for resource cleanup

#### ✅ **Validation Pipeline Orchestration**

```csharp
protected virtual async Task<List<ValidationError>> ValidateIndividualMemberAsync(
    IndividualMemberValidationContext context,
    int memberIndex,
    CancellationToken cancellationToken)
{
    // Sequential validation stages
    await ExecuteSchemaValidation(context, memberFields, memberErrors, cancellationToken);
    await ExecuteEffectiveDateValidation(context, memberFields, memberErrors, cancellationToken);
    await ExecuteMemberIdBusinessRules(context, memberFields, memberErrors, cancellationToken);
    await ExecutePlanIdValidation(context, memberFields, memberErrors, cancellationToken);

    // Parallel uniqueness validations for performance
    Task<Result>[] uniquenessSpecs =
    [
        memberUniqueEmailSpec.IsSatisfiedBy(uniquenessContext, cancellationToken),
        memberUniqueHkidSpec.IsSatisfiedBy(uniquenessContext, cancellationToken),
        memberUniquePassportSpec.IsSatisfiedBy(uniquenessContext, cancellationToken),
        memberUniqueStaffSpec.IsSatisfiedBy(uniquenessContext, cancellationToken)
    ];

    Result[] results = await Task.WhenAll(uniquenessSpecs);

    // Conditional dependent validation
    if (memberFields.IsDependent())
    {
        await ExecuteDependentValidation(context, memberFields, memberErrors, cancellationToken);
    }
}
```

#### ✅ **Comprehensive Error Handling**

- Exception wrapping with member context preservation
- Graceful degradation on individual member failures
- Detailed logging for debugging and monitoring
- Memory-efficient error aggregation

---

## 🎯 Benefits of Unified System

### ✅ **Consistency**

- Same error structure everywhere: API, Domain, Infrastructure
- Predictable error codes and messages
- Unified testing patterns

### ✅ **Developer Experience**

- IntelliSense-friendly factory methods
- Automatic message generation
- Type-safe error handling

### ✅ **Maintainability**

- Centralized error message logic
- Easy to update error formats globally
- Consistent error categorization

### ✅ **API Integration**

- GraphQL error extensions use ValidationError structure
- Frontend can handle errors consistently
- Rich context for debugging

### ✅ **Testability**

- Easy to assert on specific error codes
- Context validation in tests
- Predictable error messages

---

## 🔧 Integration with Application Layers

### **Domain Layer**: Business Rules

```csharp
// Specifications return Results with business rule violations
var result = await memberUniquenessSpec.IsSatisfiedBy(context, cancellationToken);
```

### **Application Layer**: Use Cases

```csharp
// Command handlers return Results
public async Task<Result<Policy>> Handle(CreatePolicyCommand cmd) { /* */ }
```

### **Infrastructure Layer**: External Integration

```csharp
// File parsing returns Results with validation errors
public Result<ParsedFile> ParseExcelFile(Stream file) { /* */ }
```

### **API Layer**: GraphQL/REST

```csharp
// ValidationErrors automatically convert to API error responses
return result.IsFailure ? BadRequest(result.Errors) : Ok(result.Value);
```

---

## 🏆 Key Takeaway

**One Error Type, One Pattern, Everywhere**

Every validation failure in the system - whether it's a missing required field, a complex business rule violation, or a file upload issue - uses the same `ValidationError` structure with automatic message generation and the same `Result` pattern for handling. This creates a unified, predictable, and maintainable validation system across the entire application.

---

## 🧪 Comprehensive Async Architecture Testing

The validation system includes extensive test coverage for async patterns and edge cases:

### Test Categories

#### **Race Condition Prevention**

- Concurrent validation safety tests
- Thread-safe error collection verification
- Resource contention handling

#### **Resource Management**

- Memory leak prevention tests
- Proper disposal pattern verification
- Resource cleanup on cancellation

#### **Performance & Scalability**

- Batch processing optimization tests
- Concurrency limit validation
- Timeout handling verification

#### **Exception Handling**

- Exception propagation tests
- Graceful degradation verification
- Error context preservation

#### **Cancellation Coordination**

- CancellationToken propagation tests
- Cooperative cancellation verification
- Deadlock prevention validation

### Test Implementation Example

```csharp
[Test]
public async Task ValidateUpload_WhenConcurrentValidationsExecute_ShouldPreventRaceConditions()
{
    // Arrange: Multiple concurrent validation contexts
    var uploadValidationTasks = Enumerable.Range(0, ValidationConstants.Concurrency.DefaultMaxConcurrentValidations)
        .Select(_ => CreateValidationTask())
        .ToArray();

    // Act: Execute concurrent validations
    var results = await Task.WhenAll(uploadValidationTasks);

    // Assert: No race conditions, consistent results
    AssertNoRaceConditions(results);
    AssertResourcesProperlyReleased();
}
```

This comprehensive testing ensures the async validation architecture performs reliably under concurrent load while maintaining data consistency and resource efficiency.
