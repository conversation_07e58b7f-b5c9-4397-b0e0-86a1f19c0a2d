# Auto detect text files and perform LF normalization
* text=auto

# C# files
*.cs text diff=csharp eol=lf
*.csx text diff=csharp
*.vb text
*.vbx text
*.fs text
*.fsx text
*.fsi text

# Project files
*.csproj text
*.vbproj text
*.vcxproj text
*.vcxproj.filters text
*.proj text
*.projitems text
*.shproj text
*.fsproj text
*.dbproj text
*.sln text eol=lf

# Configuration files
*.config text
*.json text
*.xml text
*.yml text
*.yaml text

# Documentation
*.md text
*.txt text

# Scripts
*.ps1 text
*.sh text eol=lf
*.bat text eol=lf
*.cmd text eol=lf

# Ignore binary files
*.dll binary
*.exe binary
*.pdb binary
*.nupkg binary
*.snupkg binary
