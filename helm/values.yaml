covergo-app:
  fullnameOverride: "covergo-policies-v3"
  labels:
    team: "backend"
  image:
    repository: registry-name/covergo/policies-v3
  deployment:
    create: true
    startupProbe:
      enabled: true
      httpGet:
        path: /startupz
        port: 8080
    readinessProbe:
      enabled: true
      httpGet:
        path: /readyz
        port: 8080
    livenessProbe:
      enabled: true
      httpGet:
        path: /healthz
        port: 8080
    resources: 
      limits:
        cpu: 1000m
        memory: 2000M
      requests:
        cpu: 500m
        memory: 1000M
    ports:
      containerPort: 8080
  service:
    create: true
    type: ClusterIP
    port: 8080
    targetPort: 8080
  ports:
    containerPort: 8080
  autoscaling:
    enabled: true
    cooldownPeriod: 300
    maxReplicaCount: 6
    minReplicaCount: 3
    pollingInterval: 30
    triggers:
      - typeName: Utilization
        typeValue: "75"
        type: cpu
      - typeName: Utilization
        typeValue: "75"
        type: memory
